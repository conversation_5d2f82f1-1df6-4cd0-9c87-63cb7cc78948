<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="https://storage.yandexcloud.net/lpo-test/dist/favicon.ico">
    <title>Переход на внешний сайт - ЛитПортал</title>
    <style>
        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        .container {
            max-width: 500px;
            background: white;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 20px;
            background: #2196F3;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        
        h1 {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #333;
        }
        
        .url-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            word-break: break-all;
            font-family: monospace;
            color: #495057;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn-primary {
            background: #2196F3;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1976D2;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }
        
        .countdown {
            margin-top: 20px;
            color: #666;
            font-size: 14px;
        }
        
        .browser-info {
            margin-top: 20px;
            padding: 15px;
            background: #e3f2fd;
            border-radius: 8px;
            color: #1565c0;
            font-size: 14px;
        }
        
        @media (max-width: 480px) {
            .container {
                margin: 20px;
                padding: 30px 20px;
            }
            
            .buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🔗</div>
        <h1>Переход на внешний сайт</h1>
        <p>Вы переходите на внешний сайт:</p>
        <div class="url-display" id="targetUrl">Загрузка...</div>
        
        <div class="buttons">
            <a href="#" id="continueBtn" class="btn btn-primary">Продолжить</a>
            <a href="javascript:history.back()" class="btn btn-secondary">Назад</a>
        </div>
        
        <div class="countdown" id="countdown"></div>
        <div class="browser-info" id="browserInfo" style="display: none;"></div>
    </div>

    <script>
        // Получаем параметры из URL
        const urlParams = new URLSearchParams(window.location.search);
        const targetUrl = urlParams.get('to');
        const browserType = urlParams.get('badbrowser');
        
        // Декодируем URL
        const decodedUrl = targetUrl ? decodeURIComponent(targetUrl) : '';
        
        // Отображаем URL
        document.getElementById('targetUrl').textContent = decodedUrl || 'Неизвестный адрес';
        
        // Настраиваем кнопку продолжения
        const continueBtn = document.getElementById('continueBtn');
        if (decodedUrl) {
            continueBtn.href = decodedUrl;
        } else {
            continueBtn.style.display = 'none';
        }
        
        // Показываем информацию о браузере, если это переход для обновления браузера
        if (browserType) {
            const browserInfo = document.getElementById('browserInfo');
            const browserNames = {
                'firefox': 'Mozilla Firefox',
                'chrome': 'Google Chrome',
                'opera': 'Opera',
                'yandex': 'Яндекс.Браузер'
            };
            
            const browserName = browserNames[browserType] || browserType;
            browserInfo.innerHTML = `Рекомендуется установить <strong>${browserName}</strong> для лучшей работы с сайтом.`;
            browserInfo.style.display = 'block';
        }
        
        // Автоматический редирект через 10 секунд
        let countdown = 10;
        const countdownElement = document.getElementById('countdown');
        
        function updateCountdown() {
            if (countdown > 0) {
                countdownElement.textContent = `Автоматический переход через ${countdown} секунд...`;
                countdown--;
                setTimeout(updateCountdown, 1000);
            } else {
                if (decodedUrl) {
                    window.location.href = decodedUrl;
                }
            }
        }
        
        // Запускаем обратный отсчет только если есть валидный URL
        if (decodedUrl) {
            updateCountdown();
        }
        
        // Обработка клика по кнопке "Продолжить"
        continueBtn.addEventListener('click', function(e) {
            if (!decodedUrl) {
                e.preventDefault();
                alert('Неверный адрес для перехода');
            }
        });
    </script>
</body>
</html>
