import json
from channels.generic.websocket import AsyncWebsocketConsumer

class BookChapterConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        print(f"Попытка WebSocket connect: book_id={self.scope['url_route']['kwargs']['book_id']}, user={self.scope.get('user')}")
        self.book_id = self.scope['url_route']['kwargs']['book_id']
        self.room_group_name = f'book_{self.book_id}'
        await self.channel_layer.group_add(self.room_group_name, self.channel_name)
        await self.accept()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(self.room_group_name, self.channel_name)

    async def receive(self, text_data):
        # Можно реализовать обработку входящих сообщений от клиента, если нужно
        pass

    async def chapter_published(self, event):
        await self.send(text_data=json.dumps({
            'type': 'chapter_published',
            'chapter_id': event['chapter_id'],
            'title': event['title'],
            'book_is_finished': event.get('book_is_finished', False),  # Для обратной совместимости
            'book_status': event.get('book_status', 'in_progress'),  # Новое поле status
            'is_batch_operation': event.get('is_batch_operation', False),  # Флаг массовой операции
        }))

    async def chapter_unpublished(self, event):
        await self.send(text_data=json.dumps({
            'type': 'chapter_unpublished',
            'chapter_id': event['chapter_id'],
            'title': event['title'],
            'book_is_finished': event.get('book_is_finished', False),  # Для обратной совместимости
            'book_status': event.get('book_status', 'draft'),  # Новое поле status
            'is_batch_operation': event.get('is_batch_operation', False),  # Флаг массовой операции
        }))

    async def chapters_batch_published(self, event):
        await self.send(text_data=json.dumps({
            'type': 'chapters_batch_published',
            'chapters': event['chapters'],
            'count': event['count'],
            'book_is_finished': event.get('book_is_finished', False),
            'book_status': event.get('book_status', 'in_progress'),
            'was_scheduled': event.get('was_scheduled', False),
        }))

    async def chapters_batch_unpublished(self, event):
        await self.send(text_data=json.dumps({
            'type': 'chapters_batch_unpublished',
            'chapters': event['chapters'],
            'count': event['count'],
            'book_is_finished': event.get('book_is_finished', False),
            'book_status': event.get('book_status', 'draft'),
            'was_batch_operation': event.get('was_batch_operation', False),
        }))