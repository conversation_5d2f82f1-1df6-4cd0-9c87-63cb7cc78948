/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    "./index.html",
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      typography: {
        DEFAULT: {
          css: {
            maxWidth: 'none',
            img: {
              marginTop: '1em',
              marginBottom: '1em',
            },
            'img.align-left': {
              float: 'left',
              marginRight: '1em',
            },
            'img.align-right': {
              float: 'right',
              marginLeft: '1em',
            },
            'img.align-center': {
              display: 'block',
              marginLeft: 'auto',
              marginRight: 'auto',
            },
            '.indent': {
              textIndent: '2em',
            },
            a: {
              textDecoration: 'underline',
              '&:hover': {
                color: '#3b82f6',
              },
            },
          },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
