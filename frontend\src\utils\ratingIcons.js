/**
 * Форматирует число с пробелами через каждые 3 цифры
 * @param {number|string} number - Число для форматирования
 * @returns {string} Отформатированное число
 * 
 * Примеры:
 * formatNumber(1000) → "1 000"
 * formatNumber(1000000) → "1 000 000"
 * formatNumber(123456789) → "123 456 789"
 */
export const formatNumber = (number) => {
  if (number === null || number === undefined) {
    return '0';
  }
  
  const numStr = String(number);
  
  // Разделяем число на разряды по 3 цифры справа
  return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
};

/**
 * Получает URL значка рейтинга в зависимости от уровня
 * 
 * Уровни рейтинга:
 * - Начальный: 0-4999 (зеленый значок)
 * - Повышенный: 5000-49999 (синий значок)
 * - Профессиональный: 50000+ (красный значок)
 * 
 * @param {number} rating - Общий рейтинг пользователя
 * @returns {string} URL значка рейтинга
 */
export const getRatingIcon = (rating) => {
  const baseUrl = 'https://storage.yandexcloud.net/lpo-test/dist/icons/';
  
  // Обработка случаев, когда рейтинг не определен
  const ratingValue = rating || 0;
  
  if (ratingValue >= 50000) {
    return `${baseUrl}rating_red.webp`;
  } else if (ratingValue >= 5000) {
    return `${baseUrl}rating_blue.webp`;
  } else {
    return `${baseUrl}rating_green.webp`;
  }
};

/**
 * Получает название уровня рейтинга
 * @param {number} rating - Общий рейтинг пользователя
 * @returns {string} Название уровня
 */
export const getRatingLevel = (rating) => {
  const ratingValue = rating || 0;
  
  if (ratingValue >= 50000) {
    return 'Профессиональный';
  } else if (ratingValue >= 5000) {
    return 'Повышенный';
  } else {
    return 'Начальный';
  }
};

/**
 * Получает URL значка читательского рейтинга
 * @returns {string} URL значка читательского рейтинга
 */
export const getReaderRatingIcon = () => {
  const baseUrl = 'https://storage.yandexcloud.net/lpo-test/dist/icons/';
  return `${baseUrl}rating_readers.webp`;
};

/**
 * Получает стили для смещения иконки читательского рейтинга
 * @param {number} offsetX - Смещение по горизонтали в пикселях (положительное = вправо)
 * @param {number} offsetY - Смещение по вертикали в пикселях (положительное = вниз)
 * @returns {Object} CSS стили для применения к иконке
 * 
 * Примеры использования:
 * getReaderRatingIconOffset(0, 0)    // без смещения
 * getReaderRatingIconOffset(2, -1)   // сдвиг на 2px вправо и 1px вверх
 * getReaderRatingIconOffset(-3, 1)   // сдвиг на 3px влево и 1px вниз
 */
export const getReaderRatingIconOffset = (offsetX = 0, offsetY = 0) => {
  return {
    transform: `translate(${offsetX}px, ${offsetY}px)`,
  };
};

/**
 * Получает URL значка авторского рейтинга
 * @returns {string} URL значка авторского рейтинга
 */
export const getAuthorRatingIcon = () => {
  const baseUrl = 'https://storage.yandexcloud.net/lpo-test/dist/icons/';
  return `${baseUrl}rating_writers.webp`;
};

/**
 * Получает стили для смещения иконки авторского рейтинга
 * @param {number} offsetX - Смещение по горизонтали в пикселях (положительное = вправо)
 * @param {number} offsetY - Смещение по вертикали в пикселях (положительное = вниз)
 * @returns {Object} CSS стили для применения к иконке
 * 
 * Примеры использования:
 * getAuthorRatingIconOffset(0, 0)    // без смещения
 * getAuthorRatingIconOffset(2, -1)   // сдвиг на 2px вправо и 1px вверх
 * getAuthorRatingIconOffset(-3, 1)   // сдвиг на 3px влево и 1px вниз
 */
export const getAuthorRatingIconOffset = (offsetX = 0, offsetY = 0) => {
  return {
    transform: `translate(${offsetX}px, ${offsetY}px)`,
  };
};

/**
 * Получает URL значка подписчиков
 * @returns {string} URL значка подписчиков
 */
export const getFollowersIcon = () => {
  const baseUrl = 'https://storage.yandexcloud.net/lpo-test/dist/icons/';
  return `${baseUrl}followers.webp`;
};

/**
 * Получает стили для смещения иконки подписчиков
 * @param {number} offsetX - Смещение по горизонтали в пикселях (положительное = вправо)
 * @param {number} offsetY - Смещение по вертикали в пикселях (положительное = вниз)
 * @returns {Object} CSS стили для применения к иконке
 * 
 * Примеры использования:
 * getFollowersIconOffset(0, 0)    // без смещения
 * getFollowersIconOffset(2, -1)   // сдвиг на 2px вправо и 1px вверх
 * getFollowersIconOffset(-3, 1)   // сдвиг на 3px влево и 1px вниз
 */
export const getFollowersIconOffset = (offsetX = 0, offsetY = 0) => {
  return {
    transform: `translate(${offsetX}px, ${offsetY}px)`,
  };
};

/**
 * Получает URL значка друзей
 * @returns {string} URL значка друзей
 */
export const getFriendsIcon = () => {
  const baseUrl = 'https://storage.yandexcloud.net/lpo-test/dist/icons/';
  return `${baseUrl}friends.webp`;
};

/**
 * Получает стили для смещения иконки друзей
 * @param {number} offsetX - Смещение по горизонтали в пикселях (положительное = вправо)
 * @param {number} offsetY - Смещение по вертикали в пикселях (положительное = вниз)
 * @returns {Object} CSS стили для применения к иконке
 * 
 * Примеры использования:
 * getFriendsIconOffset(0, 0)    // без смещения
 * getFriendsIconOffset(2, -1)   // сдвиг на 2px вправо и 1px вверх
 * getFriendsIconOffset(-3, 1)   // сдвиг на 3px влево и 1px вниз
 */
export const getFriendsIconOffset = (offsetX = 0, offsetY = 0) => {
  return {
    transform: `translate(${offsetX}px, ${offsetY}px)`,
  };
}; 