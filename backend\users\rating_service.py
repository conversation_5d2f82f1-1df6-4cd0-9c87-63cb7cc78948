"""
Сервис для работы с системой рейтингов и метрик пользователей.
Обновленная версия с новыми формулами расчета.
"""

from django.db import transaction, models
from django.utils import timezone
from django.db.models import Sum, Count, Q, F
from typing import Dict, List, Optional, Tuple
import logging
from decimal import Decimal

from .models import (
    User, UserStats, RatingCalculationRule, 
    UserMetricHistory, RatingRecalculationTask
)
from books.models import Book, Comment, Review, Like

logger = logging.getLogger(__name__)


class RatingService:
    """
    Основной сервис для работы с рейтингами пользователей.
    Реализует новые формулы расчета.
    """
    
    @staticmethod
    def get_or_create_user_stats(user: User) -> UserStats:
        """Получить или создать статистику пользователя."""
        stats, created = UserStats.objects.get_or_create(
            user=user,
            defaults={
                'total_rating': user.reader_rating + user.author_rating,
                'reader_rating': user.reader_rating,
                'author_rating': user.author_rating,
            }
        )
        
        # Выдаем приветственный бонус новым пользователям
        if created and not stats.registration_bonus_given:
            RatingService.give_registration_bonus(user, stats)
            
        return stats
    
    @staticmethod
    def give_registration_bonus(user: User, stats: UserStats = None):
        """Выдать приветственный бонус за регистрацию."""
        if stats is None:
            stats = RatingService.get_or_create_user_stats(user)
            
        if not stats.registration_bonus_given:
            RatingService.update_metric(
                user=user,
                action_type='welcome_bonus',
                metric_name='reader_rating',
                change_delta=10,
                created_by='system'
            )
            stats.registration_bonus_given = True
            stats.save(update_fields=['registration_bonus_given'])
            logger.info(f"Выдан приветственный бонус пользователю {user.username}")
    
    @staticmethod
    def calculate_comment_points(commenter_stats: UserStats, comment_type: str = 'book', 
                               comment_length: int = 0) -> int:
        """
        Вычислить баллы за комментарий с учетом ограничений.
        """
        # Проверяем минимальную длину комментария
        if comment_length < 100:
            return 0
            
        # Проверяем дневной лимит
        commenter_stats.reset_daily_comments_if_needed()
        if not commenter_stats.can_comment_today():
            return 0
            
        # Получаем базовые баллы в зависимости от типа комментария
        return commenter_stats.get_comment_base_points(comment_type)
    
    @staticmethod
    def calculate_like_impact(liker_stats: UserStats, content_type: str = 'book', 
                            is_like: bool = True) -> int:
        """
        Вычислить влияние лайка/дизлайка на рейтинг получателя.
        """
        weight = liker_stats.get_like_weight(content_type)
        return weight if is_like else -weight
    
    @classmethod
    @transaction.atomic
    def process_comment_added(cls, comment, comment_type='book', content_author=None):
        """
        Обработать добавление комментария с новой логикой.
        Обрабатывает основные комментарии, ответы на комментарии и ответы на ответы одинаково.
        """
        commenter = comment.user
        commenter_stats = cls.get_or_create_user_stats(commenter)

        # Вычисляем баллы за комментарий (для всех типов комментариев)
        comment_length = len(comment.text) if hasattr(comment, 'text') else 0
        points = cls.calculate_comment_points(commenter_stats, comment_type, comment_length)

        # Определяем тип действия для логирования
        if hasattr(comment, 'parent') and comment.parent:
            if hasattr(comment, 'reply_to') and comment.reply_to:
                action_type = 'reply_to_reply_added'  # Ответ на ответ
            else:
                action_type = 'reply_to_comment_added'  # Ответ на комментарий
        else:
            action_type = 'comment_added'  # Основной комментарий

        if points > 0:
            # Начисляем баллы комментатору (для всех типов комментариев)
            cls.update_metric(
                user=commenter,
                action_type=action_type,
                metric_name='reader_rating',
                change_delta=points,
                related_object=comment
            )

            # Обновляем счетчик дневных комментариев (все типы считаются вместе)
            commenter_stats.daily_comments_count += 1
            commenter_stats.last_comment_date = timezone.now().date()
            commenter_stats.save(update_fields=['daily_comments_count', 'last_comment_date'])

        # Начисляем баллы автору контента (если это не его комментарий и комментатор не удален)
        # ВАЖНО: Авторский рейтинг начисляется ВСЕГДА, независимо от дневного лимита комментатора
        if content_author and content_author != commenter and not commenter.is_deleted:
            author_stats = cls.get_or_create_user_stats(content_author)
            author_points = author_stats.get_comment_base_points('book')  # Авторские баллы

            cls.update_metric(
                user=content_author,
                action_type='comment_received',
                metric_name='author_rating',
                change_delta=author_points,
                related_object=comment
            )
    
    @classmethod
    @transaction.atomic
    def process_like_added(cls, like, content_type='book', content_author=None, target_user=None):
        """
        Обработать добавление лайка с новой логикой.
        """
        liker = like.user
        liker_stats = cls.get_or_create_user_stats(liker)
        
        if content_type == 'book' and content_author:
            # Лайк к книге - автор получает фиксированные баллы
            cls.update_metric(
                user=content_author,
                action_type='book_like_received',
                metric_name='author_rating',
                change_delta=15,  # Изменено с 5 на 15 баллов за лайк
                related_object=like
            )
        elif content_type == 'comment' and target_user:
            # Лайк/дизлайк к комментарию - влияние зависит от уровня лайкера и типа реакции
            # Проверяем, что лайкер не удален
            if not liker.is_deleted:
                is_like = like.reaction == 'like'  # Проверяем тип реакции
                impact = cls.calculate_like_impact(liker_stats, 'book', is_like)

                # Определяем тип действия для истории
                if is_like:
                    action_type = 'comment_like_received'
                else:
                    action_type = 'comment_dislike_received'

                cls.update_metric(
                    user=target_user,
                    action_type=action_type,
                    metric_name='reader_rating',
                    change_delta=impact,
                    related_object=like.comment  # Передаем комментарий, чтобы получить книгу
                )
    
    @classmethod
    @transaction.atomic
    def process_purchase(cls, purchase):
        """Обработать покупку книги."""
        buyer = purchase.user
        author = purchase.book.author
        
        # Покупатель получает баллы
        cls.update_metric(
            user=buyer,
            action_type='book_purchased',
            metric_name='reader_rating',
            change_delta=150,
            related_object=purchase
        )
        
        # Автор получает баллы
        cls.update_metric(
            user=author,
            action_type='book_sold',
            metric_name='author_rating',
            change_delta=100,
            related_object=purchase
        )
    
    @classmethod
    @transaction.atomic
    def process_award(cls, award):
        """Обработать награду (донат)."""
        giver = award.user
        receiver = award.book.author if hasattr(award, 'book') else award.author
        
        # Дающий награду получает читательские баллы
        cls.update_metric(
            user=giver,
            action_type='award_given',
            metric_name='reader_rating',
            change_delta=70,
            related_object=award
        )
        
        # Получающий награду получает авторские баллы
        cls.update_metric(
            user=receiver,
            action_type='award_received',
            metric_name='author_rating',
            change_delta=100,
            related_object=award
        )
    
    @classmethod
    @transaction.atomic
    def process_blog_post(cls, post):
        """Обработать публикацию поста в блоге."""
        author = post.author
        
        # Проверяем условия для начисления баллов
        if (hasattr(post, 'category') and post.category == 'selfpromo') or \
           (hasattr(post, 'visibility') and post.visibility == 'friends_only'):
            return  # Не начисляем баллы
            
        cls.update_metric(
            user=author,
            action_type='blog_post_published',
            metric_name='author_rating',
            change_delta=20,
            related_object=post
        )
    
    @classmethod
    @transaction.atomic
    def process_subscription_change(cls, subscription, is_subscribe=True):
        """Обработать подписку/отписку."""
        author = subscription.to_user
        
        change_delta = 20 if is_subscribe else -20
        action_type = 'subscriber_added' if is_subscribe else 'subscriber_removed'
        
        cls.update_metric(
            user=author,
            action_type=action_type,
            metric_name='author_rating',
            change_delta=change_delta,
            related_object=subscription
        )
    
    @classmethod
    @transaction.atomic
    def process_reading_session(cls, user, minutes_read):
        """Обработать сессию чтения."""
        # Начисляем баллы за каждые 30 минут чтения
        full_sessions = minutes_read // 30
        if full_sessions > 0:
            points = full_sessions * 60
            cls.update_metric(
                user=user,
                action_type='reading_session',
                metric_name='reader_rating',
                change_delta=points
            )
    
    @classmethod
    @transaction.atomic
    def process_book_transition(cls, book, old_status=None, new_status=None):
        """
        Обрабатывает переход книги из одного состояния в другое с начислением рейтинга автору.
        
        Правила начисления рейтинга:
        - Рассказы: 
          * Черновик (draft) -> В процессе публикации (in_progress): +10
          * В процессе публикации (in_progress) -> Завершено (finished): +10
          * Черновик (draft) -> Завершено (finished): +20
          * Обратные переходы: -10, -10, -20 соответственно
        - Романы/Повести:
          * Черновик (draft) -> В процессе публикации (in_progress): +20
          * В процессе публикации (in_progress) -> Завершено (finished): +30
          * Черновик (draft) -> Завершено (finished): +50
          * Обратные переходы: -20, -30, -50 соответственно
        """
        author = book.author
        book_type = book.type
        points = 0
        action_type = ""
        
        # Отладочное логирование
        logger.info(f"ОТЛАДКА: process_book_transition вызван для книги {book.id}, тип: {book_type}, переход: {old_status} -> {new_status}")
        logger.info(f"ОТЛАДКА: Исходные значения книги: is_published={book.is_published}, is_finished={book.is_finished}, status={book.status}")
        
        # Определяем статусы
        if old_status is None:
            # Определяем старый статус на основе книги
            old_status = book.status
            logger.info(f"ОТЛАДКА: Старый статус определен из книги: {old_status}")
        
        if new_status is None:
            # Определяем новый статус на основе книги
            new_status = book.status
            logger.info(f"ОТЛАДКА: Новый статус определен из книги: {new_status}")
        
        # Преобразуем старые названия статусов в новые, если они переданы в старом формате
        status_mapping = {
            "draft": "draft",
            "partial": "in_progress",
            "finished": "finished"
        }
        
        if old_status in status_mapping:
            old_status = status_mapping[old_status]
            logger.info(f"ОТЛАДКА: Старый статус преобразован в новый формат: {old_status}")
        
        if new_status in status_mapping:
            new_status = status_mapping[new_status]
            logger.info(f"ОТЛАДКА: Новый статус преобразован в новый формат: {new_status}")
        
        # Если статус не изменился, ничего не делаем
        if old_status == new_status:
            logger.info(f"ОТЛАДКА: Статус не изменился ({old_status}), выходим")
            return
        
        # Определяем тип перехода для внутренней логики
        internal_old_status = "draft" if old_status == "draft" else ("partial" if old_status == "in_progress" else "finished")
        internal_new_status = "draft" if new_status == "draft" else ("partial" if new_status == "in_progress" else "finished")
        transition = f"{internal_old_status}_to_{internal_new_status}"
        logger.info(f"ОТЛАДКА: Определен внутренний переход: {transition}, тип книги: {book_type}")
        
        # Проверяем тип книги и устанавливаем значение по умолчанию, если он не определен
        if book_type is None or book_type == '':
            logger.warning(f"ОТЛАДКА: Тип книги не определен, используем 'story' по умолчанию")
            book_type = 'story'  # По умолчанию используем рассказ
        
        # Рассказы
        if book_type == 'story' or book_type == 'story_collection':
            logger.info(f"ОТЛАДКА: Обработка для рассказа")
            if transition == "draft_to_partial":
                points = 10
                action_type = "story_draft_to_partial"
            elif transition == "partial_to_finished":
                points = 10
                action_type = "story_partial_to_finished"
            elif transition == "draft_to_finished":
                points = 20
                action_type = "story_draft_to_finished"
            # Обратные переходы
            elif transition == "partial_to_draft":
                points = -10
                action_type = "story_partial_to_draft"
            elif transition == "finished_to_partial":
                points = -10
                action_type = "story_finished_to_partial"
                logger.info(f"ОТЛАДКА: Специальная обработка для {action_type}: {author.author_rating} -> {author.author_rating - 10}")
            elif transition == "finished_to_draft":
                points = -20
                action_type = "story_finished_to_draft"
        
        # Романы и повести
        elif book_type == 'novel' or book_type == 'novella':
            logger.info(f"ОТЛАДКА: Обработка для романа/повести")
            if transition == "draft_to_partial":
                points = 20
                action_type = "novel_draft_to_partial"
            elif transition == "partial_to_finished":
                points = 30
                action_type = "novel_partial_to_finished"
            elif transition == "draft_to_finished":
                points = 50
                action_type = "novel_draft_to_finished"
            # Обратные переходы
            elif transition == "partial_to_draft":
                points = -20
                action_type = "novel_partial_to_draft"
            elif transition == "finished_to_partial":
                points = -30
                action_type = "novel_finished_to_partial"
            elif transition == "finished_to_draft":
                points = -50
                action_type = "novel_finished_to_draft"
        else:
            # Неизвестный тип книги - используем логику для рассказов по умолчанию
            logger.warning(f"ОТЛАДКА: Неизвестный тип книги: {book_type}, используем логику для рассказов")
            if transition == "draft_to_partial":
                points = 10
                action_type = "story_draft_to_partial"
            elif transition == "partial_to_finished":
                points = 10
                action_type = "story_partial_to_finished"
            elif transition == "draft_to_finished":
                points = 20
                action_type = "story_draft_to_finished"
            # Обратные переходы
            elif transition == "partial_to_draft":
                points = -10
                action_type = "story_partial_to_draft"
            elif transition == "finished_to_partial":
                points = -10
                action_type = "story_finished_to_partial"
            elif transition == "finished_to_draft":
                points = -20
                action_type = "story_finished_to_draft"
        
        # Если определены баллы и тип действия, обновляем рейтинг
        if points != 0 and action_type:
            logger.info(f"ОТЛАДКА: Начисляем {points} баллов за действие {action_type}")
            cls.update_metric(
                user=author,
                action_type=action_type,
                metric_name='author_rating',
                change_delta=points,
                related_object=book
            )
            
            logger.info(f"Начислено {points} баллов автору {author.username} за переход книги {book.id} из {old_status} в {new_status}")
        else:
            logger.warning(f"ОТЛАДКА: Не удалось определить баллы или тип действия для перехода {transition} и типа книги {book_type}")
    
    @staticmethod
    def calculate_reader_metrics(user: User) -> Dict[str, int]:
        """Вычислить читательские метрики пользователя."""
        return {
            'comments_left_count': Comment.objects.filter(user=user).count(),
            'reviews_left_count': Review.objects.filter(user=user).count(),
            'likes_left_count': Like.objects.filter(user=user).count(),
            'reading_time_minutes': 0,  # Будет обновляться через сессии чтения
            'books_read_count': 0,      # Потребует модели ReadingProgress
        }
    
    @staticmethod
    def calculate_author_metrics(user: User) -> Dict[str, int]:
        """Вычислить авторские метрики пользователя."""
        user_books = user.books.all()
        
        # Агрегированные данные по книгам
        book_stats = user_books.aggregate(
            total_books=Count('id'),
            published_books=Count('id', filter=Q(is_published=True)),
            finished_books=Count('id', filter=Q(is_finished=True)),
            total_likes=Count('likes'),
            total_reviews=Count('reviews'),
            total_comments=Count('comments'),
        )
        
        return {
            'books_count': book_stats['total_books'] or 0,
            'published_books_count': book_stats['published_books'] or 0,
            'finished_books_count': book_stats['finished_books'] or 0,
            'books_likes_count': book_stats['total_likes'] or 0,
            'books_reviews_count': book_stats['total_reviews'] or 0,
            'books_comments_count': book_stats['total_comments'] or 0,
        }
    
    @staticmethod
    def calculate_social_metrics(user: User) -> Dict[str, int]:
        """Вычислить социальные метрики пользователя."""
        friends_count = user.sent_friend_requests.filter(accepted=True).count() + \
                       user.received_friend_requests.filter(accepted=True).count()
        
        return {
            'friends_count': friends_count,
            'subscribers_count': user.user_subscribers.count(),
            'subscriptions_count': user.user_subscriptions.count(),
        }
    
    @classmethod
    @transaction.atomic
    def process_user_deletion(cls, deleted_user: User):
        """
        Обработать удаление пользователя - отозвать влияние его активности на рейтинги других.

        ВАЖНО: Этот метод должен вызываться ДО физического удаления лайков/комментариев,
        чтобы избежать двойного списания рейтинга через сигналы post_delete.
        """
        logger.info(f"Начинаем отзыв рейтинга для удаленного пользователя {deleted_user.username}")

        # Импортируем модели
        from books.models import CommentLike, Comment

        # 1. Отзываем влияние лайков к комментариям
        comment_likes = CommentLike.objects.filter(user=deleted_user)
        for like in comment_likes:
            try:
                # Получаем пользователя, чей комментарий лайкнули
                comment_author = like.comment.user
                if comment_author != deleted_user and not comment_author.is_deleted:
                    # Получаем статистику удаленного пользователя для расчета веса
                    deleted_user_stats = cls.get_or_create_user_stats(deleted_user)

                    # Вычисляем влияние лайка (отрицательное для отзыва)
                    is_like = like.reaction == 'like'
                    impact = cls.calculate_like_impact(deleted_user_stats, 'book', is_like)

                    # Отзываем влияние
                    cls.update_metric(
                        user=comment_author,
                        action_type='comment_like_revoked',
                        metric_name='reader_rating',
                        change_delta=-impact,  # Отрицательное значение для отзыва
                        related_object=like,
                        created_by='system'
                    )

                    logger.info(f"Отозван лайк от {deleted_user.username} к комментарию {like.comment.id}, влияние: {-impact}")
            except Exception as e:
                logger.error(f"Ошибка при отзыве лайка {like.id}: {e}")

        # 2. Отзываем влияние комментариев на авторский рейтинг
        comments = Comment.objects.filter(user=deleted_user)
        for comment in comments:
            try:
                if hasattr(comment, 'book') and comment.book:
                    book_author = comment.book.author
                    if book_author != deleted_user and not book_author.is_deleted:
                        # Получаем статистику автора книги для расчета баллов
                        author_stats = cls.get_or_create_user_stats(book_author)
                        author_points = author_stats.get_comment_base_points('book')

                        # Отзываем баллы автора
                        cls.update_metric(
                            user=book_author,
                            action_type='comment_received_revoked',
                            metric_name='author_rating',
                            change_delta=-author_points,  # Отрицательное значение для отзыва
                            related_object=comment,
                            created_by='system'
                        )

                        logger.info(f"Отозван комментарий от {deleted_user.username} к книге {comment.book.id}, влияние: {-author_points}")
            except Exception as e:
                logger.error(f"Ошибка при отзыве комментария {comment.id}: {e}")

        logger.info(f"Завершен отзыв рейтинга для удаленного пользователя {deleted_user.username}")

    @classmethod
    def recalculate_user_stats(cls, user: User, save: bool = True) -> UserStats:
        """
        Полный пересчет всех метрик пользователя.
        """
        stats = cls.get_or_create_user_stats(user)
        
        # Собираем все метрики
        all_metrics = {}
        all_metrics.update(cls.calculate_reader_metrics(user))
        all_metrics.update(cls.calculate_author_metrics(user))
        all_metrics.update(cls.calculate_social_metrics(user))
        
        # Обновляем поля статистики
        for field_name, value in all_metrics.items():
            if hasattr(stats, field_name):
                setattr(stats, field_name, value)
        
        # В новой системе рейтинги считаются просто как сумма
        # reader_rating и author_rating уже содержат правильные значения
        stats.total_rating = stats.reader_rating + stats.author_rating
        stats.recalculation_scheduled = False
        
        if save:
            stats.save()
            
            # Обновляем также поля в основной модели User для обратной совместимости
            user.reader_rating = stats.reader_rating
            user.author_rating = stats.author_rating
            user.save(update_fields=['reader_rating', 'author_rating'])
        
        logger.info(f"Пересчитана статистика для пользователя {user.username}")
        return stats
    
    @classmethod
    @transaction.atomic
    def update_metric(cls, user: User, action_type: str, metric_name: str, 
                     change_delta: int, related_object=None, 
                     created_by: str = 'system') -> None:
        """
        Обновить метрику пользователя с записью в историю.
        """
        stats = cls.get_or_create_user_stats(user)
        
        # Получаем текущие значения
        old_value = getattr(stats, metric_name, 0)
        new_value = old_value + change_delta
        
        # Сохраняем старые рейтинги
        old_reader_rating = stats.reader_rating
        old_author_rating = stats.author_rating
        old_total_rating = stats.total_rating
        
        # Обновляем метрику
        # Проверяем, является ли action_type связанным с переходом из finished в partial для рассказа
        if action_type == 'story_finished_to_partial':
            # Для этого конкретного случая, всегда вычитаем 10 баллов
            setattr(stats, metric_name, old_value - 10)
            logger.info(f"Специальная обработка для story_finished_to_partial: {old_value} -> {old_value - 10}")
        else:
            # Для всех остальных случаев используем стандартную логику
            setattr(stats, metric_name, max(0, new_value))
        
        # Пересчитываем общий рейтинг
        stats.total_rating = stats.reader_rating + stats.author_rating
        
        stats.save()
        
        # Обновляем поля в User для обратной совместимости
        user.reader_rating = stats.reader_rating
        user.author_rating = stats.author_rating
        user.save(update_fields=['reader_rating', 'author_rating'])
        
        # Записываем в новую историю рейтингов
        from .models import RatingHistory
        from django.contrib.contenttypes.models import ContentType

        content_type = None
        object_id = None
        if related_object:
            content_type = ContentType.objects.get_for_model(related_object)
            object_id = related_object.pk

        RatingHistory.objects.create(
            user=user,
            metric_name=metric_name,
            action_type=action_type,
            change_delta=change_delta,
            old_value=old_value,
            new_value=getattr(stats, metric_name),
            created_by=created_by,
            content_type=content_type,
            object_id=object_id
        )

        # Также записываем в старую историю для обратной совместимости
        history_data = {
            'user': user,
            'action_type': action_type,
            'metric_name': metric_name,
            'old_value': old_value,
            'new_value': getattr(stats, metric_name),
            'change_delta': change_delta,
            'reader_rating_before': old_reader_rating,
            'reader_rating_after': stats.reader_rating,
            'author_rating_before': old_author_rating,
            'author_rating_after': stats.author_rating,
            'total_rating_before': old_total_rating,
            'total_rating_after': stats.total_rating,
            'created_by': created_by,
        }

        if related_object:
            history_data.update({
                'related_object_type': related_object.__class__.__name__,
                'related_object_id': related_object.id,
            })

        UserMetricHistory.objects.create(**history_data)
        
        logger.info(f"Обновлена метрика {metric_name} для {user.username}: {old_value} -> {new_value}")
    
    @staticmethod
    def schedule_recalculation(user: User, task_type: str = 'user_stats', 
                             priority: int = 5, parameters: Dict = None) -> RatingRecalculationTask:
        """
        Запланировать пересчет рейтинга пользователя.
        """
        # Отменяем существующие незавершенные задачи
        RatingRecalculationTask.objects.filter(
            user=user,
            task_type=task_type,
            status__in=['pending', 'processing']
        ).update(status='cancelled')
        
        task = RatingRecalculationTask.objects.create(
            task_type=task_type,
            user=user,
            priority=priority,
            parameters=parameters or {},
        )
        
        # Помечаем статистику как требующую пересчета
        stats = RatingService.get_or_create_user_stats(user)
        stats.recalculation_scheduled = True
        stats.save(update_fields=['recalculation_scheduled'])
        
        return task
    
    @staticmethod
    def get_leaderboard(rating_type: str = 'total', limit: int = 100) -> List[UserStats]:
        """
        Получить список лидеров по рейтингу.
        """
        field_name = f'{rating_type}_rating'
        return UserStats.objects.select_related('user').order_by(f'-{field_name}')[:limit]
    
    @staticmethod
    def get_user_rating_position(user: User, rating_type: str = 'total') -> int:
        """
        Получить позицию пользователя в рейтинге.
        """
        stats = RatingService.get_or_create_user_stats(user)
        field_name = f'{rating_type}_rating'
        user_rating = getattr(stats, field_name)
        
        # Подсчитываем количество пользователей с более высоким рейтингом
        higher_count = UserStats.objects.filter(
            **{f'{field_name}__gt': user_rating}
        ).count()
        
        return higher_count + 1


class RatingEventHandlers:
    """
    Обработчики событий для автоматического обновления рейтингов.
    Обновленные обработчики согласно новым формулам.
    """
    
    @staticmethod
    def on_comment_added(comment, comment_type='book'):
        """Пользователь добавил комментарий."""
        content_author = None
        if hasattr(comment, 'book') and comment.book:
            content_author = comment.book.author
        elif hasattr(comment, 'post') and comment.post:
            content_author = comment.post.author
            
        RatingService.process_comment_added(comment, comment_type, content_author)
    
    @staticmethod
    def on_comment_removed(comment, comment_type='book'):
        """Пользователь удалил комментарий - обратный эффект."""
        commenter = comment.user
        commenter_stats = RatingService.get_or_create_user_stats(commenter)
        
        # Вычисляем сколько баллов было начислено (обратный расчет)
        comment_length = len(comment.text) if hasattr(comment, 'text') else 0
        points = RatingService.calculate_comment_points(commenter_stats, comment_type, comment_length)
        
        if points > 0:
            RatingService.update_metric(
                user=commenter,
                action_type='comment_removed',
                metric_name='reader_rating',
                change_delta=-points,
                related_object=comment
            )
    
    @staticmethod
    def on_like_added(like, content_type='book'):
        """Пользователь поставил лайк."""
        content_author = None
        target_user = None
        
        if content_type == 'book' and hasattr(like, 'book'):
            content_author = like.book.author
        elif content_type == 'comment' and hasattr(like, 'comment'):
            target_user = like.comment.user
            
        RatingService.process_like_added(like, content_type, content_author, target_user)
    
    @staticmethod
    def on_like_removed(like, content_type='book'):
        """Пользователь убрал лайк - обратный эффект."""
        liker = like.user
        liker_stats = RatingService.get_or_create_user_stats(liker)
        
        if content_type == 'book' and hasattr(like, 'book'):
            # Убираем баллы у автора книги
            RatingService.update_metric(
                user=like.book.author,
                action_type='book_like_removed',
                metric_name='author_rating',
                change_delta=-15,  # Изменено с -5 на -15 баллов при снятии лайка
                related_object=like
            )
        elif content_type == 'comment' and hasattr(like, 'comment'):
            # Убираем влияние лайка/дизлайка на комментарий
            was_like = like.reaction == 'like'
            # Получаем базовый вес без учета знака
            weight = liker_stats.get_like_weight('book')

            # Определяем тип действия для истории и правильный change_delta
            if was_like:
                action_type = 'comment_like_removed'
                change_delta = -weight  # Убираем положительный эффект лайка
            else:
                action_type = 'comment_dislike_removed'
                change_delta = weight   # Убираем отрицательный эффект дизлайка (добавляем положительное)

            RatingService.update_metric(
                user=like.comment.user,
                action_type=action_type,
                metric_name='reader_rating',
                change_delta=change_delta,
                related_object=like.comment  # Передаем комментарий, чтобы получить книгу
            )
    
    @staticmethod
    def on_subscription_added(subscription):
        """Пользователь подписался."""
        RatingService.process_subscription_change(subscription, True)
    
    @staticmethod
    def on_subscription_removed(subscription):
        """Пользователь отписался."""
        RatingService.process_subscription_change(subscription, False)
    
    @staticmethod
    def on_book_purchased(purchase):
        """Книга куплена."""
        RatingService.process_purchase(purchase)
    
    @staticmethod
    def on_award_given(award):
        """Награда выдана."""
        RatingService.process_award(award)
    
    @staticmethod
    def on_blog_post_published(post):
        """Пост в блоге опубликован."""
        RatingService.process_blog_post(post)
    
    @staticmethod
    def on_reading_session_completed(user, minutes_read):
        """Сессия чтения завершена."""
        RatingService.process_reading_session(user, minutes_read)
        
    @staticmethod
    def on_book_status_changed(book, old_status=None, new_status=None):
        """Изменился статус книги."""
        logger.info(f"ОТЛАДКА RatingEventHandlers.on_book_status_changed: Вызван для книги {book.id}, переход {old_status} -> {new_status}")
        RatingService.process_book_transition(book, old_status, new_status)

    @staticmethod
    def on_user_deleted(user):
        """Пользователь удален - отзываем его влияние на рейтинги."""
        RatingService.process_user_deletion(user)


def setup_default_rating_rules():
    """
    Создать правила расчета рейтингов по умолчанию.
    Обновленные правила согласно новым формулам.
    """
    # В новой системе рейтинги рассчитываются через прямые начисления баллов
    # Правила здесь больше для совместимости и возможности корректировок
    
    default_rules = [
        # Читательский рейтинг (базовые значения для справки)
        ('reader', 'welcome_bonus', 10.0, None, 'Приветственный бонус'),
        ('reader', 'comment_book_beginner', 5.0, None, 'Комментарий к книге (начальный)'),
        ('reader', 'comment_book_advanced', 10.0, None, 'Комментарий к книге (повышенный)'),
        ('reader', 'comment_book_pro', 15.0, None, 'Комментарий к книге (профессиональный)'),
        ('reader', 'comment_blog', 2.0, None, 'Комментарий к блогу'),
        ('reader', 'reading_session', 60.0, None, 'За 30 минут чтения'),
        ('reader', 'book_purchase', 150.0, None, 'Покупка книги'),
        ('reader', 'award_given', 70.0, None, 'Награда произведению'),
        
        # Авторский рейтинг
        ('author', 'book_sold', 100.0, None, 'Продажа книги'),
        ('author', 'award_received', 100.0, None, 'Получена награда'),
        ('author', 'blog_post', 20.0, None, 'Публикация поста'),
        ('author', 'book_like', 15.0, None, 'Лайк к книге'),  # Изменено с 20.0 на 15.0
        ('author', 'comment_received_beginner', 5.0, None, 'Комментарий от начального'),
        ('author', 'comment_received_advanced', 10.0, None, 'Комментарий от повышенного'),
        ('author', 'comment_received_pro', 15.0, None, 'Комментарий от профессионального'),
        ('author', 'subscriber_added', 20.0, None, 'Новый подписчик'),
        
        # Переходы статусов книг (рассказы) - положительные
        ('author', 'story_draft_to_partial', 10.0, None, 'Рассказ: черновик → частичная публикация'),
        ('author', 'story_partial_to_finished', 10.0, None, 'Рассказ: частичная публикация → готовое произведение'),
        ('author', 'story_draft_to_finished', 20.0, None, 'Рассказ: черновик → готовое произведение'),
        
        # Переходы статусов книг (рассказы) - отрицательные
        ('author', 'story_partial_to_draft', -10.0, None, 'Рассказ: частичная публикация → черновик'),
        ('author', 'story_finished_to_partial', -10.0, None, 'Рассказ: готовое произведение → частичная публикация'),
        ('author', 'story_finished_to_draft', -20.0, None, 'Рассказ: готовое произведение → черновик'),
        
        # Удаление книг (рассказы) - отрицательные
        ('author', 'story_delete_in_progress', -10.0, None, 'Удаление рассказа в процессе публикации'),
        ('author', 'story_delete_finished', -20.0, None, 'Удаление завершенного рассказа'),
        
        # Переходы статусов книг (романы/повести) - положительные
        ('author', 'novel_draft_to_partial', 20.0, None, 'Роман/повесть: черновик → частичная публикация'),
        ('author', 'novel_partial_to_finished', 30.0, None, 'Роман/повесть: частичная публикация → готовое произведение'),
        ('author', 'novel_draft_to_finished', 50.0, None, 'Роман/повесть: черновик → готовое произведение'),
        
        # Переходы статусов книг (романы/повести) - отрицательные
        ('author', 'novel_partial_to_draft', -20.0, None, 'Роман/повесть: частичная публикация → черновик'),
        ('author', 'novel_finished_to_partial', -30.0, None, 'Роман/повесть: готовое произведение → частичная публикация'),
        ('author', 'novel_finished_to_draft', -50.0, None, 'Роман/повесть: готовое произведение → черновик'),
        
        # Удаление книг (романы/повести) - отрицательные
        ('author', 'novel_delete_in_progress', -20.0, None, 'Удаление романа/повести в процессе публикации'),
        ('author', 'novel_delete_finished', -50.0, None, 'Удаление завершенного романа/повести'),
        
        # Общий рейтинг (просто сумма)
        ('total', 'reader_rating', 1.0, None, 'Читательский рейтинг'),
        ('total', 'author_rating', 1.0, None, 'Авторский рейтинг'),
    ]
    
    for rating_type, metric_name, weight, max_contrib, description in default_rules:
        RatingCalculationRule.objects.get_or_create(
            rating_type=rating_type,
            metric_name=metric_name,
            defaults={
                'weight': Decimal(str(weight)),
                'max_contribution': max_contrib,
                'description': description,
            }
        )
