import React from 'react';
import { message } from 'antd';
import { CloseOutlined } from '@ant-design/icons';

export const useCustomMessage = () => {
  const showSuccessWithClose = (content, options = {}) => {
    const { duration = 0, key = 'customMessage', ...otherOptions } = options;
    const actualDuration = duration === 0 ? 4 : duration;
    
    return message.success({
      content: (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
          <span style={{ flex: 1, paddingRight: '12px' }}>{content}</span>
          <CloseOutlined 
            className="custom-message-close"
            style={{ 
              cursor: 'pointer', 
              color: '#666', 
              fontSize: '12px',
            }}
            onClick={() => message.destroy(key)}
          />
        </div>
      ),
      duration: actualDuration,
      key,
      ...otherOptions
    });
  };

  const showErrorWithClose = (content, options = {}) => {
    const { duration = 0, key = 'customMessage', ...otherOptions } = options;
    const actualDuration = duration === 0 ? 4 : duration;
    
    return message.error({
      content: (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
          <span style={{ flex: 1, paddingRight: '12px' }}>{content}</span>
          <CloseOutlined 
            className="custom-message-close"
            style={{ 
              cursor: 'pointer', 
              color: '#666', 
              fontSize: '12px',
            }}
            onClick={() => message.destroy(key)}
          />
        </div>
      ),
      duration: actualDuration,
      key,
      ...otherOptions
    });
  };

  const showInfoWithClose = (content, options = {}) => {
    const { duration = 0, key = 'customMessage', ...otherOptions } = options;
    const actualDuration = duration === 0 ? 4 : duration;
    
    return message.info({
      content: (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
          <span style={{ flex: 1, paddingRight: '12px' }}>{content}</span>
          <CloseOutlined 
            className="custom-message-close"
            style={{ 
              cursor: 'pointer', 
              color: '#666', 
              fontSize: '12px',
            }}
            onClick={() => message.destroy(key)}
          />
        </div>
      ),
      duration: actualDuration,
      key,
      ...otherOptions
    });
  };

  return {
    showSuccessWithClose,
    showErrorWithClose,
    showInfoWithClose
  };
};