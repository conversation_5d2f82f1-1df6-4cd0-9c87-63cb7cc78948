import React, { createContext, useContext, useState, useCallback } from 'react';

const UserCacheContext = createContext();

export const useUserCache = () => {
    const context = useContext(UserCacheContext);
    if (!context) {
        throw new Error('useUserCache must be used within a UserCacheProvider');
    }
    return context;
};

export const UserCacheProvider = ({ children }) => {
    const [userCache, setUserCache] = useState(new Map());
    const [loadingUsers, setLoadingUsers] = useState(new Set());

    // Получить пользователя из кэша или загрузить его
    const getUser = useCallback(async (userId, username) => {
        // Используем userId как ключ кэша, если есть, иначе username
        const cacheKey = userId || username;
        
        // Если пользователь уже в кэше, возвращаем его
        if (userCache.has(cacheKey)) {
            return userCache.get(cacheKey);
        }

        // Если пользователь уже загружается, ждем
        if (loadingUsers.has(cacheKey)) {
            return new Promise((resolve) => {
                const checkCache = () => {
                    if (userCache.has(cacheKey)) {
                        resolve(userCache.get(cacheKey));
                    } else {
                        setTimeout(checkCache, 100);
                    }
                };
                checkCache();
            });
        }

        // Начинаем загрузку
        setLoadingUsers(prev => new Set(prev).add(cacheKey));

        try {
            // Определяем URL для запроса
            let url;
            if (userId) {
                // Если есть userId, используем публичный API по ID (если такой есть)
                // Пока используем username из объекта пользователя
                url = `/api/users/public/${username}/`;
            } else {
                url = `/api/users/public/${username}/`;
            }

            const response = await fetch(url, {
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch user data: ${response.status}`);
            }

            const userData = await response.json();
            
            // Сохраняем в кэш
            setUserCache(prev => new Map(prev).set(cacheKey, userData));
            
            return userData;
        } catch (error) {
            console.error('Error loading user data:', error);
            // В случае ошибки возвращаем базовые данные, если они есть
            return { id: userId, username, display_name: username };
        } finally {
            // Убираем из списка загружающихся
            setLoadingUsers(prev => {
                const newSet = new Set(prev);
                newSet.delete(cacheKey);
                return newSet;
            });
        }
    }, [userCache, loadingUsers]);

    // Добавить пользователя в кэш (например, из уже загруженных данных сообщений)
    const cacheUser = useCallback((user) => {
        if (!user || (!user.id && !user.username)) return;
        
        const cacheKey = user.id || user.username;
        setUserCache(prev => new Map(prev).set(cacheKey, user));
    }, []);

    // Обновить данные пользователя в кэше
    const updateUser = useCallback((userId, updates) => {
        const cacheKey = userId;
        setUserCache(prev => {
            const newCache = new Map(prev);
            const existingUser = newCache.get(cacheKey);
            if (existingUser) {
                newCache.set(cacheKey, { ...existingUser, ...updates });
            }
            return newCache;
        });
    }, []);

    // Очистить кэш
    const clearCache = useCallback(() => {
        setUserCache(new Map());
        setLoadingUsers(new Set());
    }, []);

    // Получить пользователя из кэша без загрузки
    const getCachedUser = useCallback((userId, username) => {
        const cacheKey = userId || username;
        return userCache.get(cacheKey);
    }, [userCache]);

    const value = {
        getUser,
        cacheUser,
        updateUser,
        clearCache,
        getCachedUser,
        isLoading: (userId, username) => {
            const cacheKey = userId || username;
            return loadingUsers.has(cacheKey);
        }
    };

    return (
        <UserCacheContext.Provider value={value}>
            {children}
        </UserCacheContext.Provider>
    );
};