# VK ID - Подробная инструкция получения ключей

## 🔑 Пошаговое получение ключей VK ID

### Шаг 1: Переход в кабинет VK ID
1. Перейдите на **https://id.vk.com/about/business/go**
2. Нажмите **"Подключить VK ID"** или **"Войти в кабинет"**
3. Авторизуйтесь через ваш аккаунт ВКонтакте

### Шаг 2: Создание приложения
1. В кабинете VK ID нажмите **"Создать приложение"**
2. Заполните форму:
   - **Название приложения**: Ваше название (например, "My Website")
   - **Описание**: Краткое описание вашего сайта
   - **Сайт приложения**: `https://localhost:8000` (VK ID требует HTTPS!)
   - **Redirect URI**: `https://localhost:8000/api/users/auth/vk/`

### Шаг 3: Настройка OAuth
1. После создания приложения перейдите в **"Настройки"**
2. Найдите раздел **"OAuth настройки"** или **"Web"**
3. Добавьте Redirect URI:
   ```
   https://localhost:8000/api/users/auth/vk/
   ```
4. Выберите необходимые права доступа:
   - ✅ **Базовая информация профиля**
   - ✅ **Email адрес** (если доступно)

## ⚠️ ВАЖНО: VK ID требует HTTPS!

VK ID больше не поддерживает HTTP для localhost. У вас есть 2 варианта:

### Вариант 1: Используйте HTTPS для локальной разработки

#### Настройка Django с SSL сертификатом:

1. Установите `django-extensions`:
   ```bash
   pip install django-extensions
   ```

2. Добавьте в `INSTALLED_APPS`:
   ```python
   INSTALLED_APPS = [
       # ... ваши приложения
       'django_extensions',
   ]
   ```

3. Запустите сервер с HTTPS:
   ```bash
   python manage.py runserver_plus --cert-file cert.pem --key-file key.pem
   ```

4. Обновите настройки VK views в Django:
   ```python
   class VKLoginView(SocialLoginView):
       adapter_class = VKOAuth2Adapter
       callback_url = "https://localhost:8000/api/users/auth/vk/"
       client_class = OAuth2Client
   ```

### Вариант 2: Используйте ngrok для туннелирования

1. Скачайте ngrok: https://ngrok.com/
2. Запустите Django на обычном порту:
   ```bash
   python manage.py runserver
   ```
3. В другом терминале запустите ngrok:
   ```bash
   ngrok http 8000
   ```
4. Используйте HTTPS URL от ngrok (например: `https://abc123.ngrok.io`)
5. В VK ID настройках укажите:
   - **Базовый домен**: `https://abc123.ngrok.io`
   - **Redirect URI**: `https://abc123.ngrok.io/api/users/auth/vk/`

### Вариант 3: Альтернативный способ через dev.vk.com

Если HTTPS настройка сложна, попробуйте старый интерфейс:

1. Перейдите на **https://dev.vk.com/**
2. Создайте **"Мини-приложение"**
3. Там может быть более гибкие настройки для localhost

### Шаг 4: Получение ключей
1. В разделе **"Настройки"** найдите:
   - **App ID** (ID приложения) - это ваш `VK_CLIENT_ID`
   - **Service Token** или **Secret Key** - это ваш `VK_SECRET_KEY`

2. Скопируйте эти значения в ваш `.env` файл:
   ```bash
   VK_CLIENT_ID=your_app_id_here
   VK_SECRET_KEY=your_secret_key_here
   ```

## 🔍 Где найти ключи в интерфейсе

### Вариант 1: В разделе "Ключи"
```
Кабинет VK ID → Ваше приложение → Ключи
├── App ID: 12345678 (это VK_CLIENT_ID)
└── Service Token: abcd1234... (это VK_SECRET_KEY)
```

### Вариант 2: В разделе "Настройки"
```
Кабинет VK ID → Ваше приложение → Настройки → OAuth
├── ID приложения: 12345678
└── Защищенный ключ: abcd1234...
```

## ⚠️ Важные моменты

1. **Redirect URI должен точно совпадать**:
   - В настройках VK ID: `http://localhost:8000/api/users/auth/vk/`
   - В Django settings: то же самое

2. **Для продакшена обновите**:
   - Сайт приложения: `https://yourdomain.com`
   - Redirect URI: `https://yourdomain.com/api/users/auth/vk/`

3. **Проверьте права доступа**:
   - Обязательно включите доступ к email
   - Базовая информация профиля

## 🧪 Проверка настройки

Создайте тестовую ссылку для проверки:
```
https://id.vk.com/auth?app_id=YOUR_APP_ID&redirect_uri=http://localhost:8000/api/users/auth/vk/&response_type=code&scope=email
```

Замените `YOUR_APP_ID` на ваш реальный App ID и откройте ссылку в браузере.

## 📞 Если ничего не работает

1. **Проверьте статус приложения** - оно должно быть активным
2. **Убедитесь в правильности Redirect URI** - частая причина ошибок
3. **Проверьте права доступа** - нужен доступ к email
4. **Очистите кэш браузера** и попробуйте снова

## 🔄 Альтернативный путь через VK Developers

Если кабинет VK ID не работает, можно попробовать через старый интерфейс:

1. Перейдите на **https://dev.vk.com/**
2. Создайте приложение типа **"Мини-приложение"**
3. В настройках найдите OAuth параметры
4. Получите ID приложения и секретный ключ

**Примечание**: Новые приложения рекомендуется создавать через кабинет VK ID, так как это современный способ интеграции. 