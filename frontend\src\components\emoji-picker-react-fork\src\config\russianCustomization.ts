/**
 * Русская кастомизация для emoji-picker-react
 * 
 * Этот файл содержит:
 * 1. Русские переводы названий эмодзи
 * 2. Дополнительные ключевые слова для поиска
 * 3. Список исключенных эмодзи
 * 
 * Инструкция по использованию:
 * 1. Добавьте русские переводы в объект russianEmojiNames
 * 2. Добавьте ключевые слова в объект emojiSearchKeywords
 * 3. Добавьте unicode эмодзи для исключения в массив excludedEmojis
 */

// Русские переводы названий эмодзи
// Ключ - это unicode эмодзи (unified), значение - русское название
export const russianEmojiNames: Record<string, string> = {
  // Примеры - добавьте свои переводы здесь
  '1f600': 'улыбающееся лицо',
  '1f603': 'улыбающееся лицо с открытым ртом',
  '1f604': 'улыбающееся лицо с открытым ртом и улыбающимися глазами',
  '1f601': 'ухмыляющееся лицо с улыбающимися глазами',
  '1f606': 'улыбающееся лицо с открытым ртом и плотно закрытыми глазами',
  '1f605': 'улыбающееся лицо с открытым ртом и холодным потом',
  '1f923': 'катающееся по полу от смеха',
  '1f602': 'лицо со слезами радости',
  '1f642': 'слегка улыбающееся лицо',
  '1f643': 'перевернутое лицо',
  '1f609': 'подмигивающее лицо',
  '1f60a': 'улыбающееся лицо с улыбающимися глазами',
  '1f607': 'улыбающееся лицо с нимбом',
  '1f970': 'улыбающееся лицо с тремя сердечками',
  '1f60d': 'улыбающееся лицо с глазами-сердечками',
  '1f929': 'пораженное лицо со звездными глазами',
  '1f618': 'лицо, посылающее поцелуй',
  '1f617': 'целующее лицо',
  '263a-fe0f': 'улыбающееся лицо',
  '1f61a': 'целующее лицо с закрытыми глазами',
  '1f619': 'целующее лицо с улыбающимися глазами',
  '1f60b': 'лицо, наслаждающееся вкусной едой',
  '1f61b': 'лицо с высунутым языком',
  '1f61c': 'лицо с высунутым языком и подмигивающим глазом',
  '1f61d': 'лицо с высунутым языком и плотно закрытыми глазами',
  '1f911': 'лицо с денежным ртом',
  '1f917': 'обнимающее лицо',
  '1f914': 'думающее лицо',
  '1f910': 'лицо на молнии',
  '1f610': 'нейтральное лицо',
  '1f611': 'невыразительное лицо',
  '1f636': 'лицо без рта',
  '1f60f': 'ухмыляющееся лицо',
  '1f612': 'недовольное лицо',
  '1f644': 'лицо с закатывающимися глазами',
  '1f62c': 'гримасничающее лицо',
  '1f925': 'лживое лицо',
  '1f60c': 'облегченное лицо',
  '1f614': 'задумчивое лицо',
  '1f62a': 'сонное лицо',
  '1f924': 'пускающее слюни лицо',
  '1f634': 'спящее лицо',
  '1f637': 'лицо в медицинской маске',
  '1f912': 'лицо с термометром',
  '1f915': 'лицо с повязкой на голове',
  '1f922': 'тошнотворное лицо',
  '1f927': 'чихающее лицо',
  '1f975': 'горячее лицо',
  '1f976': 'холодное лицо',
  '1f635': 'головокружительное лицо',
  '1f920': 'лицо в ковбойской шляпе',
  '1f973': 'праздничное лицо',
  '1f60e': 'улыбающееся лицо в солнцезащитных очках',
  '1f913': 'лицо ботаника',
  '1f615': 'смущенное лицо',
  '1f61f': 'обеспокоенное лицо',
  '1f641': 'слегка хмурящееся лицо',
  '2639-fe0f': 'хмурящееся лицо',
  '1f62e': 'лицо с открытым ртом',
  '1f62f': 'притихшее лицо',
  '1f632': 'удивленное лицо',
  '1f633': 'покрасневшее лицо',
  '1f97a': 'умоляющее лицо',
  '1f626': 'хмурящееся лицо с открытым ртом',
  '1f627': 'мучающееся лицо',
  '1f628': 'испуганное лицо',
  '1f630': 'лицо с открытым ртом и холодным потом',
  '1f625': 'разочарованное, но облегченное лицо',
  '1f622': 'плачущее лицо',
  '1f62d': 'громко плачущее лицо',
  '1f631': 'лицо, кричащее от страха',
  '1f616': 'сбитое с толку лицо',
  '1f623': 'упорствующее лицо',
  '1f61e': 'разочарованное лицо',
  '1f613': 'лицо с холодным потом',
  '1f629': 'усталое лицо',
  '1f62b': 'уставшее лицо',
  '1f971': 'зевающее лицо',
  '1f624': 'лицо с торжествующим видом',
  '1f621': 'надутое лицо',
  '1f620': 'сердитое лицо',
  '1f608': 'улыбающийся чертенок',
  '1f47f': 'чертенок',
  '1f480': 'череп',
  '2620-fe0f': 'череп и скрещенные кости',
  '1f4a9': 'куча какашек',
  '1f921': 'лицо клоуна',
  '1f479': 'японский огр',
  '1f47a': 'японский гоблин',
  '1f47b': 'призрак',
  '1f47d': 'инопланетянин',
  '1f47e': 'инопланетный монстр',
  '1f916': 'лицо робота',
  
  // Сердечки
  '2764-fe0f': 'красное сердце',
  '1f9e1': 'оранжевое сердце',
  '1f49b': 'желтое сердце',
  '1f49a': 'зеленое сердце',
  '1f499': 'синее сердце',
  '1f49c': 'фиолетовое сердце',
  '1f90e': 'коричневое сердце',
  '1f5a4': 'черное сердце',
  '1f90d': 'белое сердце',
  '1f494': 'разбитое сердце',
  '1f495': 'два сердца',
  '1f496': 'сверкающее сердце',
  '1f497': 'растущее сердце',
  '1f493': 'бьющееся сердце',
  '1f49e': 'вращающиеся сердца',
  '1f498': 'сердце со стрелой',
  '1f49d': 'сердце с лентой',
  '1f49f': 'украшение сердца',
  '2763-fe0f': 'восклицательный знак сердца',
  
  // Жесты рук
  '1f44b': 'машущая рука',
  '1f44d': 'большой палец вверх',
  '1f44e': 'большой палец вниз',
  '1f44c': 'знак ОК',
  '270c-fe0f': 'знак победы',
  '1f91e': 'скрещенные пальцы',
  '1f918': 'знак рогов',
  '1f919': 'знак "позвони мне"',
  '1f448': 'указательный палец влево',
  '1f449': 'указательный палец вправо',
  '1f446': 'указательный палец вверх',
  '1f447': 'указательный палец вниз',
  '261d-fe0f': 'указательный палец вверх',
  '1f595': 'средний палец',
  '270a': 'поднятый кулак',
  '1f44a': 'кулак',
  '1f91b': 'кулак влево',
  '1f91c': 'кулак вправо',
  '1f44f': 'хлопающие руки',
  '1f64c': 'поднятые руки',
  '1f450': 'открытые руки',
  '1f64f': 'сложенные руки',
  '1f4aa': 'напряженный бицепс',
  
  // Добавьте больше переводов здесь...
};

// Дополнительные ключевые слова для поиска
// Ключ - unicode эмодзи, значение - массив ключевых слов на русском
export const emojiSearchKeywords: Record<string, string[]> = {
  // Примеры - добавьте свои ключевые слова здесь
  '1f600': ['радость', 'счастье', 'веселье', 'смех', 'позитив'],
  '1f603': ['радость', 'счастье', 'веселье', 'смех', 'позитив', 'открытый рот'],
  '1f602': ['смех', 'слезы', 'радость', 'веселье', 'ржач', 'ржака', 'ржу', 'лол'],
  '1f923': ['смех', 'ржач', 'ржака', 'ржу', 'лол', 'катаюсь', 'умираю'],
  '1f609': ['подмигивание', 'флирт', 'игривость', 'намек'],
  '1f60d': ['любовь', 'влюбленность', 'сердечки', 'обожание'],
  '1f618': ['поцелуй', 'любовь', 'нежность', 'романтика'],
  '1f914': ['думаю', 'размышление', 'раздумье', 'хм', 'хмм'],
  '1f62d': ['плач', 'слезы', 'грусть', 'печаль', 'рыдание'],
  '1f621': ['злость', 'гнев', 'ярость', 'бешенство', 'сердитый'],
  '1f620': ['злость', 'гнев', 'ярость', 'бешенство', 'сердитый'],
  '1f4a9': ['какашка', 'говно', 'дерьмо', 'фекалии', 'кал'],
  '1f47b': ['призрак', 'дух', 'привидение', 'страшно', 'хэллоуин'],
  '1f480': ['смерть', 'череп', 'кости', 'страшно', 'хэллоуин'],
  
  // Сердечки
  '2764-fe0f': ['любовь', 'сердце', 'романтика', 'чувства', 'красный'],
  '1f9e1': ['любовь', 'сердце', 'романтика', 'чувства', 'оранжевый'],
  '1f49b': ['любовь', 'сердце', 'романтика', 'чувства', 'желтый'],
  '1f49a': ['любовь', 'сердце', 'романтика', 'чувства', 'зеленый'],
  '1f499': ['любовь', 'сердце', 'романтика', 'чувства', 'синий'],
  '1f49c': ['любовь', 'сердце', 'романтика', 'чувства', 'фиолетовый'],
  '1f494': ['разбитое сердце', 'расставание', 'грусть', 'боль', 'разлука'],
  
  // Жесты
  '1f44d': ['лайк', 'одобрение', 'согласие', 'хорошо', 'отлично', 'класс'],
  '1f44e': ['дизлайк', 'неодобрение', 'плохо', 'не нравится'],
  '1f44c': ['окей', 'хорошо', 'отлично', 'идеально', 'ок'],
  '270c-fe0f': ['победа', 'мир', 'два пальца', 'виктория'],
  '1f64f': ['молитва', 'спасибо', 'пожалуйста', 'благодарность', 'просьба'],
  '1f4aa': ['сила', 'мощь', 'мускулы', 'спорт', 'тренировка'],
  
  // Добавьте больше ключевых слов здесь...
};

// Список исключенных эмодзи (unicode)
// Добавьте сюда unicode эмодзи, которые хотите скрыть
export const excludedEmojis: string[] = [
  // Примеры ЛГБТ-связанных эмодзи (добавьте нужные)
  // '1f3f3-fe0f-200d-1f308', // радужный флаг
  // '1f3f3-fe0f-200d-26a7-fe0f', // трансгендерный флаг
  
  // Добавьте другие эмодзи для исключения здесь...
  // Например:
  // '1f595', // средний палец (если хотите исключить)
  // '1f4a9', // какашка (если хотите исключить)
];

/**
 * Функция для получения русского названия эмодзи
 */
export function getRussianEmojiName(unified: string): string | null {
  return russianEmojiNames[unified] || null;
}

/**
 * Функция для получения ключевых слов эмодзи
 */
export function getEmojiKeywords(unified: string): string[] {
  return emojiSearchKeywords[unified] || [];
}

/**
 * Функция для проверки, исключен ли эмодзи
 */
export function isEmojiExcluded(unified: string): boolean {
  return excludedEmojis.includes(unified);
}

// ============================================================================
// ИНСТРУКЦИИ ПО ИНТЕГРАЦИИ
// ============================================================================

/**
 * ИНСТРУКЦИЯ 1: Модификация dataUtils/emojiSelectors.ts
 *
 * Найдите функцию emojiNames и замените её на:
 *
 * import { getRussianEmojiName, getEmojiKeywords } from '../config/russianCustomization';
 *
 * export function emojiNames(emoji: DataEmoji): string[] {
 *   const originalNames = emoji[EmojiProperties.name] || [];
 *   const unified = emojiUnified(emoji);
 *
 *   // Добавляем русское название
 *   const russianName = getRussianEmojiName(unified);
 *   const keywords = getEmojiKeywords(unified);
 *
 *   const allNames = [...originalNames];
 *
 *   if (russianName) {
 *     allNames.unshift(russianName); // Русское название в начало
 *   }
 *
 *   // Добавляем ключевые слова
 *   allNames.push(...keywords);
 *
 *   return allNames;
 * }
 */

/**
 * ИНСТРУКЦИЯ 2: Модификация hooks/useDisallowedEmojis.ts
 *
 * Добавьте импорт в начало файла:
 * import { isEmojiExcluded } from '../config/russianCustomization';
 *
 * Найдите функцию useIsEmojiDisallowed и замените её на:
 *
 * export function useIsEmojiDisallowed() {
 *   const disallowedEmojis = useDisallowedEmojis();
 *   const isUnicodeHidden = useIsUnicodeHidden();
 *
 *   return function isEmojiDisallowed(emoji: DataEmoji) {
 *     const unified = unifiedWithoutSkinTone(emojiUnified(emoji));
 *
 *     // Проверяем исключенные эмодзи из русской кастомизации
 *     if (isEmojiExcluded(unified)) {
 *       return true;
 *     }
 *
 *     return Boolean(disallowedEmojis[unified] || isUnicodeHidden(unified));
 *   };
 * }
 */

/**
 * ИНСТРУКЦИЯ 3: Модификация components/emoji/EmojiButton.tsx (для отображения русских названий)
 *
 * Найдите компонент EmojiButton и добавьте импорт:
 * import { getRussianEmojiName } from '../../config/russianCustomization';
 *
 * В функции компонента найдите где формируется title или aria-label и замените на:
 *
 * const russianName = getRussianEmojiName(unified);
 * const displayName = russianName || emojiNames(emoji)[0] || '';
 *
 * Используйте displayName вместо оригинального названия.
 */

/**
 * ИНСТРУКЦИЯ 4: Модификация RussianEmojiPicker.jsx
 *
 * Добавьте в ваш RussianEmojiPicker.jsx импорт:
 * import { excludedEmojis } from './emoji-picker-react-fork/src/config/russianCustomization';
 *
 * И передайте исключенные эмодзи в EmojiPicker:
 *
 * <EmojiPicker
 *   {...props}
 *   hiddenEmojis={excludedEmojis}
 *   locale="ru"
 * />
 */
