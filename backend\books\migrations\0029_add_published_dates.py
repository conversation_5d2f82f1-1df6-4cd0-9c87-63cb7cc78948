# Generated by Django 5.0.2 on 2025-06-23 11:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('books', '0028_add_comment_soft_delete'),
    ]

    operations = [
        migrations.AddField(
            model_name='book',
            name='published_at',
            field=models.DateTimeField(blank=True, help_text='Дата первой публикации книги', null=True),
        ),
        migrations.AddField(
            model_name='bookchapter',
            name='published_at',
            field=models.DateTimeField(blank=True, help_text='Дата публикации главы', null=True),
        ),
        migrations.AddField(
            model_name='bookchapter',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
