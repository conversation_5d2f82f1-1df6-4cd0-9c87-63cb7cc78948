from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from rest_framework import serializers
from django.db.models import Q, Count, OuterRef, Subquery
from django.contrib.auth import get_user_model
from books.models import Book, Genre, Hashtag
from books.serializers import BookListSerializer, GenreSerializer, HashtagSerializer
from users.serializers import MiniUserSerializer

User = get_user_model()

class SearchBookSerializer(serializers.ModelSerializer):
    """Специальный сериализатор для результатов поиска книг"""
    author = serializers.SerializerMethodField()
    genres = GenreSerializer(many=True, read_only=True)
    hashtags = HashtagSerializer(many=True, read_only=True)
    cover = serializers.SerializerMethodField()
    views_count = serializers.SerializerMethodField()
    likes_count = serializers.SerializerMethod<PERSON>ield()
    library_count = serializers.SerializerMethodField()
    comments_count = serializers.SerializerMethodField()

    class Meta:
        model = Book
        fields = ['id', 'title', 'description', 'cover', 'author', 'genres', 'hashtags', 'created_at', 
                 'views_count', 'likes_count', 'library_count', 'comments_count']

    def get_author(self, obj):
        return {
            'id': obj.author.id,
            'username': obj.author.username,
            'display_name': obj.author.display_name,
        }

    def get_cover(self, obj):
        if obj.cover_mini:
            return obj.cover_mini.url
        elif obj.cover:
            return obj.cover.url
        return None

    def get_views_count(self, obj):
        # Пытаемся использовать аннотированное поле, если есть
        if hasattr(obj, 'views_count'):
            return obj.views_count
        return obj.viewed_by.count()

    def get_likes_count(self, obj):
        # Пытаемся использовать аннотированное поле, если есть
        if hasattr(obj, 'likes_count'):
            return obj.likes_count
        return obj.likes.count()

    def get_library_count(self, obj):
        # Пытаемся использовать аннотированное поле, если есть
        if hasattr(obj, 'library_count'):
            return obj.library_count
        return obj.in_libraries.count()

    def get_comments_count(self, obj):
        # Пытаемся использовать аннотированное поле, если есть
        if hasattr(obj, 'comments_count'):
            return obj.comments_count
        return obj.comments.filter(deleted_by_user=False, deleted_by_admin=False).count()

@api_view(['GET'])
@permission_classes([AllowAny])
def search_view(request):
    """
    Универсальный поиск по книгам и авторам
    """
    query = request.GET.get('q', '').strip()
    
    if not query:
        return Response(
            {'books': [], 'authors': []},
            status=status.HTTP_200_OK
        )
    
    # Разбиваем запрос на слова для более гибкого поиска
    query_words = query.lower().split()
    
    try:
        # Поиск книг
        books_query = Q()
        
        # Поиск по названию книги
        for word in query_words:
            books_query |= Q(title__icontains=word)
        
        # Поиск по автору (имя и логин)
        for word in query_words:
            books_query |= Q(author__display_name__icontains=word)
            books_query |= Q(author__username__icontains=word)
        
        # Поиск по жанрам
        matching_genres = Genre.objects.filter(
            name__icontains=query
        ).values_list('id', flat=True)
        
        if matching_genres:
            books_query |= Q(genres__id__in=matching_genres)
        
        # Поиск по хештегам
        matching_hashtags = Hashtag.objects.filter(
            name__icontains=query
        ).values_list('id', flat=True)
        
        if matching_hashtags:
            books_query |= Q(hashtags__id__in=matching_hashtags)
        
        # Получаем книги без аннотаций и пусть сериализатор сам считает через fallback методы
        books = Book.objects.filter(
            books_query,
            status__in=['in_progress', 'finished']  # Используем новое поле status вместо is_published
        ).select_related(
            'author'
        ).prefetch_related(
            'genres', 'hashtags'
        ).distinct().order_by('-created_at')[:20]
        
        # Поиск авторов
        authors_query = Q()
        
        for word in query_words:
            authors_query |= Q(display_name__icontains=word)
            authors_query |= Q(username__icontains=word)
        
        # Получаем авторов у которых есть хотя бы одна опубликованная книга
        authors = User.objects.filter(
            authors_query
        ).annotate(
            published_books_count=Count('books', filter=Q(books__status__in=['in_progress', 'finished']))
        ).filter(
            published_books_count__gt=0
        ).select_related().distinct().order_by('-author_rating', '-reader_rating')[:10]  # Ограничиваем результат
        
        # Сериализуем результаты
        books_serializer = SearchBookSerializer(books, many=True, context={'request': request})
        authors_serializer = MiniUserSerializer(authors, many=True, context={'request': request})
        
        return Response({
            'books': books_serializer.data,
            'authors': authors_serializer.data,
            'query': query
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        print(f"Search error: {e}")
        import traceback
        traceback.print_exc()
        return Response(
            {'error': 'Ошибка при выполнении поиска'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )