import React, { useState } from 'react';
import { Card, InputNumber, Select, Space, Typography, Divider, Button } from 'antd';
import { LABEL_SETTINGS } from '../utils/labelSettings';

const { Title, Text } = Typography;
const { Option } = Select;

const LabelSettingsPanel = ({ onSettingsChange, onPreview }) => {
  const [settings, setSettings] = useState(LABEL_SETTINGS);

  const handleSettingChange = (labelType, field, value) => {
    const newSettings = {
      ...settings,
      [labelType]: {
        ...settings[labelType],
        [field]: value
      }
    };
    setSettings(newSettings);
    onSettingsChange?.(newSettings);
  };

  const resetToDefaults = () => {
    setSettings(LABEL_SETTINGS);
    onSettingsChange?.(LABEL_SETTINGS);
  };

  const renderLabelSettings = (labelType, title) => {
    const labelSettings = settings[labelType];
    
    return (
      <Card size="small" title={title} style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Размер:</Text>
            <div style={{ display: 'flex', gap: 8, marginTop: 4 }}>
              <div>
                <Text>Ширина:</Text>
                <InputNumber
                  size="small"
                  min={10}
                  max={300}
                  value={labelSettings.width}
                  onChange={(value) => handleSettingChange(labelType, 'width', value)}
                  addonAfter="px"
                />
              </div>
              <div>
                <Text>Высота:</Text>
                <InputNumber
                  size="small"
                  min={10}
                  max={300}
                  value={labelSettings.height}
                  onChange={(value) => handleSettingChange(labelType, 'height', value)}
                  addonAfter="px"
                />
              </div>
            </div>
          </div>

          <div>
            <Text strong>Отступы:</Text>
            <div style={{ display: 'flex', gap: 8, marginTop: 4 }}>
              <div>
                <Text>По X:</Text>
                <InputNumber
                  size="small"
                  min={0}
                  max={100}
                  value={labelSettings.offsetX}
                  onChange={(value) => handleSettingChange(labelType, 'offsetX', value)}
                  addonAfter="px"
                />
              </div>
              <div>
                <Text>По Y:</Text>
                <InputNumber
                  size="small"
                  min={0}
                  max={100}
                  value={labelSettings.offsetY}
                  onChange={(value) => handleSettingChange(labelType, 'offsetY', value)}
                  addonAfter="px"
                />
              </div>
            </div>
          </div>

          <div>
            <Text strong>Позиция:</Text>
            <Select
              size="small"
              value={labelSettings.position}
              onChange={(value) => handleSettingChange(labelType, 'position', value)}
              style={{ width: '100%', marginTop: 4 }}
            >
              <Option value="top-left">Верхний левый</Option>
              <Option value="top-right">Верхний правый</Option>
              <Option value="bottom-left">Нижний левый</Option>
              <Option value="bottom-right">Нижний правый</Option>
            </Select>
          </div>

          <div style={{ fontSize: 12, color: '#666' }}>
            {labelSettings.description}
          </div>
        </Space>
      </Card>
    );
  };

  return (
    <div style={{ padding: 16, backgroundColor: '#f5f5f5', borderRadius: 8 }}>
      <Title level={4}>Настройки лейблов</Title>
      
      {renderLabelSettings('ageRating', '🔞 Лейбл 18+')}
      {renderLabelSettings('profanity', '🤬 Лейбл ненормативной лексики')}
      
      <Divider />
      
      <Space>
        <Button onClick={resetToDefaults}>
          Сбросить к умолчанию
        </Button>
        <Button type="primary" onClick={onPreview}>
          Применить и предпросмотр
        </Button>
      </Space>

      <Divider />
      
      <div style={{ fontSize: 12, color: '#666' }}>
        <Text strong>Справка:</Text>
        <ul style={{ margin: '8px 0', paddingLeft: 20 }}>
          <li>Размеры указываются в пикселях</li>
          <li>Отступы считаются от краев обложки</li>
          <li>Обложка имеет размер 700x1000 пикселей</li>
          <li>Изменения применяются в реальном времени</li>
        </ul>
      </div>
    </div>
  );
};

export default LabelSettingsPanel;
