# Generated manually for timezone display value field

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0044_readingsession_userlibrary'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='timezone_display_value',
            field=models.CharField(default='Europe/Moscow_msk', help_text='Сохраняет выбранный пользователем вариант (например, Europe/Moscow_msk или Europe/Moscow_spb)', max_length=40, verbose_name='Выбранное значение часового пояса'),
        ),
    ]
