<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="https://storage.yandexcloud.net/lpo-test/dist/favicon.ico">
    <title>Ваш браузер устарел - ЛитПортал</title>
    <style>
        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            text-align: center;
        }
        
        .header {
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            color: #d32f2f;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .description {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 50px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .browsers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 30px;
            max-width: 720px;
            margin: 0 auto;
        }
        
        .browser-item {
            background: white;
            border-radius: 12px;
            padding: 30px 20px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 2px solid transparent;
        }
        
        .browser-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #2196F3;
            text-decoration: none;
            color: inherit;
        }
        
        .browser-icon {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            display: block;
            border-radius: 8px;
        }
        
        .browser-name {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .footer {
            margin-top: 60px;
            padding-top: 30px;
            border-top: 1px solid #ddd;
            color: #888;
            font-size: 0.9em;
        }
        
        /* Адаптивность для мобильных устройств */
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .description {
                font-size: 1em;
            }
            
            .browsers-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
            
            .browser-item {
                padding: 20px 15px;
            }
            
            .browser-icon {
                width: 80px;
                height: 80px;
            }
            
            .browser-name {
                font-size: 1.1em;
            }
        }
        
        @media (max-width: 480px) {
            .browsers-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Ваш браузер устарел</h1>
        </div>
        
        <div class="description">
            Из-за этого ЛитПортал может работать медленно и нестабильно.<br>
            Обновите свой браузер или установите один из рекомендуемых:
        </div>
        
        <div class="browsers-grid">
            <a href="away.html?to=https%3A%2F%2Fwww.mozilla.org&badbrowser=firefox" class="browser-item">
                <img src="https://storage.yandexcloud.net/lpo-test/dist/browsers/firefox.png" alt="Firefox" class="browser-icon">
                <h3 class="browser-name">Firefox</h3>
            </a>
            
            <a href="away.html?to=https%3A%2F%2Fbrowser.yandex.ru%2F&badbrowser=yandex" class="browser-item">
                <img src="https://storage.yandexcloud.net/lpo-test/dist/browsers/yandex.png" alt="Yandex" class="browser-icon">
                <h3 class="browser-name">Yandex</h3>
            </a>
            
            <a href="away.html?to=https%3A%2F%2Fwww.google.com%2Fchrome&badbrowser=chrome" class="browser-item">
                <img src="https://storage.yandexcloud.net/lpo-test/dist/browsers/chrome.png" alt="Chrome" class="browser-icon">
                <h3 class="browser-name">Chrome</h3>
            </a>
            
            <a href="away.html?to=https%3A%2F%2Fwww.opera.com&badbrowser=opera" class="browser-item">
                <img src="https://storage.yandexcloud.net/lpo-test/dist/browsers/opera.png" alt="Opera" class="browser-icon">
                <h3 class="browser-name">Opera</h3>
            </a>
        </div>
        
        <div class="footer">
            <p>Для корректной работы сайта рекомендуется использовать современные версии браузеров</p>
            <div id="browserInfo" style="margin-top: 15px; font-size: 0.8em; color: #999;"></div>
        </div>
    </div>

    <script>
        // Показываем информацию о браузере, если она есть
        try {
            const redirectInfo = sessionStorage.getItem('browserCheckRedirect');
            if (redirectInfo) {
                const info = JSON.parse(redirectInfo);
                const browserInfoEl = document.getElementById('browserInfo');

                let reasons = [];
                if (info.reason.oldBrowser) reasons.push('устаревшая версия браузера');
                if (info.reason.noWebP) reasons.push('нет поддержки WebP');
                if (info.reason.noModernJS) reasons.push('нет поддержки современного JavaScript');
                if (info.reason.noModernCSS) reasons.push('нет поддержки современного CSS');

                if (reasons.length > 0) {
                    browserInfoEl.innerHTML = `
                        <strong>Обнаружено:</strong> ${info.browser.name} ${info.browser.version}<br>
                        <strong>Проблемы:</strong> ${reasons.join(', ')}
                    `;
                }
            }
        } catch(e) {
            // Игнорируем ошибки
        }
    </script>
</body>
</html>
