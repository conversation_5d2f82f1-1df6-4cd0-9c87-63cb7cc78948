import { useState, useEffect, useCallback } from 'react';

// Глобальный кеш для книг пользователей
const userBooksCache = new Map();
const loadingUsers = new Set();

export const useUserBooks = (username) => {
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchUserBooks = useCallback(async (username) => {
    if (!username) return { books: [], counts: { finished: 0, inProgress: 0, drafts: 0 } };

    // Проверяем кеш
    if (userBooksCache.has(username)) {
      const cachedData = userBooksCache.get(username);
      // Проверяем, не устарели ли данные (кешируем на 5 минут)
      if (Date.now() - cachedData.timestamp < 5 * 60 * 1000) {
        return cachedData.data;
      }
    }

    // Если уже загружается, ждем
    if (loadingUsers.has(username)) {
      return new Promise((resolve) => {
        const checkCache = () => {
          if (userBooksCache.has(username) && !loadingUsers.has(username)) {
            resolve(userBooksCache.get(username).data);
          } else {
            setTimeout(checkCache, 100);
          }
        };
        checkCache();
      });
    }

    // Начинаем загрузку
    loadingUsers.add(username);

    try {
      const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      const response = await fetch(`${backendUrl}/api/users/${username}/books/`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch books: ${response.status}`);
      }

      const data = await response.json();
      const booksArr = Array.isArray(data.results) ? data.results : (Array.isArray(data) ? data : (data.books || []));
      
      // Фильтруем книги не в статусе создания
      const visibleBooks = booksArr.filter(b => b.creation_status !== 'creating');
      
      // Вычисляем счетчики
      const finished = visibleBooks.filter(b => b.is_published && b.is_finished).length;
      const inProgress = visibleBooks.filter(b => b.is_published && !b.is_finished).length;
      const drafts = visibleBooks.filter(b => !b.is_published).length;

      const result = {
        books: booksArr,
        counts: { finished, inProgress, drafts }
      };

      // Кешируем результат
      userBooksCache.set(username, {
        data: result,
        timestamp: Date.now()
      });

      return result;
    } catch (error) {
      console.error('Error loading user books:', error);
      throw error;
    } finally {
      loadingUsers.delete(username);
    }
  }, []);

  useEffect(() => {
    if (!username) {
      setBooks([]);
      setLoading(false);
      return;
    }

    const loadBooks = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await fetchUserBooks(username);
        setBooks(result.books);
      } catch (err) {
        setError(err.message);
        setBooks([]);
      } finally {
        setLoading(false);
      }
    };

    loadBooks();
  }, [username, fetchUserBooks]);

  // Метод для обновления кеша (например, после создания/удаления книги)
  const refreshBooks = useCallback(async () => {
    if (!username) return;
    
    // Очищаем кеш для этого пользователя
    userBooksCache.delete(username);
    
    try {
      setLoading(true);
      setError(null);
      const result = await fetchUserBooks(username);
      setBooks(result.books);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [username, fetchUserBooks]);

  // Получение счетчиков из кеша без загрузки
  const getBookCounts = useCallback((targetUsername = username) => {
    if (!targetUsername) return { finished: 0, inProgress: 0, drafts: 0 };
    
    const cached = userBooksCache.get(targetUsername);
    if (cached) {
      return cached.data.counts;
    }
    
    // Если нет в кеше, вычисляем из текущих books
    if (targetUsername === username && books.length > 0) {
      const visibleBooks = books.filter(b => b.creation_status !== 'creating');
      const finished = visibleBooks.filter(b => b.is_published && b.is_finished).length;
      const inProgress = visibleBooks.filter(b => b.is_published && !b.is_finished).length;
      const drafts = visibleBooks.filter(b => !b.is_published).length;
      return { finished, inProgress, drafts };
    }
    
    return { finished: 0, inProgress: 0, drafts: 0 };
  }, [username, books]);

  return {
    books,
    loading,
    error,
    refreshBooks,
    getBookCounts
  };
};

// Хук только для получения счетчиков (без загрузки всех книг)
export const useUserBookCounts = (username) => {
  const [counts, setCounts] = useState({ finished: 0, inProgress: 0, drafts: 0 });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!username) {
      setCounts({ finished: 0, inProgress: 0, drafts: 0 });
      setLoading(false);
      return;
    }

    const loadCounts = async () => {
      try {
        setLoading(true);
        
        // Сначала проверяем кеш
        const cached = userBooksCache.get(username);
        if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
          setCounts(cached.data.counts);
          setLoading(false);
          return;
        }

        // Если в кеше нет или устарело, загружаем
        const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
        const response = await fetch(`${backendUrl}/api/users/${username}/books/`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch books: ${response.status}`);
        }

        const data = await response.json();
        const booksArr = Array.isArray(data.results) ? data.results : (Array.isArray(data) ? data : (data.books || []));
        
        // Фильтруем книги не в статусе создания
        const visibleBooks = booksArr.filter(b => b.creation_status !== 'creating');
        
        // Вычисляем счетчики
        const finished = visibleBooks.filter(b => b.is_published && b.is_finished).length;
        const inProgress = visibleBooks.filter(b => b.is_published && !b.is_finished).length;
        const drafts = visibleBooks.filter(b => !b.is_published).length;

        const newCounts = { finished, inProgress, drafts };
        setCounts(newCounts);

        // Обновляем кеш
        userBooksCache.set(username, {
          data: { books: booksArr, counts: newCounts },
          timestamp: Date.now()
        });

      } catch (error) {
        console.error('Error loading user book counts:', error);
        setCounts({ finished: 0, inProgress: 0, drafts: 0 });
      } finally {
        setLoading(false);
      }
    };

    loadCounts();
  }, [username]);

  return { counts, loading };
};

// Функция для очистки кеша конкретного пользователя
export const clearUserBooksCache = (username) => {
  if (username) {
    userBooksCache.delete(username);
  }
};

// Функция для очистки всего кеша
export const clearAllUserBooksCache = () => {
  userBooksCache.clear();
}; 