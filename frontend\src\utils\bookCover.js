// Получить URL обложки книги с учётом типа
export function getBookCoverUrl(book) {
  if (!book) return '';
  if (book.cover) return book.cover;
  if (book.cover_temp_url) return book.cover_temp_url;
  return '';
}

// Получить URL миниатюры обложки книги (140x200)
export function getBookCoverMiniUrl(book) {
  if (!book) return '';
  if (book.cover_mini_url) return book.cover_mini_url;
  if (book.cover_mini) return book.cover_mini;
  return '';
} 