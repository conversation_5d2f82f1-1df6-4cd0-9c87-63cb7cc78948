/* Firefox-специфичные исправления для Tailwind CSS */

/* Отключаем проблемные webkit-свойства в Firefox */
@-moz-document url-prefix() {
  /* Скрываем webkit-специфичные стили от Firefox */
  html {
    -webkit-text-size-adjust: auto !important;
  }
  
  /* Убираем moz-osx-font-smoothing предупреждения */
  * {
    -moz-osx-font-smoothing: unset !important;
  }
  
  /* Исправляем outline проблемы */
  button:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 2px solid #3b82f6 !important;
    outline-offset: 2px !important;
  }
  
  /* Убираем webkit-specific pseudo-elements */
  input::-webkit-input-placeholder,
  textarea::-webkit-input-placeholder {
    display: none !important;
  }
  
  /* Заменяем на Firefox-совместимые */
  input::placeholder,
  textarea::placeholder {
    color: #6b7280 !important;
    opacity: 1 !important;
  }
}

/* Универсальные исправления */
/* Убираем все webkit-свойства которые Firefox не понимает */
html {
  -webkit-text-size-adjust: none !important;
  -moz-text-size-adjust: none !important;
  text-size-adjust: none !important;
}

/* Более безопасная версия font-smoothing */
html, body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-smooth: always;
}

/* Исправляем проблемы с селекторами */
@supports selector(:focus-visible) {
  *:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
}

@supports not selector(:focus-visible) {
  *:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
} 