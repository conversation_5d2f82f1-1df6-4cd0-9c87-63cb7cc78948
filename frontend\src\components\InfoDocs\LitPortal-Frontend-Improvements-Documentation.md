# LitPortal Frontend - Документация улучшений и исправлений

## Оглавление
1. [Архитектура редакторов](#архитектура-редакторов)
2. [Система ограничений и защиты](#система-ограничений-и-защиты)
3. [Оптимизация публикации](#оптимизация-публикации)
4. [Функция разделения глав](#функция-разделения-глав)
5. [Улучшения интерфейса](#улучшения-интерфейса)
6. [Исправления багов](#исправления-багов)

---

## Архитектура редакторов

### Связки редакторов по формам произведений

LitPortal использует специализированные редакторы для разных форм произведений, каждый из которых оптимизирован под конкретные потребности:

#### Текущая архитектура:
```
1. 📖 Рассказ (story)
   └── StoryEditor + ChapterEditor

2. 📚 Роман (novel)
   └── NovelEditor + ChapterEditorV2

3. 📝 Повесть (novella)
   └── TaleEditor + ChapterEditorV3

4. 📑 Сборник рассказов (story_collection)
   └── StorybookEditor + ChapterEditorV4

5. 🎭 Сборник стихов (poetry_collection)
   └── PoembookEditor + ChapterEditorV5
```

### Выбор редактора в EditBook.jsx

Логика выбора редактора основана на поле `book.type`:

```javascript
// Рассказ - простой редактор с одной главой
{book.type === 'story' && (
  <StoryEditor
    bookId={id}
    book={book}
    chapters={chapters}
    // ... остальные пропсы
  />
)}

// Роман - полнофункциональный редактор с множественными главами
{book.type === 'novel' && (
  <NovelEditor
    bookId={id}
    book={book}
    chapters={chapters}
    // ... остальные пропсы
  />
)}

// Повесть - адаптированный редактор для средних произведений
{book.type === 'novella' && (
  <TaleEditor
    bookId={id}
    book={book}
    chapters={chapters}
    // ... остальные пропсы
  />
)}

// Сборник рассказов - редактор для коллекций
{book.type === 'story_collection' && (
  <StorybookEditor
    bookId={id}
    book={book}
    chapters={chapters}
    // ... остальные пропсы
  />
)}

// Сборник стихов - специализированный редактор для поэзии
{book.type === 'poetry_collection' && (
  <PoembookEditor
    bookId={id}
    book={book}
    chapters={chapters}
    // ... остальные пропсы
  />
)}
```

### Специализация редакторов глав

#### ChapterEditor (для рассказов):
- **Простой интерфейс** для одной главы
- **Базовая функциональность** редактирования
- **Ограниченный набор** инструментов

#### ChapterEditorV2 (для романов):
- **Полный набор функций** для сложных произведений
- **Разделение глав** (функция split)
- **Расширенные инструменты** форматирования
- **Массовые операции** с главами

#### ChapterEditorV3 (для повестей):
- **Адаптированный интерфейс** для средних произведений
- **Баланс функциональности** между простотой и мощностью
- **Оптимизированные инструменты** для повестей

#### ChapterEditorV4 (для сборников рассказов):
- **Специализация для коллекций** отдельных произведений
- **Управление множественными** независимыми частями
- **Инструменты для организации** сборника

#### ChapterEditorV5 (для сборников стихов):
- **Поэтическое форматирование** и структура
- **Специальные инструменты** для стихов
- **Адаптированный интерфейс** для поэзии

### Структура файлов:

```
frontend/src/
├── pages/
│   └── EditBook.jsx                    # Основная логика выбора редактора
├── components/
│   ├── BookEditors/
│   │   ├── StoryEditor.jsx            # Редактор рассказов
│   │   ├── NovelEditor.jsx            # Редактор романов
│   │   ├── TaleEditor.jsx             # Редактор повестей
│   │   ├── StorybookEditor.jsx        # Редактор сборников рассказов
│   │   └── PoembookEditor.jsx         # Редактор сборников стихов
│   ├── ChapterEditor.jsx              # Базовый редактор глав
│   ├── ChapterEditorV2.jsx            # Расширенный редактор (романы)
│   ├── ChapterEditorV3.jsx            # Адаптированный редактор (повести)
│   ├── ChapterEditorV4.jsx            # Коллекционный редактор (сборники рассказов)
│   └── ChapterEditorV5.jsx            # Поэтический редактор (сборники стихов)
```

### Преимущества архитектуры:

#### ✅ Специализация:
- **Каждый редактор** оптимизирован под свою форму произведения
- **Нет лишней функциональности** в простых редакторах
- **Максимум возможностей** в сложных редакторах

#### ✅ Масштабируемость:
- **Легко добавлять** новые типы произведений
- **Независимое развитие** каждого редактора
- **Модульная архитектура** компонентов

#### ✅ Поддержка:
- **Изолированные изменения** не влияют на другие редакторы
- **Четкое разделение** ответственности
- **Простота отладки** и тестирования

### Типы произведений в системе:

```javascript
const BOOK_TYPES = [
  {
    value: 'story',
    label: 'Рассказ',
    comment: 'Рассказ — короткое произведение, состоит из одной части.',
    editor: 'StoryEditor',
    chapterEditor: 'ChapterEditor'
  },
  {
    value: 'novella',
    label: 'Повесть',
    comment: 'Повесть — среднее по объёму произведение, может содержать главы.',
    editor: 'TaleEditor',
    chapterEditor: 'ChapterEditorV3'
  },
  {
    value: 'novel',
    label: 'Роман',
    comment: 'Роман — крупное произведение, обычно делится на главы.',
    editor: 'NovelEditor',
    chapterEditor: 'ChapterEditorV2'
  },
  {
    value: 'story_collection',
    label: 'Сборник рассказов',
    comment: 'Сборник рассказов — несколько рассказов в одной книге.',
    editor: 'StorybookEditor',
    chapterEditor: 'ChapterEditorV4'
  },
  {
    value: 'poetry_collection',
    label: 'Сборник поэзии',
    comment: 'Сборник поэзии — сборник стихотворений.',
    editor: 'PoembookEditor',
    chapterEditor: 'ChapterEditorV5'
  }
];
```

---

## Система ограничений и защиты

### Лимиты контента
```
Романы:
- Максимум на главу: 100,000 символов
- Максимум на произведение: 2,500,000 символов

Повести:
- Максимум на главу: 50,000 символов
- Максимум на произведение: 200,000 символов

Сборники рассказов:
- Максимум на рассказ: 50,000 символов
- Максимум на произведение: 2,500,000 символов

Сборники поэзии:
- Максимум на поэтическое произведение: 50,000 символов
- Максимум на произведение: 2,500,000 символов

Рассказы:
- Максимум на произведение: 50,000 символов

DOCX файлы:
- Максимальный размер: 5 МБ
```

### Защита от превышения лимитов

#### 1. Проверка при публикации
```javascript
const checkPublishLimits = (chaptersToPublish) => {
  // Вычисляем опубликованный + запланированный + планируемый контент
  const currentPublishedStats = safeChapters.reduce((total, chapter) => {
    if (chapter.is_published || chapter.scheduled_publish_at) {
      const plainText = (chapter.content || '').replace(/<[^>]*>/g, '');
      return total + plainText.length;
    }
    return total;
  }, 0);

  const newContentStats = chaptersToPublish.reduce((total, chapter) => {
    const plainText = (chapter.content || '').replace(/<[^>]*>/g, '');
    return total + plainText.length;
  }, 0);

  const totalAfterPublish = currentPublishedStats + newContentStats;
  const maxLimit = book.book_type === 'story' ? MAX_STORY_LENGTH : MAX_NOVEL_LENGTH;

  if (totalAfterPublish > maxLimit) {
    // Показываем модальное окно с объяснением превышения
    return false;
  }
  return true;
};
```

#### 2. Блокировка сохранения при превышении
- Проверка лимитов перед сохранением главы
- Предупреждение пользователя о превышении
- Блокировка сохранения с объяснением

#### 3. Защита при завершении произведения
- Проверка общего объема при установке статуса "Завершено"
- Блокировка завершения при превышении лимитов
- Информативные сообщения об ошибках

---

## Оптимизация публикации

### Массовая публикация

#### БЫЛО (неэффективно):
```javascript
// Поштучная публикация
for (const chapter of chapters) {
  await fetch(`/api/books/${book.id}/chapters/${chapter.id}/`, {
    method: 'PATCH',
    body: JSON.stringify({ is_published: true }),
  });
}
```

#### СТАЛО (оптимально):
```javascript
// Batch публикация
await fetch(`/api/books/${book.id}/chapters/batch_publish/`, {
  method: 'PATCH',
  body: JSON.stringify({ 
    chapter_ids: chapters.map(ch => ch.id),
    is_published: true 
  })
});
```

### Панель массовых операций (BatchOperationsPanel)

#### Расположение:
```
1. Панель ВВЕРХУ - под кнопкой "Выбрать всё" перед списком глав
2. Панель ВНИЗУ - под кнопкой "Выбрать всё" после списка глав

Условие появления: selectedChapters.length > 1
```

#### Функциональность:
```
🟢 Кнопка публикации/снятия с публикации:
   - Иконка: EyeOutlined (зеленый) / EyeInvisibleOutlined (оранжевый)
   - Функция: handleBatchTogglePublish (оптимизирована)
   - Проверка лимитов: ✅ Добавлена

🔵 Кнопка планирования/отмены планирования:
   - Иконка: HourglassOutlined (синий) / перечеркнутые часы (оранжевый)
   - Функция: openScheduleModal / handleBatchCancelSchedule
```

### Оптимизированные места публикации:
- ✅ **BatchOperationsPanel** - массовые операции
- ✅ **Модальное окно подтверждения** - публикация с пропусками
- ✅ **Модальное окно последней главы** - завершение произведения
- ✅ **Отложенная публикация** - планирование

---

## Функция разделения глав

### Как использовать:
1. **Выделите заголовок** новой главы в редакторе
2. **Нажмите кнопку "Разделить главу"** (иконка ножниц)
3. **Подтвердите разделение** в диалоге

### Результат:
- Текст выше выделения остается в текущей главе
- Текст ниже выделения переходит в новую главу
- Новая глава получает заголовок: "Глава X: [выделенный текст]"
- Порядок всех последующих глав автоматически обновляется

### Валидация:
- Минимум 2 символа в выделенном тексте
- Минимум 5 символов в каждой части после разделения
- Проверка прав доступа и обработка ошибок

### API интеграция:
```
POST /api/books/{bookId}/chapters/ - создание новой главы
PATCH /api/books/{bookId}/chapters/{chapterId}/ - обновление текущей главы
PATCH /api/books/{bookId}/chapters/reorder/ - обновление порядка глав
```

---

## Улучшения интерфейса

### Модальное окно планирования публикации

#### Исправленные проблемы:
1. **Убран синий фокус** в поле даты
2. **Исправлена логика доступности дат**

#### CSS стили для убирания фокуса:
```css
.no-focus-outline .ant-picker-input > input:focus {
  box-shadow: none !important;
  border-color: #d9d9d9 !important;
  outline: none !important;
}
```

#### Логика доступности дат:
```javascript
disabledDate={current => {
  if (!current || !minScheduleTime) return false;
  
  const currentDate = current.startOf('day');
  const minDate = minScheduleTime.startOf('day');
  
  // Если дата раньше сегодняшней - блокируем
  if (currentDate.isBefore(minDate)) return true;
  
  // Если дата позже сегодняшней - разрешаем
  if (currentDate.isAfter(minDate)) return false;
  
  // Если сегодняшняя дата - проверяем доступное время
  const isToday = currentDate.isSame(minDate);
  if (isToday) {
    const currentHour = minScheduleTime.hour();
    const currentMinute = minScheduleTime.minute();
    
    // Если есть доступное время - разрешаем
    if (currentHour < 23) return false;
    if (currentHour === 23 && currentMinute < 59) return false;
    
    // Если 23:59 - блокируем
    return true;
  }
  
  return false;
}}
```

### Визуальные улучшения:
- **Обновлены размеры иконок** в панелях инструментов
- **Исправлены стили модальных окон** для лучшего отображения
- **Улучшена цветовая схема** кнопок и элементов интерфейса
- **Оптимизированы размеры** элементов для лучшей читаемости

---

## Исправления багов

### 1. Синхронизация заголовков глав
- Исправлена синхронизация заголовков между редактором и списком глав
- Автоматическое обновление заголовков при изменении

### 2. Исправления в разделении глав
- **Нумерация глав**: правильная нумерация после разделения
- **HTML структура**: корректная обработка HTML тегов
- **API формат**: правильный формат данных для API
- **Конфликты порядка**: предотвращение конфликтов при обновлении порядка

### 3. Исправления переменных
- Исправлена переменная `showBatchDeleteConfirm` в массовых операциях
- Корректная обработка состояний модальных окон

### 4. Обновления текстов предупреждений
- Более информативные сообщения о превышении лимитов
- Четкие инструкции для пользователей
- Правильные расчеты в предупреждениях

### 5. Исправления в проверке контента
- Правильная проверка только опубликованного контента
- Учет запланированного контента при проверке лимитов
- Корректная обработка потенциального опубликованного контента

---

## Технические детали

### Структура проекта:
```
frontend/src/
├── pages/
│   └── EditBook.jsx - основная логика выбора редактора
├── components/
│   ├── BookEditors/
│   │   ├── StoryEditor.jsx - редактор рассказов
│   │   ├── NovelEditor.jsx - редактор романов
│   │   ├── TaleEditor.jsx - редактор повестей
│   │   ├── StorybookEditor.jsx - редактор сборников рассказов
│   │   └── PoembookEditor.jsx - редактор сборников стихов
│   ├── ChapterEditor.jsx - базовый редактор глав (рассказы)
│   ├── ChapterEditorV2.jsx - расширенный редактор (романы)
│   ├── ChapterEditorV3.jsx - адаптированный редактор (повести)
│   ├── ChapterEditorV4.jsx - коллекционный редактор (сборники рассказов)
│   ├── ChapterEditorV5.jsx - поэтический редактор (сборники стихов)
│   └── BatchOperationsPanel - панель массовых операций
```

### Ключевые функции:
- `checkPublishLimits()` - проверка лимитов публикации
- `handleBatchPublishConfirm()` - оптимизированная массовая публикация
- `handleChapterSplit()` - разделение глав (только в ChapterEditorV2)
- `handleBatchTogglePublish()` - массовые операции в панели

### Редакторы по типам произведений:
- `StoryEditor` - простой редактор для рассказов
- `NovelEditor` - полнофункциональный редактор для романов
- `TaleEditor` - адаптированный редактор для повестей
- `StorybookEditor` - коллекционный редактор для сборников рассказов
- `PoembookEditor` - поэтический редактор для сборников стихов

### Константы лимитов:
```javascript
// Романы
const MAX_CHAPTER_LENGTH = 100000;  // 100k символов на главу (романы)
const MAX_NOVEL_LENGTH = 2500000;   // 2.5M символов на произведение (романы)

// Повести (TaleEditor)
const MAX_CHAPTER_LENGTH = 50000;   // 50k символов на главу (повести)
const MAX_NOVEL_LENGTH = 200000;    // 200k символов на произведение (повести)

// Сборники рассказов (StorybookEditor)
const MAX_CHAPTER_LENGTH = 50000;   // 50k символов на рассказ в сборнике
const MAX_NOVEL_LENGTH = 2500000;   // 2.5M символов на произведение

// Сборники поэзии (PoembookEditor)
const MAX_CHAPTER_LENGTH = 50000;   // 50k символов на поэтическое произведение в сборнике
const MAX_NOVEL_LENGTH = 2500000;   // 2.5M символов на произведение

// Рассказы
const MAX_STORY_LENGTH = 50000;     // 50k символов на произведение (рассказы)
const MAX_DOCX_SIZE = 5 * 1024 * 1024; // 5 МБ для DOCX файлов
```

---

## Результаты улучшений

### ✅ Производительность:
- **Быстрая массовая публикация** через batch API
- **Оптимизированные запросы** к серверу
- **Меньше нагрузки** на сеть и сервер

### ✅ Безопасность:
- **Комплексная защита** от превышения лимитов
- **Проверка во всех местах** публикации
- **Блокировка некорректных операций**

### ✅ Пользовательский опыт:
- **Интуитивное разделение глав** одним кликом
- **Удобное планирование** публикации
- **Информативные сообщения** об ошибках
- **Чистый и профессиональный** интерфейс

### ✅ Надежность:
- **Исправлены критические баги**
- **Улучшена обработка ошибок**
- **Консистентное поведение** во всех редакторах
