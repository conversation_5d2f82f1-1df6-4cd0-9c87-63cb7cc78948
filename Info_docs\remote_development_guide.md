# Удаленная разработка с полным контролем серверов

## 🎯 **Цель**: Редактировать код прямо на серверах и видеть изменения в реальном времени

## 🛠 **Метод 1: VS Code Remote Development (ЛУЧШИЙ)**

### Установка расширений VS Code:
- **Remote - SSH**: Подключение к серверам по SSH
- **Remote - SSH: Editing Configuration Files**: Управление конфигами
- **Remote Development Pack**: Весь пакет для удаленной работы

### Настройка SSH подключений:

1. **Откройте VS Code → Command Palette (Ctrl+Shift+P)**
2. **Выберите: "Remote-SSH: Open Configuration File"**
3. **Добавьте конфигурацию серверов:**

```ssh
# Сервер с Backend
Host litportal-backend
    HostName *************  # IP вашего backend сервера
    User your_username
    Port 22
    IdentityFile ~/.ssh/id_rsa
    ForwardAgent yes

# Сервер с Frontend  
Host litportal-frontend
    HostName *************  # IP вашего frontend сервера
    User your_username
    Port 22
    IdentityFile ~/.ssh/id_rsa
    ForwardAgent yes

# Сервер с базой данных (если нужен доступ)
Host litportal-db
    HostName *************
    User your_username
    Port 22
    IdentityFile ~/.ssh/id_rsa
```

### Подключение к серверам:

1. **Ctrl+Shift+P → "Remote-SSH: Connect to Host"**
2. **Выберите сервер (litportal-backend или litportal-frontend)**
3. **VS Code откроет новое окно, подключенное к серверу**
4. **Откройте папку с проектом: File → Open Folder**

### Работа с кодом:
- **Редактируйте файлы прямо на сервере**
- **Изменения сохраняются автоматически**
- **Можете использовать терминал сервера**
- **Отладка работает как локально**

## 🚀 **Метод 2: Настройка автоперезагрузки на серверах**

### Backend сервер (Django):

```bash
# На backend сервере
cd /path/to/your/backend

# Установите watchdog для автоперезагрузки
pip install watchdog

# Создайте скрипт автозапуска
nano restart_django.py
```

```python
#!/usr/bin/env python3
import os
import signal
import subprocess
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class DjangoRestartHandler(FileSystemEventHandler):
    def __init__(self):
        self.process = None
        self.start_django()
    
    def start_django(self):
        if self.process:
            self.process.terminate()
            self.process.wait()
        
        print("🚀 Запускаю Django...")
        self.process = subprocess.Popen([
            'python', 'manage.py', 'runserver', '0.0.0.0:8000'
        ])
    
    def on_modified(self, event):
        if event.src_path.endswith('.py'):
            print(f"📝 Изменен файл: {event.src_path}")
            self.start_django()

if __name__ == "__main__":
    event_handler = DjangoRestartHandler()
    observer = Observer()
    observer.schedule(event_handler, '.', recursive=True)
    observer.start()
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
        if event_handler.process:
            event_handler.process.terminate()
    observer.join()
```

### Frontend сервер (React/Vite):

```bash
# На frontend сервере
cd /path/to/your/frontend

# Установите PM2 для управления процессами
npm install -g pm2

# Создайте конфиг PM2
nano ecosystem.config.js
```

```javascript
module.exports = {
  apps: [{
    name: 'litportal-frontend',
    script: 'npm',
    args: 'run dev',
    cwd: '/path/to/your/frontend',
    instances: 1,
    watch: true,
    watch_delay: 1000,
    ignore_watch: ['node_modules', 'dist', '.git'],
    env: {
      NODE_ENV: 'development',
      HOST: '0.0.0.0',
      PORT: 5173
    }
  }]
}
```

```bash
# Запуск с автоперезагрузкой
pm2 start ecosystem.config.js
pm2 logs  # Просмотр логов
pm2 restart litportal-frontend  # Перезапуск
```

## 🔧 **Метод 3: Docker с hot-reload**

### Создайте Docker конфигурацию на каждом сервере:

**Backend сервер - docker-compose.dev.yml:**
```yaml
version: '3.8'
services:
  django:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/app  # Монтируем код для hot-reload
    command: python manage.py runserver 0.0.0.0:8000
    environment:
      - DEBUG=True
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
```

**Frontend сервер - docker-compose.dev.yml:**
```yaml
version: '3.8'
services:
  vite:
    build: .
    ports:
      - "5173:5173"
    volumes:
      - .:/app
      - /app/node_modules  # Исключаем node_modules
    command: npm run dev -- --host 0.0.0.0
    environment:
      - NODE_ENV=development
    restart: unless-stopped
```

## 📁 **Метод 4: Синхронизация файлов (rsync/scp)**

### Автоматическая синхронизация при изменениях:

```bash
# Локально - скрипт для синхронизации
nano sync_to_servers.sh
```

```bash
#!/bin/bash

# Функция синхронизации backend
sync_backend() {
    echo "🔄 Синхронизирую backend..."
    rsync -avz --delete \
        --exclude 'venv/' \
        --exclude '__pycache__/' \
        --exclude '*.pyc' \
        ./backend/ user@backend-server:/path/to/backend/
    
    # Перезапуск Django на сервере
    ssh user@backend-server "sudo systemctl restart litportal-django"
}

# Функция синхронизации frontend
sync_frontend() {
    echo "🔄 Синхронизирую frontend..."
    rsync -avz --delete \
        --exclude 'node_modules/' \
        --exclude 'dist/' \
        ./frontend/ user@frontend-server:/path/to/frontend/
    
    # Перезапуск фронтенда на сервере
    ssh user@frontend-server "cd /path/to/frontend && npm run build"
}

# Отслеживание изменений файлов
inotifywait -m -r -e modify,create,delete ./backend ./frontend |
while read path action file; do
    if [[ $path == *"backend"* ]]; then
        sync_backend
    elif [[ $path == *"frontend"* ]]; then
        sync_frontend
    fi
done
```

## 🌐 **Метод 5: Веб-IDE прямо на сервере**

### Установка Code-Server на сервере:

```bash
# На любом из серверов
curl -fsSL https://code-server.dev/install.sh | sh

# Настройка
mkdir -p ~/.config/code-server
cat > ~/.config/code-server/config.yaml << EOF
bind-addr: 0.0.0.0:8080
auth: password
password: your_secure_password
cert: false
EOF

# Запуск
code-server --bind-addr 0.0.0.0:8080
```

Теперь можете редактировать код через браузер: `http://server-ip:8080`

## 🔒 **Безопасность удаленной разработки**

### 1. SSH ключи вместо паролей:
```bash
# Генерация ключа
ssh-keygen -t rsa -b 4096

# Копирование на все серверы
ssh-copy-id user@backend-server
ssh-copy-id user@frontend-server
```

### 2. VPN для безопасного доступа:
```bash
# Или используйте SSH туннели
ssh -L 8000:localhost:8000 user@backend-server
ssh -L 5173:localhost:5173 user@frontend-server
```

### 3. Firewall правила:
```bash
# На серверах - разрешить доступ только с ваших IP
ufw allow from YOUR_IP to any port 22
ufw allow from YOUR_IP to any port 8000
ufw allow from YOUR_IP to any port 5173
```

## ⚡ **Рекомендуемый workflow**

### Вариант А: VS Code Remote (Самый удобный)
1. **Подключаетесь к backend серверу через VS Code**
2. **Редактируете Python код**
3. **Django автоматически перезагружается**
4. **В другом окне VS Code подключаетесь к frontend серверу**
5. **Редактируете React код**
6. **Vite автоматически обновляет страницу**

### Вариант Б: Локальное редактирование + автосинхронизация
1. **Редактируете код локально**
2. **Скрипт автоматически загружает на серверы**
3. **Серверы автоматически перезапускаются**

### Вариант В: Веб-IDE
1. **Открываете code-server в браузере**
2. **Редактируете код прямо в браузере**
3. **Все работает как в обычном VS Code**

## 🛠 **Мониторинг и логи**

### Просмотр логов с всех серверов:
```bash
# Мультиплексор для одновременного просмотра
tmux new-session -d -s logs
tmux split-window -h
tmux send-keys -t 0 'ssh backend-server "tail -f /var/log/django.log"' Enter
tmux send-keys -t 1 'ssh frontend-server "tail -f /var/log/vite.log"' Enter
tmux attach-session -t logs
```

### PM2 мониторинг:
```bash
# На серверах
pm2 monit  # Веб-интерфейс мониторинга
```

## 📋 **Быстрый старт для вашего случая**

1. **Установите VS Code Remote-SSH расширение**
2. **Настройте SSH доступ к серверам**
3. **Подключитесь к backend серверу**
4. **Откройте проект и начните редактировать**
5. **В новом окне подключитесь к frontend серверу**
6. **Работайте как с локальными файлами!**

Это даст вам полный контроль над серверами с комфортом локальной разработки! 