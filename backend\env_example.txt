# Настройки базы данных - готовы к масштабированию!
DB_NAME=litportal
DB_USER=root
DB_PASSWORD=your_password_here
DB_HOST=localhost
DB_PORT=3307

# Redis - готов к выносу на отдельный сервер
REDIS_URL=redis://localhost:6379/0

# Настройки для production
DEBUG=True
SECRET_KEY=your-secret-key-here

# Yandex Object Storage
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_STORAGE_BUCKET_NAME=your_bucket_name
AWS_S3_ENDPOINT_URL=https://storage.yandexcloud.net
AWS_S3_REGION_NAME=ru-central1
AWS_S3_PUBLIC_MEDIA_PREFIX=media
AWS_S3_PRIVATE_MEDIA_PREFIX=private
AWS_S3_DIST_PREFIX=dist

# OAuth настройки для авторизации через соц.сети
# VK ID (из настроек вашего VK приложения)
VK_CLIENT_ID=your_vk_app_id
VK_SECRET_KEY=your_vk_protected_key

# Yandex ID
YANDEX_CLIENT_ID=your_yandex_client_id
YANDEX_CLIENT_SECRET=your_yandex_client_secret

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Одноклассники
OK_APP_ID=your_ok_app_id
OK_SECRET_KEY=your_ok_secret_key
OK_PUBLIC_KEY=your_ok_public_key

# Для будущего масштабирования (когда понадобится)
# DB_HOST=*************  # IP сервера с БД
# REDIS_URL=redis://*************:6379/0  # IP сервера с Redis 