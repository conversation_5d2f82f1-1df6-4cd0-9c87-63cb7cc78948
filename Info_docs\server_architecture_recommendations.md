# Архитектура серверов для LitPortal: анализ и рекомендации

## 🔍 **Анализ текущего проекта**

### Текущая архитектура:
- **Django Backend** с DRF API
- **React Frontend** (Vite)
- **MySQL Database**
- **Redis** для Celery
- **Yandex Object Storage** для медиа
- **WebSocket** для real-time

### Сложность проекта:
- ✅ Пользовательская система (авторы, читатели)
- ✅ Книги с главами и комментариями
- ✅ Real-time уведомления
- ✅ Загрузка файлов
- ✅ Async задачи (Celery)

## 🎯 **Рекомендация: НЕ разделяйте на 3 сервера сразу!**

### **Почему стоит начать с одного сервера:**

#### 💰 **Экономические причины:**
- Стоимость 3х VDS vs 1 мощный = ~2-3x дороже
- Дополнительные расходы на трафик между серверами
- Сложность администрирования = больше времени = деньги

#### 🛠 **Технические причины:**
- **Латентность**: БД на отдельном сервере = +2-5ms к каждому запросу
- **Сложность деплоя**: 3 сервера = 3x больше точек отказа  
- **Отладка**: Логи разбросаны по 3м серверам
- **Мониторинг**: Нужен сложный мониторинг сети

#### 📊 **Масштабирование реально нужно когда:**
- **>1000** активных пользователей одновременно
- **>100GB** базы данных  
- **>500** запросов в секунду
- **>10GB** RAM использует Django

## 🚀 **Рекомендуемая стратегия развития**

### **Этап 1: Один мощный сервер (0-1000 пользователей)**

```yaml
Конфигурация VDS:
  CPU: 4-6 cores
  RAM: 16-32GB  
  SSD: 200-500GB
  Сеть: 1Gbps
  
Примерная стоимость: 3000-5000₽/месяц
```

**Архитектура на одном сервере:**
```
┌─────────────────────────────────────────┐
│              VDS Server                 │
├─────────────────────────────────────────┤
│ nginx (реверс-прокси + статика)         │
├─────────────────────────────────────────┤
│ Django (backend API)                    │
├─────────────────────────────────────────┤
│ React (frontend, собранный)             │
├─────────────────────────────────────────┤
│ MySQL (база данных)                     │
├─────────────────────────────────────────┤
│ Redis (кеш + Celery)                    │
├─────────────────────────────────────────┤
│ Celery Workers                          │
└─────────────────────────────────────────┘
```

### **Этап 2: Вынос БД (1000-5000 пользователей)**

```yaml
Frontend + Backend Server:
  CPU: 4-6 cores
  RAM: 16GB
  SSD: 100GB
  
Database Server:
  CPU: 2-4 cores  
  RAM: 16-32GB
  SSD: 500GB-1TB (быстрый для БД)

Общая стоимость: 5000-8000₽/месяц
```

### **Этап 3: Полное разделение (5000+ пользователей)**

```yaml
Frontend Server:
  CPU: 2-4 cores
  RAM: 8GB
  SSD: 100GB
  
Backend Server:
  CPU: 4-8 cores
  RAM: 16-32GB  
  SSD: 200GB
  
Database Server:
  CPU: 4-8 cores
  RAM: 32-64GB
  SSD: 1TB+

Общая стоимость: 8000-15000₽/месяц
```

## 🔧 **Конфигурации для разных нагрузок**

### **🌱 Стартовая конфигурация (до 100 пользователей онлайн)**

**Один сервер VDS:**
```yaml
Характеристики:
  CPU: 2-4 cores (Intel/AMD)
  RAM: 8-16GB
  SSD: 100-200GB
  Bandwidth: 1Gbps
  
Провайдеры (ориентировочно):
  - VDS64: ~2000₽/месяц
  - TimeWeb: ~2500₽/месяц  
  - Selectel: ~3000₽/месяц
```

**Установка всех сервисов:**
```bash
# Основные компоненты
sudo apt update && sudo apt install -y \
    nginx mysql-server redis-server \
    python3.11 python3.11-venv \
    nodejs npm certbot

# Python зависимости
pip3 install -r requirements.txt

# Node.js зависимости 
npm install && npm run build
```

### **🚀 Производственная конфигурация (100-1000 пользователей)**

**Один мощный сервер:**
```yaml
Характеристики:
  CPU: 6-8 cores
  RAM: 32GB
  SSD: 500GB NVMe
  Bandwidth: 1-10Gbps
  
Ориентировочная стоимость: 4000-6000₽/месяц
```

**Оптимизированная настройка:**
```nginx
# nginx.conf
worker_processes auto;
worker_connections 1024;

upstream django {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    client_max_body_size 100M;
    
    # Frontend
    location / {
        root /var/www/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API  
    location /api/ {
        proxy_pass http://django;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # Static/Media
    location /static/ {
        alias /var/www/backend/staticfiles/;
        expires 1y;
    }
}
```

### **⚡ Масштабируемая конфигурация (1000+ пользователей)**

**Вариант А: 2 сервера (Frontend+Backend + Database)**
```yaml
App Server:
  CPU: 6-8 cores
  RAM: 32GB
  SSD: 300GB
  Роль: nginx + Django + Redis + Frontend
  
DB Server:  
  CPU: 4-8 cores
  RAM: 32-64GB
  SSD: 1TB NVMe (высокий IOPS)
  Роль: только MySQL

Общая стоимость: 6000-10000₽/месяц
```

**Вариант Б: 3 сервера (полное разделение)**
```yaml
Frontend Server:
  CPU: 2-4 cores
  RAM: 8GB
  SSD: 100GB
  Роль: nginx + React (статика)
  
Backend Server:
  CPU: 6-8 cores  
  RAM: 24GB
  SSD: 200GB
  Роль: Django + Redis + Celery
  
Database Server:
  CPU: 4-8 cores
  RAM: 32GB+
  SSD: 1TB+ NVMe
  Роль: только MySQL

Общая стоимость: 8000-12000₽/месяц
```

## 📊 **Когда переходить на следующий этап**

### **Сигналы для масштабирования:**

#### 🔄 **Перейти с 1 на 2 сервера когда:**
- CPU usage >80% стабильно
- MySQL queries >200/sec
- RAM usage >90%
- Response time >500ms
- Concurrent users >500

#### 🔄 **Перейти на 3 сервера когда:**
- Database queries >1000/sec  
- Network bandwidth >500Mbps
- Concurrent users >2000
- Нужна geographical distribution

### **Мониторинг для принятия решений:**
```bash
# CPU и память
htop, free -h

# База данных
SHOW GLOBAL STATUS LIKE 'Questions';
SHOW GLOBAL STATUS LIKE 'Threads_connected';

# Django
# Установите django-silk для профилирования

# Nginx
tail -f /var/log/nginx/access.log | wc -l
```

## 🛠 **Практическая реализация для вашего проекта**

### **Сейчас рекомендую: один сервер VDS**

**Конфигурация для старта:**
```yaml
VDS характеристики:
  CPU: 4 cores
  RAM: 16GB  
  SSD: 200GB
  Стоимость: ~3500₽/месяц

Этого хватит на:
  - 500+ одновременных пользователей
  - 50GB база данных
  - 100+ запросов/секунду
  - Все ваши текущие фичи
```

### **Настройка production-ready окружения:**

```bash
# 1. Системные пакеты
sudo apt update && sudo apt install -y \
    nginx mysql-server redis-server \
    python3.11 python3.11-venv nodejs npm \
    certbot python3-certbot-nginx

# 2. Настройка MySQL
sudo mysql_secure_installation
sudo mysql -e "CREATE DATABASE litportal;"
sudo mysql -e "CREATE USER 'litportal'@'localhost' IDENTIFIED BY 'strong_password';"
sudo mysql -e "GRANT ALL ON litportal.* TO 'litportal'@'localhost';"

# 3. Python окружение
python3.11 -m venv /var/www/backend/venv
source /var/www/backend/venv/bin/activate
pip install -r requirements.txt

# 4. Frontend сборка
cd /var/www/frontend
npm install
npm run build

# 5. Django настройка
cd /var/www/backend
python manage.py migrate
python manage.py collectstatic --noinput
python manage.py createsuperuser

# 6. Systemd сервисы
sudo systemctl enable nginx mysql redis-server
sudo systemctl start nginx mysql redis-server
```

### **Файлы конфигурации:**

**systemd service для Django:**
```ini
# /etc/systemd/system/litportal-django.service
[Unit]
Description=LitPortal Django
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/var/www/backend
Environment=PATH=/var/www/backend/venv/bin
ExecStart=/var/www/backend/venv/bin/gunicorn config.wsgi:application --bind 127.0.0.1:8000 --workers 3
Restart=always

[Install]
WantedBy=multi-user.target
```

**systemd service для Celery:**
```ini
# /etc/systemd/system/litportal-celery.service
[Unit]
Description=LitPortal Celery  
After=network.target redis.service

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/var/www/backend
Environment=PATH=/var/www/backend/venv/bin
ExecStart=/var/www/backend/venv/bin/celery -A config worker -l info
Restart=always

[Install]
WantedBy=multi-user.target
```

## 📈 **План масштабирования**

### **Этап 1 (сейчас):** 
- ✅ Один VDS сервер
- ✅ Все компоненты на одной машине
- ✅ SSL сертификаты (Let's Encrypt)
- ✅ Мониторинг и резервное копирование

### **Этап 2 (через 6-12 месяцев):**
- 🔄 Вынос БД на отдельный сервер
- 🔄 Настройка репликации MySQL
- 🔄 Load balancer (nginx)

### **Этап 3 (через 12-24 месяца):**
- 🔄 Отдельный frontend сервер
- 🔄 CDN для статики
- 🔄 Кеширование (Memcached/Redis Cluster)

### **Этап 4 (через 24+ месяца):**
- 🔄 Микросервисы (если нужно)
- 🔄 Kubernetes/Docker Swarm
- 🔄 Multi-region deployment

## 💡 **Итоговая рекомендация**

**Начните с одного мощного VDS сервера на 16GB RAM!**

**Плюсы такого подхода:**
- ✅ Минимальная сложность
- ✅ Быстрый деплой и отладка  
- ✅ Экономия денег на старте
- ✅ Простое администрирование
- ✅ Легко масштабировать потом

**Переходите на разделение только когда:**
- Появятся реальные проблемы с производительностью
- Будет стабильная пользовательская база
- Будет понимание узких мест в системе

Ваш проект легко выдержит первые 1000+ пользователей на одном сервере! 