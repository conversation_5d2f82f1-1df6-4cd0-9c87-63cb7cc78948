# Локальная разработка с подключением к серверу

## 🎯 **Основные сценарии**

### Сценарий 1: Frontend локально → Backend на сервере
**Плюсы:** Быстрая разработка фронтенда, актуальные данные
**Минусы:** Зависимость от интернета, сложность отладки backend

### Сценарий 2: В<PERSON>е локально → Database на сервере  
**Плюсы:** Полный контроль, актуальные данные, быстрая отладка
**Минусы:** Нужен доступ к БД сервера

### Сценарий 3: Гибридный подход
**Плюсы:** Максимальная гибкость
**Минусы:** Сложная настройка

## 🛠 **Настройка Frontend → Server Backend**

### 1. Обновите настройки CORS на сервере

В `backend/config/settings.py` на сервере:
```python
# Добавьте ваш локальный адрес
CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://*************:5173",  # Ваш локальный IP
    # Добавьте другие нужные домены
]

CORS_ALLOW_CREDENTIALS = True
```

### 2. Настройте frontend для работы с сервером

В `frontend/src/api.ts` (или где у вас API настройки):
```javascript
// Для разработки
const API_BASE_URL = process.env.NODE_ENV === 'development' 
    ? 'https://your-server.com/api'  // Сервер
    : '/api';  // Локальный

// Или через .env файл
const API_BASE_URL = import.meta.env.VITE_API_URL || '/api';
```

Создайте `frontend/.env.development`:
```bash
VITE_API_URL=https://your-server.com/api
```

### 3. Обновите все API вызовы

```javascript
// Было
fetch('/api/users/profile/')

// Стало  
fetch(`${API_BASE_URL}/users/profile/`)
```

## 🗄 **Настройка Local Backend → Remote Database**

### 1. Настройте доступ к удаленной БД

В `backend/.env.development`:
```bash
# Удаленная база данных
DB_NAME=production_db_name
DB_USER=your_db_user  
DB_PASSWORD=your_db_password
DB_HOST=your-server.com  # IP или домен сервера
DB_PORT=3306

# Или для PostgreSQL
DATABASE_URL=**************************************/dbname
```

### 2. Обновите settings.py для разных окружений

```python
import os
from pathlib import Path

# Определяем окружение
ENVIRONMENT = os.environ.get('DJANGO_ENV', 'development')

if ENVIRONMENT == 'development':
    # Локальная разработка с удаленной БД
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': os.environ.get('DB_NAME'),
            'USER': os.environ.get('DB_USER'),
            'PASSWORD': os.environ.get('DB_PASSWORD'),
            'HOST': os.environ.get('DB_HOST'),  # Удаленный сервер
            'PORT': os.environ.get('DB_PORT', '3306'),
            'OPTIONS': {
                'charset': 'utf8mb4',
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            },
        }
    }
    
    # Отладка для разработки
    DEBUG = True
    
    # Разрешенные хосты для локальной разработки
    ALLOWED_HOSTS = ['localhost', '127.0.0.1', '*************']
    
else:
    # Продакшн настройки
    DEBUG = False
    # ... продакшн конфигурация
```

### 3. Настройте SSH туннель для безопасного доступа к БД

```bash
# Создание SSH туннеля к БД
ssh -L 3306:localhost:3306 <EMAIL>

# Теперь можно подключаться к localhost:3306
```

В `.env`:
```bash
DB_HOST=localhost  # Через SSH туннель
DB_PORT=3306
```

## 🔐 **Безопасность и доступы**

### 1. Настройте VPN или SSH-ключи
```bash
# Генерация SSH ключа
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Копирование ключа на сервер
ssh-copy-id <EMAIL>
```

### 2. Ограничьте доступ к БД по IP
В настройках MySQL/PostgreSQL на сервере:
```sql
-- Разрешить доступ только с вашего IP
GRANT ALL PRIVILEGES ON database_name.* TO 'user'@'your-ip-address';
```

## 🚀 **Автоматизация разработки**

### 1. Создайте скрипты для быстрого переключения

`scripts/dev-local.sh`:
```bash
#!/bin/bash
export DJANGO_ENV=local
export DATABASE_URL=sqlite:///local.db
python manage.py runserver
```

`scripts/dev-remote.sh`:
```bash
#!/bin/bash
export DJANGO_ENV=development  
export DB_HOST=your-server.com
python manage.py runserver
```

### 2. Docker Compose для гибридной разработки

`docker-compose.dev.yml`:
```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=https://your-server.com/api
    volumes:
      - ./frontend:/app
  
  # Локальные сервисы при необходимости
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
```

## 📋 **Практические команды**

### Синхронизация медиа файлов с сервера
```bash
# Скачать медиа файлы с сервера
rsync -avz user@server:/path/to/media/ ./backend/media/

# Загрузить изменения на сервер
rsync -avz ./backend/media/ user@server:/path/to/media/
```

### Дамп и восстановление БД
```bash
# Создать дамп БД с сервера
ssh user@server "mysqldump dbname" > local_dump.sql

# Восстановить в локальную БД
mysql local_db < local_dump.sql
```

### Синхронизация статики
```bash
# Собрать статику локально для сервера
python manage.py collectstatic

# Загрузить на сервер
rsync -avz ./backend/staticfiles/ user@server:/path/to/static/
```

## ⚡ **Рекомендуемый workflow**

1. **Разработка фронтенда**: Используйте сервер API
2. **Разработка бэкенда**: Используйте локальную копию БД
3. **Тестирование интеграции**: Гибридный подход
4. **Деплой**: Автоматический через CI/CD

## 🔧 **Инструменты для комфортной работы**

- **ngrok**: Для тестирования webhook'ов
- **Postman/Insomnia**: Для тестирования API
- **DBeaver**: Для работы с удаленной БД
- **SSH туннели**: Для безопасного доступа
- **Docker**: Для изоляции сервисов

## 📝 **Что лучше выбрать?**

**Для большинства случаев рекомендую:**
- Frontend локально → Backend на сервере
- Периодическая синхронизация БД для актуальных данных
- Использование SSH туннелей для безопасности 