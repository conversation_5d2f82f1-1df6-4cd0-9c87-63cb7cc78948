from django.db.models.signals import post_save, post_delete, m2m_changed
from django.dispatch import receiver
from django.conf import settings
import logging

from .models import Message, FeedEvent, FriendRequest, User, Subscription, UserStats
from .services import NotificationService
from .rating_service import RatingEventHandlers
from .tasks import update_user_metric

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Message)
def update_unread_messages_on_message_save(sender, instance, created, **kwargs):
    """Обновить уведомления при создании/изменении сообщения"""
    if created:
        logger.info(f"New message created: {instance.id} from {instance.sender.username} to {instance.recipient.username}")
        # Обновляем уведомления для получателя
        NotificationService.update_unread_messages_count(instance.recipient)
    else:
        # Если сообщение было помечено как прочитанное
        if instance.is_read:
            logger.info(f"Message marked as read: {instance.id}")
            NotificationService.update_unread_messages_count(instance.recipient)

@receiver(post_save, sender=FeedEvent)
def update_feed_events_on_event_save(sender, instance, created, **kwargs):
    """Обновить уведомления при создании/изменении события ленты"""
    if created:
        logger.info(f"New feed event created: {instance.event_type} for user {instance.user.username}")
        NotificationService.update_feed_events_count(instance.user)
    elif instance.is_read:
        # Если событие было помечено как прочитанное
        NotificationService.update_feed_events_count(instance.user)

@receiver(post_save, sender=FriendRequest)
def update_friend_requests_on_request_save(sender, instance, created, **kwargs):
    """Обновить уведомления при создании/изменении запроса в друзья"""
    if created:
        logger.info(f"New friend request: {instance.from_user.username} -> {instance.to_user.username}")
        NotificationService.update_friend_requests_count(instance.to_user)
    else:
        # Если запрос был принят или отклонен
        if instance.accepted:
            logger.info(f"Friend request accepted: {instance.from_user.username} -> {instance.to_user.username}")
            NotificationService.update_friend_requests_count(instance.to_user)

@receiver(post_delete, sender=Message)
def update_unread_messages_on_message_delete(sender, instance, **kwargs):
    """Обновить уведомления при удалении сообщения"""
    logger.info(f"Message deleted: {instance.id}")
    NotificationService.update_unread_messages_count(instance.recipient)

@receiver(post_delete, sender=FeedEvent)
def update_feed_events_on_event_delete(sender, instance, **kwargs):
    """Обновить уведомления при удалении события ленты"""
    logger.info(f"Feed event deleted: {instance.id}")
    NotificationService.update_feed_events_count(instance.user)

@receiver(post_delete, sender=FriendRequest)
def update_friend_requests_on_request_delete(sender, instance, **kwargs):
    """Обновить уведомления при удалении запроса в друзья"""
    logger.info(f"Friend request deleted: {instance.from_user.username} -> {instance.to_user.username}")
    NotificationService.update_friend_requests_count(instance.to_user)

@receiver(post_save, sender=Subscription)
def handle_subscription_created(sender, instance, created, **kwargs):
    """Обработка создания подписки с новой логикой рейтингов."""
    if created:
        # Используем новые обработчики событий
        RatingEventHandlers.on_subscription_added(instance)

@receiver(post_delete, sender=Subscription)
def handle_subscription_deleted(sender, instance, **kwargs):
    """Обработка удаления подписки с новой логикой рейтингов."""
    # Используем новые обработчики событий
    RatingEventHandlers.on_subscription_removed(instance)

@receiver(post_save, sender=FriendRequest)
def handle_friend_request_updated(sender, instance, created, **kwargs):
    """Обработка создания/обновления заявки в друзья."""
    if not created and instance.accepted:
        # Заявка была принята
        if getattr(settings, 'USE_ASYNC_RATING_UPDATE', True):
            # Увеличиваем счетчик друзей у обоих пользователей
            update_user_metric.delay(
                user_id=instance.from_user.id,
                action_type='friend_added',
                metric_name='friends_count',
                change_delta=1,
                related_object_type='FriendRequest',
                related_object_id=instance.id
            )
            
            update_user_metric.delay(
                user_id=instance.to_user.id,
                action_type='friend_added',
                metric_name='friends_count',
                change_delta=1,
                related_object_type='FriendRequest',
                related_object_id=instance.id
            )

@receiver(post_delete, sender=FriendRequest)
def handle_friend_request_deleted(sender, instance, **kwargs):
    """Обработка удаления заявки в друзья."""
    if instance.accepted:
        # Удаляется принятая заявка (разрыв дружбы)
        if getattr(settings, 'USE_ASYNC_RATING_UPDATE', True):
            update_user_metric.delay(
                user_id=instance.from_user.id,
                action_type='friend_removed',
                metric_name='friends_count',
                change_delta=-1,
                related_object_type='FriendRequest',
                related_object_id=instance.id
            )
            
            update_user_metric.delay(
                user_id=instance.to_user.id,
                action_type='friend_removed',
                metric_name='friends_count',
                change_delta=-1,
                related_object_type='FriendRequest',
                related_object_id=instance.id
            )

@receiver(post_save, sender='users.Message')
def handle_message_created(sender, instance, created, **kwargs):
    """Обработка создания сообщения."""
    if created:
        if getattr(settings, 'USE_ASYNC_RATING_UPDATE', True):
            update_user_metric.delay(
                user_id=instance.sender.id,
                action_type='message_sent',
                metric_name='messages_sent_count',
                change_delta=1,
                related_object_type='Message',
                related_object_id=instance.id
            )

@receiver(post_save, sender=User)
def create_user_stats(sender, instance, created, **kwargs):
    """Создание статистики для нового пользователя с приветственным бонусом."""
    if created:
        from .rating_service import RatingService
        
        # Создаем статистику и выдаем приветственный бонус
        stats = RatingService.get_or_create_user_stats(instance)
        logger.info(f"Создана статистика для нового пользователя {instance.username}")

# @receiver(post_save, sender='books.BookChapter')
# def handle_chapter_created(sender, instance, created, **kwargs):
#     """Обработка создания/обновления главы."""
#     if created:
#         # Новая глава создана
#         if getattr(settings, 'USE_ASYNC_RATING_UPDATE', True):
#             update_user_metric.delay(
#                 user_id=instance.book.author.id,
#                 action_type='chapter_added',
#                 metric_name='total_chapters_count',
#                 change_delta=1,
#                 related_object_type='BookChapter',
#                 related_object_id=instance.id
#             )

# @receiver(post_delete, sender='books.BookChapter')
# def handle_chapter_deleted(sender, instance, **kwargs):
#     """Обработка удаления главы."""
#     try:
#         author_id = instance.book.author.id
#         if getattr(settings, 'USE_ASYNC_RATING_UPDATE', True):
#             update_user_metric.delay(
#                 user_id=author_id,
#                 action_type='chapter_removed',
#                 metric_name='total_chapters_count',
#                 change_delta=-1,
#                 related_object_type='BookChapter',
#                 related_object_id=None
#             )
#     except Exception as e:
#         logger.warning(f"Не удалось обработать удаление главы: {e}")


# === НОВЫЕ СИГНАЛЫ ДЛЯ ОБНОВЛЕННОЙ СИСТЕМЫ РЕЙТИНГОВ ===

@receiver(post_save, sender='books.Comment')
def handle_book_comment_created(sender, instance, created, **kwargs):
    """Обработка создания/изменения комментария к книге."""
    if created and hasattr(instance, 'book'):
        # Комментарий к книге
        RatingEventHandlers.on_comment_added(instance, 'book')

@receiver(post_delete, sender='books.Comment')
def handle_book_comment_deleted(sender, instance, **kwargs):
    """Обработка удаления комментария к книге."""
    if hasattr(instance, 'book'):
        RatingEventHandlers.on_comment_removed(instance, 'book')

@receiver(post_save, sender='books.Like')
def handle_book_like_created(sender, instance, created, **kwargs):
    """Обработка создания лайка к книге."""
    if created:
        if hasattr(instance, 'book'):
            RatingEventHandlers.on_like_added(instance, 'book')
        elif hasattr(instance, 'comment'):
            RatingEventHandlers.on_like_added(instance, 'comment')

@receiver(post_delete, sender='books.Like')
def handle_book_like_deleted(sender, instance, **kwargs):
    """Обработка удаления лайка."""
    if hasattr(instance, 'book'):
        RatingEventHandlers.on_like_removed(instance, 'book')
    elif hasattr(instance, 'comment'):
        RatingEventHandlers.on_like_removed(instance, 'comment')

# Сигналы для блога (когда будет реализован)
# @receiver(post_save, sender='blog.BlogComment')
# def handle_blog_comment_created(sender, instance, created, **kwargs):
#     """Обработка создания комментария к блогу."""
#     if created:
#         RatingEventHandlers.on_comment_added(instance, 'blog')

# @receiver(post_save, sender='blog.BlogPost')
# def handle_blog_post_created(sender, instance, created, **kwargs):
#     """Обработка создания поста в блоге."""
#     if created:
#         RatingEventHandlers.on_blog_post_published(instance)

# Сигналы для покупок и наград (когда будут реализованы)
# @receiver(post_save, sender='payments.BookPurchase')
# def handle_book_purchase(sender, instance, created, **kwargs):
#     """Обработка покупки книги."""
#     if created:
#         RatingEventHandlers.on_book_purchased(instance)

# @receiver(post_save, sender='payments.Award')
# def handle_award_given(sender, instance, created, **kwargs):
#     """Обработка выдачи награды."""
#     if created:
#         RatingEventHandlers.on_award_given(instance) 
