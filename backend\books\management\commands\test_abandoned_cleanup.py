from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from books.models import Book
from books.tasks import cleanup_abandoned_books


class Command(BaseCommand):
    help = 'Test abandoned books cleanup'

    def add_arguments(self, parser):
        parser.add_argument(
            '--list-only',
            action='store_true',
            help='Only list abandoned books, do not clean',
        )
        parser.add_argument(
            '--sync',
            action='store_true',
            help='Run synchronously (for testing)',
        )

    def handle(self, *args, **options):
        self.stdout.write("=== Abandoned Books Cleanup Test ===")
        
        # Находим заброшенные книги
        cutoff_date = timezone.now() - timedelta(days=2)
        abandoned_books = Book.objects.filter(
            creation_status='creating',
            created_at__lt=cutoff_date
        )
        
        self.stdout.write(f"Cutoff date: {cutoff_date}")
        self.stdout.write(f"Found {abandoned_books.count()} abandoned books")
        
        if abandoned_books.exists():
            self.stdout.write("\n=== Abandoned Books List ===")
            for book in abandoned_books:
                self.stdout.write(
                    f"Book {book.id}: created {book.created_at}, "
                    f"status: {book.creation_status}, "
                    f"title: '{book.title or 'No title'}'"
                )
                
                # Показываем обложки
                covers = []
                if book.cover:
                    covers.append(f"cover: {book.cover.name}")
                if book.cover_mini:
                    covers.append(f"cover_mini: {book.cover_mini.name}")
                if book.cover_editor:
                    covers.append(f"cover_editor: {book.cover_editor.name}")
                
                if covers:
                    self.stdout.write(f"  Covers: {', '.join(covers)}")
                else:
                    self.stdout.write(f"  No covers")
        else:
            self.stdout.write("No abandoned books found")
        
        if not options['list_only'] and abandoned_books.exists():
            self.stdout.write(f"\n=== Starting Cleanup ===")
            if options['sync']:
                # Выполняем синхронно
                cleanup_abandoned_books()
                self.stdout.write(
                    self.style.SUCCESS("Abandoned books cleanup completed")
                )
            else:
                # Выполняем асинхронно
                cleanup_abandoned_books.delay()
                self.stdout.write(
                    self.style.SUCCESS("Abandoned books cleanup task scheduled")
                )
        elif options['list_only']:
            self.stdout.write("\n--list-only specified, skipping cleanup")
        else:
            self.stdout.write("\nNo abandoned books to clean")
