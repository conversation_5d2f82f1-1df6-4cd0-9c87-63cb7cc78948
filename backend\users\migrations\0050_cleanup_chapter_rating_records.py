# Data migration to clean up old chapter-related rating records

from django.db import migrations


def cleanup_chapter_records(apps, schema_editor):
    """Удаляем все записи рейтинга, связанные с главами."""
    RatingHistory = apps.get_model('users', 'RatingHistory')
    UserMetricHistory = apps.get_model('users', 'UserMetricHistory')
    RatingCalculationRule = apps.get_model('users', 'RatingCalculationRule')

    # Удаляем записи с action_type, связанными с главами
    chapter_action_types = [
        'chapter_added',
        'chapter_removed',
        'chapter_published',
        'chapter_unpublished'
    ]

    # Подсчитываем количество записей для логирования
    rating_count = RatingHistory.objects.filter(action_type__in=chapter_action_types).count()
    metric_count = UserMetricHistory.objects.filter(action_type__in=chapter_action_types).count()
    rules_count = RatingCalculationRule.objects.filter(metric_name='total_chapters_count').count()

    # Удаляем записи
    RatingHistory.objects.filter(action_type__in=chapter_action_types).delete()
    UserMetricHistory.objects.filter(action_type__in=chapter_action_types).delete()
    RatingCalculationRule.objects.filter(metric_name='total_chapters_count').delete()

    print(f"Удалено {rating_count} записей из RatingHistory")
    print(f"Удалено {metric_count} записей из UserMetricHistory")
    print(f"Удалено {rules_count} правил расчета для total_chapters_count")


def reverse_cleanup(apps, schema_editor):
    """Обратная операция - ничего не делаем, так как данные уже удалены."""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0049_remove_userstats_total_chapters_count_and_more'),
    ]

    operations = [
        migrations.RunPython(cleanup_chapter_records, reverse_cleanup),
    ]
