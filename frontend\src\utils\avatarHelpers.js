import { getCachedUserAvatar } from './avatarCache';

/**
 * Безопасно рендерит аватар пользователя с fallback на иконку
 * @param {Object} user - Объект пользователя
 * @param {string} size - Размер аватара ('mini' или 'full')
 * @param {string} backendUrl - URL бэкенда
 * @param {number} imageVersion - Версия изображения
 * @param {JSX.Element} fallbackIcon - Иконка fallback
 * @param {string} className - CSS классы для изображения
 * @param {Object} style - Inline стили
 * @param {Function} onError - Обработчик ошибки загрузки
 * @returns {JSX.Element} JSX элемент с аватаром или fallback
 */
export const SafeAvatar = ({ 
  user, 
  size = 'mini', 
  backendUrl = '', 
  imageVersion, 
  fallbackIcon, 
  className = '', 
  style = {},
  onError,
  ...props 
}) => {
  const avatarUrl = getCachedUserAvatar(user, size, backendUrl, imageVersion);
  
  if (!avatarUrl) {
    return fallbackIcon || <div className={`bg-gray-300 ${className}`} style={style}></div>;
  }
  
  return (
    <img
      src={avatarUrl}
      alt="avatar"
      className={className}
      style={style}
      onError={onError || ((e) => { e.target.style.display = 'none'; })}
      {...props}
    />
  );
};

/**
 * Получает URL аватара безопасно (не возвращает null)
 * @param {Object} user - Объект пользователя
 * @param {string} size - Размер аватара
 * @param {string} backendUrl - URL бэкенда
 * @param {number} imageVersion - Версия изображения
 * @param {string} fallbackUrl - URL fallback изображения
 * @returns {string} URL аватара или fallback
 */
export const getSafeAvatarUrl = (user, size = 'mini', backendUrl = '', imageVersion, fallbackUrl = '') => {
  const avatarUrl = getCachedUserAvatar(user, size, backendUrl, imageVersion);
  return avatarUrl || fallbackUrl;
}; 