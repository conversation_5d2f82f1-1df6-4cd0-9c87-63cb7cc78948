# Система очистки обложек книг

## Описание

Система автоматической очистки неиспользуемых обложек книг в S3 хранилище Yandex Cloud.

## Компоненты

### 1. Автоматическая очистка после сохранения обложки

**Задача:** `cleanup_book_covers`
**Триггер:** Автоматически через 5 секунд после сохранения обложки
**Действие:** Удаляет старые неиспользуемые файлы обложек, оставляя только:
- `cover` - основная обложка с лейблами
- `cover_mini` - миниатюра с лейблами  
- `cover_editor` - обложка без лейблов для редактора

### 2. Периодическая очистка заброшенных книг

**Задача:** `cleanup_abandoned_books`
**Расписание:** Каждый понедельник в 3:00 UTC
**Критерии:** Книги в статусе `creating` старше 2 дней
**Действие:** Удаляет все обложки из папок заброшенных книг

## Структура файлов

```
s3://bucket/covers/books/{book_id}/
├── {book_id}_cover.webp          # Основная обложка с лейблами
├── {book_id}_cover_mini.jpg      # Миниатюра с лейблами
├── {book_id}_cover_editor.webp   # Обложка без лейблов для редактора
└── [старые файлы]                # Удаляются автоматически
```

## Ручное управление

### Очистка конкретной книги
```bash
# Асинхронно (через Celery)
python manage.py cleanup_covers --book-id 123

# Синхронно (для тестирования)
python manage.py cleanup_covers --book-id 123 --sync
```

### Очистка заброшенных книг
```bash
# Асинхронно (через Celery)
python manage.py cleanup_covers --abandoned

# Синхронно (для тестирования)
python manage.py cleanup_covers --abandoned --sync
```

### Тестирование заброшенных книг
```bash
# Только показать список заброшенных книг
python manage.py test_abandoned_cleanup --list-only

# Выполнить очистку синхронно
python manage.py test_abandoned_cleanup --sync
```

## Логирование

Все операции логируются с уровнем INFO:
- Начало и завершение очистки
- Количество удаленных файлов
- Ошибки при работе с S3

## Настройки

### Celery Beat (периодические задачи)
```python
# config/celery.py
app.conf.beat_schedule = {
    'cleanup-abandoned-books': {
        'task': 'books.tasks.cleanup_abandoned_books',
        'schedule': crontab(hour=3, minute=0, day_of_week=1),
    },
}
```

### Переменные окружения
```
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_STORAGE_BUCKET_NAME=your_bucket_name
```

## Безопасность

- Задачи имеют retry механизм при ошибках
- Проверка существования книги перед очисткой
- Логирование всех операций удаления
- Graceful handling ошибок S3

## Запуск Celery

### Windows
```bash
# Запуск (откроет два окна)
start_celery.bat

# Остановка (закрыть окна Celery Worker и Celery Beat)
```

### Linux/Mac
```bash
# Запуск
chmod +x start_celery.sh stop_celery.sh
./start_celery.sh

# Остановка
./stop_celery.sh
```

### Ручной запуск
```bash
# Worker (в одном терминале)
celery -A config worker --loglevel=info

# Beat (в другом терминале)
celery -A config beat --loglevel=info
```

## Мониторинг

Рекомендуется мониторить:
- Логи Celery для ошибок задач
- Размер S3 bucket
- Количество обработанных книг в логах

### Проверка статуса
```bash
# Проверка заброшенных книг
python manage.py test_abandoned_cleanup --list-only

# Проверка путей конкретной книги
python manage.py debug_cover_paths 167

# Проверка S3 подключения
python manage.py test_s3_connection
```
