import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../theme/ThemeContext';
import { Form, Input, Button, Upload, message, Typography, Select, Spin, Radio } from 'antd';
import { UploadOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const Container = styled.div`
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
`;

const StyledForm = styled(Form)`
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const BOOK_TYPES = [
  { value: 'story', label: 'Рассказ', comment: 'Рассказ — короткое произведение, состоит из одной части.' },
  { value: 'novella', label: 'Повесть', comment: 'Повесть — среднее по объёму произведение, может содержать главы.' },
  { value: 'novel', label: 'Роман', comment: 'Роман — крупное произведение, обычно делится на главы.' },
  { value: 'story_collection', label: 'Сборник рассказов', comment: 'Сборник рассказов — несколько рассказов в одной книге.' },
  { value: 'poetry_collection', label: 'Сборник поэзии', comment: 'Сборник поэзии — сборник стихотворений.' },
];

function generateTempCover(title, type) {
  // Здесь можно реализовать генерацию canvas или SVG для предпросмотра
  // Пока просто возвращаем заглушку
  return `/static/cover_placeholder.jpg`;
}

function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

const CreateBook = () => {
  const { username } = useParams();
  const { user } = useAuth();
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [selectedType, setSelectedType] = useState('');
  const [form] = Form.useForm();

  const handleBack = () => {
    navigate(`/lpu/${username}/books`);
  };

  const handleFinish = async (values) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('title', values.title);
      formData.append('type', values.type);
      formData.append('is_published', false);
      const csrfToken = getCookie('csrftoken');
      const response = await fetch(`/api/books/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        body: formData,
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Ошибка при создании книги');
      const data = await response.json();
      message.success('Черновик книги создан!');
      navigate(`/lpu/${username}/books`);
    } catch (e) {
      message.error('Ошибка при создании книги');
    } finally {
      setLoading(false);
    }
  };

  if (!user || user.username !== username) {
    return (
      <Container>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title
          level={2}
          style={{ margin: 0, color: theme === 'dark' ? '#fff' : '#222', transition: 'color 0.2s' }}
        >
          Добавление нового произведения
        </Title>
        <Button
          onClick={handleBack}
          icon={<ArrowLeftOutlined />}
          size="large"
          className={
            'bg-transparent border border-gray-400 dark:border-gray-600 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg px-4 py-1 flex items-center'
          }
          style={{ fontWeight: 500 }}
        >
          Назад к списку
        </Button>
      </div>
      <StyledForm
        layout="vertical"
        form={form}
        initialValues={{ title: '', type: '' }}
        onFinish={handleFinish}
        className={
          'bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md'
        }
      >
        <Form.Item
          name="title"
          label={<span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Название произведения</span>}
          rules={[{ required: true, message: 'Пожалуйста, введите название произведения' }, { max: 100, message: 'Максимум 100 символов' }]}
        >
          <Input
            size="large"
            placeholder="Введите название произведения"
            maxLength={100}
            className="bg-white dark:bg-gray-900 text-gray-900 dark:text-white border-gray-300 dark:border-gray-700 rounded-lg"
            style={{ fontSize: '1rem', height: 44 }}
          />
        </Form.Item>
        <Form.Item
          name="type"
          label={<span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Тип произведения</span>}
          rules={[{ required: true, message: 'Пожалуйста, выберите тип произведения' }]}
        >
          <Radio.Group
            onChange={e => setSelectedType(e.target.value)}
            value={selectedType}
            className="w-full"
          >
            <div className="space-y-4">
              {BOOK_TYPES.map((type) => (
                <div
                  key={type.value}
                  className={`transition-colors border flex flex-col rounded-xl cursor-pointer px-5 py-4 ${
                    selectedType === type.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30 shadow-lg'
                      : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-700 bg-white dark:bg-gray-900'
                  }`}
                  onClick={() => {
                    setSelectedType(type.value);
                    form.setFieldsValue({ type: type.value });
                  }}
                  style={{ minHeight: 64 }}
                >
                  <Radio value={type.value} className="hidden" />
                  <span className="text-base font-semibold text-gray-900 dark:text-white mb-1">
                    {type.label}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400 italic">
                    {type.comment}
                  </span>
                </div>
              ))}
            </div>
          </Radio.Group>
        </Form.Item>
        <Form.Item style={{ marginBottom: 0 }}>
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button type="primary" htmlType="submit" size="large" loading={loading} className="px-8">
              Создать произведение
            </Button>
          </div>
        </Form.Item>
      </StyledForm>
    </Container>
  );
};

export default CreateBook; 