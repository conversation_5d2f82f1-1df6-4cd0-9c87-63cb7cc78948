# Generated by Django 5.0.2 on 2025-06-09 08:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0035_useravatar'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserBioImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('path', models.CharField(max_length=512)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('width', models.CharField(blank=True, max_length=16, null=True)),
                ('height', models.CharField(blank=True, max_length=16, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bio_images', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'users_bio_image',
            },
        ),
    ]
