# OAuth Testing Guide

## 🚀 Статус интеграции

✅ **Базовая настройка завершена!**
- Django-allauth установлен и настроен
- Миграции применены
- Middleware настроен
- URL маршруты созданы
- Views для OAuth готовы

## 📋 Доступные эндпоинты

| Сервис | Метод | URL | Описание |
|--------|--------|-----|----------|
| VK | POST | `/api/users/auth/vk/` | Аутентификация через VK |
| Yandex | POST | `/api/users/auth/yandex/` | Аутентификация через Yandex |
| Google | POST | `/api/users/auth/google/` | Аутентификация через Google |
| OK | POST | `/api/users/auth/ok/` | Аутентификация через Одноклассники |

## 🔧 Следующие шаги для тестирования

### 1. Создайте приложения в социальных сетях

#### VK ID
1. Перейдите на https://dev.vk.com/
2. Создайте новое приложение:
   - Выберите тип **"Мини-приложение"** (подходит для веб-интеграции)
   - Укажите название приложения
3. В настройках приложения:
   - Перейдите в раздел **"Настройки"** → **"OAuth настройки"**
   - **Доверенный redirect URI**: `http://localhost:8000/api/users/auth/vk/`
   - **Разрешения**: Отметьте "email" (если доступно)
4. Скопируйте ID приложения и Защищённый ключ из раздела "Ключи"

**Примечание для VK:**
- Если "Мини-приложение" не подходит, попробуйте "Игра" 
- Основное отличие в том, что нужно правильно настроить OAuth redirect URI
- В разделе "Настройки" найдите параметры OAuth авторизации

#### Yandex ID
1. Перейдите на https://oauth.yandex.com/client/new
2. Заполните форму:
   - **Название**: Ваше приложение
   - **Redirect URI**: `http://localhost:8000/api/users/auth/yandex/`
   - **Доступы**: Выберите "Доступ к email" и "Доступ к логину"
3. Скопируйте ID и пароль приложения

#### Google OAuth
1. Перейдите на https://console.cloud.google.com/apis/credentials
2. Создайте новый проект (если нужно)
3. Включите Google+ API
4. Создайте "OAuth 2.0 Client IDs":
   - **Тип приложения**: Веб-приложение
   - **Авторизованные URI перенаправления**: `http://localhost:8000/api/users/auth/google/`
5. Скопируйте Client ID и Client Secret

#### Одноклассники (OK)
1. Перейдите на https://apiok.ru/dev/app/create
2. Заполните форму:
   - **Название**: Ваше приложение
   - **Redirect URI**: `http://localhost:8000/api/users/auth/ok/`
3. Скопируйте ID приложения, Публичный ключ и Секретный ключ

### 2. Добавьте переменные в .env файл

Создайте файл `backend/.env`:

```bash
# OAuth VK
VK_CLIENT_ID=your_vk_app_id
VK_SECRET_KEY=your_vk_secret_key

# OAuth Yandex
YANDEX_CLIENT_ID=your_yandex_client_id
YANDEX_CLIENT_SECRET=your_yandex_client_secret

# OAuth Google
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# OAuth OK
OK_APP_ID=your_ok_app_id
OK_SECRET_KEY=your_ok_secret_key
OK_PUBLIC_KEY=your_ok_public_key
```

### 3. Добавьте Social Applications через Django Admin

1. Перейдите на http://localhost:8000/admin/
2. Войдите как суперпользователь
3. В разделе "Social Accounts" выберите "Social applications"
4. Добавьте приложения для каждого провайдера:

**VK:**
- Provider: `VK`
- Provider ID: `vk`
- Name: `VK Login`
- Client id: `ваш_vk_app_id`
- Secret key: `ваш_vk_secret_key`
- Sites: выберите `localhost`

**Yandex:**
- Provider: `Yandex`
- Provider ID: `yandex`
- Name: `Yandex Login`
- Client id: `ваш_yandex_client_id`
- Secret key: `ваш_yandex_client_secret`
- Sites: выберите `localhost`

**Google:**
- Provider: `Google`
- Provider ID: `google`
- Name: `Google Login`
- Client id: `ваш_google_client_id`
- Secret key: `ваш_google_client_secret`
- Sites: выберите `localhost`

**OK:**
- Provider: `Odnoklassniki`
- Provider ID: `odnoklassniki`
- Name: `OK Login`
- Client id: `ваш_ok_app_id`
- Secret key: `ваш_ok_secret_key`
- Key: `ваш_ok_public_key`
- Sites: выберите `localhost`

## 🧪 Тестирование OAuth Flow

### Способ 1: Через frontend кнопки

Добавьте на фронтенд кнопки для OAuth:

```javascript
// VK Login
const handleVKLogin = () => {
  const clientId = 'your_vk_app_id';
  const redirectUri = 'http://localhost:8000/api/users/auth/vk/';
  const scope = 'email';
  
  window.location.href = `https://oauth.vk.com/authorize?client_id=${clientId}&display=popup&redirect_uri=${redirectUri}&scope=${scope}&response_type=code&v=5.131`;
};

// Yandex Login  
const handleYandexLogin = () => {
  const clientId = 'your_yandex_client_id';
  const redirectUri = 'http://localhost:8000/api/users/auth/yandex/';
  
  window.location.href = `https://oauth.yandex.ru/authorize?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}`;
};

// Google Login
const handleGoogleLogin = () => {
  const clientId = 'your_google_client_id';
  const redirectUri = 'http://localhost:8000/api/users/auth/google/';
  const scope = 'profile email';
  
  window.location.href = `https://accounts.google.com/oauth/authorize?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}`;
};

// OK Login
const handleOKLogin = () => {
  const clientId = 'your_ok_app_id';
  const redirectUri = 'http://localhost:8000/api/users/auth/ok/';
  const scope = 'VALUABLE_ACCESS';
  
  window.location.href = `https://connect.ok.ru/oauth/authorize?client_id=${clientId}&scope=${scope}&response_type=code&redirect_uri=${redirectUri}`;
};
```

### Способ 2: Прямое тестирование через URL

1. Перейдите по URL авторизации (например VK):
```
https://oauth.vk.com/authorize?client_id=YOUR_VK_APP_ID&display=popup&redirect_uri=http://localhost:8000/api/users/auth/vk/&scope=email&response_type=code&v=5.131
```

2. Авторизуйтесь в социальной сети
3. Вас перенаправит на backend с кодом авторизации
4. Backend автоматически обработает OAuth и вернет JWT токены

## 📱 Ответ сервера

При успешной аутентификации вы получите:

```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "pk": 1,
    "username": "<EMAIL>",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
  }
}
```

## 🔍 Отладка

Если что-то не работает:

1. Проверьте логи Django сервера
2. Убедитесь, что redirect URI в приложениях точно совпадает
3. Проверьте, что все переменные окружения установлены
4. Убедитесь, что Social Applications созданы в Django Admin

## ✅ Готово к продакшену

После успешного тестирования:
1. Обновите redirect URI для продакшена
2. Добавьте production домены в настройки приложений
3. Обновите переменные окружения для production 