# Generated by Django 5.0.2 on 2025-06-19 10:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0041_user_books_blocks_order'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('unread_messages', 'Непрочитанные сообщения'), ('feed_events', 'События в ленте'), ('friend_requests', 'Запросы в друзья'), ('new_messages', 'Новые сообщения')], max_length=20)),
                ('data', models.<PERSON><PERSON><PERSON>ield(default=dict, help_text='Данные уведомления (количество, ID объектов и т.д.)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-updated_at'],
                'unique_together': {('user', 'notification_type')},
            },
        ),
    ]
