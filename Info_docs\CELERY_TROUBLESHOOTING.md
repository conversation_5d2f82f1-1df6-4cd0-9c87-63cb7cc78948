# Диагностика и исправление проблем с Celery

## Проблема с задержкой авто публикации

### Симптомы
- Задачи публикации выполняются с задержкой 30-40 секунд после назначенного времени
- Массовое планирование публикации занимает 10+ секунд
- Главы публикуются в случайном порядке вместо порядка по `order`
- Одиночная публикация работает идеально

### Причины и исправления

#### 1. Последовательные запросы (ИСПРАВЛЕНО)
**Проблема**: В `handleSchedulePublish` использовался `for` цикл с `await`
**Решение**: Заменили на `Promise.all()` для параллельного выполнения

#### 2. Отсутствие приоритетов (ИСПРАВЛЕНО)
**Проблема**: Задачи Celery выполнялись без учета порядка глав
**Решение**: Добавили приоритеты на основе поля `order`

#### 3. Недостаточные настройки Celery (ИСПРАВЛЕНО)
**Проблема**: Отсутствовали настройки для приоритетов и производительности
**Решение**: Добавили настройки в `settings.py`

## Диагностика

### 1. Проверка состояния Celery

```bash
# Проверка активных воркеров и задач
python manage.py check_celery_status --verbose

# Проверка только активных воркеров
celery -A config inspect active

# Проверка зарегистрированных воркеров
celery -A config inspect registered

# Проверка задач в очереди
celery -A config inspect reserved
```

### 2. Проверка просроченных глав

```bash
# Показать просроченные главы без публикации
python manage.py publish_overdue_chapters --dry-run

# Принудительно опубликовать просроченные главы
python manage.py publish_overdue_chapters

# Для конкретной книги
python manage.py publish_overdue_chapters --book-id 53
```

### 3. Мониторинг логов

```bash
# Логи Celery воркера
celery -A config worker --loglevel=info

# Логи планировщика (если используется)
celery -A config beat --loglevel=info

# Логи Django
python manage.py runserver --verbosity=2
```

## Запуск и настройка

### 1. Запуск воркеров с приоритетами

```bash
# Запуск воркера с поддержкой приоритетов
celery -A config worker --loglevel=info --concurrency=2

# Запуск воркера с именем
celery -A config worker --loglevel=info --hostname=worker1@%h

# Запуск нескольких воркеров
celery -A config worker --loglevel=info --concurrency=4 --hostname=worker@%h
```

### 2. Проверка Redis

```bash
# Подключение к Redis
redis-cli

# Проверка очередей
LLEN celery

# Очистка очередей (осторожно!)
FLUSHALL
```

## Устранение неполадок

### Проблема: Воркеры не запускаются
```bash
# Проверка подключения к Redis
redis-cli ping

# Перезапуск воркеров
pkill -f "celery worker"
celery -A config worker --loglevel=info
```

### Проблема: Задачи не выполняются
```bash
# Проверка состояния задач
python manage.py check_celery_status

# Принудительная публикация просроченных
python manage.py publish_overdue_chapters
```

### Проблема: Задержки в выполнении
```bash
# Увеличить количество воркеров
celery -A config worker --concurrency=4

# Проверить нагрузку на Redis
redis-cli info memory
```

## Оптимизация производительности

### 1. Настройки воркеров
- `--concurrency=2-4` - количество процессов
- `--prefetch-multiplier=1` - избежать перегрузки
- `--max-tasks-per-child=1000` - перезапуск для стабильности

### 2. Настройки Redis
- Увеличить `maxmemory` если нужно
- Настроить `maxmemory-policy`
- Мониторить использование памяти

### 3. Мониторинг
- Регулярно проверять `check_celery_status`
- Мониторить логи на предмет ошибок
- Проверять просроченные задачи

## Команды для быстрой диагностики

```bash
# Полная диагностика
python manage.py check_celery_status --verbose

# Проверка просроченных глав
python manage.py publish_overdue_chapters --dry-run

# Перезапуск воркеров
pkill -f "celery worker" && celery -A config worker --loglevel=info

# Очистка очередей (осторожно!)
redis-cli FLUSHALL
``` 