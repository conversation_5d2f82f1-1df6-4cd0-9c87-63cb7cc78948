# Функциональность поиска LitPortal

## Обзор
Система поиска позволяет пользователям искать книги и авторов по различным критериям.

## Компоненты

### Frontend
- **SearchBar** (`frontend/src/components/SearchBar.jsx`) - Компонент строки поиска с выпадающим полем
- **SearchResults** (`frontend/src/pages/SearchResults.jsx`) - Страница результатов поиска

### Backend
- **search_views.py** (`backend/books/search_views.py`) - API endpoint для поиска
- **SearchBookSerializer** - Специальный сериализатор для результатов поиска

## Возможности поиска

### Критерии поиска
1. **Название книги** - поиск по названию (частичное совпадение)
2. **Имя автора** - поиск по display_name автора
3. **Логин автора** - поиск по username автора
4. **Жанры** - поиск книг по жанрам
5. **Хештеги** - поиск книг по хештегам

### Типы результатов

#### Раздел "Книги"
- Отображает найденные книги
- Показывает обложку, название, автора
- Показывает жанры и хештеги (до 3 + счетчик остальных)
- Кнопка "Читать" для перехода к книге
- Вид похож на библиотеку, но без статуса чтения

#### Раздел "Авторы"
- Отображает найденных авторов
- Показывает аватар, имя, логин
- Показывает общий рейтинг автора (reader_rating + author_rating)
- Кнопка "Подробнее" для перехода к профилю
- Карточки в стиле книжных карточек

## Ограничения
- Максимум 20 книг в результатах
- Максимум 10 авторов в результатах
- Только опубликованные книги
- Только авторы с опубликованными книгами

## Использование

### Десктоп
- Кнопка лупы рядом с домиком в навбаре
- При клике разворачивается поле поиска
- Поддержка поиска по Enter

### Мобильный
- Строка поиска в мобильном меню
- Полноразмерное поле ввода

## API Endpoint

```
GET /api/search/?q={query}
```

### Ответ
```json
{
  "books": [
    {
      "id": 1,
      "title": "Название книги",
      "description": "Описание",
      "cover": "url_обложки",
      "author": {
        "id": 1,
        "username": "username",
        "display_name": "Имя автора"
      },
      "genres": [{"id": 1, "name": "Жанр"}],
      "hashtags": [{"id": 1, "name": "хештег"}],
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "authors": [
    {
      "id": 1,
      "username": "username",
      "display_name": "Имя автора",
      "avatar_url": "url_аватара",
      "reader_rating": 0,
      "author_rating": 0
    }
  ],
  "query": "поисковый запрос"
}
```

## Маршруты
- `/search?q={query}` - страница результатов поиска

## Особенности реализации
- Поиск работает с разбивкой запроса на слова
- Поиск нечувствителен к регистру
- Используются Django ORM Q-объекты для гибких запросов
- Результаты сортируются по дате создания (книги) и рейтингу (авторы)
- Поддержка как аутентифицированных, так и анонимных пользователей 