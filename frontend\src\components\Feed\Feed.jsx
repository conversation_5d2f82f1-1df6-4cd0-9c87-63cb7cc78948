import React, { useState, useContext, useCallback, useMemo } from 'react';
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { useNavigate, useParams } from 'react-router-dom';
import { AuthContext, useAuth } from '../../context/AuthContext';
import { useMessage } from '../../context/MessageContext';
import FeedItem from './FeedItem';
import { Cog6ToothIcon } from '@heroicons/react/24/outline';

const Feed = () => {
    const [page, setPage] = useState(1);
    const queryClient = useQueryClient();
    const navigate = useNavigate();
    const { username } = useParams();
    const { user, refreshUser } = useAuth();
    const { notifications, isWebSocketConnected } = useMessage();
    const [handledRequests, setHandledRequests] = useState({});
    const [markingAsRead, setMarkingAsRead] = useState(new Set());
    const backendUrl = 'http://localhost:8000';
    const { theme } = useContext(AuthContext);

    const {
        data,
        fetchNextPage,
        hasNextPage,
        isFetchingNextPage,
        status,
        error
    } = useInfiniteQuery({
        queryKey: ['feed'],
        queryFn: async ({ pageParam = 1 }) => {
            try {
                const response = await axios.get(`/api/users/feed/events/?page=${pageParam}&per_page=20`);
                return response.data;
            } catch (error) {
                console.error('Error fetching feed:', error);
                throw error;
            }
        },
        getNextPageParam: (lastPage) => {
            if (lastPage?.has_more) {
                return lastPage.page + 1;
            }
            return undefined;
        },
        retry: 1,
        refetchOnWindowFocus: false,
    });

    // Получаем настройки фильтрации из localStorage (или по умолчанию)
    const feedSettings = useMemo(() => {
        try {
            return JSON.parse(localStorage.getItem('feedSettings')) || {};
        } catch {
            return {};
        }
    }, []);

    const showUnsubscribes = feedSettings.showUnsubscribes ?? false;
    const showRemovedFromFriends = feedSettings.showRemovedFromFriends ?? false;

    // Проверка прав доступа после объявления всех хуков
    if (!user || user.username !== username) {
        return (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div className="flex justify-center items-center h-32">
                    <div className="text-red-500">
                        У вас нет доступа к этой странице
                    </div>
                </div>
            </div>
        );
    }

    // Функция для получения CSRF-токена из cookie
    const getCookie = useCallback((name) => {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return '';
    }, []);

    const markAsRead = useCallback(async (eventId) => {
        // console.log(`markAsRead called for event ${eventId}`); // Removed for performance
        
        // Проверяем, не обрабатывается ли уже это событие
        if (markingAsRead.has(eventId)) {
            // console.log(`Event ${eventId} is already being processed, skipping`); // Removed for performance
            return;
        }

        try {
            // console.log(`Starting to mark event ${eventId} as read`); // Removed for performance
            setMarkingAsRead(prev => new Set([...prev, eventId]));
            
            // Оптимистично обновляем состояние
            queryClient.setQueryData(['feed'], oldData => {
                if (!oldData) return oldData;
                
                const updatedPages = oldData.pages.map(page => ({
                    ...page,
                    events: page.events.map(event => 
                        event.id === eventId 
                            ? { ...event, is_read: true }
                            : event
                    )
                }));

                return {
                    ...oldData,
                    pages: updatedPages,
                };
            });

            await axios.post(
                '/api/auth/feed/events/mark-read/',
                { event_id: eventId },
                {
                    headers: { 'X-CSRFToken': getCookie('csrftoken') },
                    withCredentials: true
                }
            );
            // console.log(`Successfully marked event ${eventId} as read`); // Removed for performance
            
        } catch (error) {
            console.error('Error marking event as read:', error);
            // Откатываем оптимистичное обновление в случае ошибки
            queryClient.invalidateQueries(['feed']);
        } finally {
            setMarkingAsRead(prev => {
                const newSet = new Set([...prev]);
                newSet.delete(eventId);
                return newSet;
            });
            // console.log(`Removed event ${eventId} from markingAsRead set`); // Removed for performance
        }
    }, [queryClient, getCookie]);

    const markAllAsRead = useCallback(async () => {
        try {
            // Оптимистично обновляем состояние всех событий
            queryClient.setQueryData(['feed'], oldData => {
                if (!oldData) return oldData;

                return {
                    pages: oldData.pages.map(page => ({
                        ...page,
                        events: page.events.map(event => ({
                            ...event,
                            is_read: true
                        }))
                    })),
                    pageParams: oldData.pageParams
                };
            });

            await axios.post(
                '/api/auth/feed/events/mark-read/',
                {},
                {
                    headers: { 'X-CSRFToken': getCookie('csrftoken') },
                    withCredentials: true
                }
            );
        } catch (error) {
            console.error('Error marking all events as read:', error);
            // Откатываем оптимистичное обновление в случае ошибки
            queryClient.invalidateQueries(['feed']);
        }
    }, [queryClient, getCookie]);

    const handleFriendRequest = useCallback(async (username, action, eventId) => {
        try {
            if (action === 'accept') {
                await axios.post(
                    '/api/auth/friend-request/accept/',
                    { username },
                    {
                        headers: { 'X-CSRFToken': getCookie('csrftoken') },
                        withCredentials: true
                    }
                );
                setHandledRequests(prev => ({ ...prev, [eventId]: 'accepted' }));
            } else if (action === 'decline') {
                await axios.post(
                    '/api/auth/friend-request/decline/',
                    { username },
                    {
                        headers: { 'X-CSRFToken': getCookie('csrftoken') },
                        withCredentials: true
                    }
                );
                setHandledRequests(prev => ({ ...prev, [eventId]: 'declined' }));
            }
            queryClient.invalidateQueries(['feed']);
        } catch (error) {
            console.error('Error handling friend request:', error);
        }
    }, [queryClient, getCookie]);

    const handleSettingsClick = useCallback(() => {
        navigate(`/lpu/${user.username}/feed/settings`);
    }, [navigate, user?.username]);

    if (status === 'loading') {
        return (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div className="flex justify-center items-center h-32">
                    <div className="text-gray-500 dark:text-gray-400">Загрузка...</div>
                </div>
            </div>
        );
    }

    if (status === 'error') {
        return (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div className="flex justify-center items-center h-32">
                    <div className="text-red-500">
                        Ошибка при загрузке ленты: {error?.message || 'Неизвестная ошибка'}
                    </div>
                </div>
            </div>
        );
    }

    if (!data?.pages?.[0]?.events?.length) {
        return (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div className="flex justify-center items-center h-32">
                    <div className="text-gray-500 dark:text-gray-400">Нет новых уведомлений</div>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="flex justify-between items-center p-4 border-b dark:border-gray-700">
                <h2 className="text-xl font-semibold">Лента событий</h2>
                <div className="flex items-center gap-2">
                    <button
                        onClick={markAllAsRead}
                        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700"
                    >
                        Отметить все как прочитанные
                    </button>
                    <button
                        className="hidden md:inline-flex p-2 bg-white dark:bg-transparent text-gray-500 dark:text-gray-300 hover:text-yellow-600 hover:bg-gray-100 dark:hover:text-yellow-400 dark:hover:bg-gray-800 relative"
                        title="Настройки ленты"
                        onClick={handleSettingsClick}
                    >
                        <Cog6ToothIcon className="h-5 w-5" />
                    </button>
                </div>
            </div>
            
            <div className="max-h-[600px] overflow-y-auto scroll-smooth">
                <div className="divide-y dark:divide-gray-700">
                    {data.pages.map((page, i) => (
                        <React.Fragment key={i}>
                            {page.events.map((event) => (
                                <FeedItem
                                    key={event.id}
                                    event={event}
                                    onMarkAsRead={markAsRead}
                                    backendUrl={backendUrl}
                                    onFriendRequest={handleFriendRequest}
                                />
                            ))}
                        </React.Fragment>
                    ))}
                </div>
                
                {hasNextPage && (
                    <div className="p-4 text-center border-t dark:border-gray-700">
                        <button
                            onClick={() => fetchNextPage()}
                            disabled={isFetchingNextPage}
                            className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center mx-auto gap-2"
                        >
                            {isFetchingNextPage ? (
                                <>
                                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                    Загрузка...
                                </>
                            ) : (
                                'Загрузить еще'
                            )}
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default Feed; 