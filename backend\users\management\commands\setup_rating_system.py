"""
Management команда для настройки системы рейтингов.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import User, UserStats
from users.rating_service import setup_default_rating_rules, RatingService
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Настройка системы рейтингов: создание правил и инициализация статистики'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--create-rules',
            action='store_true',
            help='Создать правила расчета рейтингов по умолчанию'
        )
        
        parser.add_argument(
            '--recalculate-all',
            action='store_true',
            help='Пересчитать рейтинги всех пользователей'
        )
        
        parser.add_argument(
            '--create-stats',
            action='store_true',
            help='Создать статистику для пользователей, у которых ее нет'
        )
        
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Размер батча для обработки пользователей (по умолчанию 100)'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Показать что будет сделано, но не выполнять изменения'
        )
    
    def handle(self, *args, **options):
        dry_run = options['dry_run']
        batch_size = options['batch_size']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('РЕЖИМ СИМУЛЯЦИИ: изменения не будут сохранены')
            )
        
        if options['create_rules']:
            self.create_rating_rules(dry_run)
        
        if options['create_stats']:
            self.create_user_stats(dry_run, batch_size)
        
        if options['recalculate_all']:
            self.recalculate_all_ratings(dry_run, batch_size)
        
        if not any([options['create_rules'], options['create_stats'], options['recalculate_all']]):
            self.stdout.write(
                self.style.WARNING(
                    'Не выбрано ни одно действие. Используйте --help для списка опций.'
                )
            )
    
    def create_rating_rules(self, dry_run):
        """Создание правил расчета рейтингов по умолчанию."""
        self.stdout.write('Создание правил расчета рейтингов...')
        
        if not dry_run:
            try:
                with transaction.atomic():
                    setup_default_rating_rules()
                self.stdout.write(
                    self.style.SUCCESS('✓ Правила расчета рейтингов созданы')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ Ошибка при создании правил: {e}')
                )
        else:
            self.stdout.write(
                self.style.SUCCESS('✓ [СИМУЛЯЦИЯ] Правила расчета рейтингов будут созданы')
            )
    
    def create_user_stats(self, dry_run, batch_size):
        """Создание статистики для пользователей, у которых ее нет."""
        self.stdout.write('Создание статистики пользователей...')
        
        # Находим пользователей без статистики
        users_without_stats = User.objects.filter(stats__isnull=True)
        total_count = users_without_stats.count()
        
        if total_count == 0:
            self.stdout.write(
                self.style.SUCCESS('✓ У всех пользователей уже есть статистика')
            )
            return
        
        self.stdout.write(f'Найдено пользователей без статистики: {total_count}')
        
        if not dry_run:
            created_count = 0
            error_count = 0
            
            for i in range(0, total_count, batch_size):
                batch = users_without_stats[i:i + batch_size]
                
                for user in batch:
                    try:
                        RatingService.get_or_create_user_stats(user)
                        created_count += 1
                        
                        if created_count % 50 == 0:
                            self.stdout.write(f'  Создано: {created_count}/{total_count}')
                            
                    except Exception as e:
                        error_count += 1
                        logger.error(f'Ошибка при создании статистики для пользователя {user.id}: {e}')
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'✓ Создана статистика для {created_count} пользователей'
                )
            )
            
            if error_count > 0:
                self.stdout.write(
                    self.style.WARNING(f'⚠ Ошибок: {error_count}')
                )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'✓ [СИМУЛЯЦИЯ] Будет создана статистика для {total_count} пользователей'
                )
            )
    
    def recalculate_all_ratings(self, dry_run, batch_size):
        """Пересчет рейтингов всех пользователей."""
        self.stdout.write('Пересчет рейтингов всех пользователей...')
        
        total_count = User.objects.count()
        
        if total_count == 0:
            self.stdout.write(
                self.style.WARNING('Пользователи не найдены')
            )
            return
        
        self.stdout.write(f'Будет пересчитан рейтинг для {total_count} пользователей')
        
        if not dry_run:
            processed_count = 0
            error_count = 0
            
            for i in range(0, total_count, batch_size):
                batch = User.objects.all()[i:i + batch_size]
                
                for user in batch:
                    try:
                        RatingService.recalculate_user_stats(user)
                        processed_count += 1
                        
                        if processed_count % 50 == 0:
                            self.stdout.write(f'  Обработано: {processed_count}/{total_count}')
                            
                    except Exception as e:
                        error_count += 1
                        logger.error(f'Ошибка при пересчете рейтинга пользователя {user.id}: {e}')
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'✓ Пересчитан рейтинг для {processed_count} пользователей'
                )
            )
            
            if error_count > 0:
                self.stdout.write(
                    self.style.WARNING(f'⚠ Ошибок: {error_count}')
                )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'✓ [СИМУЛЯЦИЯ] Будет пересчитан рейтинг для {total_count} пользователей'
                )
            )
    
    def show_current_stats(self):
        """Показать текущую статистику системы рейтингов."""
        from django.db.models import Count, Avg, Max, Min
        from users.models import RatingCalculationRule, UserMetricHistory
        
        self.stdout.write('\n' + '='*50)
        self.stdout.write('ТЕКУЩАЯ СТАТИСТИКА СИСТЕМЫ РЕЙТИНГОВ')
        self.stdout.write('='*50)
        
        # Статистика пользователей
        users_stats = User.objects.aggregate(
            total_users=Count('id'),
            users_with_stats=Count('stats'),
        )
        
        self.stdout.write(f"Всего пользователей: {users_stats['total_users']}")
        self.stdout.write(f"С статистикой: {users_stats['users_with_stats']}")
        
        # Статистика рейтингов
        if users_stats['users_with_stats'] > 0:
            rating_stats = UserStats.objects.aggregate(
                avg_total=Avg('total_rating'),
                max_total=Max('total_rating'),
                min_total=Min('total_rating'),
                avg_reader=Avg('reader_rating'),
                max_reader=Max('reader_rating'),
                avg_author=Avg('author_rating'),
                max_author=Max('author_rating'),
            )
            
            self.stdout.write(f"\nРейтинги:")
            self.stdout.write(f"  Общий: среднй={rating_stats['avg_total']:.1f}, макс={rating_stats['max_total']}")
            self.stdout.write(f"  Читательский: среднй={rating_stats['avg_reader']:.1f}, макс={rating_stats['max_reader']}")
            self.stdout.write(f"  Авторский: среднй={rating_stats['avg_author']:.1f}, макс={rating_stats['max_author']}")
        
        # Правила расчета
        rules_count = RatingCalculationRule.objects.count()
        active_rules_count = RatingCalculationRule.objects.filter(is_active=True).count()
        
        self.stdout.write(f"\nПравила расчета: {active_rules_count}/{rules_count} активных")
        
        # История метрик
        history_count = UserMetricHistory.objects.count()
        self.stdout.write(f"Записей в истории метрик: {history_count}")
        
        self.stdout.write('='*50 + '\n') 