.custom-hashtag-input-wrapper {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

.custom-hashtag-input-field {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  min-height: 44px;
  border: 1.5px solid var(--border-color, #d9d9d9);
  border-radius: 12px;
  background: var(--input-bg, #fff);
  padding: 4px 8px;
  transition: border-color 0.2s, box-shadow 0.2s;
  cursor: text;
}

.custom-hashtag-input-field.active {
  border-color: var(--primary, #1677ff);
  box-shadow: 0 0 0 2px rgba(22,119,255,0.12);
}

.custom-hashtag-input-wrapper.light {
  --border-color: #d9d9d9;
  --input-bg: #fff;
  --primary: #1677ff;
  --chip-bg: #e6f4ff;
  --chip-color: #1677ff;
  --chip-close: #1677ff;
  --dropdown-bg: #fff;
  --dropdown-border: #d9d9d9;
  --dropdown-hover: #f0f7ff;
  --placeholder: #bfbfbf;
}

.custom-hashtag-input-wrapper.dark {
  --border-color: #333c4d;
  --input-bg: #111827;
  --primary: #1677ff;
  --chip-bg: #223355;
  --chip-color: #6eaaff;
  --chip-close: #6eaaff;
  --dropdown-bg: #232a36;
  --dropdown-border: #333c4d;
  --dropdown-hover: #223355;
  --placeholder: #5a6a7a;
}

.hashtag-chip {
  display: flex;
  align-items: center;
  background: #2563eb !important;
  color: #fff !important;
  border-radius: 8px;
  font-size: 13px;
  margin: 2px 4px 2px 0;
  padding: 2px 8px 2px 6px;
  line-height: 1.5;
  user-select: none;
}

.hashtag-chip-label {
  margin-right: 4px;
  color: #fff !important;
}

.hashtag-chip-close {
  cursor: pointer;
  font-size: 15px;
  color: #ef4444 !important;
  margin-left: 2px;
  transition: color 0.2s;
}
.hashtag-chip-close:hover {
  color: #ff7875 !important;
}

.hashtag-input {
  border: none;
  outline: none;
  background: transparent;
  min-width: 120px;
  flex: 1;
  font-size: 15px;
  color: #222;
  padding: 4px 0;
  box-shadow: none !important;
}
.hashtag-input:focus {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}
.hashtag-input:disabled {
  background: transparent;
  color: var(--placeholder);
  cursor: not-allowed;
}
.hashtag-input::placeholder {
  color: var(--placeholder);
  opacity: 1;
}

.custom-hashtag-input-wrapper.dark .hashtag-input {
  color: #fff;
}

.hashtag-dropdown {
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  z-index: 10;
  background: var(--dropdown-bg);
  border: 1.5px solid var(--dropdown-border);
  border-radius: 10px;
  margin-top: 4px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.07);
  max-height: 180px;
  overflow-y: auto;
}

.hashtag-dropdown-item {
  padding: 8px 16px;
  font-size: 14px;
  color: var(--chip-color);
  cursor: pointer;
  transition: background 0.15s;
}
.hashtag-dropdown-item:hover {
  background: var(--dropdown-hover);
}

.hashtag-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  padding: 4px 8px;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.custom-hashtag-input-wrapper.dark .hashtag-error {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
} 