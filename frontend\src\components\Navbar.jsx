import React, { Fragment, useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Menu, Transition } from '@headlessui/react';
import {
  ChevronDownIcon,
  Bars3Icon,
  XMarkIcon,
  UserCircleIcon,
  BellIcon,
  PlusIcon,
  SunIcon,
  MoonIcon,
  ArrowRightOnRectangleIcon,
  HomeIcon
} from '@heroicons/react/24/outline';
import { EnvelopeIcon } from '@heroicons/react/24/solid';
import { useTheme } from '../theme/ThemeContext';
import { useAuth } from '../context/AuthContext';
import Container from './Container';
import { useFeedCount } from '../hooks/useFeedCount';
import { useMessage } from '../context/MessageContext';
import { getCachedUserAvatar, clearAvatarCache } from '../utils/avatarCache';
import { getCSRFToken } from '../utils/csrf';
import { useUserBookCounts } from '../hooks/useUserBooks';
import SearchBar from './SearchBar';

function AutoIcon(props) {
  return (
    <span {...props} className="font-bold text-lg" style={{fontFamily: 'sans-serif'}}>A</span>
  );
}

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

// Error Boundary для Navbar
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }
    static getDerivedStateFromError(error) {
        return { hasError: true };
    }
    componentDidCatch(error, errorInfo) {
        // Можно отправить ошибку на сервер или в логи
        console.error('Navbar error:', error, errorInfo);
    }
    render() {
        if (this.state.hasError) {
            return <div className="bg-red-100 text-red-700 p-2 text-center">Ошибка в меню. Попробуйте обновить страницу.</div>;
        }
        return this.props.children;
    }
}

const NavbarContent = () => {
  const { isAuthenticated, user, logout, avatarVersion } = useAuth();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const menuRef = useRef(null);
  const buttonRef = useRef(null);
  const { themeMode, toggleTheme } = useTheme();
  const backendUrl = 'http://localhost:8000'; // или ваш реальный адрес
  const { data: feedCount } = useFeedCount();
  const { unreadMessagesCount, fetchUnreadCount } = useMessage();
  const [menuPosition, setMenuPosition] = useState({ top: 0, right: 0 });
  const [error, setError] = useState(null);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Используем хук для кеширования счетчиков книг
  const { counts: bookCounts } = useUserBookCounts(user?.username);

  // Очищаем кеш аватаров при изменении пользователя
  useEffect(() => {
    clearAvatarCache();
  }, [user?.id]);

  const handleLogout = async () => {
    setError(null);
    setIsLoggingOut(true);
    try {
      await logout();
    } catch (error) {
      setError('Ошибка при выходе: ' + error.message);
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  useEffect(() => {
    if (!user) return;
    // Немедленный ping при монтировании
    fetch('/api/auth/ping/', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'X-CSRFToken': getCSRFToken()
      }
    });
    // Периодический ping
    const interval = setInterval(() => {
      fetch('/api/auth/ping/', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'X-CSRFToken': getCSRFToken()
        }
      });
    }, 60000); // 60 секунд
    return () => clearInterval(interval);
  }, [user]);

  // Обработчик клика вне меню
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && 
          !menuRef.current.contains(event.target) && 
          buttonRef.current && 
          !buttonRef.current.contains(event.target)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Обновляем позицию меню при открытии
  const updateMenuPosition = () => {
    if (buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const navRect = buttonRef.current.closest('nav').getBoundingClientRect();
      
      // Вычисляем позицию относительно навбара
      setMenuPosition({
        top: buttonRect.bottom - navRect.top,
        right: navRect.right - buttonRect.right
      });
    }
  };

  // Обновляем позицию при открытии меню и при изменении размера окна
  useEffect(() => {
    if (isMenuOpen) {
      // Небольшая задержка для гарантии, что DOM обновился
      requestAnimationFrame(() => {
        updateMenuPosition();
      });
      
      window.addEventListener('resize', updateMenuPosition);
      return () => window.removeEventListener('resize', updateMenuPosition);
    }
  }, [isMenuOpen]);

  // Определяем текущую тему через CSS-переменные
  useEffect(() => {
    const updateTheme = () => {
      const root = document.documentElement;
      const isDark = root.classList.contains('dark');
      
      // Устанавливаем CSS-переменные для кнопки
      root.style.setProperty('--button-text', isDark ? '#9CA3AF' : '#6B7280');
      root.style.setProperty('--button-hover-text', isDark ? '#E5E7EB' : '#374151');
      root.style.setProperty('--button-hover-bg', isDark ? '#1F2937' : '#F3F4F6');
      root.style.setProperty('--button-focus-ring', isDark ? '#60A5FA' : '#3B82F6');
    };

    // Создаем наблюдатель за изменениями в DOM
    const observer = new MutationObserver(updateTheme);
    
    // Начинаем наблюдение
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    // Первоначальное обновление
    updateTheme();

    return () => observer.disconnect();
  }, []);

  // Основной рендер навбара (только если не loading)
  return (
    <nav className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 shadow-sm sticky top-0 z-50 transition-colors duration-300">
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}
      <div className="max-w-[1200px] mx-auto w-full px-4">
        <div className="flex justify-between h-16 items-center">
          {/* Логотип */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <img src="https://storage.yandexcloud.net/lpo-test/dist/lpo/logo/logolpo.webp" alt="Логотип сайта" className="h-10 w-auto" />
            </Link>
            <Link
              to="/"
              className="ml-4 hidden md:inline-flex p-1.5 bg-white dark:bg-gray-900 text-gray-700 dark:text-gray-200 hover:text-blue-700 dark:hover:text-blue-400 rounded-full hover:ring-1 hover:ring-blue-700"
              onMouseDown={(e) => e.currentTarget.blur()}
            >
              <HomeIcon className="h-6 w-6" />
            </Link>
            <div className="ml-3 hidden md:block">
              <SearchBar />
            </div>
          </div>

          {/* Desktop menu */}
          <div className="hidden md:flex md:items-center md:space-x-4">
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-3">
            <Link
              to={user ? `/lpu/${user.username}/messages` : '/login'}
              className="hidden md:inline-flex p-1.5 bg-white dark:bg-gray-900 text-gray-500 dark:text-gray-300 hover:text-green-600 relative rounded-full hover:ring-1 hover:ring-green-600"
              onMouseDown={(e) => e.currentTarget.blur()}
              title="Сообщения"
            >
              <EnvelopeIcon className="h-5 w-5" />
              {unreadMessagesCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-1">{unreadMessagesCount}</span>
              )}
            </Link>
            <Link
              to={user ? `/lpu/${user.username}/feed` : '/login'}
              className="hidden md:inline-flex p-1.5 bg-white dark:bg-gray-900 text-gray-500 dark:text-gray-300 hover:text-yellow-600 relative rounded-full hover:ring-1 hover:ring-yellow-600"
              onMouseDown={(e) => e.currentTarget.blur()}
              title="Уведомления"
            >
              <BellIcon className="h-5 w-5" />
              {feedCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-1">{feedCount}</span>
              )}
            </Link>
            <button
              onClick={toggleTheme}
              className="p-1.5 rounded-full bg-white dark:bg-gray-900 text-gray-700 dark:text-gray-200"
              onMouseDown={(e) => e.currentTarget.blur()}
              title={
                themeMode === 'light'
                  ? 'Светлая тема'
                  : themeMode === 'dark'
                  ? 'Тёмная тема'
                  : 'Автоматически (по системе)'
              }
            >
              {themeMode === 'light' ? (
                <SunIcon className="h-5 w-5" />
              ) : themeMode === 'dark' ? (
                <MoonIcon className="h-5 w-5" />
              ) : (
                <AutoIcon />
              )}
            </button>

            {isAuthenticated ? (
              <Menu as="div" className="relative inline-block text-left">
                <div>
                  <Menu.Button className="inline-flex items-center p-0.5 bg-white dark:bg-gray-900 text-gray-700 dark:text-gray-200 hover:text-blue-700 dark:hover:text-blue-400">
                    {(() => {
                      const avatarUrl = getCachedUserAvatar(user, 'mini', backendUrl);
                      // Используем дополнительные поля для создания уникального ключа
                      const avatarKey = `avatar-${user.id}-${user.avatar_updated_at || 'none'}-${user.avatar_type || 1}-${avatarVersion}`;
                      return avatarUrl ? (
                        <img
                          key={avatarKey}
                          src={avatarUrl}
                          alt="mini avatar"
                          className="h-12 w-12 rounded-full border-2 border-gray-300 object-cover"
                          style={{minWidth: 48, minHeight: 48}}
                          onError={e => { e.target.style.display = 'none'; }}
                        />
                      ) : (
                        <UserCircleIcon key={`icon-${user.id}-${avatarVersion}`} className="h-12 w-12" />
                      );
                    })()}
                  </Menu.Button>
                </div>
                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items className="absolute right-0 mt-2 w-auto min-w-[150px] origin-top-right rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                    <div className="py-1">
                      <Menu.Item>
                        {({ active }) => (
                          <Link
                            to={`/lpu/${user?.username}`}
                            className={classNames(
                              active ? 'bg-gray-100 dark:bg-gray-700' : '',
                              'block px-4 py-2 text-sm text-gray-700 dark:text-gray-200'
                            )}
                          >
                            Профиль
                          </Link>
                        )}
                      </Menu.Item>
                      <Menu.Item>
                        {({ active }) => (
                          <Link
                            to={`/lpu/${user?.username}/books`}
                            className={classNames(
                              active ? 'bg-gray-100 dark:bg-gray-700' : '',
                              'block px-4 py-2 text-sm text-gray-700 dark:text-gray-200'
                            )}
                          >
                            Мои книги
                          </Link>
                        )}
                      </Menu.Item>
                      <Menu.Item>
                        {({ active }) => (
                          <Link
                            to={`/lpu/${user?.username}/feed`}
                            className={classNames(
                              active ? 'bg-gray-100 dark:bg-gray-700' : '',
                              'block px-4 py-2 text-sm text-gray-700 dark:text-gray-200'
                            )}
                          >
                            Лента
                          </Link>
                        )}
                      </Menu.Item>
                      <Menu.Item>
                        {({ active }) => (
                          <Link
                            to={`/lpu/${user?.username}/messages`}
                            className={classNames(
                              active ? 'bg-gray-100 dark:bg-gray-700' : '',
                              'block px-4 py-2 text-sm text-gray-700 dark:text-gray-200'
                            )}
                          >
                            Сообщения
                          </Link>
                        )}
                      </Menu.Item>
                      <Menu.Item>
                        {({ active }) => (
                          <Link
                            to={`/lpu/${user?.username}/library`}
                            className={classNames(
                              active ? 'bg-gray-100 dark:bg-gray-700' : '',
                              'block px-4 py-2 text-sm text-gray-700 dark:text-gray-200'
                            )}
                          >
                            Библиотека
                          </Link>
                        )}
                      </Menu.Item>
                      <Menu.Item>
                        {({ active }) => (
                          <Link
                            to={`/lpu/${user?.username}/settings`}
                            className={classNames(
                              active ? 'bg-gray-100 dark:bg-gray-700' : '',
                              'block px-4 py-2 text-sm text-gray-700 dark:text-gray-200'
                            )}
                          >
                            Настройки
                          </Link>
                        )}
                      </Menu.Item>
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            onClick={handleLogout}
                            disabled={isLoggingOut}
                            className={classNames(
                              active ? 'bg-gray-100 dark:bg-gray-700' : '',
                              'block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 bg-transparent border-none appearance-none hover:text-blue-700 dark:hover:text-blue-400',
                              isLoggingOut ? 'opacity-50 cursor-not-allowed' : ''
                            )}
                            style={{ outline: 'none', boxShadow: 'none' }}
                            type="button"
                          >
                            <div className="flex items-center justify-start">
                              <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                              {isLoggingOut ? 'Выход...' : 'Выйти'}
                            </div>
                          </button>
                        )}
                      </Menu.Item>
                    </div>
                  </Menu.Items>
                </Transition>
              </Menu>
            ) : (
              <div className="hidden md:flex space-x-2">
                <Link to="/login" className="text-gray-700 dark:text-gray-200 hover:text-blue-700 dark:hover:text-blue-400 px-3 py-2 font-medium">Войти</Link>
                <Link to="/register" className="bg-blue-500 text-white px-3 py-2 rounded hover:bg-blue-600 font-medium">Регистрация</Link>
              </div>
            )}

            {/* Мобильное меню кнопка */}
            <div className="flex items-center md:hidden">
              <button
                ref={buttonRef}
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="burger-button inline-flex items-center justify-center p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-inset transition-all duration-200"
                style={{
                  color: 'var(--button-text)',
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = 'var(--button-hover-text)';
                  e.currentTarget.style.backgroundColor = 'var(--button-hover-bg)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = 'var(--button-text)';
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
                onFocus={(e) => {
                  e.currentTarget.style.boxShadow = `0 0 0 2px var(--button-focus-ring)`;
                }}
                onBlur={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                <span className="sr-only">Открыть меню</span>
                {isMenuOpen ? (
                  <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                ) : (
                  <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

                {/* Мобильное меню */}
      <Transition
        show={isMenuOpen}
        enter="transition ease-out duration-200"
        enterFrom="opacity-0 translate-x-4"
        enterTo="opacity-100 translate-x-0"
        leave="transition ease-in duration-150"
        leaveFrom="opacity-100 translate-x-0"
        leaveTo="opacity-0 translate-x-4"
      >
        <div 
          ref={menuRef}
          className="md:hidden absolute bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 shadow-lg w-auto min-w-[200px]"
          style={{
            top: '100%',
            right: '0',
            transformOrigin: 'top right'
          }}
        >
          <div className="py-2 space-y-2 px-3">
            {/* Поиск для мобильного меню */}
            <Transition
              show={isMenuOpen}
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 -translate-y-2"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 -translate-y-2"
              className="transition-all duration-200 delay-0"
            >
              <div className="px-4 py-2">
                <SearchBar />
              </div>
            </Transition>
            <Transition
              show={isMenuOpen}
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 -translate-y-2"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 -translate-y-2"
              className="transition-all duration-200 delay-50"
            >
              <Link
                to="/"
                className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Главная
              </Link>
            </Transition>
            {isAuthenticated ? (
              <>
                <Transition
                  show={isMenuOpen}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 -translate-y-2"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 -translate-y-2"
                  className="transition-all duration-200 delay-75"
                >
                  <Link
                    to={`/lpu/${user?.username}`}
                    className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Профиль
                  </Link>
                </Transition>
                <Transition
                  show={isMenuOpen}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 -translate-y-2"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 -translate-y-2"
                  className="transition-all duration-200 delay-150"
                >
                  <Link
                    to={`/lpu/${user?.username}/books`}
                    className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Мои книги
                  </Link>
                </Transition>
                <Transition
                  show={isMenuOpen}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 -translate-y-2"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 -translate-y-2"
                  className="transition-all duration-200 delay-200"
                >
                  <Link
                    to={`/lpu/${user?.username}/feed`}
                    className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Лента
                  </Link>
                </Transition>
                <Transition
                  show={isMenuOpen}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 -translate-y-2"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 -translate-y-2"
                  className="transition-all duration-200 delay-250"
                >
                  <Link
                    to={`/lpu/${user?.username}/messages`}
                    className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Сообщения
                  </Link>
                </Transition>
                <Transition
                  show={isMenuOpen}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 -translate-y-2"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 -translate-y-2"
                  className="transition-all duration-200 delay-300"
                >
                  <Link
                    to={`/lpu/${user?.username}/library`}
                    className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Библиотека
                  </Link>
                </Transition>
                <Transition
                  show={isMenuOpen}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 -translate-y-2"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 -translate-y-2"
                  className="transition-all duration-200 delay-325"
                >
                  <Link
                    to={`/lpu/${user?.username}/settings`}
                    className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Настройки
                  </Link>
                </Transition>
                <Transition
                  show={isMenuOpen}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 -translate-y-2"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 -translate-y-2"
                  className="transition-all duration-200 delay-375"
                >
                  <button
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                    type="button"
                  >
                    <div className="flex items-center">
                      <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                      {isLoggingOut ? 'Выход...' : 'Выйти'}
                    </div>
                  </button>
                </Transition>
              </>
            ) : (
              <div className="space-y-2">
                <Link to="/login" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Войти</Link>
                <Link to="/register" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Регистрация</Link>
              </div>
            )}
          </div>
        </div>
      </Transition>
    </nav>
  );
};

// Добавляем глобальные стили для кнопки
const style = document.createElement('style');
style.textContent = `
  .burger-button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }
  .burger-button:active {
    transform: scale(0.95);
  }
`;
document.head.appendChild(style);

const Navbar = () => {
  const { user, avatarVersion } = useAuth();
  
  return (
    <ErrorBoundary>
      <NavbarContent key={`navbar-${user?.id || 'anonymous'}-${avatarVersion}`} />
    </ErrorBoundary>
  );
};

export default Navbar;