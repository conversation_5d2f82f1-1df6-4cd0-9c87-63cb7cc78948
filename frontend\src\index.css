@import './theme/custom-tooltip.css';
@import './theme/tooltip.css';
@import './theme/firefox-fixes.css';
@tailwind base;
@tailwind components;
@tailwind utilities;



:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

/* Стили для отображения эмодзи */
.bio-content {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

.bio-content img.emoji {
  display: inline;
  height: 1.2em;
  width: 1.2em;
  margin: 0 0.05em 0 0.1em;
  vertical-align: -0.1em;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

@layer base {
  body {
    @apply bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
  }
}

input[type="checkbox"].custom-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Ant Design Tooltip: светлая тема — белый фон, тёмно-серый текст */
html.light .ant-tooltip-inner {
  background: #fff !important;
  color: #333 !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}
html.light .ant-tooltip-arrow-content {
  background: #fff !important;
}

/* Унификация внешнего вида модалок Ant Design */
.ant-modal-content {
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}
html.dark .ant-modal-body {
  background: #23272f !important;
  color: #fff !important;
  border-radius: 12px !important;
  padding: 28px !important;
}
html.light .ant-modal-body {
  background: #fff !important;
  color: #222 !important;
  border-radius: 12px !important;
  padding: 28px !important;
}

/* Тёмная тема для Ant Design DatePicker */
html.dark .ant-picker-panel,
html.dark .ant-picker-dropdown,
html.dark .ant-picker,
html.dark .ant-picker-time-panel,
html.dark .ant-picker-header,
html.dark .ant-picker-content th,
html.dark .ant-picker-content td {
  background: #23272f !important;
  color: #fff !important;
  border-color: #374151 !important;
}
html.dark .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner,
html.dark .ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,
html.dark .ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {
  background: #2563eb !important;
  color: #fff !important;
}
html.dark .ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
  background: #2563eb !important;
  color: #fff !important;
}
html.dark .ant-picker-header-view {
  color: #fff !important;
}
html.dark .ant-picker-footer {
  background: #23272f !important;
  color: #fff !important;
}
html.dark .ant-picker-now {
  display: none !important;
}

/* --- Дополнительные стили для тёмной темы Ant Design DatePicker --- */
html.dark .ant-picker-input input::placeholder {
  color: #a1a1aa !important;
  opacity: 1 !important;
}
html.dark .ant-picker-suffix,
html.dark .ant-picker-clear {
  color: #a1a1aa !important;
}
html.dark .ant-picker-header button,
html.dark .ant-picker-super-prev-icon,
html.dark .ant-picker-super-next-icon,
html.dark .ant-picker-prev-icon,
html.dark .ant-picker-next-icon {
  color: #a1a1aa !important;
  fill: #a1a1aa !important;
}
html.dark .ant-picker-cell-disabled .ant-picker-cell-inner,
html.dark .ant-picker-time-panel-cell-disabled .ant-picker-time-panel-cell-inner {
  color: #6b7280 !important;
}
html.dark .ant-picker-content th {
  color: #a1a1aa !important;
}

html.dark .ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner {
  color: #fff !important;
}

/* Активные (выбираемые) часы и минуты — белые, неактивные (disabled) — серые */
html.dark .ant-picker-time-panel-cell-disabled .ant-picker-time-panel-cell-inner {
  color: #6b7280 !important;
}

/* Кастомный скроллбар для списка результатов поиска пользователей */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(156, 163, 175, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Темная тема для скроллбара */
html.dark .custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(75, 85, 99, 0.3);
}

html.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
}

html.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}
html.dark .ant-picker-time-panel-cell:not(.ant-picker-time-panel-cell-disabled) .ant-picker-time-panel-cell-inner {
  color: #fff !important;
}

/* Чекбокс "Выбрать всё"/"Отменить всё выбранное" — всегда светлый фон, при checked — голубой фон, белая галочка */
.select-all-checkbox {
  appearance: none;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid #bfc4cc;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  position: relative;
  transition: background 0.2s, border-color 0.2s;
  vertical-align: middle;
}
.select-all-checkbox:checked {
  background: #1A78F6;
  border-color: #1A78F6;
}
.select-all-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 1px;
  width: 6px;
  height: 12px;
  border: solid #fff;
  border-width: 0 2.5px 2.5px 0;
  transform: rotate(45deg);
}

/* Исправления для совместимости с Firefox */
/* 1. Исправляем text-size-adjust */
html, html.light, html.dark, :root {
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}

/* 2. Исправляем moz-osx-font-smoothing (только для Webkit) */
@supports (-webkit-font-smoothing: antialiased) {
  html, body {
    -webkit-font-smoothing: antialiased;
  }
}

/* 3. Исправляем outline проблемы */
*:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
}

/* 4. Убираем проблемные селекторы для Firefox */
@supports not selector(::-webkit-input-placeholder) {
  input::placeholder,
  textarea::placeholder {
    color: #6b7280;
    opacity: 1;
  }
}

/* Стили для кастомных уведомлений с крестиком */
.ant-message .custom-message-close {
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 4px;
}

.ant-message .custom-message-close:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
  color: #333 !important;
}

html.dark .ant-message .custom-message-close:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
}
