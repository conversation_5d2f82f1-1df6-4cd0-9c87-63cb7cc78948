import React from 'react';
import EmojiPicker from 'emoji-picker-react';

// Экспорт типов и енумов из оригинальной библиотеки
export {
  EmojiStyle,
  Theme,
  SkinTones,
  Categories,
  SuggestionMode,
  SkinTonePickerLocation
} from 'emoji-picker-react';

// Русские переводы для категорий
const russianCategoryNames = {
  'Smileys & People': 'Смайлики и люди',
  'Animals & Nature': 'Животные и природа',
  'Food & Drink': 'Еда и напитки',
  'Travel & Places': 'Путешествия и места',
  'Activities': 'Активности',
  'Objects': 'Объекты',
  'Symbols': 'Символы',
  'Flags': 'Флаги',
  'Recently Used': 'Недавно использованные'
};

// Русифицированный эмодзи пикер
export function RussianEmojiPicker(props) {
  const {
    searchPlaceholder = 'Поиск эмодзи...',
    ...otherProps
  } = props;

  return React.createElement(EmojiPicker, {
    searchPlaceholder,
    ...otherProps
  });
}

// Экспорт основного компонента
export { EmojiPicker };
export default RussianEmojiPicker;
