import moment from 'moment-timezone';

// Ручное сопоставление английских и русских названий месяцев
const MONTH_NAMES = {
  'January': 'января',
  'February': 'февраля', 
  'March': 'марта',
  'April': 'апреля',
  'May': 'мая',
  'June': 'июня',
  'July': 'июля',
  'August': 'августа',
  'September': 'сентября',
  'October': 'октября',
  'November': 'ноября',
  'December': 'декабря'
};

/**
 * Форматирует дату с принудительной заменой на русские названия месяцев
 * @param {string} date - ISO строка даты
 * @param {string} timezone - часовой пояс пользователя
 * @param {string} format - формат даты (по умолчанию 'DD MMMM YYYY г. HH:mm')
 * @returns {string} - отформатированная дата
 */
const formatDateRussian = (date, timezone, format = 'DD MMMM YYYY г. HH:mm') => {
  if (!date) return '';
  
  const momentDate = moment(date).tz(timezone || 'Europe/Moscow');
  
  if (format === 'DD MMMM YYYY г. HH:mm') {
    // Стандартный формат с принудительной заменой месяца
    const day = momentDate.format('DD');
    const monthEnglish = momentDate.format('MMMM');
    const year = momentDate.format('YYYY');
    const time = momentDate.format('HH:mm');
    
    // Заменяем название месяца на русское
    const monthRussian = MONTH_NAMES[monthEnglish] || monthEnglish.toLowerCase();
    
    return `${day} ${monthRussian} ${year} г. ${time}`;
  } else {
    // Для других форматов используем обычное форматирование
    // и заменяем английские названия месяцев на русские
    let formattedDate = momentDate.format(format);
    
    Object.entries(MONTH_NAMES).forEach(([english, russian]) => {
      formattedDate = formattedDate.replace(english, russian);
    });
    
    return formattedDate;
  }
};

/**
 * Форматирует дату для отображения в карточках книг
 * @param {string} dateString - ISO строка даты
 * @param {string} timezone - часовой пояс пользователя
 * @param {boolean} isFinished - завершена ли книга
 * @returns {string} - отформатированная строка даты
 */
export const formatBookStatusDate = (dateString, timezone, isFinished = false) => {
  if (!dateString) return '';
  
  const prefix = isFinished ? '' : 'Обновлено: ';
  const formattedDate = formatDateRussian(dateString, timezone, 'DD MMMM YYYY г. HH:mm');
  
  return `${prefix}${formattedDate}`;
};

/**
 * Форматирует дату публикации для завершенных книг
 * @param {string} publishedAt - дата первой публикации
 * @param {string} timezone - часовой пояс пользователя
 * @returns {string} - отформатированная дата публикации
 */
export const formatPublishedDate = (publishedAt, timezone) => {
  if (!publishedAt) return '';
  
  return formatDateRussian(publishedAt, timezone, 'DD MMMM YYYY г. HH:mm');
};

/**
 * Форматирует дату обновления для книг в процессе публикации
 * @param {string} updatedAt - дата последнего обновления
 * @param {string} timezone - часовой пояс пользователя
 * @returns {string} - отформатированная дата обновления
 */
export const formatUpdatedDate = (updatedAt, timezone) => {
  if (!updatedAt) return '';
  
  return `Обновлено: ${formatDateRussian(updatedAt, timezone, 'DD MMMM YYYY г. HH:mm')}`;
}; 