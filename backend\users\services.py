from .models import UserNotification, Message, FeedEvent, FriendRequest
from .consumers import NotificationConsumer
import asyncio
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import logging

class NotificationService:
    @staticmethod
    def update_unread_messages_count(user):
        """Обновить количество непрочитанных сообщений для пользователя"""
        count = Message.objects.filter(recipient=user, is_read=False).count()
        
        UserNotification.update_notification(
            user=user,
            notification_type='unread_messages',
            data={'count': count}
        )
        
        # Отправляем уведомление через WebSocket
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f'user_{user.id}',
            {
                'type': 'notification_update',
                'notification_type': 'unread_messages',
                'data': {'count': count}
            }
        )
    
    @staticmethod
    def update_feed_events_count(user):
        """Обновить количество событий в ленте для пользователя"""
        events = FeedEvent.objects.filter(user=user, is_read=False)
        
        # Учитываем настройки пользователя
        if not user.show_unsubscribes_in_feed:
            events = events.exclude(event_type='unsubscribed')
        if not user.show_removed_from_friends_in_feed:
            events = events.exclude(event_type='removed_from_friends')
        
        count = events.count()
        
        UserNotification.update_notification(
            user=user,
            notification_type='feed_events',
            data={'count': count}
        )
        
        # Отправляем уведомление через WebSocket
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f'user_{user.id}',
            {
                'type': 'notification_update',
                'notification_type': 'feed_events',
                'data': {'count': count}
            }
        )
    
    @staticmethod
    def update_friend_requests_count(user):
        """Обновить количество запросов в друзья для пользователя"""
        count = FriendRequest.objects.filter(to_user=user, accepted=False).count()
        
        UserNotification.update_notification(
            user=user,
            notification_type='friend_requests',
            data={'count': count}
        )
        
        # Отправляем уведомление через WebSocket
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f'user_{user.id}',
            {
                'type': 'notification_update',
                'notification_type': 'friend_requests',
                'data': {'count': count}
            }
        )
    
    @staticmethod
    def update_all_notifications(user):
        """Обновить все уведомления для пользователя"""
        NotificationService.update_unread_messages_count(user)
        NotificationService.update_feed_events_count(user)
        NotificationService.update_friend_requests_count(user)
    
    @staticmethod
    def get_user_notifications(user):
        """Получить все уведомления пользователя"""
        notifications = UserNotification.objects.filter(user=user)
        return {
            notification.notification_type: notification.data
            for notification in notifications
        } 