# Generated by Django 5.0.2 on 2025-07-12 14:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('users', '0046_user_is_deleted_deleted_at'),
    ]

    operations = [
        migrations.CreateModel(
            name='RatingHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('metric_name', models.CharField(choices=[('reader_rating', 'Читательский рейтинг'), ('author_rating', 'Авторский рейтинг')], max_length=20)),
                ('action_type', models.CharField(choices=[('welcome_bonus', 'Приветственный бонус'), ('comment_added', 'Комментарий к книге'), ('reply_to_comment_added', 'Ответ на комментарий'), ('reply_to_reply_added', 'Ответ на ответ'), ('comment_like_received', 'Получен лайк к комментарию'), ('book_purchased', 'Покупка книги'), ('reading_session', 'Сессия чтения'), ('award_given', 'Выдача награды'), ('comment_received', 'Получен комментарий к книге'), ('book_like_received', 'Получен лайк к книге'), ('book_sold', 'Продажа книги'), ('award_received', 'Получена награда'), ('blog_post_published', 'Опубликован пост в блоге'), ('book_status_changed', 'Изменение статуса книги'), ('comment_removed', 'Удален комментарий'), ('comment_like_revoked', 'Отозван лайк к комментарию'), ('comment_received_revoked', 'Отозван комментарий к книге')], max_length=30)),
                ('change_delta', models.IntegerField(help_text='Изменение рейтинга (может быть отрицательным)')),
                ('old_value', models.IntegerField(help_text='Значение до изменения')),
                ('new_value', models.IntegerField(help_text='Значение после изменения')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(default='system', help_text='Кто создал запись', max_length=20)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rating_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'metric_name', '-created_at'], name='users_ratin_user_id_304391_idx'), models.Index(fields=['user', '-created_at'], name='users_ratin_user_id_0eb478_idx')],
            },
        ),
    ]
