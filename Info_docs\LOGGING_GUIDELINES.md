# Руководство по логированию

## Принципы логирования

### ✅ Что логировать в продакшене:

1. **Ошибки и исключения**
   - Все исключения с полным стектрейсом
   - Ошибки аутентификации
   - Ошибки WebSocket соединений

2. **Важные бизнес-события**
   - Создание/удаление пользователей
   - Публикация/удаление книг
   - Отправка сообщений
   - Дружба/подписки

3. **Метрики производительности**
   - Время выполнения запросов
   - Количество активных WebSocket соединений
   - Использование памяти/CPU

4. **Безопасность**
   - Попытки неавторизованного доступа
   - Подозрительная активность
   - Изменения прав доступа

### ❌ Что НЕ логировать в продакшене:

1. **Отладочная информация**
   - `print()` statements
   - Подробные логи каждого HTTP запроса
   - Логи каждого WebSocket подключения
   - Внутренние данные пользователей

2. **Чувствительная информация**
   - Пароли
   - Персональные данные
   - Токены доступа

## Уровни логирования

- **DEBUG** - только для разработки
- **INFO** - общая информация о работе приложения
- **WARNING** - предупреждения, которые не критичны
- **ERROR** - ошибки, требующие внимания
- **CRITICAL** - критические ошибки

## Настройки по окружениям

### Разработка (DEBUG=True)
```python
'console': {
    'level': 'DEBUG',
    'class': 'logging.StreamHandler',
    'formatter': 'simple',
},
```

### Продакшен (DEBUG=False)
```python
'console': {
    'level': 'INFO',
    'class': 'logging.StreamHandler',
    'formatter': 'simple',
},
'file': {
    'level': 'INFO',
    'class': 'logging.FileHandler',
    'filename': 'logs/django.log',
    'formatter': 'verbose',
},
```

## Примеры правильного логирования

### ✅ Хорошо:
```python
logger.info(f"User {user.username} published book {book.title}")
logger.warning(f"Failed to send notification to user {user_id}")
logger.error(f"Database connection failed: {error}")
```

### ❌ Плохо:
```python
print(f"User {user.username} published book {book.title}")  # print вместо logger
logger.info(f"User password: {password}")  # чувствительные данные
logger.debug("Every single request")  # слишком много логов
```

## Мониторинг логов

### В продакшене рекомендуется:

1. **Ротация логов**
   - Автоматическое архивирование старых логов
   - Ограничение размера файлов логов

2. **Агрегация логов**
   - Централизованный сбор логов (ELK Stack, Graylog)
   - Алерты на критические ошибки

3. **Метрики**
   - Количество ошибок в единицу времени
   - Время отклика API
   - Количество активных пользователей

## Текущие настройки

В проекте настроено:
- Логирование в файл `logs/django.log`
- Консольный вывод для разработки
- Разные уровни для разных приложений
- Структурированные сообщения

## Рекомендации для команды

1. **Всегда используйте `logger` вместо `print`**
2. **Логируйте контекст ошибок**
3. **Не логируйте чувствительные данные**
4. **Используйте правильные уровни логирования**
5. **Добавляйте логи для важных бизнес-событий** 