#!/usr/bin/env python
"""
Скрипт для обновления возрастного рейтинга существующих книг.
Устанавливает рейтинг 0+ для всех книг, у которых он не установлен.
"""

import os
import sys
import django

# Настройка Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from books.models import Book

def update_existing_books():
    """Обновляет возрастной рейтинг для существующих книг"""
    
    # Находим все книги без установленного возрастного рейтинга или с пустым рейтингом
    from django.db import models
    books_to_update = Book.objects.filter(
        models.Q(age_rating__isnull=True) |
        models.Q(age_rating='') |
        models.Q(age_rating__exact='')
    )
    
    print(f"Найдено книг для обновления: {books_to_update.count()}")
    
    if books_to_update.count() == 0:
        print("Все книги уже имеют установленный возрастной рейтинг.")
        return
    
    # Обновляем книги пакетно
    updated_count = books_to_update.update(
        age_rating='0+',
        has_profanity=False
    )
    
    print(f"Обновлено книг: {updated_count}")
    
    # Проверяем результат
    remaining_books = Book.objects.filter(
        models.Q(age_rating__isnull=True) | 
        models.Q(age_rating='') | 
        models.Q(age_rating__exact='')
    ).count()
    
    print(f"Книг без возрастного рейтинга осталось: {remaining_books}")
    
    if remaining_books == 0:
        print("✅ Все книги успешно обновлены!")
    else:
        print("⚠️ Некоторые книги не были обновлены. Проверьте логи.")

if __name__ == '__main__':
    try:
        from django.db import models
        update_existing_books()
    except Exception as e:
        print(f"❌ Ошибка при обновлении книг: {e}")
        sys.exit(1)
