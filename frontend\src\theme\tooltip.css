/* Кастомные стили для тултипов */
.tooltip-enter {
  opacity: 0;
  transform: scale(0.95);
}

.tooltip-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 150ms ease-out, transform 150ms ease-out;
}

.tooltip-exit {
  opacity: 1;
  transform: scale(1);
}

.tooltip-exit-active {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 100ms ease-in, transform 100ms ease-in;
}

/* Улучшенный z-index для тултипов */
.tooltip-container {
  z-index: 9999;
}

/* Стили для темной и светлой темы */
.tooltip-dark {
  background-color: rgba(17, 24, 39, 0.95);
  color: white;
  border: 1px solid rgba(75, 85, 99, 0.3);
  text-shadow: none !important; /* Убираем любые унаследованные тени */
}

.tooltip-light {
  background-color: rgba(255, 255, 255, 0.95);
  color: rgba(17, 24, 39, 1);
  border: 1px solid rgba(209, 213, 219, 0.5);
  text-shadow: none !important; /* Убираем любые унаследованные тени */
}

/* Анимация появления */
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-2px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.tooltip-animated {
  animation: tooltipFadeIn 150ms ease-out;
} 