import { getUserAvatar } from './avatar';

// Кэш для аватаров пользователей
const avatarCache = new Map();
// Кэш для последних вызовов функции (мемоизация)
const memoCache = new Map();

/**
 * Получает URL аватара пользователя с кэшированием и мемоизацией
 * @param {Object} user - Объект пользователя
 * @param {string} size - Размер аватара ('mini' или 'full')
 * @param {string} backendUrl - URL бэкенда
 * @param {number} imageVersion - Версия изображения
 * @param {boolean} forceFull - Принудительно использовать полный аватар
 * @returns {string} URL аватара
 */
export const getCachedUserAvatar = (user, size = "mini", backendUrl = '', imageVersion, forceFull = false) => {
    if (!user) return null;

    // Создаем ключ для мемоизации основанный на параметрах
    const memoKey = JSON.stringify({
        userId: user.id || user.username,
        size,
        avatarType: user.avatar_type,
        avatarUpdatedAt: user.avatar_updated_at,
        displayName: user.display_name, // Добавляем display_name для учета удаленных пользователей
        imageVersion,
        forceFull
    });
    
    // Проверяем кэш мемоизации
    if (memoCache.has(memoKey)) {
        return memoCache.get(memoKey);
    }

    // Создаем ключ основного кэша
    const cacheKey = `${user.id || user.username}_${size}_${user.avatar_type || 0}_${user.avatar_updated_at || 0}_${user.display_name || ''}_${imageVersion || 0}_${forceFull}`;
    
    // Проверяем основной кэш
    if (avatarCache.has(cacheKey)) {
        const result = avatarCache.get(cacheKey);
        memoCache.set(memoKey, result);
        return result;
    }

    // Вычисляем URL аватара
    const avatarUrl = getUserAvatar(user, size, backendUrl, imageVersion, forceFull);
    
    // Проверяем что URL валидный (не null, не пустая строка, не undefined)
    const validUrl = avatarUrl && typeof avatarUrl === 'string' && avatarUrl.trim() !== '' ? avatarUrl : null;
    
    // Сохраняем в оба кэша
    avatarCache.set(cacheKey, validUrl);
    memoCache.set(memoKey, validUrl);
    
    // Ограничиваем размер кэшей
    if (avatarCache.size > 1000) {
        const firstKey = avatarCache.keys().next().value;
        avatarCache.delete(firstKey);
    }
    
    if (memoCache.size > 500) {
        const firstKey = memoCache.keys().next().value;
        memoCache.delete(firstKey);
    }
    
    return validUrl;
};

/**
 * Очищает все кэши аватаров
 */
export const clearAvatarCache = () => {
    avatarCache.clear();
    memoCache.clear();
};

/**
 * Удаляет из кэша аватары конкретного пользователя
 * @param {number|string} userId - ID или username пользователя
 */
export const clearUserAvatarCache = (userId) => {
    // Очищаем основной кэш
    const keysToDelete = [];
    for (const key of avatarCache.keys()) {
        if (key.startsWith(`${userId}_`)) {
            keysToDelete.push(key);
        }
    }
    keysToDelete.forEach(key => avatarCache.delete(key));

    // Очищаем кэш мемоизации
    const memoKeysToDelete = [];
    for (const [key, value] of memoCache.entries()) {
        try {
            const parsed = JSON.parse(key);
            if (parsed.userId === userId) {
                memoKeysToDelete.push(key);
            }
        } catch (e) {
            // Игнорируем некорректные ключи
        }
    }
    memoKeysToDelete.forEach(key => memoCache.delete(key));
};

/**
 * Принудительно обновляет кэш аватаров для пользователя
 * Полезно при изменении статуса пользователя (например, при удалении)
 * @param {Object} user - Объект пользователя
 */
export const refreshUserAvatarCache = (user) => {
    if (!user) return;

    const userId = user.id || user.username;
    clearUserAvatarCache(userId);

    // Предварительно загружаем новые аватары
    getCachedUserAvatar(user, 'mini');
    getCachedUserAvatar(user, 'full');
};