# Система запрещенных логинов

## Описание

Реализована система защиты от создания логинов, содержащих определенные запрещенные слова. Это предотвращает создание логинов типа `admin`, `moderator`, `director` и других, которые могут вводить пользователей в заблуждение.

## Запрещенные слова

Следующие слова запрещены к использованию в логинах (регистр не учитывается):

- `admin`
- `administrator` 
- `moderator`
- `login`
- `boss`
- `director`
- `litportal`

## Принцип работы

### Фронтенд
- **Проверка в реальном времени**: При вводе логина на форме регистрации происходит мгновенная проверка
- **Локальная проверка**: Первичная проверка происходит на фронтенде для быстрого отклика
- **API проверка**: Дополнительная проверка через API endpoint `/api/auth/check-username/`

### Бэкенд
- **CheckUsernameView**: API endpoint проверяет запрещенные слова и возвращает соответствующий ответ
- **RegisterView**: Блокирует регистрацию пользователей с запрещенными логинами
- **Метод `_check_forbidden_username()`**: Проверяет наличие запрещенных слов в логине

## Примеры запрещенных логинов

✅ **Разрешенные логины:**
- `user123`
- `testuser`
- `author`
- `writer`
- `reader`

❌ **Запрещенные логины:**
- `admin`
- `admin123`
- `myadmin`
- `administrator`
- `moderator`
- `director123`
- `litportal`

## Создание администраторских логинов

Для создания пользователей с запрещенными логинами (в административных целях) используйте специальный скрипт:

```bash
python create_admin_user.py <username> <email> <display_name> [password]
```

**Примеры:**
```bash
# С указанным паролем
python create_admin_user.<NAME_EMAIL> "Администратор" mypassword123

# С автогенерацией пароля
python create_admin_user.<NAME_EMAIL> "Модератор"
```

**Особенности:**
- Скрипт запросит подтверждение при создании логина с запрещенными словами
- Автоматически генерирует пароль если не указан
- Создает пользователя в обход всех проверок
- Показывает данные для входа после создания

## Технические детали

### API Responses

**Запрещенный логин:**
```json
{
  "exists": true,
  "forbidden": true,
  "message": "Данный логин содержит запрещенные слова"
}
```

**Разрешенный свободный логин:**
```json
{
  "exists": false,
  "forbidden": false
}
```

**Разрешенный занятый логин:**
```json
{
  "exists": true,
  "forbidden": false
}
```

### Ошибки регистрации

При попытке зарегистрироваться с запрещенным логином через API:
```json
{
  "username": ["Данный логин содержит запрещенные слова"]
}
```

## Файлы системы

### Фронтенд
- `frontend/src/pages/Register.jsx` - форма регистрации с проверкой

### Бэкенд  
- `backend/users/views.py` - CheckUsernameView и RegisterView
- `backend/create_admin_user.py` - скрипт для создания админских логинов

## Расширение системы

Для добавления новых запрещенных слов:

1. **Фронтенд**: Обновить массив `forbiddenWords` в `Register.jsx`
2. **Бэкенд**: Обновить массив `FORBIDDEN_WORDS` в `CheckUsernameView` и `RegisterView`
3. **Скрипт**: Обновить массив `forbidden_words` в `create_admin_user.py`

## Безопасность

- Проверка работает как на фронтенде, так и на бэкенде
- Нельзя обойти ограничения через прямые API запросы
- Администратор может создавать исключения через специальный скрипт
- Все созданные через скрипт пользователи логируются с пометкой обхода проверок 