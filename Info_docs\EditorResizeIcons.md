# Функция изменения размеров значков в панели редактора NovelEditor

## 📋 Описание

Реализована функция изменения размеров всех значков в панели редактора глав NovelEditor. Пользователи могут настраивать размер значков вручную или использовать автоматический режим, который адаптируется к ширине панели инструментов.

## ✨ Возможности

### 🎛️ Режимы размера значков
- **Маленький**: 12px - компактный размер для узких экранов
- **Средний**: 14px - стандартный размер (по умолчанию)
- **Большой**: 16px - увеличенный размер для лучшей видимости
- **Авто**: автоматический расчет на основе ширины панели
  - Ширина < 600px → 12px
  - Ширина 600-900px → 14px
  - Ширина > 900px → 16px
- **Пользовательский**: ручной ввод размера от 10 до 32 пикселей

### 💾 Сохранение настроек
- Настройки автоматически сохраняются в localStorage
- Восстанавливаются при следующем открытии редактора
- Работают для каждого пользователя индивидуально

### 🎨 Адаптивность
- Автоматическое обновление размеров при изменении ширины окна (в режиме "Авто")
- Корректная работа в светлой и темной темах
- Применяется ко всем типам значков в панели

## 🚀 Как использовать

### Шаг 1: Открытие настроек
1. Откройте редактор главы в NovelEditor
2. В панели инструментов найдите кнопку с иконкой шестеренки ⚙️
3. Нажмите на неё для открытия окна настроек

### Шаг 2: Выбор размера
1. В открывшемся модальном окне выберите нужный режим:
   - Выберите один из предустановленных размеров (Маленький/Средний/Большой)
   - Или выберите "Авто" для автоматической адаптации
   - Или выберите "Пользовательский" для ручного ввода

### Шаг 3: Настройка пользовательского размера (опционально)
1. При выборе "Пользовательский" появится поле ввода
2. Введите желаемый размер от 10 до 32 пикселей
3. Размер применится автоматически

### Шаг 4: Закрытие настроек
1. Нажмите "Закрыть" для сохранения настроек
2. Размеры значков изменятся мгновенно
3. Настройки сохранятся для следующих сессий

## 🔧 Технические детали

### Затронутые компоненты
- `ChapterEditorV2.jsx` - основной компонент редактора
- Все значки панели инструментов обновлены для поддержки динамических размеров

### Типы значков, которые изменяются
- **Текстовые кнопки**: Ж (жирный), К (курсив), П (подчёркнутый), З (зачёркнутый), ¶ (символы параграфа)
- **SVG иконки**: отмена, повтор, выравнивание текста, ссылки
- **Ant Design иконки**: изображение, полноэкранный режим, настройки
- **Компонент выбора цвета**: текст "Текст"/"Фон", цветовые квадратики, палитра цветов, описания

### Хранение данных
- `editor-icon-size-mode` - текущий режим размера (localStorage)
- `editor-custom-icon-size` - пользовательский размер в пикселях (localStorage)

## 🎯 Преимущества

1. **Персонализация**: каждый пользователь может настроить удобный размер
2. **Адаптивность**: автоматическая адаптация к размеру экрана
3. **Доступность**: улучшенная видимость для пользователей с ограниченным зрением
4. **Удобство**: простой и интуитивный интерфейс настроек
5. **Сохранение**: настройки не теряются между сессиями

## 🔍 Примеры использования

### Для мобильных устройств
Выберите "Маленький" размер или "Авто" для оптимального отображения на узких экранах.

### Для больших мониторов
Используйте "Большой" размер или "Авто" для лучшей видимости на широких экранах.

### Для пользователей с ограниченным зрением
Установите "Пользовательский" размер 18-24px для увеличенных значков.

### Для адаптивной работы
Выберите режим "Авто" - размер будет автоматически подстраиваться под ширину панели инструментов.

---

*Функция полностью интегрирована в существующий интерфейс и готова к использованию.*
