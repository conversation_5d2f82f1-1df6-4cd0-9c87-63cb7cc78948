import React, { useState, useEffect } from 'react';
import { Button, Dropdown, message } from 'antd';
import { 
  PlusOutlined, 
  ReadOutlined, 
  ClockCircleOutlined, 
  CheckCircleOutlined,
  DownOutlined 
} from '@ant-design/icons';
import { useAuth } from '../context/AuthContext';
import { csrfFetch } from '../utils/csrf';

const LibraryButtons = ({ book, size = 'small', style = {} }) => {
  const { user } = useAuth();
  const [libraryStatus, setLibraryStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  
  // Проверяем, есть ли книга в библиотеке при монтировании
  useEffect(() => {
    if (user && book) {
      checkLibraryStatus();
    }
  }, [user, book]);
  
  const checkLibraryStatus = async () => {
    try {
      const response = await csrfFetch(`/api/check-library-status/${book.id}/`, {
        credentials: 'include'
      });
      
      if (response.ok) {
        const data = await response.json();
        setLibraryStatus(data.in_library ? data.status : null);
      }
    } catch (error) {
      console.error('Error checking library status:', error);
    }
  };
  
  const addToLibrary = async (status) => {
    if (!user) {
      message.warning('Войдите в аккаунт для добавления в библиотеку');
      return;
    }
    

    
    setLoading(true);
    
    try {
      const response = await csrfFetch('/api/quick-add-to-library/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          book_id: book.id,
          status: status
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        setLibraryStatus(status);
        message.success(data.message);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Ошибка добавления в библиотеку');
      }
    } catch (error) {
      console.error('Error adding to library:', error);
      message.error(error.message || 'Ошибка добавления в библиотеку');
    } finally {
      setLoading(false);
    }
  };
  
  // Если пользователь не авторизован
  if (!user) {
    return null;
  }
  
  const statusLabels = {
    reading: 'Читаю',
    want_to_read: 'Отложено',
    read: 'Прочитано'
  };
  
  const statusIcons = {
    reading: <ReadOutlined />,
    want_to_read: <ClockCircleOutlined />,
    read: <CheckCircleOutlined />
  };
  
  const statusColors = {
    reading: 'primary',
    want_to_read: 'default',
    read: 'success'
  };
  
  // Если книга уже в библиотеке, показываем текущий статус с возможностью изменения
  if (libraryStatus) {
    const dropdownItems = [
      {
        key: 'reading',
        label: 'Читаю',
        icon: <ReadOutlined />,
        onClick: () => addToLibrary('reading')
      },
      {
        key: 'want_to_read',
        label: 'Отложено',
        icon: <ClockCircleOutlined />,
        onClick: () => addToLibrary('want_to_read')
      },
      {
        key: 'read',
        label: 'Прочитано',
        icon: <CheckCircleOutlined />,
        onClick: () => addToLibrary('read')
      }
    ].filter(item => item.key !== libraryStatus);
    
    return (
      <Dropdown
        menu={{ items: dropdownItems }}
        trigger={['click']}
        disabled={loading}
      >
        <Button
          type={statusColors[libraryStatus]}
          size={size}
          style={{
            ...style,
            width: 'auto',
            minWidth: 'fit-content',
            padding: '4px 8px',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          loading={loading}
        >
          {statusIcons[libraryStatus]} {statusLabels[libraryStatus]} <DownOutlined />
        </Button>
      </Dropdown>
    );
  }
  
  // Если книги нет в библиотеке, показываем кнопки добавления
  const addItems = [
    {
      key: 'reading',
      label: 'Читаю',
      icon: <ReadOutlined />,
      onClick: () => addToLibrary('reading')
    },
    {
      key: 'want_to_read',
      label: 'Отложено',
      icon: <ClockCircleOutlined />,
      onClick: () => addToLibrary('want_to_read')
    },
    {
      key: 'read',
      label: 'Прочитано',
      icon: <CheckCircleOutlined />,
      onClick: () => addToLibrary('read')
    }
  ];
  
  return (
    <Dropdown
      menu={{ items: addItems }}
      trigger={['click']}
      disabled={loading}
    >
      <Button
        type="default"
        size={size}
        style={{
          ...style,
          width: 'auto',
          minWidth: 'fit-content',
          padding: '4px 8px',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
        loading={loading}
        icon={<PlusOutlined />}
      >
        В библиотеку <DownOutlined />
      </Button>
    </Dropdown>
  );
};

export default LibraryButtons; 