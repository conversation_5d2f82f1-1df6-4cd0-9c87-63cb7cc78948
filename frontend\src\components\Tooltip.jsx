import React, { useState } from 'react';
import { useTheme } from '../theme/ThemeContext';
import '../theme/tooltip.css';

const Tooltip = ({ 
  children, 
  text, 
  position = 'top', 
  delay = 300,
  className = '',
  disabled = false
}) => {
  const { theme } = useTheme ? useTheme() : { theme: 'light' };
  const [isVisible, setIsVisible] = useState(false);
  const [timeoutId, setTimeoutId] = useState(null);

  const showTooltip = () => {
    if (disabled) return;
    
    const id = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    setTimeoutId(id);
  };

  const hideTooltip = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsVisible(false);
  };

  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
  };

  // Проверяем, является ли текст JSX элементом (для адаптивной ширины)
  const isJSXContent = React.isValidElement(text);

  const getArrowClasses = (position) => {
    const baseClasses = 'absolute w-0 h-0 border-4';
    const borderColor = theme === 'dark' 
      ? 'border-gray-800' 
      : 'border-gray-100';
    
    const positions = {
      top: `top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent ${borderColor.replace('border-', 'border-t-')}`,
      bottom: `bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent ${borderColor.replace('border-', 'border-b-')}`,
      left: `left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent ${borderColor.replace('border-', 'border-l-')}`,
      right: `right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent ${borderColor.replace('border-', 'border-r-')}`
    };
    
    return `${baseClasses} ${positions[position]}`;
  };

  if (disabled) {
    return children;
  }

  return (
    <div 
      className={`relative inline-block ${className}`}
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      onFocus={showTooltip}
      onBlur={hideTooltip}
    >
      {children}
      
      {isVisible && text && (
        <div className={`absolute tooltip-container ${positionClasses[position]} ${isJSXContent ? 'z-50' : ''}`}>
          <div className={`px-3 py-2 text-sm font-medium rounded-lg shadow-lg tooltip-animated backdrop-blur-sm ${
            theme === 'dark'
              ? 'tooltip-dark'
              : 'tooltip-light'
          } ${!isJSXContent ? 'whitespace-nowrap' : 'w-max'}`}>
            {text}
            {/* Стрелка */}
            <div className={getArrowClasses(position)}></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Tooltip; 