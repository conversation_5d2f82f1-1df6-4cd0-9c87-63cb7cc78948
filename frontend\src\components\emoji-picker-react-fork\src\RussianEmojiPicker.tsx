import React from 'react';
import EmojiPicker from './EmojiPickerReact';
import { PickerConfig } from './config/config';
import { Locale } from './config/categoryConfig';

export interface RussianEmojiPickerProps extends Omit<PickerConfig, 'locale'> {
  locale?: Locale;
}

/**
 * Русифицированный эмодзи пикер на основе emoji-picker-react
 * Автоматически устанавливает русскую локализацию по умолчанию
 */
const RussianEmojiPicker: React.FC<RussianEmojiPickerProps> = (props) => {
  const { locale = 'ru', ...otherProps } = props;

  return (
    <EmojiPicker
      {...otherProps}
      locale={locale}
    />
  );
};

export default RussianEmojiPicker;
