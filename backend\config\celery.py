import os
from celery import Celery
from celery.schedules import crontab

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

app = Celery('litportal')
app.config_from_object('django.conf:settings', namespace='CELERY')
app.autodiscover_tasks()

# Конфигурация периодических задач
app.conf.beat_schedule = {
    'cleanup-abandoned-books': {
        'task': 'books.tasks.cleanup_abandoned_books',
        'schedule': crontab(hour=3, minute=0, day_of_week=1),  # Каждый понедельник в 3:00
    },
}
app.conf.timezone = 'UTC'