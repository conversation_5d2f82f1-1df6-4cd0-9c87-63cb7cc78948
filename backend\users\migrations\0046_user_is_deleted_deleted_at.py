# Generated manually for user deletion fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0045_user_timezone_display_value'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='is_deleted',
            field=models.BooleanField(default=False, help_text='Помечает аккаунт как удаленный (мягкое удаление)', verbose_name='Аккаунт удален'),
        ),
        migrations.AddField(
            model_name='user',
            name='deleted_at',
            field=models.DateTimeField(blank=True, help_text='Когда аккаунт был удален', null=True, verbose_name='Дата удаления'),
        ),
    ]
