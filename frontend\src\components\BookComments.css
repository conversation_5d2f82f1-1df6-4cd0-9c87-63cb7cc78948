/* Стили для кнопок комментариев */

/* Лайк кнопка - зеленый цвет при активном состоянии */
.comment-like-button.liked {
  color: #16a34a !important;
}

.comment-like-button.liked:hover {
  color: #15803d !important;
}

.comment-like-button:not(.liked):hover {
  color: #16a34a !important;
}

/* Дизлайк кнопка - красный цвет при активном состоянии */
.comment-dislike-button.disliked {
  color: #dc2626 !important;
}

.comment-dislike-button.disliked:hover {
  color: #b91c1c !important;
}

.comment-dislike-button:not(.disliked):hover {
  color: #dc2626 !important;
}

/* Кнопка ответить - синий цвет при наведении */
.comment-reply-button:hover {
  color: #2563eb !important;
}

/* Кнопка редактировать - синий цвет при наведении */
.comment-edit-button:hover {
  color: #2563eb !important;
}

/* Кнопка удалить - красный цвет при наведении */
.comment-delete-button:hover {
  color: #dc2626 !important;
}

/* Анимация для плавного появления формы ответа */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.comment-reply-form {
  animation: fadeIn 0.3s ease-out;
}

/* Темная тема */
.dark .comment-like-button.liked {
  color: #4ade80 !important;
}

.dark .comment-like-button.liked:hover {
  color: #22c55e !important;
}

.dark .comment-like-button:not(.liked):hover {
  color: #4ade80 !important;
}

.dark .comment-dislike-button.disliked {
  color: #f87171 !important;
}

.dark .comment-dislike-button.disliked:hover {
  color: #ef4444 !important;
}

.dark .comment-dislike-button:not(.disliked):hover {
  color: #f87171 !important;
}

.dark .comment-reply-button:hover {
  color: #60a5fa !important;
}

.dark .comment-edit-button:hover {
  color: #60a5fa !important;
}

.dark .comment-delete-button:hover {
  color: #f87171 !important;
}

:root {
  --reply-close-top: 39%;
  --reply-close-right: -32px;
  --reply-counter-bottom: 6px;
  --reply-counter-right: 0px;
  --main-comment-counter-top: -26px;
  --main-comment-counter-right: 0px;
}

.comment-reply-close {
  position: absolute;
  top: var(--reply-close-top);
  right: var(--reply-close-right);
  z-index: 20;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  transform: translateY(-50%);
  color: #6b7280;
  background: #fff;
  box-shadow: 0 0 0 2px #e5e7eb;
  border: none;
  transition: color 0.15s, background 0.15s;
}

.comment-reply-close:hover {
  color: #dc2626 !important; /* красный */
  background: #fef2f2;
}

.comment-reply-close:active {
  color: #b91c1c !important; /* ярко-красный */
  background: #fee2e2;
}

.dark .comment-reply-close {
  color: #9ca3af;
  background: transparent;
  box-shadow: 0 0 0 2px #374151;
}

.dark .comment-reply-close:hover {
  color: #f87171 !important;
  background: rgba(220,38,38,0.08);
}

.dark .comment-reply-close:active {
  color: #ef4444 !important;
  background: rgba(220,38,38,0.18);
}

.comment-reply-counter {
  position: absolute;
  bottom: var(--reply-counter-bottom);
  right: var(--reply-counter-right);
  font-size: 12px;
  z-index: 10;
  padding: 2px 8px;
  border-radius: 6px;
  background: #f3f4f6;
  color: #6b7280;
  transition: background 0.15s, color 0.15s;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

.dark .comment-reply-counter {
  background: #374151;
  color: #d1d5db;
  box-shadow: 0 1px 2px rgba(0,0,0,0.12);
}

.main-comment-counter {
  position: absolute;
  top: var(--main-comment-counter-top);
  right: var(--main-comment-counter-right);
  font-size: 12px;
  z-index: 10;
  padding: 2px 8px;
  border-radius: 6px;
  background: #f3f4f6;
  color: #6b7280;
  transition: background 0.15s, color 0.15s, opacity 0.2s;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
  backdrop-filter: blur(4px);
}

.dark .main-comment-counter {
  background: #374151;
  color: #d1d5db;
  box-shadow: 0 1px 2px rgba(0,0,0,0.12);
}

.reply-mention {
  display: inline;
  font-style: italic;
  font-weight: 500;
  color: #2563eb;
  cursor: pointer;
  font-size: inherit;
  transition: color 0.2s ease;
  text-decoration: none;
}

.reply-mention:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.reply-mention:active {
  color: #1e40af;
}

.dark .reply-mention {
  color: #60a5fa;
}

.dark .reply-mention:hover {
  color: #93c5fd;
  text-decoration: underline;
}

.dark .reply-mention:active {
  color: #bfdbfe;
}

/* Стильная подсветка для @mention ссылок */
.highlight-reply {
  position: relative;
  animation: highlight-reply-pulse 2s ease-out;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #dbeafe 100%) !important;
  border-radius: 8px !important;
  box-shadow: 
    0 0 0 2px #60a5fa,
    0 4px 20px rgba(96, 165, 250, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
  transform: scale(1.02);
  transition: all 0.3s ease;
  display: inline-block !important;
  padding: 4px 8px !important;
  margin: -4px -8px !important;
  max-width: fit-content !important;
}

.dark .highlight-reply {
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.12) 0%, rgba(96, 165, 250, 0.2) 50%, rgba(96, 165, 250, 0.12) 100%) !important;
  box-shadow: 
    0 0 0 2px #3b82f6,
    0 4px 20px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(96, 165, 250, 0.1) !important;
}

@keyframes highlight-reply-pulse {
  0% { 
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #dbeafe 100%);
    box-shadow: 
      0 0 0 2px #60a5fa,
      0 4px 20px rgba(96, 165, 250, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
  }
  15% { 
    background: linear-gradient(135deg, #bfdbfe 0%, #3b82f6 50%, #bfdbfe 100%);
    box-shadow: 
      0 0 0 3px #3b82f6,
      0 6px 25px rgba(59, 130, 246, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transform: scale(1.03);
  }
  30% { 
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #dbeafe 100%);
    box-shadow: 
      0 0 0 2px #60a5fa,
      0 4px 20px rgba(96, 165, 250, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
  }
  85% { 
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #dbeafe 100%);
    box-shadow: 
      0 0 0 2px #60a5fa,
      0 4px 20px rgba(96, 165, 250, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
  }
  100% { 
    background: transparent;
    box-shadow: none;
    transform: scale(1);
  }
}

@keyframes highlight-reply-pulse-dark {
  0% { 
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.12) 0%, rgba(96, 165, 250, 0.2) 50%, rgba(96, 165, 250, 0.12) 100%);
    box-shadow: 
      0 0 0 2px #3b82f6,
      0 4px 20px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(96, 165, 250, 0.1);
    transform: scale(1.02);
  }
  15% { 
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.2) 0%, rgba(59, 130, 246, 0.35) 50%, rgba(96, 165, 250, 0.2) 100%);
    box-shadow: 
      0 0 0 3px #2563eb,
      0 6px 25px rgba(37, 99, 235, 0.3),
      inset 0 1px 0 rgba(96, 165, 250, 0.15);
    transform: scale(1.03);
  }
  30% { 
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.12) 0%, rgba(96, 165, 250, 0.2) 50%, rgba(96, 165, 250, 0.12) 100%);
    box-shadow: 
      0 0 0 2px #3b82f6,
      0 4px 20px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(96, 165, 250, 0.1);
    transform: scale(1.02);
  }
  85% { 
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.12) 0%, rgba(96, 165, 250, 0.2) 50%, rgba(96, 165, 250, 0.12) 100%);
    box-shadow: 
      0 0 0 2px #3b82f6,
      0 4px 20px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(96, 165, 250, 0.1);
    transform: scale(1.02);
  }
  100% { 
    background: transparent;
    box-shadow: none;
    transform: scale(1);
  }
}

.dark .highlight-reply {
  animation: highlight-reply-pulse-dark 2s ease-out;
}

/* Стили для модального окна удаления в темной теме */
.dark-modal-root .ant-modal .ant-modal-content {
  background: #374151 !important;
  border: 1px solid #4b5563 !important;
}

.dark-modal-root .ant-modal .ant-modal-header {
  background: #374151 !important;
  border-bottom: 1px solid #4b5563 !important;
}

.dark-modal-root .ant-modal .ant-modal-title,
.dark-modal-root .ant-modal-title {
  color: #ffffff !important;
}

.dark-modal-root .ant-modal .ant-modal-body,
.dark-modal-root .ant-modal-body,
.dark-modal-root .ant-modal-body p,
.dark-modal-root .ant-modal-body div,
.dark-modal-root .ant-modal-confirm-body {
  background: #374151 !important;
  color: #ffffff !important;
}

.dark-modal-root .ant-modal-confirm-title {
  color: #ffffff !important;
}

.dark-modal-root .ant-modal-confirm-content {
  color: #ffffff !important;
}

.dark-modal-root .ant-modal .ant-modal-footer {
  background: #374151 !important;
  border-top: 1px solid #4b5563 !important;
}

.dark-modal-root .ant-modal .ant-modal-close {
  color: #9ca3af !important;
}

.dark-modal-root .ant-modal .ant-modal-close:hover {
  color: #e5e7eb !important;
}

/* Кнопки в модальном окне темной темы */
.dark-modal-root .ant-btn-default {
  background: transparent !important;
  border-color: #4b5563 !important;
  color: #e5e7eb !important;
}

.dark-modal-root .ant-btn-default:hover {
  background: #4b5563 !important;
  border-color: #6b7280 !important;
  color: #f9fafb !important;
}

.dark-modal-root .ant-btn-dangerous {
  background: #dc2626 !important;
  border-color: #dc2626 !important;
  color: #ffffff !important;
}

.dark-modal-root .ant-btn-dangerous:hover {
  background: #b91c1c !important;
  border-color: #b91c1c !important;
  color: #ffffff !important;
}

/* Маска модального окна в темной теме */
.dark-modal-root .ant-modal-mask {
  background: rgba(0, 0, 0, 0.65) !important;
}

/* Селекторы для текста в модальном окне без иконок */
.dark-modal-root .ant-modal-confirm-title,
.dark-modal-root .ant-modal-confirm-content,
.dark-modal-root .ant-modal-title,
.dark-modal-root .ant-modal-body,
.dark-modal-root .ant-modal-body p,
.dark-modal-root .ant-modal-body div:not(.anticon):not([class*="anticon"]) {
  color: #ffffff !important;
}

.dark-modal-root .ant-btn-default {
  color: #e5e7eb !important;
}

.dark-modal-root .ant-btn-dangerous {
  color: #ffffff !important;
}

/* Сохраняем оранжевый цвет для иконки предупреждения */
.dark-modal-root .anticon-exclamation-circle {
  color: #faad14 !important;
}

/* Стили для dropdown фильтра в темной теме */
.dark-dropdown .ant-dropdown-menu {
  background-color: #1f2937 !important;
  border: 1px solid #4b5563 !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.dark-dropdown .ant-dropdown-menu-item {
  color: #d1d5db !important;
  background-color: transparent !important;
}

.dark-dropdown .ant-dropdown-menu-item:hover {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

.dark-dropdown .ant-dropdown-menu-item-selected {
  background-color: #3b82f6 !important;
  color: white !important;
}

.dark-dropdown .ant-dropdown-menu-item-selected:hover {
  background-color: #2563eb !important;
  color: white !important;
}

.dark-dropdown .ant-dropdown-menu-item:not(:last-child) {
  border-bottom: 1px solid #374151 !important;
}