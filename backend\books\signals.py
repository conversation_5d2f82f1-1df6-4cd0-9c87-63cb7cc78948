from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

@receiver(post_save, sender='books.Comment')
def update_reader_rating_on_comment(sender, instance, created, **kwargs):
    if created:
        from .models import ReaderRatingHistory
        user = instance.user
        # Логика начисления баллов (пример для рейтинга < 2000)
        points = 5 if user.reader_rating < 2000 else 10
        user.reader_rating += points
        user.save()
        ReaderRatingHistory.objects.create(
            user=user,
            action='comment_book',
            points=points,
            total=user.reader_rating
        )

@receiver(post_save, sender='books.CommentLike')
def update_reader_rating_on_comment_like(sender, instance, created, **kwargs):
    """Обработка лайков/дизлайков к комментариям через новую систему рейтингов"""
    if created:
        # Новая реакция - отправляем в асинхронную обработку
        from users.tasks import process_like_added_async
        process_like_added_async.delay(instance.id, 'comment')
    # Изменения существующих реакций обрабатываются в API, а не в сигналах

@receiver(post_delete, sender='books.CommentLike')
def decrease_reader_rating_on_comment_unlike(sender, instance, **kwargs):
    """Обработка удаления лайков/дизлайков к комментариям"""
    # Отправляем в асинхронную обработку
    from users.tasks import process_like_removed_async
    process_like_removed_async.delay(
        user_id=instance.user.id,
        comment_id=instance.comment.id,
        reaction=instance.reaction,
        content_type='comment'
    )

@receiver(post_save, sender='books.Like')
def update_author_rating_on_like(sender, instance, created, **kwargs):
    if created:
        from .models import AuthorRatingHistory
        author = instance.book.author
        points = 15  # Изменено с 5 на 15 баллов за лайк
        author.author_rating += points
        author.save()
        AuthorRatingHistory.objects.create(
            user=author,
            action='book_like',
            points=points,
            total=author.author_rating
        )

@receiver(post_save, sender='books.Purchase')
def update_ratings_on_purchase(sender, instance, created, **kwargs):
    if created:
        from .models import AuthorRatingHistory
        author = instance.book.author
        buyer = instance.user
        amount = int(instance.amount)
        # Автору +2 за каждый рубль
        author_points = 2 * amount
        author.author_rating += author_points
        author.save()
        AuthorRatingHistory.objects.create(
            user=author,
            action='book_purchase',
            points=author_points,
            total=author.author_rating
        )

@receiver(post_save, sender='books.Subscription')
def update_author_rating_on_subscribe(sender, instance, created, **kwargs):
    if created:
        from .models import AuthorRatingHistory
        author = instance.author
        points = 20  # Изменено с 50 на 20 баллов за подписку
        author.author_rating += points
        author.save()
        AuthorRatingHistory.objects.create(
            user=author,
            action='user_subscribe',
            points=points,
            total=author.author_rating
        )

@receiver(post_delete, sender='books.Subscription')
def decrease_author_rating_on_unsubscribe(sender, instance, **kwargs):
    from .models import AuthorRatingHistory
    author = instance.author
    points = 20  # Изменено с 50 на 20 баллов при отписке
    author.author_rating -= points
    author.save()
    AuthorRatingHistory.objects.create(
        user=author,
        action='user_unsubscribe',
        points=-points,
        total=author.author_rating
    )

@receiver(post_save, sender='books.Review')
def update_ratings_on_review(sender, instance, created, **kwargs):
    if created:
        from .models import AuthorRatingHistory, ReaderRatingHistory
        author = instance.book.author
        reviewer = instance.user
        # Автору +10 за каждую рецензию
        author_points = 10
        author.author_rating += author_points
        author.save()
        AuthorRatingHistory.objects.create(
            user=author,
            action='book_review',
            points=author_points,
            total=author.author_rating
        )
        # Читателю +15 за рецензию
        reader_points = 15
        reviewer.reader_rating += reader_points
        reviewer.save()
        ReaderRatingHistory.objects.create(
            user=reviewer,
            action='write_review',
            points=reader_points,
            total=reviewer.reader_rating
        )

@receiver(post_save, sender='books.Award')
def update_ratings_on_award(sender, instance, created, **kwargs):
    if created:
        from .models import AuthorRatingHistory, ReaderRatingHistory
        author = instance.book.author
        giver = instance.user
        amount = int(instance.amount)
        # Автору +3 за каждый рубль награды
        author_points = 3 * amount
        author.author_rating += author_points
        author.save()
        AuthorRatingHistory.objects.create(
            user=author,
            action='book_award',
            points=author_points,
            total=author.author_rating
        )
        # Дарителю +1 за каждый рубль
        giver_points = amount
        giver.reader_rating += giver_points
        giver.save()
        ReaderRatingHistory.objects.create(
            user=giver,
            action='give_award',
            points=giver_points,
            total=giver.reader_rating
        )

@receiver(post_save, sender='users.ReadingSession')
def update_reader_rating_on_reading_session(sender, instance, created, **kwargs):
    if instance.is_finished:
        from .models import ReaderRatingHistory
        user = instance.user
        # +5 баллов за каждую прочитанную книгу
        points = 5
        user.reader_rating += points
        user.save()
        ReaderRatingHistory.objects.create(
            user=user,
            action='finish_reading',
            points=points,
            total=user.reader_rating
        ) 