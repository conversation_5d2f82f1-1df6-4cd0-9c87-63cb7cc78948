# Generated by Django 5.0.2 on 2025-07-13 12:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0047_ratinghistory'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ratingcalculationrule',
            name='rating_type',
            field=models.CharField(choices=[('reader', 'Рейтинг читателя'), ('author', 'Авторский рейтинг'), ('total', 'Общий рейтинг')], max_length=20),
        ),
        migrations.AlterField(
            model_name='ratinghistory',
            name='action_type',
            field=models.CharField(choices=[('welcome_bonus', 'Приветственный бонус'), ('comment_added', 'Комментарий к книге'), ('reply_to_comment_added', 'Ответ на комментарий'), ('reply_to_reply_added', 'Ответ на ответ'), ('comment_like_received', 'Получен лайк к комментарию'), ('comment_dislike_received', 'Дизлайк к комментарию'), ('book_purchased', 'Покупка книги'), ('reading_session', 'Сессия чтения'), ('award_given', 'Выдача награды'), ('comment_received', 'Получен комментарий к книге'), ('book_like_received', 'Получен лайк к книге'), ('book_sold', 'Продажа книги'), ('award_received', 'Получена награда'), ('blog_post_published', 'Опубликован пост в блоге'), ('book_status_changed', 'Изменение статуса книги'), ('subscriber_added', 'Новый подписчик/друг'), ('subscriber_removed', 'Отписка/Разрыв дружбы'), ('story_finished_to_partial', 'Изменен статус рассказа из Завершено в Процесс публикации'), ('story_partial_to_draft', 'Изменен статус рассказа из Процесса публикации в Черновик'), ('story_draft_to_partial', 'Изменен статус рассказа из Черновика в Процесс публикации'), ('story_partial_to_finished', 'Рассказ завершен из Процесса публикации'), ('story_finished_to_draft', 'Изменен статус рассказа из Завершено в Черновик'), ('story_draft_to_finished', 'Рассказ завершен из Черновика'), ('novel_finished_to_partial', 'Изменен статус Повести/Романа из Завершено в Процесс публикации'), ('novel_partial_to_draft', 'Изменен статус Повести/Романа из Процесса публикации в Черновик'), ('novel_draft_to_partial', 'Изменен статус Повести/Романа из Черновика в Процесс публикации'), ('novel_partial_to_finished', 'Повесть/Роман завершен из Процесса публикации'), ('novel_finished_to_draft', 'Изменен статус Повести/Романа из Завершено в Черновик'), ('novel_draft_to_finished', 'Повесть/Роман завершен из Черновика'), ('comment_removed', 'Удален комментарий'), ('comment_like_revoked', 'Отозван лайк к комментарию'), ('comment_like_removed', 'Лайк к комментарию отозван'), ('comment_dislike_removed', 'Дизлайк к комментарию отозван'), ('comment_received_revoked', 'Отозван комментарий к книге')], max_length=30),
        ),
        migrations.AlterField(
            model_name='ratinghistory',
            name='metric_name',
            field=models.CharField(choices=[('reader_rating', 'Рейтинг читателя'), ('author_rating', 'Авторский рейтинг')], max_length=20),
        ),
    ]
