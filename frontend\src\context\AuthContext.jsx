import React, { createContext, useContext, useState, useEffect } from 'react';
import { csrfFetch } from '../utils/csrf';
import { clearAvatarCache } from '../utils/avatarCache';

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true); // Добавляем состояние loading
  const [avatarVersion, setAvatarVersion] = useState(0); // Добавляем версию для принудительного обновления

  useEffect(() => {
    // Проверяем авторизацию при загрузке
    const checkAuth = async () => {
      setLoading(true); // Устанавливаем loading в true перед запросом
      try {
        const response = await csrfFetch('/api/auth/check/', {
          credentials: 'include',
          cache: 'no-store',
        });
        if (response.ok) {
          const data = await response.json();
          if (data.is_authenticated && data.user) {
            // Очищаем кэш аватаров при начальной загрузке пользователя
            clearAvatarCache();
            setIsAuthenticated(true);
            setUser(data.user);
            setAvatarVersion(prev => prev + 1);
          } else {
            setIsAuthenticated(false);
            setUser(null);
          }
        } else {
          setIsAuthenticated(false);
          setUser(null);
        }
      } catch (err) {
        setIsAuthenticated(false);
        setUser(null);
        console.error('Auth check failed:', err);
      } finally {
        setLoading(false); // Устанавливаем loading в false после запроса
      }
    };
    checkAuth();
  }, []);

  const login = (userData) => {
    // Очищаем кэш аватаров при входе нового пользователя
    clearAvatarCache();
    setIsAuthenticated(true);
    setUser(userData);
    setAvatarVersion(prev => prev + 1);
  };

  const logout = async () => {
    try {
      const response = await csrfFetch('/api/auth/logout/', {
        method: 'POST',
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Logout failed');
      }
      
      // Очищаем все куки
      document.cookie.split(";").forEach(function(c) { 
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
      });
      
      // Очищаем кэш аватаров при выходе
      clearAvatarCache();
      
      // Сбрасываем состояние
      setIsAuthenticated(false);
      setUser(null);
      
      // Принудительно перезагружаем страницу
      window.location.href = '/';
    } catch (err) {
      console.error('Logout failed:', err);
      throw err;
    }
  };

  // Добавляем функцию для обновления user из API
  const refreshUser = async () => {
    try {
      const response = await csrfFetch('/api/auth/check/', {
        credentials: 'include',
        cache: 'no-store',
      });
      if (response.ok) {
        const data = await response.json();
        if (data.is_authenticated && data.user) {
          // Очищаем кэш аватаров при обновлении пользователя
          clearAvatarCache();
          setIsAuthenticated(true);
          setUser(data.user);
          setAvatarVersion(prev => prev + 1);
        } else {
          setIsAuthenticated(false);
          setUser(null);
        }
      } else {
        setIsAuthenticated(false);
        setUser(null);
      }
    } catch (err) {
      setIsAuthenticated(false);
      setUser(null);
      console.error('Auth refresh failed:', err);
    }
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout, refreshUser, loading, avatarVersion }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  return useContext(AuthContext);
}

export { AuthContext };