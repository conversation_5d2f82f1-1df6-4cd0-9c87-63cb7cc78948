# Настройка VK ID для OAuth авторизации

## 1. Создание приложения VK ID

1. Перейдите на https://id.vk.com/business/go
2. Войдите в аккаунт VK
3. Создайте новое приложение или выберите существующее
4. В настройках приложения найдите раздел "Ключи доступа"

## 2. Настройка доменов и режима разработки

VK ID использует другой подход - вместо redirect URI нужно настроить разрешенные домены:

**Включите "Режим разработки"** для всех типов приложений (переключатели в синий цвет)

**Добавьте домены для разработки:**
- `localhost:5173` (React frontend - стандартный порт Vite)
- `localhost:8000` (Django backend)

**Для production:**
- `yourdomain.com`

⚠️ **Важно:** VK ID работает по доменам, а не по конкретным URL!

## 3. Получение ключей

Из панели управления VK ID приложения скопируйте:

- **ID приложения** (App ID) - используется как `VK_CLIENT_ID`
- **Защищённый ключ** (Protected Key) - используется как `VK_SECRET_KEY`

## 4. Настройка переменных окружения

### Backend (.env файл в папке backend/)

```bash
# VK ID настройки
VK_CLIENT_ID=ваш_app_id
VK_SECRET_KEY=ваш_protected_key
```

### Frontend (.env файл в папке frontend/)

```bash
# VK ID настройки
VITE_VK_CLIENT_ID=ваш_app_id
```

## 5. Настройка в интерфейсе VK ID

Согласно скриншоту, который вы показали:

1. **Состояние для пользователей:** Включено ✓
2. **Режим разработки:** Включен для всех типов приложений ✓
3. **Домены добавлены:** `localhost:5173`, `localhost:8000`

## 6. Проверка настроек

1. Убедитесь что сервер Django запущен на порту 8000
2. Убедитесь что React приложение запущено на порту 3000
3. Проверьте что переменные окружения корректно загружены
4. Протестируйте авторизацию через VK ID

## 7. Возможные проблемы

### "Invalid redirect_uri"
- Проверьте точное совпадение URL в настройках VK ID
- Убедитесь что добавлен завершающий слэш
- Проверьте протокол (http vs https)

### "Invalid client_id"
- Проверьте правильность App ID в переменных окружения
- Убедитесь что переменные загружены (.env файлы на месте)

### "Access denied"
- Проверьте разрешенные домены в настройках VK ID
- Убедитесь что домен localhost добавлен для разработки

## 8. Дополнительные настройки

### Scope (области доступа)
По умолчанию запрашивается доступ к email. Можно расширить в `settings.py`:

```python
SOCIALACCOUNT_PROVIDERS = {
    'vk': {
        'SCOPE': ['email', 'offline'],  # Добавить offline для refresh token
        # ...
    }
}
```

### Кастомизация процесса авторизации
Можно переопределить поведение в `VKOAuth2Adapter` если нужно дополнительно обрабатывать данные пользователя.

## 9. Тестирование

После настройки:

1. Откройте http://localhost:3000/login
2. Нажмите кнопку "VK ID"
3. Должно открыться popup окно VK
4. После авторизации popup закроется и произойдет вход в систему

## 10. Production деплой

При переносе на сервер обновите:

1. Redirect URI в настройках VK ID приложения
2. Разрешенные домены 
3. Переменные окружения с production значениями
4. Используйте HTTPS для production

---

**Полезные ссылки:**
- [Документация VK ID](https://id.vk.com/about/business/go/docs)
- [Управление приложениями VK ID](https://id.vk.com/business/go) 