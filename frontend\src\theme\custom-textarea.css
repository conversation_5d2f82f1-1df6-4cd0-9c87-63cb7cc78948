/* Стили для кастомного textarea (аннотация) */
.ant-input::placeholder {
  color: #d1d5db !important;   /* светло-серый для темной темы */
  font-style: italic !important;
  opacity: 1 !important;
}
html.light .ant-input::placeholder {
  color: #6b7280 !important;   /* темно-серый для светлой темы */
}
html.dark .ant-input::placeholder {
  color: #d1d5db !important;   /* светло-серый для темной темы */
}

/* Для счетчика символов внизу поля аннотации */
html.dark .ant-input-show-count-suffix {
  color: #fff !important;
}
html.light .ant-input-show-count-suffix {
  color: #222 !important;
}

.custom-textarea textarea {
  line-height: 1.5;
  white-space: pre-line;
}

/* Для визуализации новых абзацев: увеличиваем разрыв между абзацами */
.custom-textarea textarea::after {
  content: '';
  display: block;
  height: 0;
}

/* Для отображения в режиме чтения (если аннотация рендерится как HTML) */
.annotation-preview p + p {
  margin-top: 1.95em; /* 1.5em * 1.3 = +30% */
} 