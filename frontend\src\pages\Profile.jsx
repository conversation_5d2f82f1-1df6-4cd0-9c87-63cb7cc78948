import { useParams, useNavigate, Outlet, Link, useLocation, useOutletContext } from 'react-router-dom';
import { useEffect, useState, useContext } from 'react';
import Container from '../components/Container';
import { AuthContext } from '../context/AuthContext';
import { useTheme } from '../theme/ThemeContext';
import { useFeedCount } from '../hooks/useFeedCount';
import ProfileSidebar from '../components/ProfileSidebar';
import { getCachedUserAvatar } from '../utils/avatarCache';
import { useMessage } from '../context/MessageContext';
import ProfileHeader from '../components/ProfileHeader';
import { useUserSettings } from '../context/UserSettingsContext';
import moment from 'moment-timezone';
import { getCSRFToken, csrfFetch } from '../utils/csrf';
import { Cog6ToothIcon } from '@heroicons/react/24/outline';
import BioEditor from '../components/OtherEditors/BioEditorX';
import { useBookCounts } from '../context/BookCountsContext';
import './Profile.css';

// Импортируем утилиты для обработки изображений
function extractImagePathsFromHtml(html) {
  const div = document.createElement('div');
  div.innerHTML = html;
  const images = div.querySelectorAll('img');
  const paths = [];
  images.forEach(img => {
    const src = img.getAttribute('src');
    if (src) {
      // Обрабатываем разные форматы URL
      if (src.startsWith('/media/')) {
        paths.push(src);
      } else if (src.includes('storage.yandexcloud.net') && src.includes('/media/public/')) {
        // Извлекаем путь из полного URL
        const pathMatch = src.match(/\/media\/public\/(.+)$/);
        if (pathMatch) {
          paths.push('/media/' + pathMatch[1]);
        }
      } else if (!src.startsWith('http') && !src.startsWith('data:')) {
        // Относительные пути
        paths.push(src.startsWith('/') ? src : '/' + src);
      }
    }
  });
  console.log('Extracted paths from HTML:', paths);
  return Array.from(new Set(paths));
}

function replaceImageSrcInHtml(html, urlMap) {
  console.log('Original HTML:', html);
  const div = document.createElement('div');
  div.innerHTML = html;
  
  // Обрабатываем изображения - ТОЧНО как в BookReader
  div.querySelectorAll('img').forEach(img => {
    const src = img.getAttribute('src');
    
    // Если URL уже абсолютный и не требует замены
    if (src && (src.startsWith('http') || src.startsWith('data:'))) {
      console.log('Skipping external image:', src);
      // Но все равно обрабатываем подпись, если изображение имеет data-caption
    } else {
      // Если есть presigned URL для этого пути, заменяем src
      if (src && urlMap[src]) {
        console.log(`Replacing path ${src} with presigned URL`);
        img.setAttribute('src', urlMap[src]);
      } else {
        console.log(`No presigned URL for path: ${src}`);
      }
    }
    
    // Проверяем, находится ли изображение в кастомном wrapper
    const isInWrapper = img.closest('.custom-resizable-image');
    
    // Если изображение уже в wrapper, просто обновляем его свойства
    if (isInWrapper) {
      return;
    }
    
    // Получаем атрибуты изображения
    const caption = img.getAttribute('data-caption');
    const dataWidth = img.getAttribute('data-width');
    const align = img.getAttribute('data-align') || 'center';
    const textWrap = img.getAttribute('data-text-wrap');
    
    // ВАЖНО: Проверяем, есть ли уже классы выравнивания на изображении
    const hasFloatClass = img.classList.contains('float-left') || img.classList.contains('float-right') || 
                         img.classList.contains('align-left') || img.classList.contains('align-right') || 
                         img.classList.contains('align-center');
    
    // Если изображение уже имеет классы выравнивания, просто добавляем подпись
    if (hasFloatClass && caption && caption.trim()) {
      // Создаем wrapper только для подписи
      const wrapper = document.createElement('div');
      
      // Копируем классы с изображения на wrapper
      img.classList.forEach(className => {
        if (className.startsWith('float-') || className.startsWith('align-')) {
          wrapper.classList.add(className);
          img.classList.remove(className);
        }
      });
      
      // Добавляем базовый класс
      wrapper.classList.add('custom-resizable-image');
      
      // Копируем стили с изображения на wrapper
      if (img.style.width) {
        wrapper.style.width = img.style.width;
        wrapper.style.position = 'relative';
        wrapper.style.maxWidth = '100%';
      }
      
      // Вставляем wrapper перед изображением
      img.parentNode.insertBefore(wrapper, img);
      
      // Перемещаем изображение в wrapper
      wrapper.appendChild(img);
      
      // Настраиваем стили изображения
      img.style.width = '100%';
      img.style.maxWidth = '100%';
      img.style.display = 'block';
      img.style.borderRadius = '8px';
      img.style.height = 'auto';
      
      // Добавляем подпись
      const captionDiv = document.createElement('div');
      captionDiv.className = 'image-caption';
      captionDiv.textContent = caption.trim();
      wrapper.appendChild(captionDiv);
      
      return;
    }
    
    // Если нет классов выравнивания, создаем полный wrapper как раньше
    if (!hasFloatClass) {
      // Определяем класс контейнера ТОЧНО как в редакторе
      let containerCssClass = '';
      if (align === 'left') {
        containerCssClass = textWrap === 'wrap' ? 'float-left' : 'align-left';
      } else if (align === 'right') {
        containerCssClass = textWrap === 'wrap' ? 'float-right' : 'align-right';
      } else {
        containerCssClass = 'align-center';
      }
      
      // Создаем NodeViewWrapper ТОЧНО как в редакторе
      const wrapper = document.createElement('div');
      wrapper.className = `custom-resizable-image ${containerCssClass}`;
      
      // Устанавливаем inline стили ТОЧНО как в NodeViewWrapper
      if (dataWidth) {
        const widthPercent = Math.max(25, Math.min(100, parseInt(dataWidth, 10) || 100));
        wrapper.style.position = 'relative';
        wrapper.style.width = widthPercent + '%';
        wrapper.style.maxWidth = '100%';
      } else {
        wrapper.style.position = 'relative';
        wrapper.style.maxWidth = '100%';
      }
      
      // Вставляем wrapper перед изображением
      img.parentNode.insertBefore(wrapper, img);
      
      // Перемещаем изображение в wrapper
      wrapper.appendChild(img);
      
      // Настраиваем стили изображения ТОЧНО как в редакторе
      img.style.width = '100%';
      img.style.maxWidth = '100%';
      img.style.display = 'block';
      img.style.borderRadius = '8px';
      img.style.height = 'auto';
      img.draggable = false;
      img.style.userSelect = 'none';
      img.style.WebkitUserSelect = 'none';
      img.style.pointerEvents = 'none';
      
      // Применяем обтекание текстом - КРИТИЧЕСКИ ВАЖНО для floating изображений
      if (textWrap === 'wrap' && (align === 'left' || align === 'right')) {
        // Применяем shape-outside к изображению
        img.style.shapeOutside = 'margin-box';
        img.style.webkitShapeOutside = 'margin-box';
        
        // Применяем float стили к wrapper-элементу для правильного обтекания
        if (align === 'left') {
          wrapper.style.float = 'left';
          wrapper.style.margin = '0 16px 16px 0';
          wrapper.style.clear = 'none';
        } else if (align === 'right') {
          wrapper.style.float = 'right';
          wrapper.style.margin = '0 0 16px 16px';
          wrapper.style.clear = 'none';
        }
        wrapper.style.zIndex = '10';
        wrapper.style.position = 'relative';
        wrapper.style.display = 'block';
      }
      
      // Добавляем подпись если есть
      if (caption && caption.trim()) {
        const captionDiv = document.createElement('div');
        captionDiv.className = 'image-caption';
        captionDiv.textContent = caption.trim();
        wrapper.appendChild(captionDiv);
      }
      
      // Сохраняем оригинальные атрибуты
      if (img.getAttribute('alt')) img.alt = img.getAttribute('alt');
      if (img.getAttribute('title')) img.title = img.getAttribute('title');
    }
  });
  
  // Обрабатываем уже существующие wrapper-элементы (для обратной совместимости)
  div.querySelectorAll('.custom-resizable-image').forEach(wrapper => {
    const img = wrapper.querySelector('img');
    if (!img) return;
    
    // Подставляем presigned URL
    const src = img.getAttribute('src');
    if (src && urlMap[src]) {
      img.setAttribute('src', urlMap[src]);
    }
    
    // Убеждаемся, что стили применены правильно
    if (wrapper.classList.contains('float-left') || wrapper.classList.contains('float-right')) {
      img.style.shapeOutside = 'margin-box';
      img.style.webkitShapeOutside = 'margin-box';
    }
    
    // ВАЖНО: Проверяем, есть ли подпись в data-caption, но нет элемента подписи
    const caption = img.getAttribute('data-caption');
    const existingCaption = wrapper.querySelector('.image-caption');
    
    if (caption && caption.trim() && !existingCaption) {
      const captionDiv = document.createElement('div');
      captionDiv.className = 'image-caption';
      captionDiv.textContent = caption.trim();
      wrapper.appendChild(captionDiv);
    }
  });
  
  // КРИТИЧЕСКИ ВАЖНО: Удаляем дублирующие wrapper-элементы
  // Если изображение находится внутри wrapper, который сам находится внутри другого wrapper
  div.querySelectorAll('.custom-resizable-image .custom-resizable-image').forEach(innerWrapper => {
    const outerWrapper = innerWrapper.parentElement;
    const img = innerWrapper.querySelector('img');
    const caption = innerWrapper.querySelector('.image-caption');
    
    if (img && outerWrapper.classList.contains('custom-resizable-image')) {
      // Перемещаем изображение и подпись в внешний wrapper
      outerWrapper.appendChild(img);
      if (caption) {
        outerWrapper.appendChild(caption);
      }
      // Удаляем внутренний wrapper
      innerWrapper.remove();
    }
  });
  
  // Обрабатываем текстовые стили - убеждаемся что все inline стили сохранены
  div.querySelectorAll('span[style]').forEach(span => {
    const style = span.getAttribute('style');
    if (style) {
      span.setAttribute('style', style);
    }
  });
  
  // Обрабатываем выравнивание параграфов
  div.querySelectorAll('p[style*="text-align"]').forEach(p => {
    const style = p.getAttribute('style');
    if (style) {
      p.setAttribute('style', style);
    }
  });
  
  const result = div.innerHTML;
  console.log('Processed HTML:', result);
  return result;
}

function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

async function fetchPresignedUrls(paths) {
  const csrfToken = getCookie('csrftoken');
  const res = await fetch('/api/auth/bio/get-image-links/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken },
    credentials: 'include',
    body: JSON.stringify({ paths }),
  });
  if (!res.ok) throw new Error('Ошибка получения ссылок');
  return await res.json();
}

function getAgeString(birthDate, timezone) {
  if (!birthDate) return null;
  const today = moment().tz(timezone);
  const dob = moment(birthDate).tz(timezone);
  let age = today.year() - dob.year();
  if (
    today.month() < dob.month() ||
    (today.month() === dob.month() && today.date() < dob.date())
  ) {
    age--;
  }
  // Склонение
  const lastDigit = age % 10;
  const lastTwo = age % 100;
  let word = 'лет';
  if (lastDigit === 1 && lastTwo !== 11) word = 'год';
  else if ([2,3,4].includes(lastDigit) && ![12,13,14].includes(lastTwo)) word = 'года';
  return `${age} ${word}`;
}

function ProfileInfoCards({ userData, timezone, editingBio, setEditingBio, bioSaving, handleSaveBio, isOwner }) {
  const { theme } = useTheme();
  return (
    <>
      {/* Карточка О себе */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2 mb-4">
        <div className="flex items-center justify-between mb-2">
          <div className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            О себе
            {isOwner && (
              <button
                onClick={() => setEditingBio(true)}
                className="ml-2 p-1 rounded-full transition-colors duration-200 focus:outline-none"
                style={{
                  background: 'transparent',
                  color: theme === 'dark' ? '#d1d5db' : '#6b7280',
                  border: 'none',
                  boxShadow: 'none',
                  transition: 'color 0.2s, background 0.2s',
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.background = theme === 'dark' ? '#374151' : '#e5e7eb';
                  e.currentTarget.style.color = theme === 'dark' ? '#fff' : '#2563eb';
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.background = 'transparent';
                  e.currentTarget.style.color = theme === 'dark' ? '#d1d5db' : '#6b7280';
                }}
                title="Редактировать информацию о себе"
              >
                <Cog6ToothIcon className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>
        {editingBio ? (
          <>
            <BioEditor
              initialContent={userData?.bio || ''}
              onSave={handleSaveBio}
              onCancel={() => setEditingBio(false)}
              userId={userData?.id}
              username={userData?.username}
            />
            {bioSaving && <div className="text-blue-500 mt-2">Сохранение...</div>}
          </>
        ) : (
          <div 
            className="bio-content"
            dangerouslySetInnerHTML={{
              __html: userData?.bio || 'Пользователь пока не добавил информацию о себе.'
            }}
          />
        )}
      </div>
      {/* Карточка Возраст */}
      {userData?.show_birth_date && userData?.birth_date && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2 mb-4">
          <div className="flex items-center gap-2">
            <span className="font-semibold">Возраст:</span> {getAgeString(userData.birth_date, timezone)}
          </div>
        </div>
      )}
      {/* Карточка E-mail */}
      {userData?.email && userData?.hide_email === false && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2 mb-4">
          <div className="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span className="font-semibold">E-mail:</span>
            <a href={`mailto:${userData.email}`} className="text-blue-600 dark:text-blue-400 hover:underline">
              {userData.email}
            </a>
          </div>
        </div>
      )}
    </>
  );
}

function ProfileLayout() {
  const { username } = useParams();
  const location = useLocation();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useContext(AuthContext);
  const { theme } = useTheme ? useTheme() : { theme: 'light' };
  const [stats, setStats] = useState({ subscribers_count: 0, friends_count: 0 });
  const [relation, setRelation] = useState(null);
  const [relationLoading, setRelationLoading] = useState(false);
  const [friendsList, setFriendsList] = useState([]);
  const [subscriptionsList, setSubscriptionsList] = useState([]);
  const [subscribersList, setSubscribersList] = useState([]);
  const [imageVersion, setImageVersion] = useState(0);
  const outletContext = useOutletContext();
  // Используем контекст для получения счетчиков книг
  const { getCounts, updateCountsFromBooks } = useBookCounts();
  const bookCounts = getCounts(username);
  const [editingBio, setEditingBio] = useState(false);
  const [bioSaving, setBioSaving] = useState(false);

  const backendUrl = 'http://localhost:8000';
  const navigate = useNavigate();
  const { data: feedCount } = useFeedCount();
  const { unreadMessagesCount } = useMessage();
  const { timezone } = useUserSettings();

  const isProfileTab = location.pathname === `/lpu/${username}`;

  const csrfToken = getCSRFToken();

  // Функция для обновления данных пользователя
  const refreshUserData = async () => {
    try {
      const res = await fetch(`/api/auth/public/${username}/`);
      if (!res.ok) throw new Error('Ошибка при получении данных');
      const data = await res.json();

      // Проверяем, удален ли аккаунт
      if (data.is_deleted) {
        setUserData(data);
        setLoading(false);
        return;
      }

      // console.log('Refreshed user data:', data); // Removed for performance
      setUserData(data);
      setImageVersion(prev => prev + 1);
    } catch (err) {
      console.error('Error refreshing user data:', err);
    }
  };

  // Универсальное обновление профиля
  async function updateProfile(fields) {
    try {
      const res = await csrfFetch('/api/auth/profile/', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        body: JSON.stringify(fields),
        credentials: 'include',
      });
      if (!res.ok) throw new Error('Ошибка при сохранении');
      // После PATCH делаем GET для получения всех актуальных данных (личных)
      const privateRes = await fetch('/api/auth/profile/', {
        credentials: 'include',
      });
      if (!privateRes.ok) throw new Error('Ошибка при получении профиля');
      const privateData = await privateRes.json();
      setUserData(prev => ({ ...prev, ...privateData }));
      return { success: true };
    } catch (err) {
      return { success: false, error: err.message };
    }
  }

  // Функция для обработки HTML биографии с presigned URL
  const processBioHtml = async (bioHtml) => {
    if (!bioHtml) return bioHtml;
    
    try {
      console.log('Processing bio HTML:', bioHtml);
      
      // Извлекаем пути изображений из HTML
      const imagePaths = extractImagePathsFromHtml(bioHtml);
      console.log('Found image paths:', imagePaths);
      
      let processedHtml = bioHtml;
      
      if (imagePaths.length > 0) {
        // Получаем presigned URL
        const urlMap = await fetchPresignedUrls(imagePaths);
        console.log('Got URL map:', urlMap);
        
        // Заменяем пути на presigned URL
        processedHtml = replaceImageSrcInHtml(bioHtml, urlMap);
      } else {
        // Даже если нет путей для замены, обрабатываем HTML для подписей
        processedHtml = replaceImageSrcInHtml(bioHtml, {});
      }
      
      console.log('Final processed HTML:', processedHtml);
      return processedHtml;
    } catch (error) {
      console.error('Ошибка обработки изображений в биографии:', error);
      return bioHtml;
    }
  };

  // При монтировании компонента
  useEffect(() => {
    if (!username) return;
    
    setLoading(true);
    setError(null);
    
    fetch(`/api/auth/public/${username}/`)
      .then(res => {
        if (!res.ok) {
          throw new Error('Пользователь не найден');
        }
        return res.json();
      })
      .then(async (data) => {
        // console.log('Loaded user data:', data); // Removed for performance

        // Проверяем, удален ли аккаунт
        if (data.is_deleted) {
          setUserData(data);
          setLoading(false);
          return;
        }

        // Обрабатываем HTML биографии для получения presigned URL изображений
        if (data.bio) {
          data.bio = await processBioHtml(data.bio);
        }

        setUserData(data);
        setLoading(false);
      })
      .catch(err => {
        console.error('Error loading user data:', err);
        setError(err.message);
        setLoading(false);
      });
  }, [username]);

  // Загружаем статистику подписчиков и друзей
  useEffect(() => {
    if (!username) return;
    fetch(`/api/auth/stats/${username}/`, { credentials: 'include' })
      .then(res => res.json())
      .then(data => setStats(data))
      .catch(() => setStats({ subscribers_count: 0, friends_count: 0 }));
  }, [username, user]);

  // Загружаем статус отношений
  useEffect(() => {
    if (!user || !username || user.username === username) return;
    setRelationLoading(true);
    fetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' })
      .then(res => res.json())
      .then(data => setRelation(data))
      .finally(() => setRelationLoading(false));
  }, [user, username]);

  // Обработчики действий
  async function handleSubscribe() {
    setRelationLoading(true);
    await csrfFetch('/api/auth/subscribe/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken },
      credentials: 'include',
      body: JSON.stringify({ username })
    });
    // Обновить статус, статистику и списки
    await Promise.all([
      fetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setRelation),
      fetch(`/api/auth/stats/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setStats),
      fetch(`/api/auth/friends/${username}/`).then(res => res.json()).then(data => setFriendsList(data.friends || [])),
      fetch(`/api/auth/subscribers/${username}/`).then(res => res.json()).then(data => setSubscribersList(data.subscribers || []))
    ]);
    setRelationLoading(false);
  }

  async function handleUnsubscribe() {
    setRelationLoading(true);
    await csrfFetch('/api/auth/subscribe/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken },
      credentials: 'include',
      body: JSON.stringify({ username })
    });
    // Обновить статус, статистику и списки
    await Promise.all([
      fetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setRelation),
      fetch(`/api/auth/stats/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setStats),
      fetch(`/api/auth/friends/${username}/`).then(res => res.json()).then(data => setFriendsList(data.friends || [])),
      fetch(`/api/auth/subscribers/${username}/`).then(res => res.json()).then(data => setSubscribersList(data.subscribers || []))
    ]);
    setRelationLoading(false);
  }

  async function handleFriendRequest() {
    setRelationLoading(true);
    try {
      const res = await csrfFetch('/api/auth/friend-request/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken },
        credentials: 'include',
        body: JSON.stringify({ username })
      });
      if (!res.ok) {
        const data = await res.json();
        if (data.error && data.error.includes('существует')) {
          await fetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' })
            .then(res => res.json())
            .then(setRelation);
          alert('Заявка уже отправлена!');
        }
        throw new Error(data.error || 'Ошибка');
      }
      // Обновляем статус отношений, статистику и списки
      await Promise.all([
        fetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setRelation),
        fetch(`/api/auth/stats/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setStats),
        fetch(`/api/auth/friends/${username}/`).then(res => res.json()).then(data => setFriendsList(data.friends || [])),
        fetch(`/api/auth/subscribers/${username}/`).then(res => res.json()).then(data => setSubscribersList(data.subscribers || []))
      ]);
    } finally {
      setRelationLoading(false);
    }
  }

  async function handleCancelFriendRequest() {
    setRelationLoading(true);
    try {
      const response = await csrfFetch('/api/auth/friend-request/', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken },
        credentials: 'include',
        body: JSON.stringify({ username })
      });
      if (!response.ok) throw new Error('Failed to cancel friend request');
      // Обновляем статус отношений, статистику и списки
      await Promise.all([
        fetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setRelation),
        fetch(`/api/auth/stats/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setStats),
        fetch(`/api/auth/friends/${username}/`).then(res => res.json()).then(data => setFriendsList(data.friends || [])),
        fetch(`/api/auth/subscribers/${username}/`).then(res => res.json()).then(data => setSubscribersList(data.subscribers || []))
      ]);
    } catch (error) {
      console.error('Error canceling friend request:', error);
    } finally {
      setRelationLoading(false);
    }
  }

  async function handleRemoveFriend() {
    setRelationLoading(true);
    await csrfFetch('/api/auth/remove-friend/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken },
      credentials: 'include',
      body: JSON.stringify({ username })
    });
    await Promise.all([
      fetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setRelation),
      fetch(`/api/auth/stats/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setStats),
      fetch(`/api/auth/friends/${username}/`).then(res => res.json()).then(data => setFriendsList(data.friends || [])),
      fetch(`/api/auth/subscribers/${username}/`).then(res => res.json()).then(data => setSubscribersList(data.subscribers || []))
    ]);
    setRelationLoading(false);
  }

  function handleAuthRequired(action) {
    navigate('/register');
  }

  // Получение списков друзей/подписок/подписчиков
  useEffect(() => {
    if (!username) return;
    fetch(`/api/auth/friends/${username}/`).then(res => res.json()).then(data => setFriendsList(data.friends || []));
    fetch(`/api/auth/subscriptions/${username}/`).then(res => res.json()).then(data => setSubscriptionsList(data.subscriptions || []));
    fetch(`/api/auth/subscribers/${username}/`).then(res => res.json()).then(data => setSubscribersList(data.subscribers || []));
  }, [username]);

  // Загружаем счетчики книг если их нет в контексте
  useEffect(() => {
    if (!username) return;
    
    // Если счетчики уже есть, не загружаем повторно
    const currentCounts = getCounts(username);
    if (currentCounts.total > 0) return;
    
    let isMounted = true; // Флаг для предотвращения обновлений после размонтирования
    
    // Загружаем книги для подсчета
    const fetchBookCounts = async () => {
      try {
        const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
        const res = await fetch(`${backendUrl}/api/users/${username}/books-with-user-simple/`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          }
        });
        if (!res.ok || !isMounted) return;
        const data = await res.json();
        const booksArr = data.books?.results || [];
        
        // Обновляем счетчики в контексте только если компонент еще смонтирован
        if (isMounted) {
          updateCountsFromBooks(username, booksArr);
        }
      } catch (error) {
        console.error('Error loading book counts:', error);
      }
    };
    
    fetchBookCounts();
    
    // Cleanup функция
    return () => {
      isMounted = false;
    };
  }, [username]); // Только username в зависимостях

  // Вынесенная панель с кнопками
  function renderProfileTabs() {
    return (
      <div className="relative flex justify-start gap-3 mt-4 mb-6 items-center pr-16">
        <Link
          to={`/lpu/${username}`}
          className={`px-4 py-2 rounded-lg ${
            location.pathname === `/lpu/${username}`
              ? 'bg-blue-500 text-white'
              : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
          }`}
        >
          Профиль
        </Link>
        <Link
          to={`/lpu/${username}/books`}
          className={`px-4 py-2 rounded-lg ${
            location.pathname === `/lpu/${username}/books`
              ? 'bg-blue-500 text-white'
              : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
          }`}
        >
          Книги{'  '}
          {/* Счетчик книг */}
          {(() => {
            const isOwner = user && user.username === username;
            const publishedCount = bookCounts.finished + bookCounts.inProgress;
            const draftsCount = bookCounts.drafts;
            
            if (isOwner) {
              // Автор видит: опубликованные / черновики (например: 2 / 1)
              if (publishedCount > 0 || draftsCount > 0) {
                return (
                  <span>
                    {publishedCount}
                    {draftsCount > 0 && <span> / {draftsCount}</span>}
                  </span>
                );
              }
            } else {
              // Гость видит только опубликованные книги (например: 2)
              if (publishedCount > 0) {
                return <span>{publishedCount}</span>;
              }
            }
            return null;
          })()}
        </Link>
        {user && user.username === username && (
          <>
            <Link
              to={`/lpu/${username}/feed`}
              className={`px-4 py-2 rounded-lg relative ${
                location.pathname === `/lpu/${username}/feed`
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
              }`}
            >
              Лента
              {feedCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {feedCount}
                </span>
              )}
            </Link>
            <Link
              to={`/lpu/${username}/messages`}
              className={`px-4 py-2 rounded-lg relative ${
                location.pathname === `/lpu/${username}/messages`
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
              }`}
            >
              Сообщения
              {unreadMessagesCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {unreadMessagesCount}
                </span>
              )}
            </Link>
            <Link
              to={`/lpu/${username}/library`}
              className={`px-4 py-2 rounded-lg ${
                location.pathname === `/lpu/${username}/library`
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
              }`}
            >
              📚 Библиотека
            </Link>
            <Link
              to={`/lpu/${username}/settings`}
              className={`px-4 py-2 rounded-lg ${
                location.pathname === `/lpu/${username}/settings`
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
              }`}
            >
              Настройки
            </Link>
          </>
        )}
      </div>
    );
  }

  const handleSaveBio = async (newBioHtml) => {
    if (!userData?.id) return;
    setBioSaving(true);
    try {
      // Сначала сохраняем био
      const res = await csrfFetch(`/api/auth/profile/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCSRFToken(),
        },
        credentials: 'include',
        body: JSON.stringify({ bio: newBioHtml }),
      });
      if (!res.ok) throw new Error('Ошибка сохранения BIO');
      
      // Затем очищаем неиспользуемые изображения
      try {
        await csrfFetch('/api/auth/bio/cleanup-images/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken(),
          },
          credentials: 'include',
          body: JSON.stringify({ html_content: newBioHtml }),
        });
      } catch (cleanupError) {
        console.warn('Ошибка очистки изображений:', cleanupError);
        // Не показываем ошибку пользователю, так как основная операция прошла успешно
      }
      
      // Обрабатываем сохраненную биографию для корректного отображения
      const processedBio = await processBioHtml(newBioHtml);
      setUserData(prev => ({ ...prev, bio: processedBio }));
      setEditingBio(false);
    } catch (e) {
      alert(e.message || 'Ошибка сохранения BIO');
    } finally {
      setBioSaving(false);
    }
  };

  if (loading) {
    return (
      <Container>
        <div className="bg-white rounded-lg shadow-md p-[10px]">
          <div className="animate-pulse">
            <div className="h-[250px] bg-gray-200 rounded-lg mb-8"></div>
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <div className="bg-white rounded-lg shadow-md p-[10px] text-center py-8">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Ошибка</h1>
          <p className="text-gray-600">{error}</p>
        </div>
      </Container>
    );
  }

  // Если аккаунт удален, показываем специальную страницу
  if (userData?.is_deleted) {
    return (
      <Container className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-[10px]">
        <div className="text-center py-12">
          <div className="w-32 h-32 mx-auto mb-6 rounded-full overflow-hidden border-4 border-gray-300 dark:border-gray-600">
            <img
              src="https://storage.yandexcloud.net/lpo-test/dist/ava_presets/ava_del.webp"
              alt="Удаленный аккаунт"
              className="w-full h-full object-cover"
            />
          </div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-4">
            Аккаунт удален
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-2">
            Этот аккаунт был удален пользователем.
          </p>
          {userData.deleted_at && (
            <p className="text-sm text-gray-500 dark:text-gray-500">
              Удален: {moment(userData.deleted_at).format('DD.MM.YYYY')}
            </p>
          )}
          <div className="mt-8">
            <Link
              to="/"
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              Вернуться на главную
            </Link>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-[10px]">
      <ProfileHeader
        username={username}
        userData={userData}
        setUserData={setUserData}
        refreshUserData={refreshUserData}
        imageVersion={imageVersion}
        stats={stats}
        relation={relation}
        relationLoading={relationLoading}
        handleSubscribe={handleSubscribe}
        handleUnsubscribe={handleUnsubscribe}
        handleFriendRequest={handleFriendRequest}
        handleCancelFriendRequest={handleCancelFriendRequest}
        handleRemoveFriend={handleRemoveFriend}
        handleAuthRequired={handleAuthRequired}
      />
      <div className="flex flex-col md:flex-row max-w-6xl mx-auto gap-[15px]">
        {user && user.username === username ? (
          <>
            {/* Основное поле слева */}
            <div className="flex-1 min-w-0 max-w-5xl pr-[0px]">
              {renderProfileTabs()}
              {isProfileTab && (
                <ProfileInfoCards
                  userData={userData}
                  timezone={timezone}
                  editingBio={editingBio}
                  setEditingBio={setEditingBio}
                  bioSaving={bioSaving}
                  handleSaveBio={handleSaveBio}
                  isOwner={user && user.username === username}
                />
              )}
              <Outlet context={{
                ...outletContext,
                userData, setUserData, updateProfile, refreshUserData, imageVersion
              }} />
            </div>
            {/* Сайдбар справа */}
            <aside className="hidden sm:block w-[250px] flex-shrink-0 ml-[0px] mt-[15px] px-[0px]">
              <ProfileSidebar 
                userData={userData} 
                isOwner={user && user.username === username}
                friendsList={friendsList}
                subscribersList={subscribersList}
                subscriptionsList={subscriptionsList}
              />
            </aside>
          </>
        ) : (
          <>
            {/* Сайдбар слева только на вкладке Профиль */}
            {isProfileTab && (
              <aside className="hidden sm:block w-[250px] flex-shrink-0 mt-[15px] mr-0 pl-0">
                <ProfileSidebar 
                  userData={userData} 
                  isOwner={false}
                  friendsList={friendsList}
                  subscribersList={subscribersList}
                  subscriptionsList={subscriptionsList}
                />
              </aside>
            )}
            {/* Основное поле справа, карточки информации над контентом */}
            <div className="flex-1 min-w-0 max-w-5xl ml-0">
              {renderProfileTabs()}
              {isProfileTab && (
                <ProfileInfoCards
                  userData={userData}
                  timezone={timezone}
                  editingBio={editingBio}
                  setEditingBio={setEditingBio}
                  bioSaving={bioSaving}
                  handleSaveBio={handleSaveBio}
                  isOwner={false}
                />
              )}
              <Outlet context={{
                ...outletContext,
                userData, setUserData, updateProfile, refreshUserData, imageVersion
              }} />
            </div>
          </>
        )}
      </div>
    </Container>
  );
}

export default ProfileLayout;