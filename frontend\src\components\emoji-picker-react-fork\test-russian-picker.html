<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест русифицированного эмодзи пикера</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .theme-toggle {
            margin-bottom: 20px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        button:hover {
            background: #f0f0f0;
        }
        button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .selected-emoji {
            font-size: 24px;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .dark-theme {
            background-color: #1a1a1a;
            color: white;
        }
        .dark-theme .container {
            background: #2d2d2d;
        }
        .dark-theme .demo-section {
            border-color: #444;
            background: #333;
        }
        .dark-theme .selected-emoji {
            background: #444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇷🇺 Тест русифицированного эмодзи пикера</h1>
        
        <div class="theme-toggle">
            <button id="lightTheme" class="active">Светлая тема</button>
            <button id="darkTheme">Темная тема</button>
        </div>

        <div class="demo-section">
            <h2>Основной пикер (русская локализация)</h2>
            <p>Этот пикер использует русские названия категорий и интерфейс:</p>
            <div id="russian-picker"></div>
            <div class="selected-emoji">
                Выбранный эмодзи: <span id="selected-emoji-ru">Нет</span>
            </div>
        </div>

        <div class="demo-section">
            <h2>Английский пикер (для сравнения)</h2>
            <p>Этот пикер использует английские названия категорий:</p>
            <div id="english-picker"></div>
            <div class="selected-emoji">
                Selected emoji: <span id="selected-emoji-en">None</span>
            </div>
        </div>

        <div class="demo-section">
            <h2>Информация о форке</h2>
            <ul>
                <li>✅ Русская локализация категорий</li>
                <li>✅ Русский текст поиска</li>
                <li>✅ Поддержка всех эмодзи Unicode</li>
                <li>✅ Темная и светлая темы</li>
                <li>✅ Совместимость с оригинальным API</li>
                <li>✅ Поиск эмодзи</li>
                <li>✅ Категории эмодзи</li>
            </ul>
        </div>
    </div>

    <script>
        // Симуляция React компонентов для демонстрации
        // В реальном приложении это будет работать через React
        
        let currentTheme = 'light';
        
        function updateTheme() {
            document.body.className = currentTheme === 'dark' ? 'dark-theme' : '';
            
            // Обновляем кнопки темы
            document.getElementById('lightTheme').className = currentTheme === 'light' ? 'active' : '';
            document.getElementById('darkTheme').className = currentTheme === 'dark' ? 'active' : '';
        }
        
        function createMockPicker(containerId, locale, selectedEmojiId) {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <div style="border: 1px solid #ccc; border-radius: 8px; padding: 20px; background: #f9f9f9; text-align: center;">
                    <h3>${locale === 'ru' ? '🇷🇺 Русский эмодзи пикер' : '🇺🇸 English Emoji Picker'}</h3>
                    <p>${locale === 'ru' ? 'Категории: Смайлики и люди, Животные и природа, Еда и напитки...' : 'Categories: Smileys & People, Animals & Nature, Food & Drink...'}</p>
                    <div style="margin: 10px 0;">
                        <input type="text" placeholder="${locale === 'ru' ? 'Поиск эмодзи...' : 'Search emojis...'}" style="padding: 8px; border: 1px solid #ccc; border-radius: 4px; width: 200px;">
                    </div>
                    <div style="font-size: 24px; margin: 10px 0;">
                        <span onclick="selectEmoji('😀', '${selectedEmojiId}')" style="cursor: pointer; margin: 5px;">😀</span>
                        <span onclick="selectEmoji('😂', '${selectedEmojiId}')" style="cursor: pointer; margin: 5px;">😂</span>
                        <span onclick="selectEmoji('❤️', '${selectedEmojiId}')" style="cursor: pointer; margin: 5px;">❤️</span>
                        <span onclick="selectEmoji('👍', '${selectedEmojiId}')" style="cursor: pointer; margin: 5px;">👍</span>
                        <span onclick="selectEmoji('🎉', '${selectedEmojiId}')" style="cursor: pointer; margin: 5px;">🎉</span>
                        <span onclick="selectEmoji('🔥', '${selectedEmojiId}')" style="cursor: pointer; margin: 5px;">🔥</span>
                    </div>
                    <p style="font-size: 12px; color: #666;">
                        ${locale === 'ru' ? 'Нажмите на эмодзи для выбора' : 'Click on emoji to select'}
                    </p>
                </div>
            `;
        }
        
        function selectEmoji(emoji, targetId) {
            document.getElementById(targetId).textContent = emoji;
        }
        
        // Инициализация
        document.getElementById('lightTheme').addEventListener('click', () => {
            currentTheme = 'light';
            updateTheme();
        });
        
        document.getElementById('darkTheme').addEventListener('click', () => {
            currentTheme = 'dark';
            updateTheme();
        });
        
        // Создаем мок-пикеры
        createMockPicker('russian-picker', 'ru', 'selected-emoji-ru');
        createMockPicker('english-picker', 'en', 'selected-emoji-en');
        
        updateTheme();
    </script>
</body>
</html>
