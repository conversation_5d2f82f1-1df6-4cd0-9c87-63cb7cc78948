import React, { createContext, useContext, useState, useEffect } from 'react';
import { useWebSocketNotifications } from '../hooks/useWebSocketNotifications';
import { useAuth } from './AuthContext';

const MessageContext = createContext();

export function MessageProvider({ children }) {
    const { isAuthenticated } = useAuth();
    const { notifications, isConnected } = useWebSocketNotifications();
    const [unreadMessagesCount, setUnreadMessagesCount] = useState(0);

    // Обновляем количество непрочитанных сообщений из WebSocket уведомлений
    useEffect(() => {
        if (notifications.unread_messages) {
            setUnreadMessagesCount(notifications.unread_messages.count || 0);
        } else {
            setUnreadMessagesCount(0);
        }
    }, [notifications.unread_messages]);

    // Сбрасываем счетчик при выходе из системы
    useEffect(() => {
        if (!isAuthenticated) {
            setUnreadMessagesCount(0);
        }
    }, [isAuthenticated]);

    // Убираем fallback запрос, так как теперь полностью полагаемся на WebSocket
    const fetchUnreadCount = async () => {
        // Этот метод больше не используется
        console.warn('fetchUnreadCount is deprecated, using WebSocket instead');
    };

    return (
        <MessageContext.Provider value={{ 
            unreadMessagesCount, 
            fetchUnreadCount,
            isWebSocketConnected: isConnected,
            notifications 
        }}>
            {children}
        </MessageContext.Provider>
    );
}

export function useMessage() {
    return useContext(MessageContext);
} 