import React, { useEffect, useRef, useState } from 'react';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import { Button } from 'antd';
import { useTheme } from '../theme/ThemeContext';
import { BoldOutlined, ItalicOutlined, UnderlineOutlined } from '@ant-design/icons';

const MAX_LENGTH = 1000;

const AnnotationEditor = ({ value = '', onChange, disabled = false }) => {
  const [charCount, setCharCount] = useState(0);
  const editorRef = useRef(null);
  const { theme } = useTheme();
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: false,
        code: false,
        blockquote: false,
        horizontalRule: false,
        bulletList: false,
        orderedList: false,
        codeBlock: false,
        hardBreak: true,
      }),
      Underline,
    ],
    content: value,
    editable: !disabled,
    onUpdate({ editor }) {
      const html = editor.getHTML();
      const text = editor.getText();
      setCharCount(text.length);
      if (text.length <= MAX_LENGTH && onChange) {
        onChange(html);
      }
    },
  });

  const [boldActive, setBoldActive] = useState(false);
  const [italicActive, setItalicActive] = useState(false);
  const [underlineActive, setUnderlineActive] = useState(false);

  // Ограничение на ввод
  useEffect(() => {
    if (!editor) return;
    editor.on('beforeInput', ({ event }) => {
      const text = editor.getText();
      if (text.length >= MAX_LENGTH && event.inputType === 'insertText') {
        event.preventDefault();
      }
    });
  }, [editor]);

  // Синхронизация value при внешнем изменении
  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value || '', false);
    }
  }, [value, editor]);

  // Настройка скролла и высоты поля
  useEffect(() => {
    if (!editorRef.current) return;
    const el = editorRef.current.querySelector('.ProseMirror');
    if (!el) return;
    el.style.minHeight = '144px'; // 6 строк по 24px
    el.style.maxHeight = '300px'; // Максимальная высота - примерно 12-13 строк
    el.style.overflowY = 'auto'; // Включаем вертикальный скролл
    el.style.resize = 'none';
    
    // Добавляем класс для кастомного скролла
    el.classList.add('annotation-custom-scrollbar');
  }, [editor]);

  const toolbarBg = theme === 'dark' ? '#23272f' : '#f3f4f6';
  const iconColor = theme === 'dark' ? '#fff' : '#222';
  const iconActiveBg = theme === 'dark' ? '#2563eb' : '#2563eb';
  const iconActiveColor = '#fff';

  return (
    <div className="annotation-editor">
      <div
        className="annotation-toolbar flex gap-0 mb-2 rounded-full px-2 py-1 items-center"
        style={{
          background: toolbarBg,
          border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb',
          width: 182, // 3*36 + 2*16 (разделители + паддинги) + запас
          margin: '0 0 10px 0',
          minHeight: 40,
          justifyContent: 'flex-start',
        }}
      >
        <button
          type="button"
          onClick={() => editor && editor.chain().focus().toggleBold().run()}
          disabled={!editor}
          onMouseDown={() => setBoldActive(true)}
          onMouseUp={() => setBoldActive(false)}
          onMouseLeave={() => setBoldActive(false)}
          style={{
            background: boldActive
              ? (theme === 'dark' ? '#1e40af' : '#1d4ed8')
              : (editor?.isActive('bold') ? iconActiveBg : 'transparent'),
            color: editor?.isActive('bold') ? iconActiveColor : iconColor,
            border: 'none',
            borderRadius: '50%',
            width: 36,
            height: 36,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 700,
            fontSize: 18,
            transition: 'background 0.15s, color 0.15s',
            outline: 'none',
            cursor: 'pointer',
            boxShadow: editor?.isActive('bold') ? '0 0 0 2px #2563eb33' : 'none',
          }}
          title="Ж — Жирный"
        >Ж</button>
        <div style={{width:2, height:24, background: theme === 'dark' ? '#374151' : '#e5e7eb', margin: '0 8px', borderRadius: 2}} />
        <button
          type="button"
          onClick={() => editor && editor.chain().focus().toggleItalic().run()}
          disabled={!editor}
          onMouseDown={() => setItalicActive(true)}
          onMouseUp={() => setItalicActive(false)}
          onMouseLeave={() => setItalicActive(false)}
          style={{
            background: italicActive
              ? (theme === 'dark' ? '#1e40af' : '#1d4ed8')
              : (editor?.isActive('italic') ? iconActiveBg : 'transparent'),
            color: editor?.isActive('italic') ? iconActiveColor : iconColor,
            border: 'none',
            borderRadius: '50%',
            width: 36,
            height: 36,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 18,
            fontStyle: 'italic',
            transition: 'background 0.15s, color 0.15s',
            outline: 'none',
            cursor: 'pointer',
            boxShadow: editor?.isActive('italic') ? '0 0 0 2px #2563eb33' : 'none',
          }}
          title="К — Курсив"
        >К</button>
        <div style={{width:2, height:24, background: theme === 'dark' ? '#374151' : '#e5e7eb', margin: '0 8px', borderRadius: 2}} />
        <button
          type="button"
          onClick={() => editor && editor.chain().focus().toggleUnderline().run()}
          disabled={!editor}
          onMouseDown={() => setUnderlineActive(true)}
          onMouseUp={() => setUnderlineActive(false)}
          onMouseLeave={() => setUnderlineActive(false)}
          style={{
            background: underlineActive
              ? (theme === 'dark' ? '#1e40af' : '#1d4ed8')
              : (editor?.isActive('underline') ? iconActiveBg : 'transparent'),
            color: editor?.isActive('underline') ? iconActiveColor : iconColor,
            border: 'none',
            borderRadius: '50%',
            width: 36,
            height: 36,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 18,
            textDecoration: 'underline',
            transition: 'background 0.15s, color 0.15s',
            outline: 'none',
            cursor: 'pointer',
            boxShadow: editor?.isActive('underline') ? '0 0 0 2px #2563eb33' : 'none',
          }}
          title="П — Подчеркнуть"
        >П</button>
      </div>
      <div
        className="custom-textarea annotation-editor-content relative"
        ref={editorRef}
        style={{
          border: theme === 'dark' ? '2px solid #374151' : '2px solid #d1d5db',
          borderRadius: 8,
          background: theme === 'dark' ? '#111827' : '#fff',
          color: theme === 'dark' ? '#fff' : '#222',
          minHeight: 144,
          padding: '12px 16px',
          fontSize: '1rem',
          transition: 'background 0.2s, color 0.2s',
          boxShadow: theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22',
          outline: editor?.isFocused ? '2px solid #2563eb' : 'none',
        }}
      >
        <EditorContent editor={editor} style={{ border: 'none', outline: 'none', background: 'transparent' }} />
        <div className="text-xs text-right absolute" style={{right: 12, bottom: 8, color: charCount > MAX_LENGTH ? '#ef4444' : '#888', pointerEvents: 'none'}}>
          {charCount} / {MAX_LENGTH}
        </div>
        <style>{`
          .ProseMirror {
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
            background: transparent !important;
            line-height: 1.5;
          }
          .ProseMirror p {
            margin-top: 0;
            margin-bottom: 0.6em;
          }
          
          /* Кастомный скроллбар для редактора аннотации */
          .annotation-custom-scrollbar::-webkit-scrollbar {
            width: 6px;
          }
          
          .annotation-custom-scrollbar::-webkit-scrollbar-track {
            background: ${theme === 'dark' ? 'rgba(75, 85, 99, 0.3)' : 'rgba(156, 163, 175, 0.1)'};
            border-radius: 3px;
          }
          
          .annotation-custom-scrollbar::-webkit-scrollbar-thumb {
            background: ${theme === 'dark' ? 'rgba(156, 163, 175, 0.5)' : 'rgba(156, 163, 175, 0.5)'};
            border-radius: 3px;
            transition: background 0.2s;
          }
          
          .annotation-custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: ${theme === 'dark' ? 'rgba(156, 163, 175, 0.7)' : 'rgba(156, 163, 175, 0.7)'};
          }
          
          /* Для Firefox */
          .annotation-custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: ${theme === 'dark' ? 'rgba(156, 163, 175, 0.5) rgba(75, 85, 99, 0.3)' : 'rgba(156, 163, 175, 0.5) rgba(156, 163, 175, 0.1)'};
          }
        `}</style>
      </div>
      <div className="text-xs italic mt-1" style={{ color: theme === 'dark' ? '#9ca3af' : '#6b7280', marginTop: 4 }}>
        (Краткое описание вашей истории)
      </div>
    </div>
  );
};

export default AnnotationEditor; 