import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../theme/ThemeContext';
import { csrfFetch } from '../utils/csrf';
import OAuthButtons from '../components/OAuthButtons';

function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth();
  const { theme } = useTheme();

  useEffect(() => {
    fetch('/api/auth/csrf/', { credentials: 'include' });
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    const payload = { 
      email: email,
      password: password
    };
    try {
      const response = await csrfFetch('/api/auth/login/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(payload),
      });
      if (response.ok) {
        const data = await response.json();
        // dj-rest-auth возвращает данные пользователя в поле 'user'
        if (data.user) {
          login(data.user);
          navigate('/');
        } else {
          setError('Ошибка входа: неверный формат ответа');
        }
      } else {
        const data = await response.json();
        // Обрабатываем разные типы ошибок от dj-rest-auth
        if (data.non_field_errors) {
          setError(data.non_field_errors[0] || 'Ошибка входа');
        } else if (data.email) {
          setError(data.email[0] || 'Ошибка с email');
        } else if (data.password) {
          setError(data.password[0] || 'Ошибка с паролем');
        } else {
          setError(data.detail || 'Ошибка входа');
        }
      }
    } catch (err) {
      setError('Ошибка сети');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 overflow-y-auto">
      <div className="flex justify-center items-center min-h-screen p-4">
        <div className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-md w-full ${theme === 'dark' ? 'dark' : ''}`}>
          <button
            onClick={() => navigate('/')}
            className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 z-20 bg-transparent p-2 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Закрыть"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <div className="flex flex-col items-center mb-4">
            <img src="/lpo/logo/logolpo.webp" alt="ЛитПортал" className="h-12 mx-auto mb-2" />
            <h1 className="text-xl font-bold text-gray-900 dark:text-white text-center">Вход на ЛитПортал</h1>
          </div>
          <form className="space-y-3" onSubmit={handleSubmit} autoComplete="on">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                autoComplete="username email"
                className="mt-1 block w-full min-w-[260px] md:min-w-[340px] rounded-md border border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm placeholder:text-xs py-2 px-3"
                placeholder="Введите ваш email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                required
              />
            </div>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Пароль
              </label>
              <input
                type="password"
                id="password"
                name="password"
                autoComplete="current-password"
                className="mt-1 block w-full min-w-[260px] md:min-w-[340px] rounded-md border border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm placeholder:text-xs py-2 px-3"
                placeholder="Введите ваш пароль"
                value={password}
                onChange={e => setPassword(e.target.value)}
                required
              />
            </div>
            {error && <div className="rounded-md bg-red-50 dark:bg-red-900/50 p-4"><p className="text-sm text-red-600 dark:text-red-400">{error}</p></div>}
            <div>
              <button
                type="submit"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Войти
              </button>
            </div>
            <p className="text-center text-sm text-gray-600 dark:text-gray-400">
              Нет аккаунта?{' '}
              <Link to="/register" className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                Зарегистрироваться
              </Link>
            </p>
          </form>
          
          {/* OAuth авторизация */}
          <OAuthButtons />
        </div>
      </div>
    </div>
  );
}

export default Login; 