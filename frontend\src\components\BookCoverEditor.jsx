import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import { Button, ColorPicker, Radio, Upload, message, Slider, Switch } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import Cropper from 'react-easy-crop';
import { useTheme } from '../theme/ThemeContext';
import { LABEL_SETTINGS, drawLabel } from '../utils/labelSettings';

const SYSTEM_COVER_PLACEHOLDER = 'https://storage.yandexcloud.net/lpo-test/dist/covertemp/covertemp.jpg';

const BookCoverEditor = forwardRef(({
  title,
  authorName,
  onCoverChange,
  onSave,
  initialColor = '#f0f0f0',
  initialGradient = null,
  initialTextColor = 'dark',
  bookId = null,
  initialCoverType = 'generated',
  initialCustomCover = null,
  authorUsername = null,
  coverUrl = null,
  coverMiniUrl = null,
  coverTempUrl = null,
  // Новые пропсы для возрастных ограничений
  ageRating = '0+',
  hasProfanity = false,
  // Пропс для состояния сохранения
  saving = false,
  // Пропсы для обновления обложки
  needsUpdate = false,
  onUpdate = null
}, ref) => {
  // Отслеживаем текущее значение заголовка из формы
  const [currentTitle, setCurrentTitle] = useState(title);
  const [coverType, setCoverType] = useState('custom');
  const [primaryColor, setPrimaryColor] = useState(initialColor);
  const [useGradient, setUseGradient] = useState(!!initialGradient);
  const [secondaryColor, setSecondaryColor] = useState(initialGradient || '#ffffff');
  const [textColor, setTextColor] = useState(initialTextColor); // 'light', 'dark'
  const [customCover, setCustomCover] = useState(initialCustomCover);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [coverVersion, setCoverVersion] = useState(Date.now());
  const [customCoverExists, setCustomCoverExists] = useState(null);
  const [generatedCoverFile, setGeneratedCoverFile] = useState(null);

  // Состояния для лейблов возрастных ограничений
  const [ageRatingLabel, setAgeRatingLabel] = useState(null);
  const [profanityLabel, setProfanityLabel] = useState(null);

  // Состояния для наложения текста на загруженную обложку
  const [customTextEnabled, setCustomTextEnabled] = useState(false);
  const [customTextColor, setCustomTextColor] = useState('dark');

  const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
  const username = authorUsername;
  const tempCoverUrl = coverTempUrl || null;

  // Предоставляем функцию сохранения через ref
  useImperativeHandle(ref, () => ({
    handleSave: handleSaveWithLabels
  }));
  const customCoverUrl = coverUrl || null;

  // Функция для загрузки лейблов
  const loadAgeRatingLabels = useCallback(async () => {
    console.log('loadAgeRatingLabels called:', { ageRating, hasProfanity });
    try {
      // Загружаем лейбл 18+ если нужен
      if (ageRating === '18+') {
        console.log('Loading 18+ label...');
        const img18 = new Image();
        img18.crossOrigin = 'anonymous';
        img18.onload = () => {
          console.log('18+ label loaded successfully');
          setAgeRatingLabel(img18);
        };
        img18.onerror = () => {
          console.error('Failed to load 18+ label');
        };
        img18.src = 'https://storage.yandexcloud.net/lpo-test/dist/lpo/18/18.png';
      } else {
        console.log('Clearing 18+ label');
        setAgeRatingLabel(null);
      }

      // Загружаем лейбл ненормативной лексики если нужен
      if (hasProfanity) {
        console.log('Loading profanity label...');
        const imgLexic = new Image();
        imgLexic.crossOrigin = 'anonymous';
        imgLexic.onload = () => {
          console.log('Profanity label loaded successfully');
          setProfanityLabel(imgLexic);
        };
        imgLexic.onerror = () => {
          console.error('Failed to load profanity label');
        };
        imgLexic.src = 'https://storage.yandexcloud.net/lpo-test/dist/lpo/18/lexic.png';
      } else {
        console.log('Clearing profanity label');
        setProfanityLabel(null);
      }
    } catch (error) {
      console.error('Ошибка загрузки лейблов:', error);
    }
  }, [ageRating, hasProfanity]);

  // Cropper states
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [cropperOpen, setCropperOpen] = useState(false);

  // Загружаем лейблы при изменении возрастных ограничений
  useEffect(() => {
    loadAgeRatingLabels();
  }, [loadAgeRatingLabels]);

  // Принудительно обновляем превью при изменении лейблов
  useEffect(() => {
    console.log('Labels changed, updating preview...');
    // Принудительно обновляем версию обложки для перегенерации превью
    setCoverVersion(Date.now());
  }, [ageRatingLabel, profanityLabel]);

  // Функция для переноса длинного текста
  const wrapText = useCallback((ctx, text, maxWidth) => {
    const words = text.split(' ');
    const lines = [];
    let currentLine = words[0];

    for (let i = 1; i < words.length; i++) {
      const word = words[i];
      const width = ctx.measureText(currentLine + ' ' + word).width;
      if (width < maxWidth) {
        currentLine += ' ' + word;
      } else {
        lines.push(currentLine);
        currentLine = word;
      }
    }
    lines.push(currentLine);
    return lines;
  }, []);

  // Функция для наложения лейблов на canvas
  const applyAgeRatingLabels = useCallback((ctx, canvasWidth, canvasHeight) => {
    console.log('applyAgeRatingLabels called:', {
      ageRating,
      hasProfanity,
      ageRatingLabel: !!ageRatingLabel,
      profanityLabel: !!profanityLabel,
      canvasSize: { width: canvasWidth, height: canvasHeight }
    });

    // Наложение лейбла 18+ (используем настройки из labelSettings.js)
    if (ageRatingLabel && ageRating === '18+') {
      drawLabel(ctx, ageRatingLabel, 'ageRating', canvasWidth, canvasHeight);
    }

    // Наложение лейбла ненормативной лексики (используем настройки из labelSettings.js)
    if (profanityLabel && hasProfanity) {
      drawLabel(ctx, profanityLabel, 'profanity', canvasWidth, canvasHeight);
    }
  }, [ageRatingLabel, profanityLabel, ageRating, hasProfanity]);

  // Функция для создания обложки без лейблов (для редактора)
  const createCoverWithoutLabels = useCallback(async (sourceCanvas) => {
    const canvas = document.createElement('canvas');
    canvas.width = 700;
    canvas.height = 1000;
    const ctx = canvas.getContext('2d');

    // Копируем исходное изображение без лейблов
    ctx.drawImage(sourceCanvas, 0, 0, 700, 1000);

    return canvas.toDataURL('image/webp', 0.9);
  }, []);

  // Функция для создания обложки с лейблами (для сайта)
  const createCoverWithLabels = useCallback(async (sourceCanvas) => {
    const canvas = document.createElement('canvas');
    canvas.width = 700;
    canvas.height = 1000;
    const ctx = canvas.getContext('2d');

    // Копируем исходное изображение
    ctx.drawImage(sourceCanvas, 0, 0, 700, 1000);

    // Накладываем лейблы с правильными размерами и позициями
    applyAgeRatingLabels(ctx, 700, 1000);

    return canvas.toDataURL('image/webp', 0.9);
  }, [applyAgeRatingLabels]);

  // Вспомогательные функции для фоновой перегенерации
  const createCanvasFromFile = useCallback(async (file) => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = 700;
        canvas.height = 1000;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, 700, 1000);
        resolve(canvas);
      };
      img.onerror = () => resolve(null);
      img.src = URL.createObjectURL(file);
    });
  }, []);

  const createCanvasFromUrl = useCallback(async (url) => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = 700;
        canvas.height = 1000;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, 700, 1000);
        resolve(canvas);
      };
      img.onerror = () => resolve(null);
      img.crossOrigin = 'anonymous';
      img.src = url;
    });
  }, []);

  // Функция обработки сохранения с созданием двух версий обложки
  const handleSaveWithLabels = useCallback(async () => {
    console.log('BookCoverEditor: handleSaveWithLabels called');
    try {
      let sourceCanvas;

      if (coverType === 'generated') {
        // Для сгенерированной обложки создаем canvas без лейблов
        sourceCanvas = document.createElement('canvas');
        sourceCanvas.width = 700;
        sourceCanvas.height = 1000;
        const ctx = sourceCanvas.getContext('2d');

        // Рисуем фон
        if (useGradient) {
          const gradient = ctx.createLinearGradient(0, 0, 0, 1000);
          gradient.addColorStop(0, primaryColor);
          gradient.addColorStop(1, secondaryColor);
          ctx.fillStyle = gradient;
        } else {
          ctx.fillStyle = primaryColor;
        }
        ctx.fillRect(0, 0, 700, 1000);

        // Настройки текста
        const titleFontSize = 60;
        const authorFontSize = 40;
        ctx.textAlign = 'center';

        // Цвет текста и обводка
        if (textColor === 'light') {
          ctx.strokeStyle = 'black';
          ctx.lineWidth = 2;
          ctx.fillStyle = 'white';
        } else {
          ctx.strokeStyle = 'white';
          ctx.lineWidth = 2;
          ctx.fillStyle = 'black';
        }

        // Название книги
        ctx.font = `bold ${titleFontSize}px Arial`;
        const titleLines = wrapText(ctx, title || 'Название книги', 600);
        let y = 200;
        titleLines.forEach(line => {
          ctx.strokeText(line, 350, y);
          ctx.fillText(line, 350, y);
          y += titleFontSize * 1.2;
        });

        const lastTitleY = y - titleFontSize * 1.2;
        const totalSpace = (500 - lastTitleY) * 0.9;
        const authorY = lastTitleY + totalSpace * 0.7;
        const lineY = lastTitleY + totalSpace * 0.3;

        // Горизонтальная черта
        ctx.beginPath();
        ctx.moveTo(200, lineY);
        ctx.lineTo(500, lineY);
        ctx.lineWidth = 3;

        if (textColor === 'light') {
          ctx.strokeStyle = 'black';
          ctx.lineWidth = 4;
          ctx.stroke();
          ctx.beginPath();
          ctx.moveTo(200, lineY);
          ctx.lineTo(500, lineY);
          ctx.strokeStyle = 'white';
          ctx.lineWidth = 2;
          ctx.stroke();
        } else {
          ctx.strokeStyle = 'white';
          ctx.lineWidth = 4;
          ctx.stroke();
          ctx.beginPath();
          ctx.moveTo(200, lineY);
          ctx.lineTo(500, lineY);
          ctx.strokeStyle = 'black';
          ctx.lineWidth = 2;
          ctx.stroke();
        }

        // Имя автора
        ctx.font = `${authorFontSize}px Arial`;
        if (textColor === 'light') {
          ctx.strokeStyle = 'black';
          ctx.lineWidth = 2;
          ctx.fillStyle = 'white';
        } else {
          ctx.strokeStyle = 'white';
          ctx.lineWidth = 2;
          ctx.fillStyle = 'black';
        }
        ctx.strokeText(authorName || 'Автор', 350, authorY);
        ctx.fillText(authorName || 'Автор', 350, authorY);

      } else if (coverType === 'custom' && customCover) {
        // Для пользовательской обложки используем загруженное изображение
        sourceCanvas = document.createElement('canvas');
        sourceCanvas.width = 700;
        sourceCanvas.height = 1000;
        const ctx = sourceCanvas.getContext('2d');

        const img = new Image();
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
          img.src = URL.createObjectURL(customCover);
        });

        ctx.drawImage(img, 0, 0, 700, 1000);

        // Применяем текст если включен
        if (customTextEnabled) {
          applyTextToCustomCover(ctx);
        }
      } else if (coverType === 'custom' && coverUrl) {
        // Для существующей обложки загружаем её с сервера
        sourceCanvas = document.createElement('canvas');
        sourceCanvas.width = 700;
        sourceCanvas.height = 1000;
        const ctx = sourceCanvas.getContext('2d');

        const img = new Image();
        img.crossOrigin = 'anonymous';
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
          img.src = coverUrl;
        });

        ctx.drawImage(img, 0, 0, 700, 1000);

        // Применяем текст если включен
        if (customTextEnabled) {
          applyTextToCustomCover(ctx);
        }
      }

      if (sourceCanvas) {
        let editorCanvas = sourceCanvas;
        let hasTextOverlay = false;
        let textColorSuffix = '';

        // Определяем есть ли наложение текста и какой цвет
        if (coverType === 'generated') {
          // Для сгенерированных обложек всегда есть текст
          hasTextOverlay = true;
          textColorSuffix = textColor === 'light' ? 'txtw' : 'txtb';

          // Создаем версию без текста для редактора (только фон)
          editorCanvas = document.createElement('canvas');
          editorCanvas.width = 700;
          editorCanvas.height = 1000;
          const editorCtx = editorCanvas.getContext('2d');

          // Рисуем только фон без текста
          if (useGradient) {
            const gradient = editorCtx.createLinearGradient(0, 0, 0, 1000);
            gradient.addColorStop(0, primaryColor);
            gradient.addColorStop(1, secondaryColor);
            editorCtx.fillStyle = gradient;
          } else {
            editorCtx.fillStyle = primaryColor;
          }
          editorCtx.fillRect(0, 0, 700, 1000);
        } else if (coverType === 'custom' && customTextEnabled) {
          // Для пользовательских обложек с включенным текстом
          hasTextOverlay = true;
          textColorSuffix = customTextColor === 'light' ? 'txtw' : 'txtb';

          // Создаем версию без текста для редактора
          editorCanvas = document.createElement('canvas');
          editorCanvas.width = 700;
          editorCanvas.height = 1000;
          const editorCtx = editorCanvas.getContext('2d');

          if (customCover) {
            // Загружаем оригинальное изображение без текста
            const img = new Image();
            await new Promise((resolve, reject) => {
              img.onload = resolve;
              img.onerror = reject;
              img.src = URL.createObjectURL(customCover);
            });
            editorCtx.drawImage(img, 0, 0, 700, 1000);
          } else if (coverUrl) {
            // Загружаем существующую обложку без текста
            const img = new Image();
            img.crossOrigin = 'anonymous';
            await new Promise((resolve, reject) => {
              img.onload = resolve;
              img.onerror = reject;
              img.src = coverUrl;
            });
            editorCtx.drawImage(img, 0, 0, 700, 1000);
          }
        }

        // Создаем две версии обложки
        const coverWithoutLabels = await createCoverWithoutLabels(editorCanvas);
        const coverWithLabels = await createCoverWithLabels(sourceCanvas);

        // Вызываем callback с обеими версиями и дополнительной информацией
        if (onSave) {
          console.log('BookCoverEditor: calling onSave callback with:', {
            type: coverType,
            hasTextOverlay,
            textColorSuffix,
            customTextEnabled,
            customTextColor,
            textColor,
            ageRating,
            hasProfanity
          });
          onSave({
            type: coverType,
            file: coverType === 'custom' ? customCover : generatedCoverFile,
            coverWithLabels,
            coverWithoutLabels,
            bookId,
            hasTextOverlay,
            textColorSuffix, // 'txtw' для светлого, 'txtb' для темного, '' для без текста
            // Всегда передаем текущие настройки возрастных ограничений
            ageRating,
            hasProfanity
          });
          console.log('BookCoverEditor: onSave callback completed');
        }
      }
    } catch (error) {
      console.error('Ошибка при создании обложек:', error);
    }
  }, [coverType, customCover, generatedCoverFile, useGradient, primaryColor, secondaryColor, textColor, title, authorName, createCoverWithoutLabels, createCoverWithLabels, onSave, wrapText, customTextEnabled, customTextColor, coverUrl]);
  const [cropperImage, setCropperImage] = useState(null);
  const [uploadImage, setUploadImage] = useState(null);
  const [uploadPreview, setUploadPreview] = useState(null);
  const [uploadError, setUploadError] = useState('');
  const [uploadLoading, setUploadLoading] = useState(false);

  const { theme } = useTheme();

  const initialCustomCoverUrl = coverUrl || customCoverUrl;

  // Генерация обложки
  // Обновляем текущий заголовок при изменении props
  useEffect(() => {
    setCurrentTitle(title);
  }, [title]);

  useEffect(() => {
    if (coverType === 'custom' && !customCover && customCoverUrl) {
      fetch(customCoverUrl, { method: 'HEAD' })
        .then(res => setCustomCoverExists(res.ok))
        .catch(() => setCustomCoverExists(false));
    }
  }, [coverType, customCoverUrl, customCover]);

  // Новый useEffect для генерации и передачи обложки только при изменении параметров генерации
  useEffect(() => {
    if (coverType === 'generated') {
      // Генерируем превью
      const canvas = document.createElement('canvas');
      canvas.width = 700;
      canvas.height = 1000;
      const ctx = canvas.getContext('2d');
      
      // Фон
      if (useGradient) {
        const gradient = ctx.createLinearGradient(0, 0, 0, 1000);
        gradient.addColorStop(0, primaryColor);
        gradient.addColorStop(1, secondaryColor);
        ctx.fillStyle = gradient;
      } else {
        ctx.fillStyle = primaryColor;
      }
      ctx.fillRect(0, 0, 700, 1000);
      
      // Настройки текста
      const titleFontSize = 60; // Увеличено до 60
      const authorFontSize = 40; // Увеличено до 40
      ctx.textAlign = 'center';
      
      // Цвет текста и обводка
      if (textColor === 'light') {
        // Светлый текст с темной обводкой
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 2;
        ctx.fillStyle = 'white';
      } else {
        // Темный текст со светлой обводкой
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 2;
        ctx.fillStyle = 'black';
      }
      
      // Название книги
      ctx.font = `bold ${titleFontSize}px Arial`;
      const titleLines = wrapText(ctx, currentTitle || 'Название книги', 600);
      let y = 200;
      titleLines.forEach(line => {
        ctx.strokeText(line, 350, y);
        ctx.fillText(line, 350, y);
        y += titleFontSize * 1.2;
      });
      
      // Сохраняем последнюю позицию y после отрисовки всех строк названия
      const lastTitleY = y - titleFontSize * 1.2; // Позиция последней строки
      
      // Определяем позицию черты и автора
      
      // Расстояние между последней строкой названия и автором (уменьшено на 10%)
      const totalSpace = (500 - lastTitleY) * 0.9;
      
      // Позиция автора - фиксированная, на расстоянии 2/3 от общего пространства после названия
      const authorY = lastTitleY + totalSpace * 0.7;
      
      // Позиция черты - на расстоянии 1/3 от общего пространства после названия
      const lineY = lastTitleY + totalSpace * 0.3;
      
      // Горизонтальная черта - используем те же цвета, что и для текста
      ctx.beginPath();
      ctx.moveTo(200, lineY);
      ctx.lineTo(500, lineY);
      ctx.lineWidth = 3;
      
      // Применяем тот же стиль обводки и заливки, что и для текста
      if (textColor === 'light') {
        // Сначала рисуем обводку
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 4;
        ctx.stroke();
        
        // Затем рисуем саму линию
        ctx.beginPath();
        ctx.moveTo(200, lineY);
        ctx.lineTo(500, lineY);
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 2;
        ctx.stroke();
      } else {
        // Сначала рисуем обводку
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 4;
        ctx.stroke();
        
        // Затем рисуем саму линию
        ctx.beginPath();
        ctx.moveTo(200, lineY);
        ctx.lineTo(500, lineY);
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 2;
        ctx.stroke();
      }
      
      // Имя автора с обводкой (используем фиксированную позицию)
      ctx.font = `${authorFontSize}px Arial`;
      // Восстанавливаем стили обводки и заливки для текста
      if (textColor === 'light') {
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 2;
        ctx.fillStyle = 'white';
      } else {
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 2;
        ctx.fillStyle = 'black';
      }
      ctx.strokeText(authorName || 'Автор', 350, authorY);
      ctx.fillText(authorName || 'Автор', 350, authorY);

      // Накладываем лейблы возрастных ограничений (только для превью)
      applyAgeRatingLabels(ctx, 700, 1000);

      // Преобразуем canvas в URL и обновляем превью
      const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
      setPreviewUrl(dataUrl);
      fetch(dataUrl)
        .then(res => res.blob())
        .then(blob => {
          const file = new File([blob], `${bookId}_cover.jpg`, { type: 'image/jpeg' });
          setGeneratedCoverFile(file);
          // Не вызываем onCoverChange при обновлении превью с лейблами
          // onCoverChange предназначен только для реальных изменений обложки
        });
    }
  }, [coverType, currentTitle, authorName, primaryColor, secondaryColor, useGradient, textColor, applyAgeRatingLabels, coverVersion]);

  // Отдельный useEffect для уведомления о реальных изменениях обложки (без лейблов)
  useEffect(() => {
    if (coverType === 'generated' && generatedCoverFile) {
      console.log('Generated cover parameters changed, notifying parent');
      onCoverChange && onCoverChange({ type: 'generated', file: generatedCoverFile });
    }
  }, [coverType, currentTitle, authorName, primaryColor, secondaryColor, useGradient, textColor, onCoverChange]);
  
  useEffect(() => {
    if (coverUrl) {
      console.log('Cover URL updated, checking labels state before applying:', {
        coverUrl,
        ageRatingLabel: !!ageRatingLabel,
        profanityLabel: !!profanityLabel,
        ageRating,
        hasProfanity
      });

      // Проверяем загружены ли лейблы
      if ((ageRating === '18+' && !ageRatingLabel) || (hasProfanity && !profanityLabel)) {
        console.log('Labels not loaded yet, waiting...');
        // Если лейблы не загружены, ждем их загрузки
        return;
      }

      console.log('Applying labels to updated cover URL');
      applyLabelsToImage(coverUrl);
    }
  }, [coverUrl, ageRatingLabel, profanityLabel]);

  // Функция для автоматического применения лейблов к изображению
  const applyLabelsToImage = async (imageUrl, textSettings = null) => {
    try {
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = 700;
        canvas.height = 1000;
        const ctx = canvas.getContext('2d');

        // Рисуем изображение
        ctx.drawImage(img, 0, 0, 700, 1000);

        // Применяем текст если включен (используем переданные настройки или текущие)
        const textEnabled = textSettings ? textSettings.enabled : customTextEnabled;
        const textColor = textSettings ? textSettings.color : customTextColor;

        if (textEnabled) {
          applyTextToCustomCover(ctx, textColor);
        }

        // Применяем лейблы возрастного рейтинга
        applyAgeRatingLabels(ctx, 700, 1000);

        // Обновляем превью с лейблами
        const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
        setPreviewUrl(dataUrl);
        console.log('Age rating labels applied to updated cover');
      };

      img.onerror = () => {
        console.error('Failed to load image for label application');
        // Если не удалось загрузить изображение, просто устанавливаем URL как есть
        setPreviewUrl(imageUrl);
      };

      img.src = imageUrl;
    } catch (error) {
      console.error('Error applying labels to image:', error);
      setPreviewUrl(imageUrl);
    }
  };

  // Функция для наложения текста на загруженную обложку (точно как при генерации)
  const applyTextToCustomCover = useCallback((ctx, textColor = null) => {
    // Настройки текста
    const titleFontSize = 60;
    const authorFontSize = 40;
    ctx.textAlign = 'center';

    // Цвет текста и обводка (используем переданный цвет или текущий)
    const colorToUse = textColor || customTextColor;
    if (colorToUse === 'light') {
      ctx.strokeStyle = 'black';
      ctx.lineWidth = 2;
      ctx.fillStyle = 'white';
    } else {
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 2;
      ctx.fillStyle = 'black';
    }

    // Название книги с обводкой
    ctx.font = `bold ${titleFontSize}px Arial`;
    const titleLines = wrapText(ctx, currentTitle || title || 'Название книги', 600);
    let y = 200;
    titleLines.forEach(line => {
      ctx.strokeText(line, 350, y);
      ctx.fillText(line, 350, y);
      y += titleFontSize * 1.2;
    });

    // Сохраняем последнюю позицию y после отрисовки всех строк названия
    const lastTitleY = y - titleFontSize * 1.2; // Позиция последней строки

    // Определяем позицию черты и автора (точно как при генерации)

    // Расстояние между последней строкой названия и автором (уменьшено на 10%)
    const totalSpace = (500 - lastTitleY) * 0.9;

    // Позиция автора - фиксированная, на расстоянии 2/3 от общего пространства после названия
    const authorY = lastTitleY + totalSpace * 0.7;

    // Позиция черты - на расстоянии 1/3 от общего пространства после названия
    const lineY = lastTitleY + totalSpace * 0.3;

    // Горизонтальная черта - используем те же цвета, что и для текста
    ctx.beginPath();
    ctx.moveTo(200, lineY);
    ctx.lineTo(500, lineY);
    ctx.lineWidth = 3;

    // Применяем тот же стиль обводки и заливки, что и для текста
    if (colorToUse === 'light') {
      // Сначала рисуем обводку
      ctx.strokeStyle = 'black';
      ctx.lineWidth = 4;
      ctx.stroke();

      // Затем рисуем саму линию
      ctx.beginPath();
      ctx.moveTo(200, lineY);
      ctx.lineTo(500, lineY);
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 2;
      ctx.stroke();
    } else {
      // Сначала рисуем обводку
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 4;
      ctx.stroke();

      // Затем рисуем саму линию
      ctx.beginPath();
      ctx.moveTo(200, lineY);
      ctx.lineTo(500, lineY);
      ctx.strokeStyle = 'black';
      ctx.lineWidth = 2;
      ctx.stroke();
    }

    // Имя автора с обводкой (используем фиксированную позицию)
    ctx.font = `${authorFontSize}px Arial`;
    // Восстанавливаем стили обводки и заливки для текста
    if (colorToUse === 'light') {
      ctx.strokeStyle = 'black';
      ctx.lineWidth = 2;
      ctx.fillStyle = 'white';
    } else {
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 2;
      ctx.fillStyle = 'black';
    }
    ctx.strokeText(authorName || 'Автор', 350, authorY);
    ctx.fillText(authorName || 'Автор', 350, authorY);
  }, [customTextColor, currentTitle, title, authorName, wrapText]);

  // Обработчик фоновой перегенерации
  useEffect(() => {
    const handleRegenerationRequest = async (event) => {
      const { ageRating, hasProfanity, bookId } = event.detail;

      console.log('BookCoverEditor: Background cover regeneration requested:', {
        ageRating,
        hasProfanity,
        bookId,
        hasCustomCover: !!customCover,
        hasGeneratedCover: !!generatedCoverFile,
        hasCoverUrl: !!coverUrl
      });

      try {
        // Простая проверка - если нет обложки, завершаем сразу
        if (!coverUrl && !customCover && !generatedCoverFile) {
          console.log('BookCoverEditor: No cover to regenerate, completing immediately');

          // Отправляем событие о завершении сразу
          const completionEvent = new CustomEvent('coverRegenerationComplete', {
            detail: { bookId, reason: 'no_cover' }
          });
          document.dispatchEvent(completionEvent);
          return;
        }

        console.log('BookCoverEditor: Starting regeneration process...');

        // Отправляем событие о завершении (заглушка для тестирования)
        setTimeout(() => {
          const completionEvent = new CustomEvent('coverRegenerationComplete', {
            detail: { bookId, reason: 'completed' }
          });
          document.dispatchEvent(completionEvent);
          console.log('BookCoverEditor: Background cover regeneration completed (placeholder)');
        }, 1500); // Увеличили до 1.5 секунд для видимости

      } catch (error) {
        console.error('BookCoverEditor: Background cover regeneration failed:', error);

        // Отправляем событие о завершении даже при ошибке
        const completionEvent = new CustomEvent('coverRegenerationComplete', {
          detail: { bookId, reason: 'error', error: error.message }
        });
        document.dispatchEvent(completionEvent);
      }
    };

    // Добавляем data-cover-editor атрибут для идентификации
    const element = document.querySelector('[data-cover-editor]') ||
                   document.querySelector('.book-cover-editor') ||
                   document.body;

    if (!element.hasAttribute('data-cover-editor')) {
      element.setAttribute('data-cover-editor', 'true');
    }

    element.addEventListener('requestCoverRegeneration', handleRegenerationRequest);

    return () => {
      element.removeEventListener('requestCoverRegeneration', handleRegenerationRequest);
    };
  }, []); // Убираем зависимости, так как функция использует актуальные значения из замыкания

  // Обработка пользовательских обложек с лейблами
  useEffect(() => {
    if (customCover && (ageRating === '18+' || hasProfanity)) {
      console.log('Processing custom cover with labels...');
      const processCustomCover = async () => {
        try {
          const canvas = document.createElement('canvas');
          canvas.width = 700;
          canvas.height = 1000;
          const ctx = canvas.getContext('2d');

          const img = new Image();
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = URL.createObjectURL(customCover);
          });

          // Рисуем изображение
          ctx.drawImage(img, 0, 0, 700, 1000);

          // Применяем текст если включен
          if (customTextEnabled) {
            applyTextToCustomCover(ctx, customTextColor);
          }

          // Накладываем лейблы
          applyAgeRatingLabels(ctx, 700, 1000);

          // Обновляем превью
          const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
          setPreviewUrl(dataUrl);

          console.log('Custom cover with labels processed successfully');
        } catch (error) {
          console.error('Error processing custom cover with labels:', error);
        }
      };

      processCustomCover();
    } else if (customCover) {
      // Если нет лейблов, просто показываем оригинальное изображение
      setPreviewUrl(URL.createObjectURL(customCover));
    }
  }, [customCover, ageRating, hasProfanity, applyAgeRatingLabels, customTextEnabled, customTextColor]);
  

  
  // Функция для получения обрезанного изображения
  async function getCroppedImg(imageSrc, cropPixels) {
    const image = new window.Image();
    image.src = imageSrc;
    await new Promise(resolve => { image.onload = resolve; });
    const canvas = document.createElement('canvas');
    canvas.width = 700;
    canvas.height = 1000;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(
      image,
      cropPixels.x, cropPixels.y, cropPixels.width, cropPixels.height,
      0, 0, 700, 1000
    );
    return new Promise(resolve => {
      canvas.toBlob(blob => {
        resolve(blob);
      }, 'image/jpeg', 0.9);
    });
  }

  // Обработка выбора файла
  function handleImageChange(e) {
    setUploadError('');
    const file = e.target.files[0];
    if (!file) return;
    const img = new window.Image();
    const reader = new FileReader();
    reader.onload = ev => {
      img.onload = () => {
        if (img.width < 700 || img.height < 1000) {
          setUploadError('Пожалуйста, загрузите изображение размером не менее 700x1000 px');
          setUploadImage(null);
          setUploadPreview(null);
        } else {
          setUploadImage(file);
          setUploadPreview(ev.target.result);
          setCropperOpen(true);
          setCropperImage(ev.target.result);
        }
      };
      img.onerror = () => {
        setUploadError('Не удалось прочитать изображение. Попробуйте другой файл.');
        setUploadImage(null);
        setUploadPreview(null);
      };
      img.src = ev.target.result;
    };
    reader.onerror = () => {
      setUploadError('Ошибка чтения файла. Попробуйте другой файл.');
      setUploadImage(null);
      setUploadPreview(null);
    };
    reader.readAsDataURL(file);
  }

  // При завершении cropper'а
  const onCropComplete = (croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  };

  // Сохранить обрезанную обложку
  async function handleCustomCoverSave() {
    if (!uploadPreview || !croppedAreaPixels) return;
    setUploadLoading(true);
    setUploadError('');
    try {
      const blob = await getCroppedImg(uploadPreview, croppedAreaPixels);
      const file = new File([blob], `${bookId}_cover.jpg`, { type: 'image/jpeg' });
      setCustomCover(file);
      setCoverVersion(Date.now());
      setCropperOpen(false);
      setUploadImage(null);
      setUploadPreview(null);
      setCroppedAreaPixels(null);
      setCrop({ x: 0, y: 0 });
      setZoom(1);
      setCoverType('custom');

      // Обновляем превью с учетом настроек текста
      updateCustomCoverPreview(file);

      onCoverChange && onCoverChange({ type: 'custom', file });
      message.success('Обрезка завершена. Не забудьте нажать Сохранить!');
    } catch (err) {
      setUploadError('Ошибка при обработке изображения');
    } finally {
      setUploadLoading(false);
    }
  }

  // Функция для обновления превью загруженной обложки с текстом
  const updateCustomCoverPreview = async (imageFile) => {
    console.log('updateCustomCoverPreview called with text settings:', {
      customTextEnabled,
      customTextColor
    });
    try {
      const canvas = document.createElement('canvas');
      canvas.width = 700;
      canvas.height = 1000;
      const ctx = canvas.getContext('2d');

      // Создаем URL для файла
      const imageUrl = URL.createObjectURL(imageFile);
      const img = new Image();

      img.onload = () => {
        // Рисуем изображение
        ctx.drawImage(img, 0, 0, 700, 1000);

        // Применяем текст если включен
        if (customTextEnabled) {
          applyTextToCustomCover(ctx, customTextColor);
        }

        // Применяем лейблы возрастного рейтинга
        applyAgeRatingLabels(ctx, 700, 1000);

        // Обновляем превью
        const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
        setPreviewUrl(dataUrl);

        // Очищаем временный URL
        URL.revokeObjectURL(imageUrl);
      };

      img.src = imageUrl;
    } catch (error) {
      console.error('Error updating custom cover preview:', error);
    }
  };


  
  // Генерация миниатюры (210x300, 100%)
  async function getMiniImg(imageSrc, cropPixels) {
    const image = new window.Image();
    image.src = imageSrc;
    await new Promise(resolve => { image.onload = resolve; });
    const canvas = document.createElement('canvas');
    canvas.width = 210;
    canvas.height = 300;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(
      image,
      cropPixels.x, cropPixels.y, cropPixels.width, cropPixels.height,
      0, 0, 210, 300
    );
    return new Promise(resolve => {
      canvas.toBlob(blob => {
        resolve(blob);
      }, 'image/jpeg', 1.0);
    });
  }
  
  // Добавить после других useEffect
  useEffect(() => {
    const styleId = 'cover-label-styles';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.innerHTML = `
        .color-light-label {
          color: #fff !important;
          text-shadow:
            1px 0 0 #000,
            0 1px 0 #000;
        }
        .color-dark-label {
          color: #111 !important;
          text-shadow:
            1px 0 0 #fff,
            0 1px 0 #fff;
        }
        .bookcover-radio-group .ant-radio-button-wrapper {
          border-radius: 8px !important;
          margin-right: 8px;
          white-space: nowrap !important;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .bookcover-radio-group .ant-radio-button-wrapper:last-child {
          margin-right: 0;
        }
        .bookcover-radio-group .ant-radio-button-wrapper:not(:first-child)::before {
          display: none !important;
        }
        /* Фиксы для ColorPicker */
        .ant-color-picker-panel {
          min-height: auto !important;
        }
        .ant-color-picker-panel .ant-color-picker-input-container {
          padding: 8px 12px !important;
          min-height: 60px !important;
        }
        .ant-color-picker-panel .ant-color-picker-input-container .ant-color-picker-format-select {
          margin-bottom: 8px !important;
        }
        .ant-color-picker-panel .ant-color-picker-input-container .ant-flex {
          gap: 8px !important;
          margin-top: 8px !important;
        }
        /* Убираем синюю рамку focus для ColorPicker */
        .ant-color-picker-panel input:focus {
          box-shadow: none !important;
          border-color: #d1d5db !important;
          outline: none !important;
        }
        .ant-color-picker-panel .ant-input:focus {
          box-shadow: none !important;
          border-color: #d1d5db !important;
          outline: none !important;
        }
        /* Только исправляем фон панели ColorPicker без блокировки функционала */
        .ant-color-picker-panel {
          background: #ffffff !important;
          border: 1px solid #d9d9d9 !important;
        }
        
        /* Скрываем именно селектор формата цвета (HEX/HSB/RGB) */
        .ant-color-picker-format-select,
        .ant-color-picker-panel .ant-color-picker-format-select,
        .ant-select.ant-color-picker-format-select {
          display: none !important;
        }
        
        /* Исправляем только текстовые поля, не трогая цветовую палитру */
        .ant-color-picker-panel .ant-input {
          background: #ffffff !important;
          color: #000000 !important;
          border: 1px solid #d9d9d9 !important;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);



  // Восстановление настроек текста из имени файла при загрузке
  useEffect(() => {
    console.log('Text restoration useEffect triggered:', {
      coverType,
      coverUrl: !!coverUrl,
      customCover: !!customCover,
      condition: coverType === 'custom' && coverUrl && !customCover
    });

    // Анализируем имя файла только для вкладки "Обложка книги"
    if (coverType === 'custom' && coverUrl && !customCover) {
      // Анализируем имя файла для восстановления настроек текста
      const filename = coverUrl.split('/').pop() || '';
      console.log('Analyzing filename for text settings:', filename);
      console.log('Current text settings before restoration:', {
        customTextEnabled,
        customTextColor
      });

      if (filename.includes('_txtw_editor_')) {
        // Светлый текст
        console.log('Found txtw suffix - setting light text');
        setCustomTextEnabled(true);
        setCustomTextColor('light');
      } else if (filename.includes('_txtb_editor_')) {
        // Темный текст
        console.log('Found txtb suffix - setting dark text');
        setCustomTextEnabled(true);
        setCustomTextColor('dark');
      } else {
        // Без текста
        console.log('No text suffix found - disabling text');
        setCustomTextEnabled(false);
        setCustomTextColor('dark');
      }

      console.log('Text settings updated, preview will update automatically via useEffect');
    }
  }, [coverType, coverUrl, customCover]);

  // Обновление превью на лету при изменении настроек текста и лейблов для загруженной обложки
  useEffect(() => {
    if (coverType === 'custom') {
      if (customCover) {
        updateCustomCoverPreview(customCover);
      } else if (coverUrl) {
        console.log('Updating preview for coverUrl with settings:', {
          customTextEnabled,
          customTextColor,
          ageRating,
          hasProfanity,
          ageRatingLabel: !!ageRatingLabel,
          profanityLabel: !!profanityLabel
        });

        // Проверяем загружены ли лейблы перед применением
        if ((ageRating === '18+' && !ageRatingLabel) || (hasProfanity && !profanityLabel)) {
          console.log('Labels not loaded yet in preview update, waiting...');
          return;
        }

        console.log('Applying labels in preview update');
        applyLabelsToImage(coverUrl, {
          enabled: customTextEnabled,
          color: customTextColor
        });
      }
    }
  }, [coverType, customTextEnabled, customTextColor, currentTitle, ageRating, hasProfanity, ageRatingLabel, profanityLabel]);

  // Сброс состояний при возврате на вкладку 'Обложка книги'
  useEffect(() => {
    if (coverType === 'custom') {
      setCustomCover(null);
      setGeneratedCoverFile(null);
      setUploadImage(null);
      setUploadPreview(null);
      setCropperOpen(false);
      setCropperImage(null);
      setCroppedAreaPixels(null);
      setCrop({ x: 0, y: 0 });
      setZoom(1);
      setUploadError('');
      // НЕ сбрасываем настройки текста - они будут восстановлены из имени файла
      // setCustomTextEnabled(false);
      // setCustomTextColor('dark');
    }
  }, [coverType]);

  return (
    <div className="flex flex-col md:flex-row gap-6">
      {/* Превью обложки */}
      <div className="w-full md:w-1/3 flex justify-center">
        <div className={theme === 'dark' ? 'p-4 rounded-lg flex justify-center items-center bg-gray-800' : 'p-4 rounded-lg flex justify-center items-center bg-gray-100'}>
          <div className="relative group cursor-pointer" style={{ width: '233px', height: '333px' }}>
            {/* Основная обложка книги */}
            <div className="relative h-full bg-gray-200 dark:bg-gray-700 overflow-hidden shadow-lg transform transition-transform duration-300 group-hover:scale-105" 
                 style={{
                   borderRadius: '0 8px 8px 0'
                 }}>
              <img
                src={previewUrl ? previewUrl : SYSTEM_COVER_PLACEHOLDER}
                alt="Обложка книги"
                className="w-full h-full object-cover"
              />

              {/* Индикатор загрузки при сохранении */}
              {saving && (
                <div className="absolute inset-0 bg-black/60 flex flex-col items-center justify-center text-white">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-3"></div>
                  <div className="text-sm font-medium">Сохраняем обложку...</div>
                </div>
              )}

              {/* Корешок книги - левая полоска */}
              <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-r from-black/30 via-black/10 to-transparent"></div>

              {/* Дополнительная тень для глубины */}
              <div className="absolute left-1 top-0 bottom-0 w-1 bg-gradient-to-r from-black/20 to-transparent"></div>

              {/* Блик на обложке */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            
            {/* Тень под книгой */}
            <div className="absolute -bottom-1 left-1 right-2 h-1 bg-black/20 rounded-full blur-sm transform transition-transform duration-300 group-hover:scale-110"></div>
          </div>
        </div>
      </div>
      
      {/* Настройки обложки */}
      <div className="flex-1">
        <div className="mb-4">
          <h3 className={theme === 'dark' ? 'text-lg font-medium mb-2 text-white' : 'text-lg font-medium mb-2 text-gray-900'}>Тип обложки</h3>
          <Radio.Group
            value={coverType}
            onChange={(e) => setCoverType(e.target.value)}
            className="flex gap-6 mb-2 bookcover-radio-group"
            optionType="button"
            buttonStyle="solid"
            disabled={needsUpdate}
          >
            <Radio.Button value="generated">Настроить новую</Radio.Button>
            <Radio.Button value="custom">Обложка книги</Radio.Button>
          </Radio.Group>
        </div>
        
        {coverType === 'generated' ? (
          <>
            <div className="mb-4">
              <h3 className={theme === 'dark' ? 'text-lg font-medium mb-2 text-white' : 'text-lg font-medium mb-2 text-gray-900'}>Цвет фона</h3>
              <div className="flex items-center gap-4">
                <div className="color-picker-light-wrapper">
                  <ColorPicker 
                    value={primaryColor} 
                    onChange={(color) => setPrimaryColor(color.toHexString())}
                    format="hex"
                    disabledAlpha
                    showText={false}
                    allowClear={false}
                  />
                </div>
                <span className={theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}>Основной цвет</span>
              </div>
            </div>
            
            <div className="mb-4">
              <div className="flex items-center gap-2">
                <span className={theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}>Использовать градиент</span>
                <Switch 
                  checked={useGradient} 
                  onChange={(checked) => setUseGradient(checked)}
                />
              </div>
              
              {useGradient && (
                <div className="mt-2 flex items-center gap-4">
                  <div className="color-picker-light-wrapper">
                    <ColorPicker 
                      value={secondaryColor} 
                      onChange={(color) => setSecondaryColor(color.toHexString())}
                      format="hex"
                      disabledAlpha
                      showText={false}
                      allowClear={false}
                    />
                  </div>
                  <span className={theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}>Второй цвет</span>
                </div>
              )}
            </div>
            
            <div className="mb-4">
              <h3 className={theme === 'dark' ? 'text-lg font-medium mb-2 text-white' : 'text-lg font-medium mb-2 text-gray-900'}>Цвет текста</h3>
              <Radio.Group 
                value={textColor} 
                onChange={(e) => setTextColor(e.target.value)}
                className={theme === 'dark' ? 'dark-radio-group' : ''}
              >
                <Radio value="light"><span className="color-light-label">Светлый</span></Radio>
                <Radio value="dark"><span className="color-dark-label">Темный</span></Radio>
              </Radio.Group>
            </div>
          </>
        ) : (
          <>
            <div className="mb-4">
              <h3 className={theme === 'dark' ? 'text-lg font-medium mb-2 text-white' : 'text-lg font-medium mb-2 text-gray-900'}>Загрузить обложку</h3>
              <div className="flex items-center gap-4">
                <label
                  htmlFor="cover-file-input"
                  className={`p-2 rounded border flex items-center ${
                    theme === 'dark'
                      ? 'bg-gray-700 text-white hover:bg-gray-600 border-gray-600 cursor-pointer'
                      : 'bg-gray-100 text-gray-900 hover:bg-gray-200 border-gray-300 cursor-pointer'
                  }`}
                  title="Выбрать файл"
                >
                  <span className="text-xl">🖼️</span>
                  <input
                    id="cover-file-input"
                    type="file"
                    accept="image/jpeg,image/png"
                    onChange={handleImageChange}
                    disabled={false}
                    style={{opacity:0, width:0, height:0, position:'absolute', pointerEvents:'none'}}
                    tabIndex={-1}
                  />
                  <span className={theme === 'dark' ? 'text-gray-300 ml-3' : 'text-gray-700 ml-3'} style={{maxWidth: 220, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'inline-block'}}>
                    {uploadImage ? uploadImage.name : 'Выберите файл...'}
                  </span>
                </label>
              </div>
              {uploadError && <div className="text-red-500 text-sm mt-2">{uploadError}</div>}
              <p className={theme === 'dark' ? 'text-sm mt-2 text-gray-400' : 'text-sm mt-2 text-gray-500'}>
                Рекомендуемый размер: 700x1000 пикселей
              </p>
            </div>

            {/* Настройки наложения текста */}
            {(customCover || coverUrl) && (
              <div className="mb-4">
                <div className="flex items-center gap-3 mb-3">
                  <Switch
                    checked={customTextEnabled}
                    onChange={setCustomTextEnabled}
                    disabled={false}
                  />
                  <h3 className={theme === 'dark' ? 'text-lg font-medium text-white' : 'text-lg font-medium text-gray-900'}>
                    Наложить текст
                  </h3>
                </div>

                {customTextEnabled && (
                  <div className="ml-6">
                    <h4 className={theme === 'dark' ? 'text-md font-medium mb-2 text-white' : 'text-md font-medium mb-2 text-gray-900'}>Цвет текста</h4>
                    <Radio.Group
                      value={customTextColor}
                      onChange={(e) => setCustomTextColor(e.target.value)}
                      className={theme === 'dark' ? 'dark-radio-group' : ''}
                      disabled={false}
                    >
                      <Radio value="light"><span className="color-light-label">Светлый</span></Radio>
                      <Radio value="dark"><span className="color-dark-label">Темный</span></Radio>
                    </Radio.Group>
                  </div>
                )}
              </div>
            )}
          </>
        )}
        {/* Cropper modal */}
        {cropperOpen && (
          <div className={theme === 'dark' ? 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70' : 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40'}>
            <div className={theme === 'dark' ? 'rounded-lg shadow-lg p-6 w-[95vw] max-w-[700px] relative bg-gray-800' : 'rounded-lg shadow-lg p-6 w-[95vw] max-w-[700px] relative bg-white'}>
              <button
                className={theme === 'dark' ? 'absolute top-2 right-2 rounded-full w-10 h-10 flex items-center justify-center shadow-lg z-10 bg-gray-700 text-white hover:bg-gray-600' : 'absolute top-2 right-2 rounded-full w-10 h-10 flex items-center justify-center shadow-lg z-10 bg-gray-200 text-gray-900 hover:bg-gray-300'}
                onClick={() => setCropperOpen(false)}
                title="Закрыть"
                style={{lineHeight: 0, border: 'none'}}>
                ×
              </button>
              <h2 className={theme === 'dark' ? 'text-xl font-bold mb-4 text-gray-100' : 'text-xl font-bold mb-4 text-gray-800'}>Обрезка обложки</h2>
              <div className="relative mb-4" style={{height: 400}}>
                <Cropper
                  image={cropperImage}
                  crop={crop}
                  zoom={zoom}
                  aspect={0.7}
                  onCropChange={setCrop}
                  onZoomChange={setZoom}
                  onCropComplete={onCropComplete}
                  restrictPosition={true}
                  minZoom={1}
                  maxZoom={3}
                  showGrid={true}
                  objectFit="contain"
                  style={{
                    containerStyle: {
                      backgroundColor: theme === 'dark' ? '#23272f' : '#f3f4f6',
                      borderRadius: '8px',
                      height: 400
                    }
                  }}
                />
              </div>
              <div className="flex items-center gap-4 mb-4">
                <input
                  type="range"
                  min={1}
                  max={3}
                  step={0.1}
                  value={zoom}
                  onChange={e => setZoom(parseFloat(e.target.value))}
                  className={theme === 'dark' ? 'w-full rounded-lg appearance-none cursor-pointer bg-gray-700' : 'w-full rounded-lg appearance-none cursor-pointer bg-gray-200'}
                />
              </div>
              <div className="flex justify-end gap-4">
                <button
                  className={theme === 'dark' ? 'px-4 py-2 rounded font-medium transition-colors bg-gray-700 text-white hover:bg-gray-600' : 'px-4 py-2 rounded font-medium transition-colors bg-gray-200 text-gray-700 hover:bg-gray-300'}
                  onClick={() => setCropperOpen(false)}
                >
                  Отмена
                </button>
                <button
                  className={theme === 'dark' ? 'px-4 py-2 rounded font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white' : 'px-4 py-2 rounded font-medium transition-colors bg-blue-500 hover:bg-blue-600 text-white'}
                  onClick={handleCustomCoverSave}
                  disabled={uploadLoading}
                >
                  {uploadLoading ? 'Сохранение...' : 'Обрезать и применить'}
                </button>
              </div>
            </div>
          </div>
        )}
        <div className="mt-4 flex gap-3">
          <Button
            type="primary"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleSaveWithLabels();
            }}
            htmlType="button"
            loading={saving}
            disabled={saving}
          >
            {saving ? 'Сохраняем...' : 'Сохранить'}
          </Button>




        </div>
      </div>
    </div>
  );
});

export default BookCoverEditor;