# Generated by Django 5.0.2 on 2025-05-16 16:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0026_dialoguserstate'),
    ]

    operations = [
        migrations.AddField(
            model_name='dialoguserstate',
            name='deleted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_dialogs', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='dialoguserstate',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dialog_states', to=settings.AUTH_USER_MODEL),
        ),
    ]
