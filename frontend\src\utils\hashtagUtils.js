/**
 * Утилиты для работы с хештегами
 * Поддерживают хештеги из нескольких слов с пробелами
 * Ограничения: до 40 символов, только русские буквы, цифры и пробелы
 */

// Константы для валидации
const MAX_HASHTAG_LENGTH = 40;
const HASHTAG_REGEX = /^[а-яё0-9\s]+$/i; // Только русские буквы, цифры и пробелы

/**
 * Очищает и нормализует хештег
 * Убирает недопустимые символы, приводит к нижнему регистру
 * Обеспечивает правильное форматирование пробелов
 * @param {string} tag - Исходный хештег
 * @returns {string} - Очищенный хештег
 */
export function cleanHashtag(tag) {
  if (!tag || typeof tag !== 'string') return '';

  return tag
    .toLowerCase() // Приводим к нижнему регистру
    .replace(/[^а-яё0-9\s]/g, '') // Убираем все кроме русских букв, цифр и пробелов
    .replace(/^\s+/, '') // Убираем пробелы в начале
    .replace(/\s+/g, ' ') // Заменяем множественные пробелы на одинарные
    .replace(/\s+$/, ''); // Убираем пробелы в конце
}

/**
 * Валидирует хештег
 * @param {string} tag - Хештег для проверки
 * @returns {Object} - Результат валидации {isValid: boolean, error: string}
 */
export function validateHashtag(tag) {
  if (!tag || typeof tag !== 'string') {
    return { isValid: false, error: 'Хештег не может быть пустым' };
  }

  const cleaned = cleanHashtag(tag);

  if (!cleaned) {
    return { isValid: false, error: 'Хештег содержит только недопустимые символы' };
  }

  if (cleaned.length > MAX_HASHTAG_LENGTH) {
    return { isValid: false, error: `Хештег не может быть длиннее ${MAX_HASHTAG_LENGTH} символов` };
  }

  if (!HASHTAG_REGEX.test(cleaned)) {
    return { isValid: false, error: 'Хештег может содержать только русские буквы, цифры и пробелы' };
  }

  // Проверяем, что хештег не состоит только из пробелов и цифр
  if (!/[а-яё]/i.test(cleaned)) {
    return { isValid: false, error: 'Хештег должен содержать хотя бы одну русскую букву' };
  }

  // Проверяем, что хештег не начинается с пробела (после очистки не должно быть, но для надежности)
  if (cleaned.startsWith(' ')) {
    return { isValid: false, error: 'Хештег не может начинаться с пробела' };
  }

  // Проверяем, что хештег не заканчивается пробелом
  if (cleaned.endsWith(' ')) {
    return { isValid: false, error: 'Хештег не может заканчиваться пробелом' };
  }

  // Проверяем, что нет множественных пробелов (после очистки не должно быть, но для надежности)
  if (/\s{2,}/.test(cleaned)) {
    return { isValid: false, error: 'Между словами может быть только один пробел' };
  }

  return { isValid: true, error: '' };
}

/**
 * Преобразует хештег для сохранения в базе данных
 * Очищает, валидирует и заменяет пробелы на подчеркивания
 * @param {string} displayTag - Хештег для отображения (с пробелами)
 * @returns {string} - Хештег для сохранения (с подчеркиваниями)
 */
export function tagForStorage(displayTag) {
  if (!displayTag || typeof displayTag !== 'string') return '';

  const cleaned = cleanHashtag(displayTag);
  if (!cleaned) return '';

  return cleaned.replace(/\s+/g, '_'); // Заменяем пробелы на подчеркивания
}

/**
 * Преобразует хештег из базы данных для отображения
 * Заменяет подчеркивания на пробелы для удобства чтения
 * @param {string} storageTag - Хештег из базы данных (с подчеркиваниями)
 * @returns {string} - Хештег для отображения (с пробелами)
 */
export function tagForDisplay(storageTag) {
  if (!storageTag || typeof storageTag !== 'string') return '';
  
  return storageTag
    .replace(/_/g, ' ') // Заменяем подчеркивания на пробелы
    .trim();
}

/**
 * Преобразует массив хештегов для сохранения
 * @param {string[]} displayTags - Массив хештегов для отображения
 * @returns {string[]} - Массив хештегов для сохранения
 */
export function tagsForStorage(displayTags) {
  if (!Array.isArray(displayTags)) return [];
  
  return displayTags
    .filter(tag => tag && typeof tag === 'string' && tag.trim())
    .map(tag => tagForStorage(tag));
}

/**
 * Преобразует массив хештегов для отображения
 * @param {string[]} storageTags - Массив хештегов из базы данных
 * @returns {string[]} - Массив хештегов для отображения
 */
export function tagsForDisplay(storageTags) {
  if (!Array.isArray(storageTags)) return [];
  
  return storageTags
    .filter(tag => tag && typeof tag === 'string' && tag.trim())
    .map(tag => tagForDisplay(tag));
}

/**
 * Нормализует хештег для поиска и сравнения
 * @param {string} tag - Хештег в любом формате
 * @returns {string} - Нормализованный хештег
 */
export function normalizeTag(tag) {
  if (!tag || typeof tag !== 'string') return '';

  const cleaned = cleanHashtag(tag);
  return cleaned.replace(/\s+/g, '_'); // Приводим к единому формату для сравнения
}

/**
 * Проверяет, содержит ли массив хештегов определенный хештег
 * Учитывает различные форматы (с пробелами и подчеркиваниями)
 * @param {string[]} tags - Массив хештегов
 * @param {string} searchTag - Искомый хештег
 * @returns {boolean} - Найден ли хештег
 */
export function includesTag(tags, searchTag) {
  if (!Array.isArray(tags) || !searchTag) return false;
  
  const normalizedSearch = normalizeTag(searchTag);
  return tags.some(tag => normalizeTag(tag) === normalizedSearch);
}
