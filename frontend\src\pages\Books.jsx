import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useUserSettings } from '../context/UserSettingsContext';
import { formatDateWithTimezone } from '../utils/formatDate';
import { csrfFetch } from '../utils/csrf';
import LibraryButtons from '../components/LibraryButtons';
import { Tooltip, Modal } from 'antd';
import { useTheme } from '../theme/ThemeContext';
import { createShortDescription } from '../utils/htmlUtils';
import '../styles/bookDescription.css';

function Books() {
  const { user } = useAuth();
  const { timezone } = useUserSettings();
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [books, setBooks] = useState([]);
  const [filteredBooks, setFilteredBooks] = useState([]);
  const [showAdultNotice, setShowAdultNotice] = useState(false);
  const [ageConfirmVisible, setAgeConfirmVisible] = useState(false);
  const [pendingBookId, setPendingBookId] = useState(null);

  const handleBookClick = (bookId) => {
    const book = filteredBooks.find(b => b.id === bookId);
    // Если книга 18+ и (гость или нет даты рождения)
    if (book?.is_adult && (!user || !user.birth_date)) {
      // Проверяем, подтверждал ли пользователь возраст ранее
      if (window.sessionStorage.getItem('adultConfirmed') === 'true') {
        navigate(`/book/${bookId}`);
      } else {
        setPendingBookId(bookId);
        setAgeConfirmVisible(true);
      }
      return;
    }
    navigate(`/book/${bookId}`);
  };

  const handleAgeConfirm = () => {
    window.sessionStorage.setItem('adultConfirmed', 'true');
    setAgeConfirmVisible(false);
    if (pendingBookId) {
      navigate(`/book/${pendingBookId}`);
      setPendingBookId(null);
    }
  };

  const handleAgeDecline = () => {
    setAgeConfirmVisible(false);
    setPendingBookId(null);
  };

  useEffect(() => {
    // Загрузка книг с бэка
    const fetchBooks = async () => {
      const res = await fetch('/api/books-list/');
      if (res.ok) {
        const data = await res.json();
        setBooks(data);
      }
    };
    fetchBooks();
  }, []);

  useEffect(() => {
    if (!user) return setFilteredBooks(books);
    const birthDate = user.birth_date;
    let age = null;
    if (birthDate) {
      const today = new Date();
      const dob = new Date(birthDate);
      age = today.getFullYear() - dob.getFullYear();
      const m = today.getMonth() - dob.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
        age--;
      }
    }
    let filtered = books;
    let hasAdult = false;
    if (!birthDate || age < 18) {
      filtered = books.filter(b => !b.is_adult);
      hasAdult = books.some(b => b.is_adult);
    }
    setFilteredBooks(filtered);
    setShowAdultNotice(!birthDate || age < 18 ? hasAdult : false);
  }, [books, user]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Книги</h1>
        <button className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
          Добавить книгу
        </button>
      </div>
      {showAdultNotice && (
        <div className="mb-4 text-sm text-yellow-700 bg-yellow-50 rounded p-3">
          Некоторые произведения скрыты, так как доступны только пользователям старше 18 лет.
        </div>
      )}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredBooks.map(book => (
          <div key={book.id} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="relative group cursor-pointer aspect-[7/10]" onClick={() => handleBookClick(book.id)}>
              {/* Основная обложка книги */}
              <div className="relative h-full bg-gray-200 dark:bg-gray-700 overflow-hidden shadow-lg transform transition-transform duration-300 group-hover:scale-105" 
                   style={{
                     borderRadius: '0 8px 8px 0'
                   }}>
                <img
                  alt={book.title}
                  src={book.cover_mini_url || book.cover_temp_url || '/covertemp/covertemp.jpg'}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.src = '/covertemp/covertemp.jpg';
                  }}
                />
                
                {/* Корешок книги - левая полоска */}
                <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-r from-black/30 via-black/10 to-transparent"></div>
                
                {/* Дополнительная тень для глубины */}
                <div className="absolute left-1 top-0 bottom-0 w-1 bg-gradient-to-r from-black/20 to-transparent"></div>
                
                {/* Блик на обложке */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              
              {/* Тень под книгой */}
              <div className="absolute -bottom-1 left-1 right-2 h-1 bg-black/20 rounded-full blur-sm transform transition-transform duration-300 group-hover:scale-110"></div>
            </div>
            <div className="p-4">
              <h2 
                className="text-xl font-semibold mb-2 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200" 
                onClick={() => handleBookClick(book.id)}
              >
                {book.title}
              </h2>
              <p className="text-gray-600 mb-4">Автор: {book.author?.display_name || book.author?.username || '—'}</p>
              <div
                className="book-description text-gray-700 mb-4 line-clamp-3"
                dangerouslySetInnerHTML={{
                  __html: book.description ? createShortDescription(book.description, 120) : '—'
                }}
              />
              
              {/* Статистические значки */}
              <div className="flex items-center justify-center gap-3 text-gray-400 text-xs mb-3 border-t pt-2">
                <Tooltip title="Просмотры" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                  <span className="flex items-center gap-1 cursor-default">
                    <svg width="14" height="14" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                      <circle cx="12" cy="12" r="3"/>
                    </svg>
                    {book.views_count || 0}
                  </span>
                </Tooltip>
                <Tooltip title="Лайки" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                  <span className="flex items-center gap-1 cursor-default">
                    <svg width="14" height="14" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path d="M4.318 6.318a4.5 4.5 0 0 1 6.364 0L12 7.636l1.318-1.318a4.5 4.5 0 1 1 6.364 6.364L12 21.682l-7.682-7.682a4.5 4.5 0 0 1 0-6.364z"/>
                    </svg>
                    {book.likes_count || 0}
                  </span>
                </Tooltip>
                <Tooltip title="В библиотеках" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                  <span className="flex items-center gap-1 cursor-default">
                    <svg width="14" height="14" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                      <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
                    </svg>
                    {book.library_count || 0}
                  </span>
                </Tooltip>
                <Tooltip title="Комментарии" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                  <span className="flex items-center gap-1 cursor-default">
                    <svg width="14" height="14" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                    </svg>
                    {book.reviews_count || 0}
                  </span>
                </Tooltip>
              </div>
              
              <div className="flex justify-between items-center mb-3">
                <span className="text-sm text-gray-500">{book.published_at ? formatDateWithTimezone(book.published_at, timezone, 'DD.MM.YYYY') : ''}</span>
                <button className="text-blue-500 hover:text-blue-600">
                  Читать
                </button>
              </div>
              <div className="flex justify-center">
                <LibraryButtons book={book} size="small" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Модальное окно подтверждения возраста */}
      <Modal
        title="Подтверждение возраста"
        open={ageConfirmVisible}
        onOk={handleAgeConfirm}
        onCancel={handleAgeDecline}
        okText="Мне есть 18 лет"
        cancelText="Отмена"
        centered
        className={theme === 'dark' ? 'dark-modal' : ''}
      >
        <p>Данное произведение содержит контент для взрослых (18+).</p>
        <p>Подтвердите, что вам исполнилось 18 лет, чтобы продолжить.</p>
      </Modal>
    </div>
  );
}

export default Books; 