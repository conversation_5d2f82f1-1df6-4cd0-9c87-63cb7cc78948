 # План оптимизации API пользователей

## 🔒 Проблема безопасности данных

**Текущая проблема**: API возвращает всю информацию о пользователе, включая приватную (дата рождения, часовой пояс, email и т.д.)

**Решения**:

### 1. **Создать отдельные API endpoints по назначению**
```python
# backend/users/urls.py
/api/users/public/{username}/avatar/     # только данные для аватара
/api/users/public/{username}/profile/    # только публичная информация профиля
/api/users/public/{username}/basic/      # минимум для списков/сообщений
```

### 2. **Использовать разные сериализаторы**
```python
# backend/users/serializers.py
class UserAvatarSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'display_name', 'avatar_url', 'avatar_thumbnail_url', 'avatar_type', 'avatar_updated_at']

class UserBasicSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'display_name', 'is_online', 'status']

class UserPublicProfileSerializer(serializers.ModelSerializer):
    # Только то, что разрешено показывать публично
    class Meta:
        model = User
        fields = ['id', 'username', 'display_name', 'bio', 'motto', 'author_rating', ...]
```

### 3. **Динамические поля через query параметры**
```python
# /api/users/public/username/?fields=avatar,display_name,status
class UserViewSet(viewsets.ModelViewSet):
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['fields'] = self.request.query_params.get('fields', '').split(',')
        return context
```

## ⚡ Проблема производительности (множественные вызовы)

**Текущая проблема**: `getUserAvatar` вызывается много раз для одного пользователя

**Решения**:

### 1. **Batch loading пользователей**
```javascript
// Загружать сразу всех пользователей из сообщений одним запросом
const userIds = messages.map(msg => msg.sender.id);
const users = await fetchUsersBatch(userIds);
```

### 2. **Предзагрузка в серверных ответах**
```python
# Включать данные пользователей сразу в ответы API
class MessageSerializer(serializers.ModelSerializer):
    sender = UserBasicSerializer(read_only=True)
    recipient = UserBasicSerializer(read_only=True)
```

### 3. **GraphQL подход**
```javascript
// Запрашивать только нужные поля
query {
  messages {
    id, text, created_at
    sender {
      id, username, display_name, avatar_url
    }
  }
}
```

## 🏗️ Архитектурные подходы

### 1. **Микросервисная архитектура**
- **User Service**: управление пользователями
- **Avatar Service**: только аватары
- **Profile Service**: публичные профили
- **Message Service**: сообщения с минимальными данными пользователей

### 2. **Кэширование на разных уровнях**
```python
# Redis кэш для публичных данных пользователей
@cache_result(timeout=3600, key_prefix='user_public')
def get_user_public_data(user_id, fields=None):
    pass
```

### 3. **CDN для статических данных**
```javascript
// Аватары через CDN с кэшированием
const avatarUrl = `${CDN_URL}/avatars/${userId}/${size}?v=${version}`;
```

## 📊 Рекомендуемый план действий

### Этап 1: **Безопасность** (приоритет высокий)
1. Создать `UserBasicSerializer` для сообщений
2. Создать `UserAvatarSerializer` для аватаров  
3. Убрать приватные поля из публичных API

### Этап 2: **Производительность** (приоритет средний)
1. Включить данные пользователей в `MessageSerializer`
2. Добавить batch loading пользователей
3. Оптимизировать кэширование

### Этап 3: **Масштабирование** (приоритет низкий)
1. Рассмотреть GraphQL
2. Микросервисы при необходимости
3. CDN для медиа-файлов

## 🎯 Быстрое решение для начала

**Самый простой и эффективный подход**:
1. Изменить `MessageSerializer` чтобы включать только нужные поля пользователя
2. Убрать отдельные запросы к `/api/users/public/`
3. Все данные пользователей получать вместе с сообщениями

Это решит и проблему безопасности, и проблему производительности одним махом!

## 🔍 Дополнительные проблемы выявленные

### Проблема с множественными вызовами getUserAvatar
- На странице пользователя без сообщений генерируется много логов `getUserAvatar called with:`
- Функция вызывается многократно для одного и того же пользователя
- Передается полная информация о пользователе включая приватные данные

### Данные которые не должны быть в публичном доступе
- `birth_date` - дата рождения (даже если скрыта на фронте)
- `timezone` - часовой пояс
- `email` - email адрес
- `auto_accept_friends` - настройки приватности
- `auto_remove_on_unfriend` - настройки приватности
- `show_birth_date` - настройки приватности
- `show_removed_from_friends_in_feed` - настройки приватности
- `show_unsubscribes_in_feed` - настройки приватности
- `books_blocks_order` - настройки интерфейса
- `hide_email` - настройки приватности

### Данные которые нужны для аватаров
- `id` - ID пользователя
- `username` - имя пользователя
- `display_name` - отображаемое имя
- `avatar_url` - URL аватара
- `avatar_thumbnail_url` - URL миниатюры аватара
- `avatar_type` - тип аватара
- `avatar_updated_at` - время обновления аватара
- `gender` - пол (для системных аватаров)
- `is_online` - статус онлайн (опционально)
- `status` - текстовый статус (опционально)