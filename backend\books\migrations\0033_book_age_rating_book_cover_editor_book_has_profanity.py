# Generated by Django 5.0.2 on 2025-07-13 12:26

import books.models
import users.storage_backends
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('books', '0032_populate_book_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='book',
            name='age_rating',
            field=models.CharField(choices=[('0+', '0+'), ('6+', '6+'), ('12+', '12+'), ('16+', '16+'), ('18+', '18+')], default='0+', help_text='Возрастное ограничение', max_length=3),
        ),
        migrations.AddField(
            model_name='book',
            name='cover_editor',
            field=models.ImageField(blank=True, help_text='Обложка без лейблов для редактора', storage=users.storage_backends.PublicMediaStorage(), upload_to=books.models.book_cover_path),
        ),
        migrations.AddField(
            model_name='book',
            name='has_profanity',
            field=models.BooleanField(default=False, help_text='Содержит ненормативную лексику'),
        ),
    ]
