from storages.backends.s3boto3 import S3Boto3Storage
from django.conf import settings

class PublicMediaStorage(S3Boto3Storage):
    location = settings.AWS_S3_PUBLIC_MEDIA_PREFIX
    default_acl = 'public-read'
    file_overwrite = False
    querystring_auth = False

class PrivateMediaStorage(S3Boto3Storage):
    location = settings.AWS_S3_PRIVATE_MEDIA_PREFIX
    default_acl = 'private'
    file_overwrite = False
    querystring_auth = True