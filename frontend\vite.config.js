import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { visualizer } from 'rollup-plugin-visualizer'

/*
 * ОПТИМИЗАЦИИ СБОРКИ:
 *
 * 1. Лимит чанков: 500KB (было 1000KB)
 * 2. Детальное разделение чанков:
 *
 *    БИБЛИОТЕКИ:
 *    - vendor-react: React core
 *    - vendor-react-dom: React DOM
 *    - vendor-react-router: React Router
 *    - vendor-antd-core: Ant Design компоненты
 *    - vendor-antd-icons: Ant Design иконки
 *    - vendor-tiptap: Редактор TipTap
 *    - vendor-heavy-*: Тяжелые библиотеки (epub, mammoth, giphy, etc.)
 *    - vendor-http: HTTP клиенты (axios)
 *    - vendor-date: Работа с датами
 *    - vendor-misc: Остальные мелкие пакеты
 *
 *    СТРАНИЦЫ:
 *    - page-stats: Статистика рейтингов
 *    - page-settings: Настройки профиля
 *    - page-reader: Читалка книг
 *    - page-editor: Редактор книг
 *    - page-profile: Профили пользователей
 *    - page-books: Просмотр книг
 *    - page-messages: Сообщения и чаты
 *    - page-feed: Лента и главная
 *    - page-library: Библиотека
 *
 * 3. Оптимизация файлов:
 *    - Хэширование для кэширования
 *    - Разделение по типам (js, css, img, media)
 *    - Удаление console.log в продакшене
 *    - Минификация с двумя проходами
 */

export default defineConfig({
  plugins: [
    react(),
    // Анализатор бандла - создает stats.html после сборки
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true,
    })
  ],
  base: process.env.VITE_BASE_URL || '/',
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Основные библиотеки
          if (id.includes('node_modules')) {
            // React экосистема - разделяем на части
            if (id.includes('react-dom')) {
              return 'vendor-react-dom';
            }
            if (id.includes('react-router')) {
              return 'vendor-react-router';
            }
            if (id.includes('react') && !id.includes('react-dom') && !id.includes('react-router')) {
              return 'vendor-react';
            }

            // Ant Design - разделяем на части
            if (id.includes('@ant-design/icons')) {
              return 'vendor-antd-icons';
            }
            if (id.includes('antd')) {
              return 'vendor-antd-core';
            }

            // TipTap редактор
            if (id.includes('@tiptap')) {
              return 'vendor-tiptap';
            }

            // Тяжелые библиотеки (используются редко)
            if (id.includes('epubjs') || id.includes('mammoth')) {
              return 'vendor-heavy-docs';
            }
            if (id.includes('react-easy-crop') || id.includes('emoji-picker-react')) {
              return 'vendor-heavy-ui';
            }
            if (id.includes('@giphy')) {
              return 'vendor-heavy-giphy';
            }

            // Утилиты - разделяем
            if (id.includes('axios')) {
              return 'vendor-http';
            }
            if (id.includes('styled-components')) {
              return 'vendor-styled';
            }

            // Чарты и графики
            if (id.includes('chart') || id.includes('d3') || id.includes('recharts')) {
              return 'vendor-charts';
            }

            // Lodash и подобные утилиты
            if (id.includes('lodash') || id.includes('ramda') || id.includes('underscore')) {
              return 'vendor-lodash';
            }

            // Валидация и формы
            if (id.includes('yup') || id.includes('joi') || id.includes('formik') || id.includes('react-hook-form')) {
              return 'vendor-forms';
            }

            // Остальные node_modules - более детальное разделение
            const packageName = id.split('node_modules/')[1]?.split('/')[0];
            if (packageName) {
              // Самые крупные библиотеки в отдельные чанки (приоритет)
              if (['monaco-editor'].includes(packageName)) {
                return `vendor-${packageName}`;
              }
              if (['pdfjs-dist', 'pdf-lib', 'jspdf'].includes(packageName)) {
                return 'vendor-pdf';
              }
              if (['mammoth', 'docx', 'xlsx', 'exceljs'].includes(packageName)) {
                return 'vendor-office';
              }
              if (['plotly.js', 'chart.js', 'echarts', 'highcharts'].includes(packageName)) {
                return 'vendor-charts-heavy';
              }
              // Дополнительные крупные пакеты, которые могут быть проблемными
              if (['mapbox-gl', 'leaflet', 'ol', 'cesium'].includes(packageName)) {
                return 'vendor-maps';
              }
              if (['ml-matrix', 'ml-regression', 'mathjs', 'numeric'].includes(packageName)) {
                return 'vendor-math';
              }
              if (['marked', 'markdown-it', 'remark', 'micromark'].includes(packageName)) {
                return 'vendor-markdown';
              }

              // Крупные пакеты в отдельные чанки
              if (['moment', 'dayjs', 'luxon', 'date-fns'].includes(packageName)) {
                return 'vendor-time';
              }
              if (['crypto-js', 'bcryptjs', 'jsonwebtoken'].includes(packageName)) {
                return 'vendor-crypto';
              }
              if (['socket.io-client', 'ws', 'sockjs-client'].includes(packageName)) {
                return 'vendor-socket';
              }

              // Дополнительные крупные пакеты
              if (['@babel', 'core-js'].includes(packageName) || packageName.startsWith('@babel/')) {
                return 'vendor-babel';
              }
              if (['tslib', 'typescript'].includes(packageName)) {
                return 'vendor-ts';
              }
              if (['classnames', 'clsx', 'cn'].includes(packageName)) {
                return 'vendor-classnames';
              }
              if (['uuid', 'nanoid', 'shortid'].includes(packageName)) {
                return 'vendor-id';
              }
              if (['qs', 'query-string', 'url-parse'].includes(packageName)) {
                return 'vendor-url';
              }

              // Дополнительные крупные библиотеки, которые могут быть проблемными
              if (['material-ui', '@mui/material', '@emotion/react', '@emotion/styled'].includes(packageName) || packageName.startsWith('@mui/')) {
                return 'vendor-mui';
              }
              if (['react-virtualized', 'react-window', 'react-virtual'].includes(packageName)) {
                return 'vendor-virtualization';
              }
              if (['immutable', 'immer', 'mobx', 'redux'].includes(packageName)) {
                return 'vendor-state';
              }
              if (['three', 'babylonjs', 'pixi.js'].includes(packageName)) {
                return 'vendor-3d';
              }

              // Специальные крупные пакеты, которые могут быть в M-R
              if (['monaco-editor', 'prismjs', 'highlight.js'].includes(packageName)) {
                return 'vendor-editor-syntax';
              }
              if (['react-query', '@tanstack/react-query', 'react-table'].includes(packageName)) {
                return 'vendor-react-tools';
              }
              if (['polished', 'postcss', 'autoprefixer'].includes(packageName)) {
                return 'vendor-css-tools';
              }
              if (['react-spring', 'framer-motion', 'react-transition-group'].includes(packageName)) {
                return 'vendor-animation';
              }

              // Более детальное разделение по алфавиту
              const firstLetter = packageName.charAt(0).toLowerCase();
              if (firstLetter >= 'a' && firstLetter <= 'c') {
                return 'vendor-misc-a-c';
              }
              if (firstLetter >= 'd' && firstLetter <= 'f') {
                return 'vendor-misc-d-f';
              }
              if (firstLetter >= 'g' && firstLetter <= 'i') {
                return 'vendor-misc-g-i';
              }
              if (firstLetter >= 'j' && firstLetter <= 'l') {
                return 'vendor-misc-j-l';
              }
              // Разделяем букву 'm' более детально, так как она содержит много крупных пакетов
              if (firstLetter === 'm') {
                // Крупные пакеты на 'm' в отдельные чанки
                if (['moment', 'mammoth', 'monaco-editor', 'markdown-it', 'material-ui', 'mobx', 'mobx-react'].includes(packageName)) {
                  return `vendor-${packageName}`;
                }
                // Специальные крупные пакеты
                if (packageName.startsWith('mui') || packageName.startsWith('@mui')) {
                  return 'vendor-mui-system';
                }
                if (packageName.startsWith('material') || packageName.includes('material')) {
                  return 'vendor-material';
                }
                if (packageName.startsWith('micro') || packageName.includes('micro')) {
                  return 'vendor-micro';
                }
                // Остальные пакеты на 'm' разделяем еще более детально
                const secondLetter = packageName.charAt(1).toLowerCase();
                if (secondLetter >= 'a' && secondLetter <= 'e') {
                  return 'vendor-misc-ma-me';
                }
                if (secondLetter >= 'f' && secondLetter <= 'i') {
                  return 'vendor-misc-mf-mi';
                }
                if (secondLetter >= 'j' && secondLetter <= 'l') {
                  return 'vendor-misc-mj-ml';
                }
                if (secondLetter >= 'm' && secondLetter <= 'o') {
                  return 'vendor-misc-mm-mo';
                }
                if (secondLetter >= 'p' && secondLetter <= 's') {
                  return 'vendor-misc-mp-ms';
                }
                return 'vendor-misc-mt-mz';
              }
              if (firstLetter === 'n') {
                return 'vendor-misc-n';
              }
              if (firstLetter >= 'o' && firstLetter <= 'p') {
                return 'vendor-misc-o-p';
              }
              if (firstLetter === 'q') {
                return 'vendor-misc-q';
              }
              if (firstLetter === 'r') {
                // Разделяем 'r' пакеты более детально
                if (['react', 'react-dom', 'react-router'].includes(packageName)) {
                  // Эти уже обработаны выше
                  return 'vendor-react-misc';
                }
                if (packageName.startsWith('react-') && !packageName.includes('router')) {
                  return 'vendor-react-ecosystem';
                }
                if (['redux', 'reselect', 'redux-saga', 'redux-thunk'].includes(packageName)) {
                  return 'vendor-redux';
                }
                const secondLetter = packageName.charAt(1).toLowerCase();
                if (secondLetter >= 'a' && secondLetter <= 'c') {
                  return 'vendor-misc-ra-rc';
                }
                if (secondLetter >= 'd' && secondLetter <= 'e') {
                  return 'vendor-misc-rd-re';
                }
                return 'vendor-misc-rf-rz';
              }
              if (firstLetter >= 's' && firstLetter <= 't') {
                return 'vendor-misc-s-t';
              }
              if (firstLetter >= 'u' && firstLetter <= 'z') {
                return 'vendor-misc-u-z';
              }
            }

            // Остальные мелкие пакеты
            return 'vendor-misc-other';
          }

          // Страницы приложения - более детальное разделение
          if (id.includes('/pages/')) {
            // Статистика в отдельный чанк
            if (id.includes('UserRatingStats') || id.includes('RatingStats')) {
              return 'page-stats';
            }

            // Настройки профиля
            if (id.includes('ProfileSettings') || id.includes('Settings')) {
              return 'page-settings';
            }

            // Книги и чтение
            if (id.includes('BookReader') || id.includes('Reader')) {
              return 'page-reader';
            }

            // Редактор книг
            if (id.includes('BookEditor') || id.includes('ChapterEditor')) {
              return 'page-editor';
            }

            // Профиль и пользователи
            if (id.includes('Profile') || id.includes('User')) {
              return 'page-profile';
            }

            // Книги (просмотр, список)
            if (id.includes('Book') && !id.includes('Editor') && !id.includes('Reader')) {
              return 'page-books';
            }

            // Сообщения и чаты
            if (id.includes('Message') || id.includes('Chat') || id.includes('Dialog')) {
              return 'page-messages';
            }

            // Лента и главная
            if (id.includes('Feed') || id.includes('Home') || id.includes('Dashboard')) {
              return 'page-feed';
            }

            // Библиотека
            if (id.includes('Library')) {
              return 'page-library';
            }

            // Остальные страницы
            return 'pages-misc';
          }

          // Компоненты
          if (id.includes('/components/')) {
            // Тяжелые компоненты
            if (id.includes('TipTapEditor') || id.includes('BookReader') || id.includes('ImageCropper')) {
              return 'components-heavy';
            }

            // Остальные компоненты остаются в основном чанке
          }
        },
        // Настройки для лучшего кэширования
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop().replace('.jsx', '').replace('.js', '') : 'chunk';
          return `js/[name]-[hash].js`;
        },
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
            return `media/[name]-[hash].${ext}`;
          }
          if (/\.(png|jpe?g|gif|svg|webp|avif)(\?.*)?$/i.test(assetInfo.name)) {
            return `img/[name]-[hash].${ext}`;
          }
          if (ext === 'css') {
            return `css/[name]-[hash].${ext}`;
          }
          return `assets/[name]-[hash].${ext}`;
        }
      }
    },
    // Оптимизированный лимит чанков - 800KB (учитывает что gzip сжимает до ~25-30%)
    chunkSizeWarningLimit: 800,
    // Включаем минификацию
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Убираем console.log в продакшене
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'], // Убираем конкретные console методы
        passes: 2, // Два прохода оптимизации
      },
      mangle: {
        safari10: true, // Совместимость с Safari 10
      },
      format: {
        comments: false, // Убираем комментарии
      }
    },
    // Дополнительные оптимизации
    cssCodeSplit: true, // Разделяем CSS
    sourcemap: false, // Отключаем sourcemap в продакшене для уменьшения размера
  },
  server: {
    host: '0.0.0.0',
    port: process.env.VITE_PORT || 5173,  // Возвращаем стандартный порт Vite
    https: false,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
      },
    },
  }
})

