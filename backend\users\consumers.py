import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
import logging

logger = logging.getLogger(__name__)

class NotificationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        from django.contrib.auth.models import AnonymousUser
        self.user = self.scope.get('user')
        
        if isinstance(self.user, AnonymousUser):
            logger.warning("WebSocket connection rejected: anonymous user")
            await self.close()
            return
        
        # Создаем группу для пользователя
        self.user_group_name = f'user_{self.user.id}'
        
        # Добавляем пользователя в группу
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"WebSocket connected: user {self.user.username}")
        
        # Отправляем текущие уведомления при подключении
        await self.send_current_notifications()

    async def disconnect(self, close_code):
        # Удаляем пользователя из группы
        await self.channel_layer.group_discard(
            self.user_group_name,
            self.channel_name
        )
        logger.info(f"WebSocket disconnected: user {getattr(self.user, 'username', 'unknown')}")

    async def receive(self, text_data):
        """Обработка входящих сообщений от клиента"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'mark_read':
                # Помечаем уведомление как прочитанное
                notification_type = data.get('notification_type')
                await self.mark_notification_read(notification_type)
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON in WebSocket message")

    async def send_current_notifications(self):
        """Отправляем текущие уведомления пользователю"""
        notifications = await self.get_user_notifications()
        await self.send(text_data=json.dumps({
            'type': 'notifications_update',
            'notifications': notifications
        }))

    async def notification_update(self, event):
        """Отправляем обновление уведомлений клиенту"""
        await self.send(text_data=json.dumps({
            'type': 'notification_update',
            'notification_type': event['notification_type'],
            'data': event['data']
        }))

    @database_sync_to_async
    def get_user_notifications(self):
        """Получить все уведомления пользователя"""
        from .models import UserNotification
        notifications = UserNotification.objects.filter(user=self.user)
        return {
            notification.notification_type: notification.data
            for notification in notifications
        }

    @database_sync_to_async
    def mark_notification_read(self, notification_type):
        """Пометить уведомление как прочитанное"""
        from .models import UserNotification
        try:
            notification = UserNotification.objects.get(
                user=self.user,
                notification_type=notification_type
            )
            # Можно добавить поле is_read в модель, если нужно
            # notification.is_read = True
            # notification.save()
        except UserNotification.DoesNotExist:
            pass

    @classmethod
    async def send_notification_to_user(cls, user_id, notification_type, data):
        """Отправить уведомление конкретному пользователю"""
        from channels.layers import get_channel_layer
        channel_layer = get_channel_layer()
        
        await channel_layer.group_send(
            f'user_{user_id}',
            {
                'type': 'notification_update',
                'notification_type': notification_type,
                'data': data
            }
        )


class DialogConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        from django.contrib.auth.models import AnonymousUser
        self.user = self.scope.get('user')
        self.dialog_id = self.scope['url_route']['kwargs']['dialog_id']
        
        if isinstance(self.user, AnonymousUser):
            logger.warning("Dialog WebSocket connection rejected: anonymous user")
            await self.close()
            return
        
        # Проверяем, что пользователь является участником диалога
        if not await self.is_dialog_participant():
            logger.warning(f"Dialog WebSocket connection rejected: user {self.user.username} not participant of dialog {self.dialog_id}")
            await self.close()
            return
        
        # Создаем группу для диалога
        self.room_group_name = f'dialog_{self.dialog_id}'
        
        # Добавляем пользователя в группу диалога
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"Dialog WebSocket connected: user {self.user.username} to dialog {self.dialog_id}")

    async def disconnect(self, close_code):
        # Удаляем пользователя из группы диалога
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        logger.info(f"Dialog WebSocket disconnected: user {getattr(self.user, 'username', 'unknown')} from dialog {getattr(self, 'dialog_id', 'unknown')}")

    async def receive(self, text_data):
        """Обработка входящих сообщений от клиента"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'typing':
                # Отправляем уведомление о печати другим участникам
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'user_typing',
                        'user_id': self.user.id,
                        'username': self.user.username
                    }
                )
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON in dialog WebSocket message")

    async def user_typing(self, event):
        """Отправляем уведомление о печати клиенту"""
        await self.send(text_data=json.dumps({
            'type': 'user_typing',
            'user_id': event['user_id'],
            'username': event['username']
        }))

    async def new_message(self, event):
        """Отправляем новое сообщение клиенту"""
        await self.send(text_data=json.dumps({
            'type': 'new_message',
            'message': event['message']
        }))

    async def messages_read(self, event):
        """Отправляем уведомление о прочтении сообщений"""
        logger.info(f"BELL: DialogConsumer.messages_read called:")
        logger.info(f"   User: {self.user.username} (ID: {self.user.id})")
        logger.info(f"   Dialog: {self.dialog_id}")
        logger.info(f"   Message IDs: {event['message_ids']}")
        logger.info(f"   Read by: {event['read_by']}")
        
        await self.send(text_data=json.dumps({
            'type': 'messages_read',
            'message_ids': event['message_ids'],
            'read_by': event['read_by']
        }))
        
        logger.info(f"CHECK: messages_read sent to {self.user.username}")

    @database_sync_to_async
    def is_dialog_participant(self):
        """Проверить, является ли пользователь участником диалога"""
        from .models import Dialog
        try:
            dialog = Dialog.objects.get(id=self.dialog_id, participants=self.user)
            return True
        except Dialog.DoesNotExist:
            return False 