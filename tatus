import { useParams, useNavigate, Outlet, Link } from 'react-router-dom';
import { useEffect, useState, useContext, useRef } from 'react';
import Container from '../components/Container';
import { AuthContext } from '../context/AuthContext';
import { useTheme } from '../theme/ThemeContext';
import Cropper from 'react-easy-crop';

function ProfileLayout() {
  const { username } = useParams();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMotto, setEditMotto] = useState(false);
  const [mottoValue, setMottoValue] = useState('');
  const [mottoError, setMottoError] = useState('');
  const [mottoLoading, setMottoLoading] = useState(false);
  const { user } = useContext(AuthContext);
  const { theme } = useTheme ? useTheme() : { theme: 'light' };
  const mottoInputRef = useRef(null);
  const [showHeaderSettings, setShowHeaderSettings] = useState(false);
  const [headerTab, setHeaderTab] = useState('preset'); // 'preset' | 'color' | 'upload' | 'frame'
  const [headerBg, setHeaderBg] = useState(null); // {type: 'solid'|'gradient'|'image', ...}
  // Цвет/градиент
  const [color1, setColor1] = useState('#b4d8ff');
  const [color2, setColor2] = useState('#d1b4ff');
  const [gradient, setGradient] = useState(false);
  // Для загрузки изображения
  const [uploadImage, setUploadImage] = useState(null); // File
  const [uploadPreview, setUploadPreview] = useState(null); // DataURL
  const [uploadError, setUploadError] = useState('');
  const [uploadLoading, setUploadLoading] = useState(false);
  // Для cropper'а
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [croppedPreview, setCroppedPreview] = useState(null);
  // Пресеты хедеров
  const [headerPresets, setHeaderPresets] = useState([]);
  const [headerPresetFilter, setHeaderPresetFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(0);
  const headersPerPage = 12;
  const headerPresetFilters = [
    { key: 'all', label: 'Все', keywords: [] },
    { key: 'light', label: 'Светлые', keywords: ['light'] },
    { key: 'dark', label: 'Темные', keywords: ['dark'] },
	{ key: 'book', label: 'Книги', keywords: ['book'] },
	{ key: 'nature', label: 'Природа', keywords: ['nature'] },
    { key: 'animals', label: 'Живая природа', keywords: ['animals'] },
    { key: 'cosmos', label: 'Космос', keywords: ['cosmos'] },
    { key: 'happy', label: 'Радостные', keywords: ['happy'] },
    { key: 'abstract', label: 'Абстрактные', keywords: ['abstract'] },
	{ key: 'fantasy', label: 'Фэнтези', keywords: ['fantasy'] },
    { key: 'sport', label: 'Спорт', keywords: ['sport'] },
    { key: 'funny', label: 'Приколы', keywords: ['funny'] },
    // ...добавляй свои категории
  ];
  const [userUploadedImage, setUserUploadedImage] = useState(null); // Добавляем состояние для загруженного изображения
  // Состояние для отслеживания изменений изображения
  const [imageVersion, setImageVersion] = useState(0);
  // 1. Добавить состояние для рамок
  const [headerFrames, setHeaderFrames] = useState([]);
  const [headerFrameFilter, setHeaderFrameFilter] = useState('all');
  const [headerFramePage, setHeaderFramePage] = useState(0);
  const framesPerPage = 12;
  const headerFrameFilters = [
    { key: 'all', label: 'Все', keywords: [] },
    { key: 'black', label: 'Черные', keywords: ['black'] },
    { key: 'white', label: 'Белые', keywords: ['white'] },
    { key: 'color', label: 'Цветные', keywords: ['color'] },
  ];
  const [stats, setStats] = useState({ subscribers_count: 0, friends_count: 0 });
  const [relation, setRelation] = useState(null);
  const [relationLoading, setRelationLoading] = useState(false);
  // Состояния для списков
  const [friendsList, setFriendsList] = useState([]);
  const [subscriptionsList, setSubscriptionsList] = useState([]);
  const [subscribersList, setSubscribersList] = useState([]);

  const backendUrl = 'http://localhost:8000'; // или твой реальный адрес backend
  console.log('Using backendUrl:', backendUrl);
  const navigate = useNavigate();

  // Функция для обновления данных пользователя
  const refreshUserData = async () => {
    try {
      const res = await fetch(`/api/auth/public/${username}/`);
      if (!res.ok) throw new Error('Ошибка при получении данных');
      const data = await res.json();
      console.log('Refreshed user data:', data);
      setUserData(data);
      setMottoValue(data.motto || '');
      // Увеличиваем версию изображения для сброса кэша
      setImageVersion(prev => prev + 1);
    } catch (err) {
      console.error('Error refreshing user data:', err);
    }
  };

  // При монтировании компонента
  useEffect(() => {
    if (!username) return;
    
    setLoading(true);
    setError(null);
    
    fetch(`/api/auth/public/${username}/`)
      .then(res => {
        if (!res.ok) {
          throw new Error('Пользователь не найден');
        }
        return res.json();
      })
      .then(data => {
        console.log('Loaded user data:', data);
        console.log('Avatar path:', data.avatar);
        setUserData(data);
        setMottoValue(data.motto || '');
        setLoading(false);
      })
      .catch(err => {
        console.error('Error loading user data:', err);
        setError(err.message);
        setLoading(false);
      });
  }, [username]);

  // При монтировании — подгружаем фон из localStorage
  useEffect(() => {
    const saved = localStorage.getItem('headerBg_' + username);
    if (saved) {
      try {
        setHeaderBg(JSON.parse(saved));
      } catch {}
    }
  }, [username]);

  // 2. useEffect для загрузки рамок
  useEffect(() => {
    console.log('Fetching header frames...');
    fetch('/api/auth/header-frames/')
      .then(res => res.json())
      .then(data => {
        console.log('Loaded header frames:', data.frames);
        // Проверяем каждый URL
        const frames = data.frames || [];
        frames.forEach(frame => {
          const fullUrl = frame.startsWith('http') ? frame : backendUrl + frame;
          console.log('Frame URL:', fullUrl);
          // Проверяем доступность каждого файла
          fetch(fullUrl)
            .then(res => {
              if (!res.ok) {
                console.error(`Frame not accessible: ${fullUrl}, status: ${res.status}`);
              } else {
                console.log(`Frame accessible: ${fullUrl}`);
              }
            })
            .catch(err => {
              console.error(`Error checking frame: ${fullUrl}`, err);
            });
        });
        setHeaderFrames(frames);
      })
      .catch(err => {
        console.error('Error loading header frames:', err);
      });
  }, []);

  // Фон хедера из API
  function getHeaderBgFromUserData() {
    if (!userData) {
      return { backgroundImage: 'linear-gradient(to right, #b4d8ff, #d1b4ff)' };
    }
    // solid + frame: backgroundImage с двумя слоями (рамка поверх фона)
    if (
      userData.header_bg_type === 'solid' &&
      userData.header_bg_color1 &&
      userData.header_frame
    ) {
      let frameUrl = userData.header_frame;
      if (!frameUrl.startsWith('http')) frameUrl = backendUrl + frameUrl;
      return {
        backgroundImage: `url(${frameUrl}), linear-gradient(to right, ${userData.header_bg_color1}, ${userData.header_bg_color1})`,
        backgroundSize: 'cover, cover',
        backgroundPosition: 'center, center',
        backgroundRepeat: 'no-repeat, 