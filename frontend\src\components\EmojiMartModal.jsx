import RussianEmojiPicker from './RussianEmojiPicker';

const EmojiMartModal = ({ open, onClose, onSelect, theme = 'light' }) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50" onClick={onClose}>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4" onClick={e => e.stopPropagation()}>
        <RussianEmojiPicker
          locale="ru"
          theme={theme}
          autoFocusSearch
          height={450}
          width={400}
          onEmojiClick={(emojiData) => {
            onSelect(emojiData.emoji);
            onClose();
          }}
        />
      </div>
    </div>
  );
};

export default EmojiMartModal; 