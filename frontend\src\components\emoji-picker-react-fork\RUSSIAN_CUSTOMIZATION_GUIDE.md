# Руководство по русской кастомизации Emoji Picker

Это руководство поможет вам внедрить три основные функции:
1. **Русские переводы названий эмодзи**
2. **Дополнительные ключевые слова для поиска**
3. **Исключение определенных эмодзи**

## Шаг 1: Настройка файла кастомизации

Файл `src/config/russianCustomization.ts` уже создан. Вам нужно:

### 1.1 Добавить русские переводы
Откройте файл и найдите объект `russianEmojiNames`. Добавьте переводы в формате:
```typescript
export const russianEmojiNames: Record<string, string> = {
  '1f600': 'улыбающееся лицо',
  '1f603': 'улыбающееся лицо с открытым ртом',
  // Добавьте больше переводов...
};
```

### 1.2 Добавить ключевые слова для поиска
Найдите объект `emojiSearchKeywords` и добавьте ключевые слова:
```typescript
export const emojiSearchKeywords: Record<string, string[]> = {
  '1f600': ['радость', 'счастье', 'веселье', 'смех', 'позитив'],
  '1f602': ['смех', 'слезы', 'радость', 'веселье', 'ржач', 'ржака'],
  // Добавьте больше ключевых слов...
};
```

### 1.3 Добавить исключенные эмодзи
Найдите массив `excludedEmojis` и добавьте unicode эмодзи для исключения:
```typescript
export const excludedEmojis: string[] = [
  '1f3f3-fe0f-200d-1f308', // радужный флаг
  '1f3f3-fe0f-200d-26a7-fe0f', // трансгендерный флаг
  // Добавьте другие эмодзи...
];
```

## Шаг 2: Модификация файлов

### 2.1 Модификация dataUtils/emojiSelectors.ts

Откройте файл `src/dataUtils/emojiSelectors.ts` и:

1. Добавьте импорт в начало файла:
```typescript
import { getRussianEmojiName, getEmojiKeywords } from '../config/russianCustomization';
```

2. Найдите функцию `emojiNames` и замените её на:
```typescript
export function emojiNames(emoji: DataEmoji): string[] {
  const originalNames = emoji[EmojiProperties.name] || [];
  const unified = emojiUnified(emoji);
  
  // Добавляем русское название
  const russianName = getRussianEmojiName(unified);
  const keywords = getEmojiKeywords(unified);
  
  const allNames = [...originalNames];
  
  if (russianName) {
    allNames.unshift(russianName); // Русское название в начало
  }
  
  // Добавляем ключевые слова
  allNames.push(...keywords);
  
  return allNames;
}
```

### 2.2 Модификация hooks/useDisallowedEmojis.ts

Откройте файл `src/hooks/useDisallowedEmojis.ts` и:

1. Добавьте импорт в начало файла:
```typescript
import { isEmojiExcluded } from '../config/russianCustomization';
```

2. Найдите функцию `useIsEmojiDisallowed` и замените её на:
```typescript
export function useIsEmojiDisallowed() {
  const disallowedEmojis = useDisallowedEmojis();
  const isUnicodeHidden = useIsUnicodeHidden();

  return function isEmojiDisallowed(emoji: DataEmoji) {
    const unified = unifiedWithoutSkinTone(emojiUnified(emoji));

    // Проверяем исключенные эмодзи из русской кастомизации
    if (isEmojiExcluded(unified)) {
      return true;
    }

    return Boolean(disallowedEmojis[unified] || isUnicodeHidden(unified));
  };
}
```

### 2.3 Модификация RussianEmojiPicker.jsx

Откройте ваш файл `RussianEmojiPicker.jsx` и:

1. Добавьте импорт:
```javascript
import { excludedEmojis } from './emoji-picker-react-fork/src/config/russianCustomization';
```

2. Обновите компонент EmojiPicker:
```javascript
<EmojiPicker
  {...props}
  hiddenEmojis={excludedEmojis}
  locale="ru"
  theme={theme === 'dark' ? 'dark' : 'light'}
/>
```

## Шаг 3: Опциональные улучшения

### 3.1 Отображение русских названий в превью

Если хотите показывать русские названия в превью эмодзи, найдите файл `src/components/emoji/EmojiButton.tsx` и:

1. Добавьте импорт:
```typescript
import { getRussianEmojiName } from '../../config/russianCustomization';
```

2. В компоненте найдите где формируется `title` или `aria-label` и замените на:
```typescript
const russianName = getRussianEmojiName(unified);
const displayName = russianName || emojiNames(emoji)[0] || '';
```

### 3.2 Русские названия в поиске

Поиск уже будет работать с русскими названиями и ключевыми словами после модификации `emojiSelectors.ts`.

## Шаг 4: Как найти unicode эмодзи

Чтобы найти unicode код эмодзи:

1. Откройте файл `src/data/emojis.json`
2. Найдите нужный эмодзи по английскому названию
3. Скопируйте значение поля `"u"` (unified)

Пример:
```json
{
  "n": ["grinning", "grinning face"],
  "u": "1f600",
  "a": "1.0"
}
```
Unicode код: `1f600`

## Шаг 5: Тестирование

После внесения изменений:

1. Перезапустите приложение
2. Откройте emoji picker
3. Проверьте:
   - Поиск по русским словам работает
   - Исключенные эмодзи не отображаются
   - Русские названия показываются (если включили превью)

## Примеры популярных эмодзи и их unicode

```typescript
// Лица
'1f600': 'улыбающееся лицо',
'1f602': 'лицо со слезами радости',
'1f923': 'катающееся по полу от смеха',
'1f60d': 'улыбающееся лицо с глазами-сердечками',
'1f914': 'думающее лицо',
'1f62d': 'громко плачущее лицо',
'1f621': 'надутое лицо',

// Сердечки
'2764-fe0f': 'красное сердце',
'1f49b': 'желтое сердце',
'1f49a': 'зеленое сердце',
'1f494': 'разбитое сердце',

// Жесты
'1f44d': 'большой палец вверх',
'1f44e': 'большой палец вниз',
'1f44c': 'знак ОК',
'1f64f': 'сложенные руки',
'1f4aa': 'напряженный бицепс',

// Животные
'1f436': 'морда собаки',
'1f431': 'морда кота',
'1f981': 'лев',
'1f42d': 'морда мыши',

// Еда
'1f355': 'кусок пиццы',
'1f354': 'гамбургер',
'1f35f': 'картофель фри',
'1f382': 'торт ко дню рождения',
```

## Поддержка

Если возникнут проблемы:
1. Проверьте правильность unicode кодов
2. Убедитесь, что все импорты добавлены
3. Перезапустите приложение после изменений
4. Проверьте консоль браузера на ошибки
