import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useUserSettings } from '../context/UserSettingsContext';
import { Form, Input, Button, Upload, message, Typography, Spin, Steps, Checkbox, Select, Alert, Modal } from 'antd';
import { UploadOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useTheme } from '../theme/ThemeContext';
import Navbar from '../components/Navbar';
import ProfileSidebar from '../components/ProfileSidebar';
import BookCoverEditor from '../components/BookCoverEditor';
import ChapterEditor from '../components/ChapterEditor';
import EditBook2 from './EditBook2';
import '../theme/custom-textarea.css';
import { formatDateWithTimezone } from '../utils/formatDate';
import AnnotationEditor from '../components/AnnotationEditor';
import NovelEditor from '../components/BookEditors/NovelEditor';
import StoryEditor from '../components/BookEditors/StoryEditor';
import TaleEditor from '../components/BookEditors/TaleEditor';
import StorybookEditor from '../components/BookEditors/StorybookEditor';
import PoembookEditor from '../components/BookEditors/PoembookEditor';
import { useEditBookStep } from '../hooks/useEditBookStep';
import '../theme/hashtag-select.css';
import CustomHashtagInput from '../components/CustomHashtagInput';
import '../components/custom-hashtag-input.css';
import AgeRatingSelector from '../components/AgeRatingSelector';
import { csrfFetch } from '../utils/csrf';
import { checkForbiddenWords } from '../utils/wordFilter';
import { drawLabel, calculateLabelPosition } from '../utils/labelSettings';
import { tagsForStorage, tagsForDisplay } from '../utils/hashtagUtils';

const { Title } = Typography;
const { TextArea } = Input;

const BOOK_TYPES = [
  { value: 'story', label: 'Рассказ', comment: 'Рассказ — состоит из одной части. Объем не превышает 50,000 знаков.' },
  { value: 'novella', label: 'Повесть', comment: 'Повесть — состоит из нескольких глав не более 50,000 зн. каждая. Объем не превышает 200,000 зн.' },
  { value: 'novel', label: 'Роман', comment: 'Роман — состоит из нескольких глав не более 100,000 зн. каждая. Объем не превышает 2,500,000 зн.' },
  { value: 'story_collection', label: 'Сборник рассказов', comment: 'Сборник рассказов — несколько рассказов в одной книге, не более 50,000 зн. каждый. Объем не превышает 2,500,000 зн.' },
  { value: 'poetry_collection', label: 'Сборник поэзии', comment: 'Сборник поэзии — несколько поэтических произведений в одной книге, не более 50,000 зн. каждое. Объем не превышает 2,500,000 зн.' },
];

function useNavigationBlocker(shouldBlock, onBlock) {
  const location = useLocation();
  const navigate = useNavigate();
  useEffect(() => {
    if (!shouldBlock) return;
    const handleClick = (e) => {
      const anchor = e.target.closest('a');
      if (anchor && anchor.href && anchor.origin === window.location.origin) {
        // Не блокируем если ссылка на текущий адрес
        if (anchor.pathname + anchor.search + anchor.hash === location.pathname + location.search + location.hash) return;
        e.preventDefault();
        onBlock(anchor.href, () => navigate(anchor.pathname + anchor.search + anchor.hash));
      }
    };
    document.addEventListener('click', handleClick, true);
    return () => {
      document.removeEventListener('click', handleClick, true);
    };
  }, [shouldBlock, onBlock, navigate, location]);
}

const EditBook = () => {
  const { id } = useParams();
  const { user } = useAuth();
  const { theme } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [book, setBook] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [chapters, setChapters] = useState([]);
  const [step, setStep] = useEditBookStep(book, chapters);
  const [contentReady, setContentReady] = useState(false); // Готовность к показу контента
  const [coverType, setCoverType] = useState('generated');
  const [customCover, setCustomCover] = useState(null);
  const [customCoverMini, setCustomCoverMini] = useState(null);
  const [coverPreview, setCoverPreview] = useState(null);
  const [chapterModal, setChapterModal] = useState({ open: false, edit: null });
  const [chapterLoading, setChapterLoading] = useState(false);
  const [liveTitle, setLiveTitle] = useState('');
  const [desc, setDesc] = useState('');
  const [genresList, setGenresList] = useState([]);
  const [genresLoading, setGenresLoading] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const isGenreLimitReached = false;
  const { timezone } = useUserSettings();
  const [bookLoaded, setBookLoaded] = useState(false);
  const [selectedGenres, setSelectedGenres] = useState([]);
  const [currentAgeRating, setCurrentAgeRating] = useState({ age_rating: '0+', has_profanity: false });
  const [backendUrl, setBackendUrl] = useState('');

  // Мемоизируем объект currentAgeRating для предотвращения лишних рендеров
  const memoizedAgeRating = useMemo(() => currentAgeRating, [currentAgeRating.age_rating, currentAgeRating.has_profanity]);

  // Ref для BookCoverEditor
  const coverEditorRef = useRef();

  // Функция для определения состояния лейблов обложки (3 состояния)
  const getCoverLabelState = (ageRating) => {
    if (ageRating.age_rating === '18+' && ageRating.has_profanity) {
      return 'both'; // 18+ + ненормативная лексика
    } else if (ageRating.age_rating === '18+' && !ageRating.has_profanity) {
      return 'age_only'; // только 18+
    } else {
      return 'none'; // без лейблов
    }
  };
  // Определяем, в процессе ли создания книга
  const isCreating = book?.creation_status === 'creating';
  // Состояния для динамических звездочек
  const [titleStar, setTitleStar] = useState(true);
  const [typeStar, setTypeStar] = useState(true);
  const [showLeaveModal, setShowLeaveModal] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [saving, setSaving] = useState(false);
  // --- Состояния для галочек ---
  const [titleValid, setTitleValid] = useState(false);
  const [typeValid, setTypeValid] = useState(false);
  const [coverValid, setCoverValid] = useState(false);
  const [descValid, setDescValid] = useState(false);
  const [genresValid, setGenresValid] = useState(false);
  const [ageRatingValid, setAgeRatingValid] = useState(false);
  // Добавляем состояние для данных автора
  const [authorData, setAuthorData] = useState(null);
  // Добавляем состояние для полноэкранного режима редактора
  const [isEditorFullWidth, setIsEditorFullWidth] = useState(false);

  // Состояния для фоновой перегенерации обложки
  const [coverNeedsRegeneration, setCoverNeedsRegeneration] = useState(false);
  const [backgroundRegenerationInProgress, setBackgroundRegenerationInProgress] = useState(false);
  // Состояние для загрузки обложки
  const [coverSaving, setCoverSaving] = useState(false);

  // Состояния для отслеживания изменений возрастного рейтинга
  const [initialAgeRating, setInitialAgeRating] = useState({ age_rating: '0+', has_profanity: false });
  const [coverNeedsUpdate, setCoverNeedsUpdate] = useState(false);

  // Отслеживаем изменения состояния лейблов обложки
  useEffect(() => {
    const currentState = getCoverLabelState(currentAgeRating);
    const initialState = getCoverLabelState(initialAgeRating);

    // Показываем кнопку "Обновить обложку" только если состояние лейблов изменилось
    setCoverNeedsUpdate(currentState !== initialState);
  }, [currentAgeRating, initialAgeRating]);



  const steps = [
    {
      title: 'Основная информация',
      content: 'first-content',
    },
    {
      title: 'Обложка и аннотация',
      content: 'second-content',
    },
    {
      title: 'Содержание',
      content: 'last-content',
    },
  ];

  useEffect(() => {
    if (!user || !id) {
      setLoading(true);
      return;
    }
    fetchBook();
    fetchChapters();
  }, [user, id, navigate]);

  // Определяем готовность контента для показа
  useEffect(() => {
    if (book && typeof book.chapters_count === 'number') {
      // Если у нас есть chapters_count, мы можем сразу определить этап
      setContentReady(true);
    } else if (book && Array.isArray(chapters)) {
      // Fallback: ждем загрузки и книги, и глав
      setContentReady(true);
    } else {
      setContentReady(false);
    }
  }, [book, chapters]);

  useEffect(() => {
    document.querySelectorAll('.ant-input-show-count-suffix').forEach(el => {
      el.style.color = theme === 'dark' ? '#fff' : '#222';
    });
    const sheet = document.createElement('style');
    sheet.innerHTML = `
      .custom-textarea textarea::placeholder {
        color: ${theme === 'dark' ? '#d1d5db' : '#6b7280'} !important;
        font-style: italic !important;
        opacity: 1 !important;
      }
      html.dark .ant-select-selector {
        background: #111827 !important;
        color: #fff !important;
        border-color: #111827 !important;
        min-height: 42px !important;
        border-width: 5px !important;
        font-size: 0.8rem !important;
        display: flex !important;
        align-items: center !important;
      }
      html.light .ant-select-selector {
        background: #fff !important;
        color: #222 !important;
        border-color: #fff !important;
        min-height: 42px !important;
		border-width: 5px !important;
        font-size: 0.8rem !important;
        display: flex !important;
        align-items: center !important;
      }
      .ant-select-selection-placeholder {
        display: flex !important;
        align-items: center !important;
        height: 100%;
        font-style: italic !important;
        opacity: 1 !important;
        color: ${theme === 'dark' ? '#d1d5db' : '#6b7280'} !important;
        font-size: 0.85rem !important;
      }
      html.dark .ant-select-selection-placeholder {
		color: #d1d5db !important;
        font-style: italic !important;
        opacity: 1 !important;
        font-size: 0.85rem !important;
      }
      /* Выпадающее меню */
      .dark-select-dropdown {
        background: #111827 !important;
        color: #fff !important;
        border: 2px solid #60A5FA !important;
        scrollbar-color: #b0b4ba #111827;
      }
      .dark-select-dropdown .ant-select-item-group {
        color: #7dd3fc !important;
        background: #111827 !important;
        font-weight: 700 !important;
        font-size: 16px !important;
        padding: 6px 16px 4px 16px !important;
      }
      .dark-select-dropdown .ant-select-item-option-content,
      .dark-select-dropdown .ant-select-item-option-grouped {
        color: #fff !important;
        background: #111827 !important;
      }
      .dark-select-dropdown .ant-select-item-option-selected,
      .dark-select-dropdown .ant-select-item-option-active {
        background: #111827 !important;
        color: #fff !important;
        border-radius: 12px !important;
        border: 2px solid #2563eb !important;
      }
      .dark-select-dropdown ::-webkit-scrollbar-thumb {
        background: #b0b4ba !important;
      }
      .light-select-dropdown {
        background: #fff !important;
        color: #222 !important;
        border: 1px solid #e5e7eb !important;
      }
      .light-select-dropdown .ant-select-item-option-content,
      .light-select-dropdown .ant-select-item-option-grouped {
        color: #222 !important;
        background: #fff !important;
      }
      .light-select-dropdown .ant-select-item-option-selected,
      .light-select-dropdown .ant-select-item-option-active {
        background: #fff !important;
        color: #222 !important;
        border-radius: 12px !important;
        border: 2px solid #2563eb !important;
      }
      
      /* Убираем браузерный outline у селекта жанров */
      .dark-select .ant-select-selector,
      .light-select .ant-select-selector {
        outline: none !important;
      }
      .dark-select .ant-select-selector:focus,
      .light-select .ant-select-selector:focus {
        outline: none !important;
      }
      /* Убираем outline у поля поиска внутри селекта */
      .dark-select .ant-select-selection-search-input,
      .light-select .ant-select-selection-search-input {
        outline: none !important;
        box-shadow: none !important;
      }
      .dark-select .ant-select-selection-search-input:focus,
      .light-select .ant-select-selection-search-input:focus {
        outline: none !important;
        box-shadow: none !important;
      }
    `;
    document.querySelectorAll('style[data-placeholder-style]').forEach(el => el.remove());
    sheet.setAttribute('data-placeholder-style', 'true');
    document.head.appendChild(sheet);
  }, [theme]);

  useEffect(() => {
    setGenresLoading(true);
    fetch('/api/genres/')
      .then(res => res.json())
      .then(data => setGenresList(data))
      .catch(() => setGenresList([]))
      .finally(() => setGenresLoading(false));
  }, []);

  useEffect(() => {
    if (isCreating) {
      setTitleStar(!(book?.title && book.title.trim().length > 0));
      setTypeStar(!(book?.type && book.type.trim().length > 0));
    }
  }, [isCreating, book?.title, book?.type]);

  useEffect(() => {
    if (book) {
      setLiveTitle(book.title || '');
      setDesc(book.description || '');
      setTitleValid(!!(book.title && book.title.trim().length > 0));
      setDescValid(!!(getTextFromHtml(book.description).trim().length > 0));
    }
  }, [book]);

  useEffect(() => {
    setTitleValid(!!(liveTitle && liveTitle.trim().length > 0));
    setTypeValid(!!(book?.type && book.type.trim().length > 0));
    setCoverValid(!!(book?.cover));
    setDescValid(!!(getTextFromHtml(desc).trim().length > 0));
    setGenresValid(Array.isArray(selectedGenres) && selectedGenres.length > 0);
    // Проверяем валидность возрастного рейтинга
    setAgeRatingValid(!!(currentAgeRating?.age_rating));
  }, [liveTitle, book?.type, book?.cover, currentAgeRating, desc, selectedGenres]);

  const fetchBook = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/books/${id}/`);
      if (!response.ok) throw new Error('Failed to fetch book');
      const data = await response.json();
      setBook(data);
      const genres = data.genres ? data.genres.map(g => String(g.id)) : [];
      setSelectedGenres(genres);

      // Устанавливаем возрастные ограничения из данных книги
      const ageRatingData = {
        age_rating: data.age_rating || '0+',
        has_profanity: data.has_profanity || false
      };
      setCurrentAgeRating(ageRatingData);
      setInitialAgeRating(ageRatingData); // Сохраняем изначальные значения
      console.log('Loaded age rating from book:', ageRatingData);

      form.setFieldsValue({
        title: data.title || '',
        type: data.type || '',
        description: data.description || '',
        cover_type: data.cover_type || '',
        genres,
        hashtag_names: data.hashtags ? tagsForDisplay(data.hashtags.map(h => h.name)) : [],
        auto_indent: data.auto_indent || false,
        age_rating: ageRatingData, // Устанавливаем возрастные ограничения в форму
      });
      setCoverType(data.cover_type || 'generated');
      setCoverPreview(data.cover ? data.cover : null);
      setBackendUrl(data.backend_url || '');
      
      // Если есть автор книги, загружаем его данные
      if (data.author && data.author.username) {
        fetchAuthorData(data.author.username);
      }
    } catch (error) {
      message.error('Ошибка при загрузке книги');
      navigate(-1);
    } finally {
      setLoading(false);
    }
  };

  // Функция для обновления только данных обложки (без перезагрузки страницы)
  const updateBookCoverData = async () => {
    try {
      console.log('Updating cover data...');
      const response = await fetch(`/api/books/${id}/`);
      if (!response.ok) throw new Error('Failed to fetch book');
      const data = await response.json();

      console.log('Received cover data from server:', {
        cover: data.cover,
        cover_editor: data.cover_editor,
        cover_type: data.cover_type
      });

      // Добавляем timestamp к URL для избежания кеширования
      const timestamp = Date.now();
      const coverWithTimestamp = data.cover ? `${data.cover}?t=${timestamp}` : data.cover;
      const coverEditorWithTimestamp = data.cover_editor ? `${data.cover_editor}?t=${timestamp}` : data.cover_editor;
      const coverMiniWithTimestamp = data.cover_mini ? `${data.cover_mini}?t=${timestamp}` : data.cover_mini;

      // Обновляем только данные обложки в состоянии книги
      setBook(prevBook => {
        const updatedBook = {
          ...prevBook,
          cover: coverWithTimestamp,
          cover_mini: coverMiniWithTimestamp,
          cover_editor: coverEditorWithTimestamp,
          cover_type: data.cover_type
        };
        console.log('Updated book state with timestamp:', updatedBook);
        return updatedBook;
      });

      // Обновляем превью обложки с timestamp
      setCoverPreview(coverWithTimestamp);
      setCoverType(data.cover_type || 'generated');

      console.log('Cover data successfully updated in UI');

    } catch (error) {
      console.error('Error updating cover data:', error);
      message.error('Ошибка при обновлении данных обложки');
    }
  };

  // Функция для загрузки данных автора
  const fetchAuthorData = async (username) => {
    try {
      const response = await fetch(`/api/auth/public/${username}/`);
      if (!response.ok) throw new Error('Failed to fetch author data');
      const data = await response.json();
      setAuthorData(data);
    } catch (error) {
      console.error('Ошибка при загрузке данных автора:', error);
    }
  };

  const fetchChapters = async () => {
    const res = await fetch(`/api/books/${id}/chapters/`);
    if (res.ok) {
      const data = await res.json();
      setChapters(Array.isArray(data.results) ? data.results : []);
    } else {
      setChapters([]);
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleNext = () => {
    form.validateFields().then(() => {
      setCurrentStep(currentStep + 1);
      setStep(step + 1);
    });
  };

  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
    setStep(step - 1);
  };

  const handleSave = async (values) => {
    try {
      const csrfToken = getCookie('csrftoken');
      if (!csrfToken) {
        throw new Error('CSRF token not found');
      }

      const response = await csrfFetch(`/api/books/${id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        body: JSON.stringify(values),
        credentials: 'include',
      });

      if (!response.ok) throw new Error('Failed to update book');

      message.success('Изменения сохранены');
    } catch (error) {
      message.error('Ошибка при сохранении изменений');
    }
  };

  const saveBookFields = async (values) => {
    // Проверяем на запрещенные слова
    const titleCheck = checkForbiddenWords(values.title || '');
    const descriptionCheck = checkForbiddenWords(values.description || '');
    
    if (titleCheck.hasForbiddenWords) {
      message.error(`Заголовок содержит запрещенные слова: ${titleCheck.forbiddenWords.join(', ')}`);
      return;
    }
    
    if (descriptionCheck.hasForbiddenWords) {
      message.error(`Описание содержит запрещенные слова: ${descriptionCheck.forbiddenWords.join(', ')}`);
      return;
    }
    
    const formDataToSend = new FormData();
    formDataToSend.append('title', values.title);
    formDataToSend.append('type', values.type);
    formDataToSend.append('description', values.description);
    // Жанры — только из формы
    const genres = form.getFieldValue('genres') || [];
    genres.forEach(id => formDataToSend.append('genre_ids', id));
    // Хештеги — преобразуем для сохранения (пробелы → подчеркивания)
    const hashtags = form.getFieldValue('hashtag_names') || [];
    const hashtagsForStorage = tagsForStorage(hashtags);
    hashtagsForStorage.forEach(tag => formDataToSend.append('hashtag_names', tag));
    // Возрастные ограничения
    const ageRatingData = form.getFieldValue('age_rating') || currentAgeRating;
    console.log('Saving age rating data:', ageRatingData);
    if (ageRatingData) {
      formDataToSend.append('age_rating', ageRatingData.age_rating || '0+');
      formDataToSend.append('has_profanity', ageRatingData.has_profanity ? 'true' : 'false');
    }
    // Убираем сохранение обложки из saveBookFields - для неё есть отдельная функция сохранения
    // if (coverType) formDataToSend.append('cover_type', coverType);
    // if (coverType === 'custom' && customCover) formDataToSend.append('cover', customCover);
    formDataToSend.append('auto_indent', values.auto_indent ? 'true' : 'false');
    // --- ВАЖНО: если книга создаётся, меняем статус на draft ---
    if (isCreating) {
      formDataToSend.append('creation_status', 'draft');
    }
    const csrfToken = getCookie('csrftoken');
    await csrfFetch(`/api/books/${id}/`, {
      method: 'PATCH',
      headers: {
        'X-CSRFToken': csrfToken,
      },
      body: formDataToSend,
      credentials: 'include',
    });
  };

  const handleStep1Finish = async (values) => {
    setLoading(true);
    try {
      // Сохраняем данные только при первом создании книги
      if (isCreating) {
        await saveBookFields(values);
        await fetchBook();
        if (book && book.id) sessionStorage.removeItem(`editbook_draft_${book.id}`);
      }

      setStep(2);

      // Убираем фоновую перегенерацию обложки при переходах
      // setTimeout(() => {
      //   regenerateCoverInBackground();
      // }, 100);

    } catch {
      message.error('Ошибка при сохранении');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveOnly = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      await saveBookFields(values);
      await fetchBook();
      message.success('Изменения сохранены');
      if (book && book.id) sessionStorage.removeItem(`editbook_draft_${book.id}`);
    } catch {
      message.error('Ошибка при сохранении');
    } finally {
      setLoading(false);
    }
  };

  // Главы (этап 2)
  const handleSaveChapter = async ({ title, content, order, chapterId, isEdit }) => {
    setChapterLoading(true);
    try {
      const csrfToken = getCookie('csrftoken');
      const method = isEdit ? 'PATCH' : 'POST'; // Используем PATCH для редактирования, POST для создания
      const url = isEdit 
        ? `/api/books/${id}/chapters/${chapterId}/` 
        : `/api/books/${id}/chapters/`; // URL для создания главы

      const res = await fetch(url, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        body: JSON.stringify({ title, content, order }),
        credentials: 'include',
      });

      if (!res.ok) {
        const errorData = await res.json();
        const errorText = book?.type === 'story' ? 'Ошибка сохранения рассказа' : 'Ошибка сохранения главы';
        message.error(`${errorText}: ${errorData.detail || res.statusText}`);
        return false;
      }

      // Разные сообщения для рассказов и других типов книг
      if (book?.type === 'story') {
        message.success(isEdit ? 'Текст рассказа успешно изменен' : 'Рассказ успешно создан');
      } else {
        message.success(isEdit ? 'Глава успешно обновлена' : 'Глава успешно добавлена');
      }
      fetchChapters();
      return true;
    } catch (error) {
      console.error('Ошибка сохранения главы:', error);
      const errorText = book?.type === 'story' ? 'Ошибка сохранения рассказа' : 'Ошибка сохранения главы';
      message.error(`${errorText}: ${error.message || error}`);
      return false;
    } finally {
      setChapterLoading(false);
    }
  };

  const handleCoverChange = ({ type, file }) => {
    console.log('handleCoverChange called with:', { type, file: !!file });
    // Обновляем состояние только если это реальное изменение обложки
    if (file && file instanceof File) {
      setCoverType('custom');
      setCustomCover(file);
      setCustomCoverMini(null);
      setCoverPreview(file);
      console.log('Cover changed - new file uploaded');
    }
  };

  // Функция для обновления обложки с текущими лейблами (пересохранение существующей обложки)
  const handleCoverUpdate = async () => {
    try {
      setCoverSaving(true);

      // Получаем текущую обложку без лейблов (editor версию)
      const currentCoverUrl = book.cover_editor || book.cover;
      if (!currentCoverUrl) {
        message.error('Нет обложки для обновления');
        return;
      }

      // Загружаем текущую обложку как изображение
      const response = await fetch(currentCoverUrl);
      const blob = await response.blob();

      // Конвертируем в base64
      const reader = new FileReader();
      const base64Data = await new Promise((resolve) => {
        reader.onload = () => resolve(reader.result);
        reader.readAsDataURL(blob);
      });

      // Создаем две версии обложки: с лейблами и без
      const coverWithoutLabels = base64Data; // Оригинальная обложка без лейблов
      let coverWithLabels = base64Data; // По умолчанию такая же

      // Если нужны лейблы, создаем версию с лейблами используя правильную систему
      if (currentAgeRating.age_rating === '18+' || currentAgeRating.has_profanity) {
        try {
          // Используем статически импортированные функции для работы с лейблами

          // Создаем canvas для наложения лейблов
          const canvas = document.createElement('canvas');
          canvas.width = 700;
          canvas.height = 1000;
          const ctx = canvas.getContext('2d');

          // Загружаем изображение
          const img = new Image();
          img.crossOrigin = 'anonymous';
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = base64Data;
          });

          // Рисуем изображение
          ctx.drawImage(img, 0, 0, 700, 1000);

          // Загружаем и накладываем лейбл 18+ если нужен
          if (currentAgeRating.age_rating === '18+') {
            const img18 = new Image();
            img18.crossOrigin = 'anonymous';
            await new Promise((resolve, reject) => {
              img18.onload = resolve;
              img18.onerror = reject;
              img18.src = 'https://storage.yandexcloud.net/lpo-test/dist/lpo/18/18.png';
            });
            drawLabel(ctx, img18, 'ageRating', 700, 1000);
          }

          // Загружаем и накладываем лейбл ненормативной лексики если нужен
          if (currentAgeRating.has_profanity) {
            const imgLexic = new Image();
            imgLexic.crossOrigin = 'anonymous';
            await new Promise((resolve, reject) => {
              imgLexic.onload = resolve;
              imgLexic.onerror = reject;
              imgLexic.src = 'https://storage.yandexcloud.net/lpo-test/dist/lpo/18/lexic.png';
            });
            drawLabel(ctx, imgLexic, 'profanity', 700, 1000);
          }

          // Получаем обложку с лейблами
          coverWithLabels = canvas.toDataURL('image/jpeg', 0.9);

          // Создаем миниатюру с лейблами (210x300)
          const miniCanvas = document.createElement('canvas');
          miniCanvas.width = 210;
          miniCanvas.height = 300;
          const miniCtx = miniCanvas.getContext('2d');

          // Рисуем уменьшенное изображение
          miniCtx.drawImage(img, 0, 0, 210, 300);

          // Загружаем и накладываем лейбл 18+ если нужен (для миниатюры)
          if (currentAgeRating.age_rating === '18+') {
            const img18Mini = new Image();
            img18Mini.crossOrigin = 'anonymous';
            await new Promise((resolve, reject) => {
              img18Mini.onload = resolve;
              img18Mini.onerror = reject;
              img18Mini.src = 'https://storage.yandexcloud.net/lpo-test/dist/lpo/18/18.png';
            });
            drawLabel(miniCtx, img18Mini, 'ageRating', 210, 300);
          }

          // Загружаем и накладываем лейбл ненормативной лексики если нужен (для миниатюры)
          if (currentAgeRating.has_profanity) {
            const imgLexicMini = new Image();
            imgLexicMini.crossOrigin = 'anonymous';
            await new Promise((resolve, reject) => {
              imgLexicMini.onload = resolve;
              imgLexicMini.onerror = reject;
              imgLexicMini.src = 'https://storage.yandexcloud.net/lpo-test/dist/lpo/18/lexic.png';
            });
            drawLabel(miniCtx, imgLexicMini, 'profanity', 210, 300);
          }

          // Получаем миниатюру с лейблами
          const coverMiniWithLabels = miniCanvas.toDataURL('image/jpeg', 0.9);

          console.log('Cover with labels created successfully');
        } catch (error) {
          console.error('Error creating cover with labels:', error);
          // В случае ошибки используем оригинальную обложку
        }
      }

      // Пересохраняем обложку с правильными версиями
      const formData = new FormData();
      formData.append('cover_type', 'custom');
      formData.append('cover_data', coverWithLabels); // Основная обложка (с лейблами если нужно)
      formData.append('cover_editor_data', coverWithoutLabels); // Обложка для редактора (без лейблов)
      if (typeof coverMiniWithLabels !== 'undefined') {
        formData.append('cover_mini_data', coverMiniWithLabels); // Миниатюра с лейблами
      }
      formData.append('age_rating', currentAgeRating.age_rating);
      formData.append('has_profanity', currentAgeRating.has_profanity);

      const csrfToken = getCookie('csrftoken');
      const saveResponse = await csrfFetch(`/api/books/${id}/`, {
        method: 'PATCH',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        body: formData,
        credentials: 'include',
      });

      const result = await saveResponse.json();
      console.log('Cover update response:', result);

      // Обновление завершено
      message.success('Обложка обновлена с новыми лейблами');

      // Обновляем изначальные значения возрастного рейтинга после сохранения
      setInitialAgeRating(currentAgeRating);
      setCoverNeedsUpdate(false);

      // Обновляем данные обложки
      updateBookCoverData();

    } catch (e) {
      console.error('handleCoverUpdate error', e);
      message.error('Ошибка при обновлении обложки');
    } finally {
      setCoverSaving(false);
    }
  };

  const handleCoverSave = async (coverData) => {
    console.log('handleCoverSave called with:', coverData);
    try {
      setCoverSaving(true); // Начинаем загрузку

      const formData = new FormData();
      formData.append('cover_type', 'custom');

      // Если это новый формат с двумя версиями обложки
      if (coverData && typeof coverData === 'object' && coverData.coverWithLabels && coverData.coverWithoutLabels) {
        console.log('Saving cover with both versions (with and without labels) - SYNC');

        // Отправляем base64 данные напрямую
        formData.append('cover_data', coverData.coverWithLabels);
        formData.append('cover_editor_data', coverData.coverWithoutLabels);

        // Добавляем информацию о наложении текста для правильного именования файлов
        console.log('Text overlay info:', {
          hasTextOverlay: coverData.hasTextOverlay,
          textColorSuffix: coverData.textColorSuffix,
          hasTextOverlayType: typeof coverData.hasTextOverlay,
          textColorSuffixType: typeof coverData.textColorSuffix,
          condition1: !!coverData.hasTextOverlay,
          condition2: !!coverData.textColorSuffix,
          bothConditions: !!(coverData.hasTextOverlay && coverData.textColorSuffix)
        });
        if (coverData.hasTextOverlay && coverData.textColorSuffix) {
          formData.append('text_overlay_suffix', coverData.textColorSuffix);
          console.log('Added text_overlay_suffix to formData:', coverData.textColorSuffix);
        } else {
          console.log('No text overlay suffix added - missing hasTextOverlay or textColorSuffix', {
            hasTextOverlay: coverData.hasTextOverlay,
            textColorSuffix: coverData.textColorSuffix
          });
        }

        // Используем данные о возрастных ограничениях из coverData (то что видно на экране)
        if (coverData.ageRating !== undefined && coverData.hasProfanity !== undefined) {
          formData.append('age_rating', coverData.ageRating);
          formData.append('has_profanity', coverData.hasProfanity);
          console.log('Using age rating from coverData:', {
            ageRating: coverData.ageRating,
            hasProfanity: coverData.hasProfanity
          });
        } else {
          // Fallback к текущим настройкам если данные не переданы
          formData.append('age_rating', currentAgeRating.age_rating);
          formData.append('has_profanity', currentAgeRating.has_profanity);
          console.log('Using fallback age rating from currentAgeRating:', currentAgeRating);
        }
      } else {
        console.log('Saving cover in legacy format (single file) - SYNC');
        // Старый формат - конвертируем в base64
        const fileOrUrl = typeof coverData === 'object' ? coverData.file : coverData;
        if (fileOrUrl instanceof File) {
          const reader = new FileReader();
          const base64Data = await new Promise((resolve) => {
            reader.onload = () => resolve(reader.result);
            reader.readAsDataURL(fileOrUrl);
          });
          formData.append('cover_data', base64Data);
        }
      }

      const csrfToken = getCookie('csrftoken');
      const response = await csrfFetch(`/api/books/${id}/`, {
        method: 'PATCH',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        body: formData,
        credentials: 'include',
      });

      const result = await response.json();
      console.log('Cover save response:', result);

      // Синхронное сохранение завершено
      message.success('Обложка сохранена');
      setCoverNeedsRegeneration(false);

      // Обновляем изначальные значения возрастного рейтинга после сохранения
      // Используем данные из coverData если они есть, иначе текущие настройки
      if (coverData && coverData.ageRating !== undefined && coverData.hasProfanity !== undefined) {
        const savedAgeRating = {
          age_rating: coverData.ageRating,
          has_profanity: coverData.hasProfanity
        };
        setInitialAgeRating(savedAgeRating);
        setCurrentAgeRating(savedAgeRating); // Синхронизируем текущие настройки
        console.log('Updated age rating from coverData:', savedAgeRating);
      } else {
        setInitialAgeRating(currentAgeRating);
        console.log('Updated age rating from currentAgeRating:', currentAgeRating);
      }
      setCoverNeedsUpdate(false);

      // Обновляем данные обложки
      updateBookCoverData();

    } catch (e) {
      console.error('handleCoverSave error', e);
      message.error('Ошибка при сохранении обложки');
      setCoverNeedsRegeneration(false);
    } finally {
      setCoverSaving(false); // Завершаем загрузку в любом случае
    }
  };



  // Функция фоновой перегенерации обложки
  const regenerateCoverInBackground = async () => {
    if (!coverNeedsRegeneration || backgroundRegenerationInProgress) {
      console.log('Skipping regeneration:', {
        coverNeedsRegeneration,
        backgroundRegenerationInProgress
      });
      return;
    }

    console.log('Starting background cover regeneration...', {
      ageRating: currentAgeRating.age_rating,
      hasProfanity: currentAgeRating.has_profanity,
      bookId: id
    });

    setBackgroundRegenerationInProgress(true);

    try {
      // Получаем текущую обложку из редактора
      const coverEditorRef = document.querySelector('[data-cover-editor]') ||
                           document.querySelector('.book-cover-editor') ||
                           document.body;

      if (!coverEditorRef) {
        console.log('Cover editor not found, skipping regeneration');
        setBackgroundRegenerationInProgress(false);
        return;
      }

      // Запрашиваем у редактора обложек перегенерацию с текущими настройками
      const regenerationEvent = new CustomEvent('requestCoverRegeneration', {
        detail: {
          ageRating: currentAgeRating.age_rating,
          hasProfanity: currentAgeRating.has_profanity,
          bookId: id
        }
      });

      console.log('Dispatching regeneration event to:', coverEditorRef);
      coverEditorRef.dispatchEvent(regenerationEvent);

      // Таймаут безопасности - если через 10 секунд событие не пришло
      setTimeout(() => {
        if (backgroundRegenerationInProgress) {
          console.log('Regeneration timeout - forcing completion');
          setBackgroundRegenerationInProgress(false);
          setCoverNeedsRegeneration(false);
        }
      }, 10000);

    } catch (error) {
      console.error('Background cover regeneration failed:', error);
      setBackgroundRegenerationInProgress(false);
    }
  };

  // Обработчик успешной перегенерации
  useEffect(() => {
    const handleRegenerationComplete = (event) => {
      console.log('Background cover regeneration completed', event.detail);
      setBackgroundRegenerationInProgress(false);
      setCoverNeedsRegeneration(false);

      // Обновляем данные книги для отображения новой обложки
      updateBookCoverData();
    };

    document.addEventListener('coverRegenerationComplete', handleRegenerationComplete);

    return () => {
      document.removeEventListener('coverRegenerationComplete', handleRegenerationComplete);
    };
  }, [fetchBook]);

  // Проверка заполненности обязательных полей для создания
  const isCreateSaveEnabled = isCreating && titleValid && typeValid && coverValid && descValid && genresValid;

  // --- useEffect для onbeforeunload ---
  useEffect(() => {
    if (!isCreating || step !== 1) return;
    const handleBeforeUnload = (e) => {
      e.preventDefault();
      e.returnValue = '';
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [isCreating, step]);

  // --- useEffect для sendBeacon при закрытии вкладки/браузера ---
  useEffect(() => {
    if (!isCreating || !id || step !== 1) return;
    const handleUnload = (e) => {
      try {
        const url = `/api/books/${id}/abandon/`;
        const data = JSON.stringify({ abandon: true });
        navigator.sendBeacon(url, data);
      } catch (err) {
        // ignore
      }
    };
    window.addEventListener('unload', handleUnload);
    return () => window.removeEventListener('unload', handleUnload);
  }, [isCreating, id, step]);

  // --- Обработчик удаления черновика ---
  const handleDeleteDraft = async () => {
    if (!isCreating) {
      setShowLeaveModal(false);
      return;
    }
    setSaving(true);
    try {
      const csrfToken = getCookie('csrftoken');
      const response = await fetch(`/api/books/${id}/`, {
        method: 'DELETE',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Ошибка при удалении черновика');
      message.success('Черновик удалён');
      setShowLeaveModal(false);
      navigate('/profile'); // или куда нужно перенаправить
    } catch (e) {
      message.error('Ошибка при удалении черновика');
    } finally {
      setSaving(false);
    }
  };

  useNavigationBlocker(
    isCreating && step === 1,
    (nextUrl, proceed) => {
      setShowLeaveModal(true);
      setPendingNavigation(() => proceed);
    }
  );

  // --- Обработчик для кнопки "Редактор текста" в режиме редактирования ---
  const handleGoToEditor = async () => {
    try {
      // При переходе во второй шаг не сохраняем данные книги
      // const values = await form.validateFields();
      setLoading(true);
      // await saveBookFields(values);
      setStep(2); // Переход к редактору текста
      // if (book && book.id) sessionStorage.removeItem(`editbook_draft_${book.id}`);

      // Убираем фоновую перегенерацию обложки при переходах
      // setTimeout(() => {
      //   regenerateCoverInBackground();
      // }, 100);

    } catch {
      message.error('Ошибка при переходе');
    } finally {
      setLoading(false);
    }
  };

  // --- Обработчик для кнопки "Отмена" в режиме редактирования ---
  const handleCancelEdit = () => {
    navigate(-1);
  };

  // --- Обработчик для кнопки "Сохранить изменения" в режиме редактирования ---
  const handleSaveChanges = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // Проверяем на запрещенные слова
      const titleCheck = checkForbiddenWords(values.title || '');
      const descriptionCheck = checkForbiddenWords(values.description || '');

      if (titleCheck.hasForbiddenWords) {
        message.error(`Заголовок содержит запрещенные слова: ${titleCheck.forbiddenWords.join(', ')}`);
        setLoading(false);
        return;
      }

      if (descriptionCheck.hasForbiddenWords) {
        message.error(`Описание содержит запрещенные слова: ${descriptionCheck.forbiddenWords.join(', ')}`);
        setLoading(false);
        return;
      }

      // Создаем FormData только с нужными полями (без формы произведения и обложки)
      const formDataToSend = new FormData();

      // Название книги
      if (values.title !== undefined) {
        formDataToSend.append('title', values.title);
      }

      // Аннотация
      if (values.description !== undefined) {
        formDataToSend.append('description', values.description);
      }

      // Жанры произведения
      const genres = form.getFieldValue('genres') || [];
      genres.forEach(id => formDataToSend.append('genre_ids', id));

      // Хештеги — преобразуем для сохранения (пробелы → подчеркивания)
      const hashtags = form.getFieldValue('hashtag_names') || [];
      const hashtagsForStorage = tagsForStorage(hashtags);
      hashtagsForStorage.forEach(tag => formDataToSend.append('hashtag_names', tag));

      // Возрастные ограничения (все настройки)
      const ageRatingData = form.getFieldValue('age_rating') || currentAgeRating;
      if (ageRatingData) {
        formDataToSend.append('age_rating', ageRatingData.age_rating || '0+');
        formDataToSend.append('has_profanity', ageRatingData.has_profanity ? 'true' : 'false');
      }

      // НЕ сохраняем: форму произведения (type) и обложку (cover, cover_type)

      const csrfToken = getCookie('csrftoken');
      const response = await csrfFetch(`/api/books/${id}/`, {
        method: 'PATCH',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        body: formDataToSend,
        credentials: 'include',
      });

      if (!response.ok) throw new Error('Failed to update book');

      message.success('Изменения сохранены');
      await fetchBook(); // Обновляем данные книги

    } catch (error) {
      console.error('Error saving changes:', error);
      message.error('Ошибка при сохранении изменений');
    } finally {
      setLoading(false);
    }
  };

  // --- Автосохранение черновика в sessionStorage ---
  useEffect(() => {
    if (book && book.id) {
      const draft = {
        title: liveTitle,
        type: book.type,
        description: desc,
        genres: selectedGenres,
        hashtag_names: form.getFieldValue('hashtag_names') || [], // Сохраняем в черновик как есть (с пробелами)
        auto_indent: form.getFieldValue('auto_indent') || false,
        age_rating: currentAgeRating,
      };
      sessionStorage.setItem(`editbook_draft_${book.id}`, JSON.stringify(draft));
    }
  }, [liveTitle, book?.type, currentAgeRating, desc, selectedGenres]);

  // --- Подгрузка черновика из sessionStorage при загрузке book ---
  useEffect(() => {
    if (book && book.id) {
      const draftRaw = sessionStorage.getItem(`editbook_draft_${book.id}`);
      if (draftRaw) {
        try {
          const draft = JSON.parse(draftRaw);
          setLiveTitle(draft.title || book.title || '');
          setDesc(draft.description || book.description || '');
          setSelectedGenres(draft.genres || []);
          const ageRatingData = draft.age_rating || { age_rating: book.age_rating || '0+', has_profanity: book.has_profanity || false };
          setCurrentAgeRating(ageRatingData);
          console.log('Loaded age rating from draft:', ageRatingData);
          form.setFieldsValue({
            title: draft.title || book.title || '',
            type: draft.type || book.type || '',
            description: draft.description || book.description || '',
            genres: draft.genres || [],
            hashtag_names: draft.hashtag_names || [], // Черновик уже содержит хештеги с пробелами
            auto_indent: draft.auto_indent || false,
            age_rating: ageRatingData,
          });
        } catch {}
      } else {
        setLiveTitle(book.title || '');
        setDesc(book.description || '');
        setSelectedGenres(book.genres ? book.genres.map(g => String(g.id)) : []);
        const ageRatingData = { age_rating: book.age_rating || '0+', has_profanity: book.has_profanity || false };
        setCurrentAgeRating(ageRatingData);
        console.log('Loaded age rating from book (no draft):', ageRatingData);
        form.setFieldsValue({
          title: book.title || '',
          type: book.type || '',
          description: book.description || '',
          genres: book.genres ? book.genres.map(g => String(g.id)) : [],
          hashtag_names: book.hashtags ? tagsForDisplay(book.hashtags.map(h => h.name)) : [], // Преобразуем для отображения
          auto_indent: book.auto_indent || false,
          age_rating: ageRatingData,
        });
        console.log('Set form values with age rating:', ageRatingData);
      }
    }
  }, [book]);

  // Функция переключения полноэкранного режима редактора
  const handleToggleEditorFullWidth = () => {
    setIsEditorFullWidth(!isEditorFullWidth);
  };

  if (loading) {
    return (
      <div className="p-5 max-w-[1200px] mx-auto">
        <div className="text-center py-[50px]">
          <Spin size="large" />
        </div>
      </div>
    );
  }

  const isOwner = user && book && user.id === book.author.id;

  // Проверка 18+
  let isAdultBlocked = false;
  if (book && book.is_adult && !isOwner) {
    const birthDate = user?.birth_date;
    let age = null;
    if (birthDate) {
      const today = new Date();
      const dob = new Date(birthDate);
      age = today.getFullYear() - dob.getFullYear();
      const m = today.getMonth() - dob.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
        age--;
      }
    }
    if (!birthDate || age < 18) {
      isAdultBlocked = true;
    }
  }

  if (!book) return null;

  if (isAdultBlocked) {
    return (
      <div className="p-5 max-w-[1200px] mx-auto">
        <div className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded p-8 text-center text-lg font-semibold">
          Контент доступен только пользователям старше 18 лет
        </div>
      </div>
    );
  }

  // --- Обработчики для title и desc ---
  const handleTitleChange = (e) => {
    const value = e.target.value;
    setLiveTitle(value);
    setTitleValid(!!(value && value.trim().length > 0));
    form.setFieldsValue({ title: value });
    setTitleStar(!(value && value.trim().length > 0));
  };

  const handleDescChange = (val) => {
    setDesc(val);
    setDescValid(!!(getTextFromHtml(val).trim().length > 0));
  };

  const handleAgeRatingChange = (rating) => {
    console.log('Age rating changed in EditBook:', rating);
    setCurrentAgeRating(rating);
    setAgeRatingValid(!!(rating && rating.age_rating));

    // Помечаем, что обложка требует обновления
    setCoverNeedsRegeneration(true);
  };

  // Показываем загрузку, пока не определили правильный этап
  if (!contentReady) {
    return (
      <div className={"min-h-screen " + (theme === 'dark' ? 'bg-gray-900' : 'bg-gray-100') + " flex flex-col items-center justify-center"}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className={theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}>
            Загрузка редактора...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={"min-h-screen " + (theme === 'dark' ? 'bg-gray-900' : 'bg-gray-100') + " flex flex-col"}>
      <div className="max-w-[1200px] mx-auto w-full flex flex-row">
        {/* Основная рабочая область */}
        <main className={"flex flex-col items-center justify-start mt-[10px] " + (isEditorFullWidth ? "w-full" : "flex-1 mr-[10px]")}>
          <div className={"w-full rounded-xl shadow p-6 flex flex-col md:flex-row " + (theme === 'dark' ? 'bg-gray-800' : 'bg-white')}>
            {/* Форма */}
            <div className="flex-1">
              {step === 1 && book && (
                <>
                  <div style={{ textAlign: 'center', marginBottom: 32 }}>
                    <Title
                      level={3}
                      style={{
                        margin: 0,
                        color: theme === 'dark' ? '#fff' : '#222',
                        transition: 'color 0.2s'
                      }}
                    >
                      1. Основные настройки
                    </Title>
                  </div>
                  <Form
                    layout="vertical"
                    initialValues={{}}
                    onFinish={handleStep1Finish}
                    form={form}
                    className={theme === 'dark' ? 'dark-form' : ''}
                    style={{ width: '100%' }}
                  >
                    <div className={"bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2 mb-6"}>
                      <Form.Item
                        name="title"
                        label={<span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Название книги{titleValid && <span style={{color:'#22c55e',marginLeft:8}}>&#10003;</span>}</span>}
                        rules={isCreating ? [
                          { required: true, message: 'Пожалуйста, введите название книги' },
                          { max: 100, message: 'Максимум 100 символов' }
                        ] : [
                          { max: 100, message: 'Максимум 100 символов' }
                        ]}
                      >
                        <Input
                          size="large"
                          placeholder="Введите название книги"
                          maxLength={100}
                          value={liveTitle}
                          onChange={handleTitleChange}
                          className="max-w-md w-1/2 px-3 py-2 rounded border bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700 focus:outline-none"
                          style={{ height: 40 }}
                        />
                      </Form.Item>
                    </div>
                    <div className={"bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2 mb-6"}>
                      <Form.Item
                        name="type"
                        label={<span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Форма произведения{typeValid && <span style={{color:'#22c55e',marginLeft:8}}>&#10003;</span>}</span>}
                        rules={isCreating ? [
                          { required: true, message: 'Пожалуйста, выберите форму произведения' }
                        ] : []}
                      >
                        <div>
                          {isCreating ? (
                            <Select
                              size="large"
                              placeholder="Не выбрано"
                              value={book.type || ''}
                              onChange={value => {
                                form.setFieldsValue({ type: value });
                                setBook({ ...book, type: value });
                                setTypeStar(!(value && value.trim().length > 0));
                              }}
                              className="max-w-md w-1/2"
                            >
                              <Select.Option value="">Не выбрано</Select.Option>
                              {BOOK_TYPES.map(t => (
                                <Select.Option key={t.value} value={t.value}>{t.label}</Select.Option>
                              ))}
                            </Select>
                          ) : (
                            <Input
                              size="large"
                              value={BOOK_TYPES.find(t => t.value === book.type)?.label || ''}
                              disabled
                              className="max-w-md w-1/2 px-3 py-2 rounded border bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700 focus:outline-none"
                              style={{
                                height: 40,
                                background: theme === 'dark' ? '#23272f' : '#f5f5f5',
                                color: theme === 'dark' ? '#a1a1aa' : '#888',
                                opacity: 0.85,
                                fontStyle: 'italic',
                                cursor: 'not-allowed'
                              }}
                            />
                          )}
                          {BOOK_TYPES.find(t => t.value === book.type)?.comment && (
                            <div className="text-xs italic text-gray-500 dark:text-gray-400 mt-1">
                              ({BOOK_TYPES.find(t => t.value === book.type)?.comment})
                            </div>
                          )}
                        </div>
                      </Form.Item>
                    </div>
                    <div className={"bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2 mb-6"}>
                      <div className="mb-4">
                        <label className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                          Обложка{coverValid && <span style={{color:'#22c55e',marginLeft:8}}>&#10003;</span>}
                        </label>
                      </div>
                      <BookCoverEditor
                        ref={coverEditorRef}
                        key={`cover-editor-${book.cover_editor || book.cover || 'default'}`}
                        title={liveTitle || book.title || ''}
                        authorName={user?.display_name || user?.username}
                        authorUsername={user?.username}
                        bookId={id}
                        onCoverChange={handleCoverChange}
                        onSave={handleCoverSave}
                        initialCoverType={book.cover_type === 'system' ? 'system' : 'custom'}
                        coverUrl={book.cover_editor ? book.cover_editor : (book.cover ? book.cover : null)}
                        ageRating={memoizedAgeRating.age_rating}
                        hasProfanity={memoizedAgeRating.has_profanity}
                        saving={coverSaving}
                        needsUpdate={coverNeedsUpdate}
                        onUpdate={handleCoverUpdate}
                      />
                    </div>
                    <div className={"bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2 mb-6"}>
                      <Form.Item
                        name="description"
                        label={<span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Аннотация{descValid && <span style={{color:'#22c55e',marginLeft:8}}>&#10003;</span>}</span>}
                        rules={[{ required: true, message: 'Пожалуйста, заполните аннотацию к книге' }]}
                      >
                        <AnnotationEditor
                          value={desc}
                          onChange={handleDescChange}
                          disabled={loading}
                        />
                      </Form.Item>
                    </div>
                    <div className={"bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2 mb-6"}>
                      <Form.Item
                        name="genres"
                        label={<span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Жанры произведения{genresValid && <span style={{color:'#22c55e',marginLeft:8}}>&#10003;</span>}</span>}
                        rules={[{ required: true, message: 'Выберите хотя бы один жанр' }]}
                      >
                        <div>
                          <Select
                          mode="multiple"
                          value={selectedGenres}
                          onChange={selected => {
                            if (selected.length > 3) {
                              message.warning('Можно выбрать не более 3 жанров: один основной и два дополнительных');
                              return;
                            }
                            setSelectedGenres(selected);
                            form.setFieldsValue({ genres: selected });
                          }}
                          showSearch
                          placeholder={genresLoading ? "Загрузка жанров..." : "Выберите жанры (максимум 3)"}
                          loading={genresLoading}
                          className={theme === 'dark' ? 'dark-select' : 'light-select'}
                          style={{
                            minHeight: 46,
                            fontSize: '1rem',
                            color: theme === 'dark' ? '#fff' : '#222',
                            borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
                            borderWidth: 2,
                            borderRadius: 8,
                          }}
                          classNames={{
                            popup: {
                              root: theme === 'dark' ? 'dark-select-dropdown' : 'light-select-dropdown'
                            }
                          }}
                          tagRender={({ value, closable, onClose, disabled }) => {
                            if (typeof value === 'undefined') return null;
                            const genreObj = genresList.find(g => String(g.id) === String(value));
                            const label = genreObj ? genreObj.name : value;
                            const index = selectedGenres.indexOf(value);
                            const isDark = theme === 'dark';
                            return (
                              <span
                                style={{
                                  display: 'inline-flex',
                                  alignItems: 'center',
                                  background: index === 0
                                    ? (isDark ? '#2563eb' : '#2563eb')
                                    : (isDark ? '#374151' : '#e5e7eb'),
                                  color: index === 0
                                    ? '#fff'
                                    : (isDark ? '#fff' : '#222'),
                                  borderRadius: 16,
                                  padding: '2px 10px',
                                  marginRight: 6,
                                  fontWeight: index === 0 ? 600 : 400,
                                  fontSize: '0.9rem',
                                  opacity: disabled ? 0.5 : 1,
                                  transition: 'background 0.2s, color 0.2s',
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                }}
                              >
                                <span style={{
                                  overflow: 'hidden',
                                  whiteSpace: 'nowrap',
                                  textOverflow: 'ellipsis',
                                  display: 'inline-block',
                                  verticalAlign: 'middle',
                                }}>{label}</span>
                                {closable && (
                                  <span
                                    onClick={e => {
                                      e.stopPropagation();
                                      if (onClose) onClose();
                                    }}
                                    style={{
                                      marginLeft: 6,
                                      cursor: 'pointer',
                                      fontWeight: 700,
                                      color: '#ef4444',
                                    }}
                                  >×</span>
                                )}
                                {index === 0 && <span style={{ marginLeft: 6, fontSize: 11, fontWeight: 400, opacity: 0.7 }}>(основной)</span>}
                              </span>
                            );
                          }}
                          filterOption={(input, option) => {
                            const genre = genresList.find(g => String(g.id) === String(option.value));
                            if (!genre) return false;
                            return genre.name.toLowerCase().includes(input.toLowerCase());
                          }}
                        >
                          {genresList.map(g => (
                            <Select.Option key={g.id} value={String(g.id)}>
                              <span
                                style={{
                                  fontWeight: g.parent_id === null ? 700 : 400,
                                  fontSize: g.parent_id === null ? '1.15em' : '1em',
                                  color: g.parent_id === null
                                    ? (theme === 'dark' ? '#60A5FA' : '#2563eb')
                                    : (theme === 'dark' ? '#fff' : '#222'),
                                  letterSpacing: g.parent_id === null ? '0.5px' : undefined,
                                }}
                              >
                                {g.name}
                              </span>
                            </Select.Option>
                          ))}
                          </Select>
                          <div className="text-xs italic text-gray-500 dark:text-gray-400 mt-1">(Один основной и до двух дополнительных жанров)</div>
                        </div>
                      </Form.Item>
                    </div>
                    <div className={"bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2 mb-6"}>
                      <Form.Item
                        name="age_rating"
                        label={<span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Возрастные ограничения{ageRatingValid && <span style={{color:'#22c55e',marginLeft:8}}>&#10003;</span>}</span>}
                        rules={[{
                          required: true,
                          message: 'Пожалуйста, выберите возрастное ограничение',
                          validator: (_, value) => {
                            if (!value || !value.age_rating) {
                              return Promise.reject(new Error('Пожалуйста, выберите возрастное ограничение'));
                            }
                            return Promise.resolve();
                          }
                        }]}
                      >
                        <div className="space-y-4">
                          <AgeRatingSelector
                            value={memoizedAgeRating}
                            disabled={loading}
                            onChange={handleAgeRatingChange}
                          />

                          {/* Кнопка обновления обложки при изменении лейблов */}
                          {coverNeedsUpdate && (
                            <div className="flex items-center gap-3 pt-2">
                              <Button
                                type="primary"
                                size="small"
                                onClick={() => {
                                  // Вызываем сохранение из BookCoverEditor через ref
                                  // Это дублирует функции кнопки "Сохранить" в редакторе обложек
                                  if (coverEditorRef.current && coverEditorRef.current.handleSave) {
                                    coverEditorRef.current.handleSave();
                                  } else {
                                    console.error('BookCoverEditor ref not available');
                                    message.error('Ошибка: редактор обложек недоступен');
                                  }
                                }}
                                loading={coverSaving}
                                disabled={coverSaving}
                                className="bg-blue-500 hover:bg-blue-600"
                              >
                                Обновить обложку
                              </Button>

                              {/* Тултип с объяснением */}
                              <div
                                className="w-5 h-5 rounded-full bg-orange-500 text-white flex items-center justify-center text-xs cursor-help"
                                title="При смене возрастного рейтинга 18+ и обратно, необходимо обновить обложку для наложения необходимых маркировок"
                              >
                                !
                              </div>
                            </div>
                          )}
                        </div>
                      </Form.Item>
                    </div>
                    <div className={"bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2 mb-6"}>
                      <Form.Item
                        name="hashtag_names"
                        valuePropName="value"
                        getValueFromEvent={v => v}
                        label={<span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Хештеги <span className="text-xs text-gray-400">(не обязательно)</span></span>}
                      >
                        <div>
                          <CustomHashtagInput
                            value={Array.isArray(form.getFieldValue('hashtag_names')) ? form.getFieldValue('hashtag_names') : []}
                            onChange={value => form.setFieldsValue({ hashtag_names: Array.isArray(value) ? value : [] })}
                          />
                          <div className="text-xs italic text-gray-500 dark:text-gray-400">
                            (Можете указать до 10-ти хештегов к вашей книге. Хештеги могут содержать несколько слов (до 40 символов), только русские буквы, цифры и одинарные пробелы между словами. Для завершения используйте Enter, запятую или точку.)
                          </div>
                        </div>
                      </Form.Item>
                    </div>
                    {isCreating ? (
                      <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%', marginTop: 24 }}>
                        <Button
                          danger
                          size="large"
                          style={{
                            minWidth: 120,
                            background: theme === 'dark' ? '#dc2626' : '#f87171',
                            color: theme === 'dark' ? '#fff' : '#fff',
                            border: 'none',
                            boxShadow: theme === 'dark' ? '0 2px 8px #1e293b33' : '0 2px 8px #f8717133',
                          }}
                          onClick={() => setShowLeaveModal(true)}
                          disabled={saving}
                          onMouseOver={e => e.currentTarget.style.background = theme === 'dark' ? '#b91c1c' : '#ef4444'}
                          onMouseOut={e => e.currentTarget.style.background = theme === 'dark' ? '#dc2626' : '#f87171'}
                        >
                          Отмена
                        </Button>
                        <Button
                          type="primary"
                          size="large"
                          htmlType="submit"
                          disabled={!isCreateSaveEnabled || loading}
                          style={{
                            minWidth: 120,
                            background: theme === 'dark' ? '#2563eb' : '#2563eb',
                            color: '#fff',
                            border: 'none',
                            boxShadow: theme === 'dark' ? '0 2px 8px #1e293b33' : '0 2px 8px #2563eb33',
                          }}
                          onMouseOver={e => e.currentTarget.style.background = theme === 'dark' ? '#1d4ed8' : '#1d4ed8'}
                          onMouseOut={e => e.currentTarget.style.background = theme === 'dark' ? '#2563eb' : '#2563eb'}
                        >
                          Сохранить
                        </Button>
                      </div>
                    ) : (
                      <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%', marginTop: 24 }}>
                        <Button
                          danger
                          size="large"
                          style={{
                            minWidth: 120,
                            background: theme === 'dark' ? '#dc2626' : '#f87171',
                            color: theme === 'dark' ? '#fff' : '#fff',
                            border: 'none',
                            boxShadow: theme === 'dark' ? '0 2px 8px #1e293b33' : '0 2px 8px #f8717133',
                          }}
                          onClick={handleCancelEdit}
                          disabled={saving || loading}
                          onMouseOver={e => e.currentTarget.style.background = theme === 'dark' ? '#b91c1c' : '#ef4444'}
                          onMouseOut={e => e.currentTarget.style.background = theme === 'dark' ? '#dc2626' : '#f87171'}
                        >
                          Отмена
                        </Button>
                        <div style={{ display: 'flex', gap: '12px' }}>
                          <Button
                            size="large"
                            style={{
                              minWidth: 180,
                              background: theme === 'dark' ? '#059669' : '#10b981',
                              color: '#fff',
                              border: 'none',
                              boxShadow: theme === 'dark' ? '0 2px 8px #1e293b33' : '0 2px 8px #10b98133',
                            }}
                            loading={loading}
                            onClick={handleSaveChanges}
                            disabled={loading}
                            onMouseOver={e => e.currentTarget.style.background = theme === 'dark' ? '#047857' : '#059669'}
                            onMouseOut={e => e.currentTarget.style.background = theme === 'dark' ? '#059669' : '#10b981'}
                          >
                            Сохранить изменения
                          </Button>
                          <Button
                            type="primary"
                            size="large"
                            style={{
                              minWidth: 180,
                              background: theme === 'dark' ? '#2563eb' : '#2563eb',
                              color: '#fff',
                              border: 'none',
                              boxShadow: theme === 'dark' ? '0 2px 8px #1e293b33' : '0 2px 8px #2563eb33',
                            }}
                            loading={loading}
                            onClick={handleGoToEditor}
                            disabled={!(titleValid && typeValid && coverValid && descValid && genresValid) || loading}
                            onMouseOver={e => e.currentTarget.style.background = theme === 'dark' ? '#1d4ed8' : '#1d4ed8'}
                            onMouseOut={e => e.currentTarget.style.background = theme === 'dark' ? '#2563eb' : '#2563eb'}
                          >
                            Редактор текста
                          </Button>
                        </div>
                      </div>
                    )}
                  </Form>
                </>
              )}
              {step === 2 && (
                <>
                  {/* Индикатор фоновой перегенерации обложки */}
                  {backgroundRegenerationInProgress && (
                    <Alert
                      message="Обновление обложки"
                      description="Обложка обновляется с учетом возрастных ограничений..."
                      type="info"
                      showIcon
                      closable
                      onClose={() => {
                        console.log('Manually closing regeneration indicator');
                        setBackgroundRegenerationInProgress(false);
                        setCoverNeedsRegeneration(false);
                      }}
                      style={{ marginBottom: 16 }}
                    />
                  )}

                  {book.type === 'story' && (
                    <StoryEditor
                      bookId={id}
                      book={book}
                      chapters={chapters}
                      handleSaveChapter={handleSaveChapter}
                      chapterModal={chapterModal}
                      setChapterModal={setChapterModal}
                      chapterLoading={chapterLoading}
                      setChapters={setChapters}
                      onBack={() => setStep(1)}
                      fetchBook={fetchBook}
                      fetchChapters={fetchChapters}
                      setChapterLoading={setChapterLoading}
                      isFullWidth={isEditorFullWidth}
                      onToggleFullWidth={handleToggleEditorFullWidth}
                    />
                  )}
                  {book.type === 'novel' && (
                    <NovelEditor
                      bookId={id}
                      book={book}
                      chapters={chapters}
                      handleSaveChapter={handleSaveChapter}
                      chapterModal={chapterModal}
                      setChapterModal={setChapterModal}
                      chapterLoading={chapterLoading}
                      setChapters={setChapters}
                      onBack={() => setStep(1)}
                      fetchChapters={fetchChapters}
                      isOwner={isOwner}
                      isFullWidth={isEditorFullWidth}
                      onToggleFullWidth={handleToggleEditorFullWidth}
                    />
                  )}
                  {book.type === 'novella' && (
                    <TaleEditor
                      bookId={id}
                      book={book}
                      chapters={chapters}
                      handleSaveChapter={handleSaveChapter}
                      chapterModal={chapterModal}
                      setChapterModal={setChapterModal}
                      chapterLoading={chapterLoading}
                      setChapters={setChapters}
                      onBack={() => setStep(1)}
                      fetchChapters={fetchChapters}
                      isOwner={isOwner}
                      isFullWidth={isEditorFullWidth}
                      onToggleFullWidth={handleToggleEditorFullWidth}
                    />
                  )}
                  {book.type === 'story_collection' && (
                    <StorybookEditor
                      bookId={id}
                      book={book}
                      chapters={chapters}
                      handleSaveChapter={handleSaveChapter}
                      chapterModal={chapterModal}
                      setChapterModal={setChapterModal}
                      chapterLoading={chapterLoading}
                      setChapters={setChapters}
                      onBack={() => setStep(1)}
                      fetchChapters={fetchChapters}
                      isOwner={isOwner}
                      isFullWidth={isEditorFullWidth}
                      onToggleFullWidth={handleToggleEditorFullWidth}
                    />
                  )}
                  {book.type === 'poetry_collection' && (
                    <PoembookEditor
                      bookId={id}
                      book={book}
                      chapters={chapters}
                      handleSaveChapter={handleSaveChapter}
                      chapterModal={chapterModal}
                      setChapterModal={setChapterModal}
                      chapterLoading={chapterLoading}
                      setChapters={setChapters}
                      onBack={() => setStep(1)}
                      fetchChapters={fetchChapters}
                      isOwner={isOwner}
                      isFullWidth={isEditorFullWidth}
                      onToggleFullWidth={handleToggleEditorFullWidth}
                    />
                  )}
                </>
              )}
            </div>
          </div>
        </main>
        {/* Sidebar справа */}
        {!isEditorFullWidth && (
          <aside className="w-[250px] mt-[10px] pt-0 pr-0 flex-shrink-0 flex-col justify-start hidden md:flex">
            <ProfileSidebar userData={authorData} isOwner={isOwner} />
          </aside>
        )}
      </div>
      <Modal
        open={showLeaveModal}
        onCancel={() => setShowLeaveModal(false)}
        onOk={handleDeleteDraft}
        okText="Удалить и выйти"
        cancelText="Остаться"
        confirmLoading={saving}
        className={theme === 'dark' ? 'dark-modal' : ''}
        styles={{
          header: { background: theme === 'dark' ? '#23272f' : '#fff', borderBottom: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}` },
          content: { background: theme === 'dark' ? '#23272f' : '#fff' },
          mask: { backdropFilter: 'blur(4px)' },
        }}
      >
        <p style={{ fontSize: '1.1rem', fontWeight: 500 }}>
          Вы не завершили процесс создания черновика. При отмене или выходе вся информация будет утеряна. Удалить черновик?
        </p>
      </Modal>
    </div>
  );
};

// Вспомогательная функция для получения CSRF токена
function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

// --- ВСПОМОГАТЕЛЬНАЯ ФУНКЦИЯ ---
function getTextFromHtml(html) {
  const tmp = document.createElement('div');
  tmp.innerHTML = html || '';
  return tmp.textContent || tmp.innerText || '';
}

export default EditBook;