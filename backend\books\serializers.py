from rest_framework import serializers
from .models import Book, BookChapter, Like, Review, Comment, CommentLike, Genre, Hashtag, create_cover_thumbnail_from_file, book_cover_path, book_covermini_path, book_cover_editor_path, book_cover_editor_path_with_suffix
import os
from django.conf import settings
import bleach
from bleach.css_sanitizer import CSSSanitizer
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from PIL import Image
import io
from celery.result import AsyncResult
from books.tasks import publish_chapter_task, publish_chapters_batch_task, save_book_cover_async
import pytz
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.utils import timezone
import logging
import base64

logger = logging.getLogger(__name__)

ALLOWED_TAGS = ['b', 'strong', 'i', 'em', 'u', 's', 'br', 'p', 'span', 'img']
ALLOWED_ATTRS = {
    'span': ['style'],
    'p': ['style'],
    'img': ['src', 'alt', 'width', 'height', 'style', 'class', 'data-width', 'data-align', 'data-text-wrap', 'data-caption']
}

# CSS санитайзер для разрешения CSS свойств
CSS_SANITIZER = CSSSanitizer(allowed_css_properties=[
    'color', 'background-color', 'font-size', 'font-weight', 'font-style', 
    'text-decoration', 'text-align', 'margin', 'padding'
])

# Список запрещенных слов
FORBIDDEN_WORDS = [
    'анус', 'аборт', 'бздун', 'беспезды', 'бздюх', 'бля', 'блудилище', 'блядво', 'блядеха', 'блядина', 'блядистка', 'блядище', 'блядки', 'блядование', 'блядовать', 'блядовитый', 'блядовозка', 'блядолиз', 'блядоход', 'блядский', 'блядство', 'блядствовать', 'блядун', 'блядь', 'бляди', 'бляд', 'блядюга', 'блядюра', 'блядюшка', 'блядюшник', 'бордель', 'вагина', 'вафлист', 'вжопить', 'вжопиться', 'вздрачивание', 'вздрачивать', 'вздрачиваться', 'вздрочить', 'вздрочиться', 'вздрючить', 'вздрючивание', 'вздрючивать', 'взъебка', 'взъебщик', 'взъебнуть', 'вислозадая', 'влагалище', 'вхуйнуть', 'вхуйнуться', 'вхуякать', 'вхуякаться', 'вхуя', 'вхуякивать', 'вхуякиваться', 'вхуякнуть', 'вхуякнуться', 'вхуяривание', 'вхуяривать', 'вхуяриваться', 'вхуярить', 'вхуяриться', 'вхуячивание', 'вхуячивать', 'вхуячиваться', 'вхуячить', 'вхуячиться', 'вхуяшивать', 'вхуяшиваться', 'вхуяшить', 'вхуяшиться', 'въебать', 'въебаться', 'въебашивать', 'въебашиваться', 'въебашить', 'въебашиться', 'въебенивать', 'въебениваться', 'въебенить', 'въебениться', 'выблядок', 'выебанный', 'выебат', 'выебаться', 'высрать', 'высраться', 'выссать', 'выссаться', 'высераться', 'выссереть', 'говнецо', 'говнистый', 'говниться', 'говно', 'говновоз', 'говнодав', 'говноеб', 'говноед', 'говномес', 'говномер', 'говносерка', 'говнюк', 'голожопая', 'гомик', 'гомосек', 'гондон', 'гонорея', 'давалка', 'двужопник', 'дерьмо', 'дерьмоед', 'дерьмовый', 'дилдо', 'додрочить', 'додрочиться', 'доебать', 'доебаться', 'доебенивать', 'доебениваться', 'доебенить', 'доебениться', 'долбоеб', 'допиздить', 'допиздиться', 'допиздовать', 'допиздоваться', 'допиздовывать', 'допиздовываться', 'допиздохать', 'допиздохаться', 'допиздохивать', 'допиздохиваться', 'допиздошить', 'допиздошиться', 'допиздошивать', 'допиздошиваться', 'допиздюлить', 'допиздюлиться', 'допиздюливать', 'допиздюливаться', 'допиздюрить', 'допиздюриться', 'допиздюривать', 'допиздюриваться', 'допиздюхать', 'допиздюхаться', 'допиздюхивать', 'допиздюхиваться', 'допиздякать', 'допиздякаться', 'допиздякивать', 'допиздякиваться', 'допиздярить', 'допиздяриться', 'допиздяривать', 'допиздяриваться', 'допиздяхать', 'допиздяхаться', 'допиздяхивать', 'допиздяхиваться', 'допиздячить', 'допиздячиться', 'допиздячивать', 'допиздячиваться', 'допиздяшить', 'допиздяшиться', 'допиздяшивать', 'допиздяшиваться', 'допиздоболивать', 'допиздоболиваться', 'допиздоболиться', 'допиздюкать', 'допиздюкаться', 'допиздюкивать', 'допиздюкиваться', 'допизживать', 'дотрахать', 'дотрахаться', 'дохуйнуть', 'дохуякать', 'дохуякаться', 'дохуякивать', 'дохуякиваться', 'дохуяривать', 'дохуяриваться', 'дохуярить', 'дохуяриться', 'дохуячить', 'дохуячиться', 'дохуячивать', 'дохуячиваться', 'дрисня', 'дристать', 'дристун', 'дроченье', 'дрочилыцик', 'дрочить', 'дрочиться', 'дрочка', 'дрючить', 'дрючиться', 'дурак', 'дуроеб', 'выебать', 'ебало', 'ебальник', 'ебальные', 'ебальный', 'ебанатик', 'ебанашка', 'ебанутый', 'ебануть', 'ебануться', 'ебать', 'ебат', 'ебаться', 'ебатьс', 'ебитесь', 'ебло', 'еблом', 'еблысь', 'ебля', 'ебнуть', 'ебнуться', 'ебня', 'ебучий', 'заебла', 'надроченный', 'объебешь', 'поебать', 'жирнозадый', 'жопа', 'жопой', 'жопастая', 'жопоеб', 'жопенци', 'жопища', 'жопка', 'жопник', 'жополиз', 'жополизание', 'жопоногий', 'жопочка', 'жопочник', 'жопство', 'жопу', 'забздеть', 'заблядовать', 'заблядоваться', 'задница', 'задрачивать', 'задрачиваться', 'задроченный', 'задрочить', 'задрочиться', 'задрючить', 'задрючиться', 'заебанный', 'заебать', 'заебаться', 'заебательская', 'заебашивать', 'заебашиваться', 'заебашить', 'заебашиться', 'заебенивать', 'заебениваться', 'заебенить', 'заебениться', 'залупа', 'залупу', 'залупаться', 'залупенить', 'залупень', 'залупить', 'залупляться', 'залупистый', 'запиздарить', 'запизденная', 'запизденелый', 'запиздить', 'запиздиться', 'запиздоболивать', 'запиздоболиваться', 'запиздоболить', 'запиздоболиться', 'запиздовать', 'запиздоваться', 'запиздовывать', 'запиздовываться', 'запиздохать', 'запиздошить', 'запиздошиться', 'запиздошивать', 'запиздошиваться', 'запиздюкать', 'запиздюкаться', 'запиздюкивать', 'запиздюкиваться', 'запиздюлить', 'запиздюлиться', 'запиздюливать', 'запиздюливаться', 'запиздюрить', 'запиздюриться', 'запиздюривать', 'запиздюриваться', 'запиздюхать', 'запиздюхаться', 'запиздюхивать', 'запиздюхиваться', 'запиздючить', 'запиздючиться', 'запиздючивать', 'запиздючиваться', 'засранец', 'засранка', 'засранный', 'засратый', 'засрать', 'засраться', 'зассать', 'затраханный', 'затрахать', 'затрахаться', 'затрахивать', 'затрахиваться', 'захуить', 'захуйнуть', 'захуйнуться', 'захуякать', 'захуякаться', 'захуякивать', 'захуякиваться', 'захуярить', 'захуяриться', 'захуяривать', 'захуяриваться', 'захуячить', 'захуячиться', 'захуячивать', 'захуячиваться', 'захуяшить', 'захуяшиться', 'захуяшивать', 'захуяшиваться', 'злоебучий', 'издрочиться', 'измандить', 'измандиться', 'измандовать', 'измандоваться', 'измандовывать', 'измандовываться', 'изъебать', 'изъебаться', 'изъебашить', 'изъебашиться', 'изъебашивать', 'изъебашиваться', 'изъебенить', 'изъебениться', 'изъебенивать', 'изъебениваться', 'изъеб', 'испиздеться', 'испиздить', 'испражнение', 'испражняться', 'исхуякать', 'исхуякаться', 'исхуякивать', 'исхуякиваться', 'исхуярить', 'исхуяриться', 'исхуяривать', 'какать', 'какашка', 'кастрат', 'кастрировать', 'клитор', 'клоака', 'кнахт', 'кончить', 'косоебить', 'косоебиться', 'кривохуй', 'курва', 'курвиный', 'лахудра', 'лох', 'лохудра', 'лохматка', 'манда', 'мандавошка', 'мандавоха', 'мандить', 'мандиться', 'мандоватая', 'мандовать', 'мандохать', 'мандохаться', 'мандохивать', 'мандохиваться', 'мандошить', 'мастурбатор', 'минет', 'минетить', 'минетка', 'минетчик', 'минетчица', 'мозгоеб', 'мозгоебатель', 'мозгоебать', 'мозгоебка', 'мокрожопый', 'мокропиздая', 'моча', 'мочиться', 'мудак', 'мудашвили', 'мудило', 'мудильщик', 'мудистый', 'мудить', 'мудоеб', 'наебанный', 'наебка', 'наебщик', 'наебывать', 'наебываться', 'наебыш', 'набздеть', 'наблядоваться', 'надроченный', 'надрочивать', 'надрочить', 'надрочиться', 'надристать', 'наебать', 'наебаться', 'наебнуть', 'наебнуться', 'накакать', 'накакаться', 'накакивать', 'напиздить', 'напиздошить', 'напиздюрить', 'напиздюриться', 'насрать', 'насраться', 'нассать', 'нассаться', 'натрахать', 'натрахаться', 'натрахивать', 'натрахиваться', 'нахуякать', 'нахуякаться', 'нахуякивать', 'нахуякиваться', 'нахуярить', 'нахуяриться', 'нахуяриться', 'нахуяривать', 'нахуяриваться', 'нахуячить', 'нахуячиться', 'нахуячивать', 'нахуячиваться', 'нахуяшить', 'недоебанный', 'недоносок', 'неебущий', 'нищеебство', 'оебыват', 'обдристанный', 'обдристать', 'обдрочиться', 'обосранец', 'обосранная', 'обосраный', 'обосрать', 'обосраться', 'обоссанец', 'обоссаный', 'обоссать', 'обоссаться', 'обоссаться', 'обоссывать', 'обоссываться', 'обпиздить', 'обпиздиться', 'обпиздовать', 'обпиздоваться', 'обпиздовывать', 'обпиздовываться', 'обпиздохать', 'обпиздохаться', 'обпиздохивать', 'обпиздохиваться', 'обпиздошить', 'обтрахать', 'обтрахаться', 'обтрахивать', 'обтрахиваться', 'обхуярить', 'обхуяриться', 'обхуячить', 'объебать', 'объебаться', 'объебенить', 'объебнуть', 'объебон', 'одинхуй', 'однапизда', 'однохуйственно', 'оебать', 'оебашивать', 'оебашить', 'оебенивать', 'оебенить', 'опедерастить', 'опизденеть', 'опизденный', 'опизденно', 'опиздеть', 'опиздить', 'остоебеть', 'остоебенить', 'остоебенило', 'остопиздеть', 'остопиздело', 'остохуело', 'остохуеть', 'отдрачивать', 'отдрачиваться', 'отдрочить', 'отдрочиться', 'отпиздить', 'отпиздошить', 'отпиздяшить', 'отпиздяшиться', 'отпиздяшивание', 'отпиздяшивать', 'отпиздяшиваться', 'отсасывать', 'отсасываться', 'отсосать', 'отсосаться', 'оттраханная', 'оттрахать', 'оттрахаться', 'оттрахивать', 'оттрахиваться', 'отхерачить', 'отхуякать', 'отхуякаться', 'отхуякивать', 'отхуякиваться', 'отхуярить', 'отхуяриться', 'отхуяривать', 'отхуяриваться', 'отхуячить', 'отхуячиться', 'отхуячивать', 'отхуячиваться', 'отхуяшить', 'отхуяшиться', 'отхуяшивать', 'отхуяшиваться', 'отъебать', 'отъебывание', 'отъебывать', 'отъебываться', 'отъебашить', 'отъебашивание', 'отъебашивать', 'отъебашиваться', 'отъебенить', 'отъебениться', 'отъебенивать', 'отъебениваться', 'отъебнуть', 'отьебаться', 'отьебашиться', 'отьебенивание', 'отьебнуться', 'охуевать', 'охуевающий', 'охуевший', 'охуение', 'охуенно', 'охуенные', 'охуеть', 'охуительно', 'охуительный', 'охуякать', 'охуякаться', 'охуякивать', 'охуякиваться', 'охуякнуть', 'охуякнуться', 'охуярить', 'охуяриться', 'охуяривать', 'охуяриваться', 'охуячить', 'охуячиться', 'охуячивать', 'охуячиваться', 'охуяшить', 'охуяшиться', 'охуяшивать', 'охуяшиваться', 'очко', 'перднуть', 'падла', 'падлюка', 'педераст', 'педерастина', 'педерастический', 'педерастия', 'педик', 'педрило', 'пежить', 'пенис', 'пердеж', 'пердеть', 'пердун', 'перебздеть', 'передрачивать', 'передрочить', 'передрочиться', 'переебаться', 'переебашить', 'перетрахать', 'перетрахаться', 'перетрахивать', 'перетрахиваться', 'перехуйнуть', 'перехуйнуться', 'перехуякнуть', 'перехуякнуться', 'перехуякать', 'перехуякаться', 'перехуякивать', 'перехуякиваться', 'перехуярить', 'перехуяриться', 'перехуяривать', 'перехуяриваться', 'перехуячить', 'перехуячиться', 'перехуячивать', 'перехуячиваться', 'пидорас', 'пидор', 'пизда', 'пизданутая', 'пиздануть', 'пиздануться', 'пиздато', 'пизденка', 'пизденочка', 'пиздень', 'пизденыш', 'пиздеть', 'пиздец', 'пиздища', 'пиздобол', 'пиздовать', 'пиздолиз', 'пиздомол', 'пиздосос', 'пиздоход', 'пиздуй', 'пиздун', 'пиздюга', 'пиздюлей', 'пиздюли', 'пиздюлина', 'пиздюк', 'пиздюкать', 'пиздюкаться', 'пиздюшка', 'пиздякать', 'пиздятина', 'пиздятиной', 'пиздячий', 'писька', 'писюлек', 'плоскозадая', 'поебочка', 'поебывать', 'поебываться', 'поблудить', 'поблядовать', 'поблядушка', 'подосрать', 'подосраться', 'подоссать', 'подпиздить', 'подпиздовать', 'подпиздоваться', 'подпиздовывать', 'подпиздовываться', 'подпиздохать', 'подпиздохаться', 'подпиздохивать', 'подпиздохиваться', 'подпиздошить', 'подпиздошиться', 'подпиздошивать', 'подпиздякать', 'подпиздякаться', 'подпиздякивать', 'подпиздякиваться', 'подпиздярить', 'подпиздяриться', 'подпиздяривать', 'подпиздяриваться', 'подпиздяхать', 'подпиздяхаться', 'подпиздяхивать', 'подпиздяхиваться', 'подпиздячить', 'подпиздячиться', 'подпиздячивать', 'подпиздячиваться', 'подпиздяшить', 'подпиздяшиться', 'подпиздяшивать', 'подпиздяшиваться', 'подристывать', 'подрочить', 'подсирать', 'подхуякнуть', 'подхуякнуться', 'подхуякать', 'подхуякаться', 'подхуякивать', 'подхуякиваться', 'подхуярить', 'подхуяриться', 'подхуяривать', 'подхуяриваться', 'подхуячивать', 'подхуячиться', 'подхуячивать', 'подхуячиваться', 'подхуяшить', 'подхуяшиться', 'подхуяшивать', 'подхуяшиваться', 'подъеб', 'подъебать', 'подъебаться', 'подъебашить', 'подъебнуть', 'подъебка', 'подъебашивать', 'подъябывать', 'поебанный', 'поебать', 'поебаться', 'поебень', 'поебистика', 'поебон', 'поебончик', 'попердеть', 'попердеться', 'попердывать', 'попизденная', 'попиздеть', 'попиздистее', 'попиздить', 'попиздиться', 'попиздоватей', 'попиздоболивать', 'попиздоболиваться', 'попиздоболить', 'попиздоболиться', 'попиздовать', 'попиздоваться', 'попиздовывать', 'попиздовываться', 'попиздохать', 'попиздохаться', 'попиздохивать', 'попиздохиваться', 'попиздошить', 'попиздошиться', 'попиздошивать', 'попиздошиваться', 'попиздюкать', 'попиздюкаться', 'попиздюкивать', 'попиздюкиваться', 'попиздюлить', 'попиздюлиться', 'попиздюливать', 'попиздюливаться', 'попиздюрить', 'попиздюриться', 'попиздюривать', 'попиздюхать', 'попиздюриваться', 'попиздюхаться', 'попиздюхивать', 'попиздюхиваться', 'попиздякать', 'попиздякаться', 'попиздякивать', 'попиздякиваться', 'попиздярить', 'попиздяриться', 'попиздяривать', 'попиздяриваться', 'попиздяхать', 'попиздяхаться', 'попиздяхивать', 'попиздяхиваться', 'попиздячить', 'попиздячиться', 'попиздячивать', 'попиздячиваться', 'попиздяшить', 'попиздяшиться', 'попиздяшивать', 'попиздяшиваться', 'попизживать', 'попизживаться', 'потаскун', 'потаскуха', 'потраханная', 'потрахать', 'потрахаться', 'потрахивать', 'потрахиваться', 'похер', 'похуист', 'похуякать', 'похуякаться', 'похуякивать', 'похуякиваться', 'похуярить', 'похуяриться', 'похуяривать', 'похуяриваться', 'похуячить', 'похуячиться', 'похуячивать', 'похуячиваться', 'похуяшить', 'похуяшиться', 'похуяшивать', 'похуяшиваться', 'разблядоваться', 'раздрочить', 'раздрочиться', 'раззалупаться', 'разнохуйственно', 'разъебать', 'разъебаться', 'разъебашить', 'разъебашиться', 'разъебашивать', 'разъебашиваться', 'разъебенить', 'разъебениться', 'разъебенивать', 'разъебениваться', 'распиздить', 'распиздиться', 'распиздовать', 'распиздоваться', 'распиздовывать', 'распиздовываться', 'распиздохать', 'распиздохаться', 'распиздохивать', 'распиздохиваться', 'распиздошить', 'распиздошиться', 'распиздошивать', 'распиздошиваться', 'распиздон', 'распиздяй', 'расхуярить', 'расхуяриться', 'расхуяривать', 'расхуяриваться', 'расхуячить', 'расхуячиться', 'расхуячивать', 'расхуячиваться', 'сдрочить', 'сестроеб', 'сифилитик', 'сифилюга', 'скурвиться', 'смандить', 'смандиться', 'смандить', 'сперматозавр', 'спиздеть', 'стерва', 'стервоза', 'сука', 'суки', 'сукин', 'сукины', 'суходрочка', 'суходрочкой', 'сучара', 'сучий', 'сучка', 'сучье', 'схуякать', 'схуякаться', 'схуякивать', 'схуякиваться', 'схуярить', 'схуяриться', 'схуяривать', 'схуяриваться', 'схуячить', 'схуячиться', 'схуячивать', 'съебывать', 'съебываться', 'съебать', 'съебаться', 'съебашить', 'съебашиться', 'съебашивать', 'съебашиваться', 'съебенить', 'съебениться', 'съебенивать', 'тварь', 'толстожопый', 'толстозадая', 'торчило', 'траханье', 'трахать', 'трахаться', 'трахнуть', 'трахнуться', 'трепак', 'триппер', 'уебывать', 'уебываться', 'уебыш', 'ублюдок', 'уебать', 'уебашить', 'уебашивать', 'уебенить', 'уебище', 'усраться', 'усрачка', 'уссать', 'уссаться', 'ухуякать', 'ухуякаться', 'ухуякивать', 'ухуякиваться', 'ухуярить', 'ухуяриться', 'ухуяривать', 'ухуяриваться', 'ухуячить', 'ухуячиться', 'ухуячивать', 'ухуячиваться', 'ухуяшить', 'ухуяшиться', 'ухуяшивать', 'ухуяшиваться', 'фаллос', 'фекал', 'фекалий', 'фекалии', 'хер', 'херами', 'херня', 'херовина', 'херов', 'хрен', 'хреново', 'хреновое', 'хреновый', 'хуевина', 'хуев', 'хуево', 'хуевый', 'хуек', 'хуечек', 'худоебина', 'хуебень', 'хуев', 'хуева', 'хуевато', 'хуеватый', 'хуеглот', 'хуегрыз', 'хуедрыга', 'хуемудрие', 'хуемыслие', 'хуеньки', 'хуеплет', 'хуесос', 'хуета', 'хуетень', 'хуец', 'хуила', 'хуиный', 'хуистый', 'хуишко', 'хуище', 'хуи', 'хуило', 'хуйло', 'хуй', 'хуйство', 'хуйнуть', 'хуйня', 'хуйню', 'хули', 'хуюжить', 'хуюжиться', 'хуюживать', 'хуюживаться', 'хуюшки', 'хуя', 'хуяк', 'хуякать', 'хуями', 'хуярить', 'хуяриться', 'хуястый', 'хуячий', 'хуячить', 'хуячиться', 'хуяшить', 'целка', 'целку', 'целочка', 'черножопые', 'чернозадый', 'член', 'шалава', 'шлюха', 'шмара', 'шмарить', 'шмариться', 'хуйло', 'отъебись', 'отьебись', 'спам', 'spam', 'мудила', 'пидарасы'
]

def check_forbidden_words(text):
    """Проверяет текст на наличие запрещенных слов"""
    if not text:
        return False, []
    
    words = text.lower().split()
    found_words = [word for word in words if word in FORBIDDEN_WORDS]
    return len(found_words) > 0, found_words

class BookChapterSerializer(serializers.ModelSerializer):
    class Meta:
        model = BookChapter
        fields = ['id', 'title', 'content', 'order', 'is_published', 'created_at', 'updated_at', 'published_at', 'scheduled_publish_at', 'publish_as_finished']
        read_only_fields = ['id', 'created_at', 'updated_at', 'published_at']

    def validate_content(self, value):
        logger.info(f"Validating content: length={len(value or '')}, contains color: {'color:' in (value or '')}")
        # Очищаем HTML теги и CSS свойства
        cleaned = bleach.clean(
            value, 
            tags=ALLOWED_TAGS, 
            attributes=ALLOWED_ATTRS, 
            css_sanitizer=CSS_SANITIZER,
            strip=True
        )
        logger.info(f"After bleach.clean: length={len(cleaned or '')}, contains color: {'color:' in (cleaned or '')}")
        return cleaned

class BookChapterShortSerializer(serializers.ModelSerializer):
    class Meta:
        model = BookChapter
        fields = ['id', 'title', 'order', 'is_published', 'created_at', 'updated_at', 'published_at', 'scheduled_publish_at', 'publish_as_finished']
        read_only_fields = ['id', 'created_at', 'updated_at', 'published_at']

class GenreSerializer(serializers.ModelSerializer):
    parent_id = serializers.IntegerField(source='parent.id', allow_null=True, read_only=True)

    class Meta:
        model = Genre
        fields = ['id', 'name', 'parent_id']

class HashtagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Hashtag
        fields = ['id', 'name']

    def validate_name(self, value):
        if value:
            has_forbidden, forbidden_words = check_forbidden_words(value)
            if has_forbidden:
                raise serializers.ValidationError(f"Хештег содержит запрещенные слова: {', '.join(forbidden_words)}")
        return value.lower() if value else value

class BookListSerializer(serializers.ModelSerializer):
    """Оптимизированный сериализатор для списка книг без избыточных данных"""
    author_display_name = serializers.CharField(source='author.display_name', read_only=True)
    author_username = serializers.CharField(source='author.username', read_only=True)
    author_avatar_url = serializers.SerializerMethodField()
    cover_mini_url = serializers.SerializerMethodField()
    cover_temp_url = serializers.SerializerMethodField()
    genres = GenreSerializer(many=True, read_only=True)
    hashtags = HashtagSerializer(many=True, read_only=True)
    
    # Новые поля статистики в нужном порядке
    views_count = serializers.IntegerField(read_only=True)  # Просмотры
    likes_count = serializers.IntegerField(read_only=True)  # Лайки  
    library_count = serializers.IntegerField(read_only=True)  # Добавления в библиотеки 
    reviews_count = serializers.IntegerField(read_only=True)  # Отзывы
    comments_count = serializers.IntegerField(read_only=True)  # Комментарии
    
    # Старые поля которые остаются для совместимости  
    chapters_count = serializers.IntegerField(read_only=True)
    published_chapters_count = serializers.IntegerField(read_only=True)
    is_liked = serializers.BooleanField(read_only=True)

    class Meta:
        model = Book
        fields = [
            'id', 'title', 'description', 'cover_type', 'type', 'is_published', 'is_finished', 'status',
            'is_adult', 'age_rating', 'has_profanity', 'auto_indent', 'created_at', 'updated_at', 'published_at',
            'position_finished', 'position_in_progress', 'position_draft',
            'creation_status',
            # Optimized author fields
            'author_display_name', 'author_username', 'author_avatar_url',
            # Optimized cover fields
            'cover_mini_url', 'cover_temp_url',
            # Optimized relations
            'genres', 'hashtags',
            # Optimized counts (новый порядок)
            'views_count', 'likes_count', 'library_count', 'reviews_count', 'comments_count',
            # Старые поля для совместимости
            'chapters_count', 'published_chapters_count', 'is_liked'
        ]

    def get_author_avatar_url(self, obj):
        return obj.author.avatar.url if obj.author.avatar else None

    def get_cover_mini_url(self, obj):
        if obj.cover_mini:
            return obj.cover_mini.url
        return None

    def get_cover_temp_url(self, obj):
        if obj.cover_type != 'custom' and obj.cover:
            return obj.cover.url
        return None

class BookSerializer(serializers.ModelSerializer):
    author = serializers.SerializerMethodField()
    chapters = serializers.SerializerMethodField()
    chapters_count = serializers.SerializerMethodField()  # Быстрый подсчет глав для UI логики
    views_count = serializers.SerializerMethodField()
    likes_count = serializers.SerializerMethodField()
    library_count = serializers.SerializerMethodField()
    reviews_count = serializers.SerializerMethodField()
    comments_count = serializers.SerializerMethodField()
    is_liked = serializers.SerializerMethodField()
    genres = GenreSerializer(many=True, read_only=True)
    genre_ids = serializers.PrimaryKeyRelatedField(
        queryset=Genre.objects.all(),
        many=True,
        write_only=True,
        required=False
    )
    hashtags = HashtagSerializer(many=True, read_only=True)
    hashtag_names = serializers.ListField(
        child=serializers.CharField(max_length=50),
        write_only=True,
        required=False
    )
    position_finished = serializers.IntegerField(read_only=True)
    position_in_progress = serializers.IntegerField(read_only=True)
    position_draft = serializers.IntegerField(read_only=True)
    cover_mini_url = serializers.SerializerMethodField()
    cover_temp_url = serializers.SerializerMethodField()
    creation_status = serializers.CharField()
    type = serializers.CharField(allow_blank=True, allow_null=True, required=False)
    cover_mini = serializers.ImageField(read_only=True)

    class Meta:
        model = Book
        fields = [
            'id', 'title', 'description', 'cover', 'cover_mini', 'cover_editor', 'is_published', 'is_finished', 'status',
            'author', 'chapters', 'chapters_count', 'views_count', 'likes_count', 'library_count', 'reviews_count', 'comments_count',
            'is_liked', 'created_at', 'updated_at', 'published_at', 'cover_type', 'type', 'genres', 'genre_ids', 'is_adult',
            'age_rating', 'has_profanity', 'hashtags', 'hashtag_names', 'auto_indent',
            'position_finished', 'position_in_progress', 'position_draft',
            'cover_mini_url', 'cover_temp_url',
            'creation_status',
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'published_at']

    def get_author(self, obj):
        return {
            'id': obj.author.id,
            'username': obj.author.username,
            'display_name': obj.author.display_name,
            'avatar': obj.author.avatar.url if obj.author.avatar else None
        }

    def get_views_count(self, obj):
        # Пытаемся использовать аннотированное поле, если есть
        if hasattr(obj, 'views_count'):
            return obj.views_count
        return obj.viewed_by.count()

    def get_likes_count(self, obj):
        # Пытаемся использовать аннотированное поле, если есть
        if hasattr(obj, 'likes_count'):
            return obj.likes_count
        return obj.likes.count()

    def get_library_count(self, obj):
        # Пытаемся использовать аннотированное поле, если есть
        if hasattr(obj, 'library_count'):
            return obj.library_count
        return obj.in_libraries.count()

    def get_reviews_count(self, obj):
        # Пытаемся использовать аннотированное поле, если есть
        if hasattr(obj, 'reviews_count'):
            return obj.reviews_count
        return obj.reviews.count()

    def get_comments_count(self, obj):
        # Пытаемся использовать аннотированное поле, если есть
        if hasattr(obj, 'comments_count'):
            # Проверяем, нужно ли пересчитать с учетом новой логики
            # Если аннотация не учитывает скрытие удаленных без ответов
            pass
        
        # Считаем вручную с новой логикой:
        # 1. Все неудаленные комментарии
        # 2. Удаленные комментарии, которые имеют ответы
        from django.db.models import Q, Exists, OuterRef
        
        # Подзапрос для проверки наличия неудаленных ответов
        has_replies = Comment.objects.filter(
            parent=OuterRef('pk'),
            deleted_by_user=False,
            deleted_by_admin=False
        )
        
        # Получаем комментарии для подсчета
        visible_comments = obj.comments.filter(
            Q(deleted_by_user=False, deleted_by_admin=False) |  # Неудаленные
            Q(
                Q(deleted_by_user=True) | Q(deleted_by_admin=True),  # Удаленные
                Exists(has_replies)  # но имеющие ответы
            )
        )
        
        return visible_comments.count()

    def get_is_liked(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.likes.filter(user=request.user).exists()
        return False

    def get_chapters_count(self, obj):
        """Быстрый подсчет глав для определения этапа редактирования"""
        user = self.context['request'].user
        if obj.author == user:
            return obj.chapters.count()
        # Для других пользователей показываем главы только если книга опубликована
        if obj.status in ['in_progress', 'finished']:
            return obj.chapters.filter(is_published=True).count()
        return 0

    def get_chapters(self, obj):
        user = self.context['request'].user
        # Если сериализатор используется для списка (list), не возвращаем content
        request = self.context.get('request')
        view = self.context.get('view')
        is_list = False
        if view and hasattr(view, 'action') and view.action == 'list':
            is_list = True
        # Для user_books (функция, не viewset) — всегда короткий сериализатор
        if self.context.get('short_chapters', False) or is_list:
            if obj.author == user:
                return BookChapterShortSerializer(obj.chapters.all(), many=True).data
            return BookChapterShortSerializer(obj.chapters.filter(is_published=True), many=True).data
        # Для детального просмотра — полный сериализатор
        if obj.author == user:
            return BookChapterSerializer(obj.chapters.all(), many=True).data
        return BookChapterSerializer(obj.chapters.filter(is_published=True), many=True).data

    def get_cover_mini_url(self, obj):
        if obj.cover_mini:
            return obj.cover_mini.url
        return None

    def get_cover_temp_url(self, obj):
        # Если cover_type == 'custom', temp не нужен, иначе возвращаем url cover (если есть)
        if obj.cover_type != 'custom' and obj.cover:
            return obj.cover.url
        return None

    def create(self, validated_data):
        genre_ids = validated_data.pop('genre_ids', [])
        hashtag_names = validated_data.pop('hashtag_names', None)
        book = super().create(validated_data)
        if genre_ids:
            book.genres.set(genre_ids)
        if hashtag_names:
            hashtags = []
            for name in hashtag_names:
                tag, _ = Hashtag.objects.get_or_create(name=name.lower())
                hashtags.append(tag)
            book.hashtags.set(hashtags)
        return book

    def update(self, instance, validated_data):
        genre_ids = validated_data.pop('genre_ids', None)
        hashtag_names = validated_data.pop('hashtag_names', None)
        request = self.context.get('request')
        cover_file = request.FILES.get('cover') if request else None
        cover_editor_file = request.FILES.get('cover_editor') if request else None

        # Проверяем, нужно ли асинхронное сохранение
        async_save = request.data.get('async_save', False) if request else False
        cover_data = request.data.get('cover_data') if request else None
        cover_editor_data = request.data.get('cover_editor_data') if request else None
        cover_mini_data = request.data.get('cover_mini_data') if request else None
        text_overlay_suffix = request.data.get('text_overlay_suffix') if request else None

        logger.info(f"BookSerializer.update: async_save={async_save}, has_cover_data={bool(cover_data)}, has_editor_data={bool(cover_editor_data)}, has_mini_data={bool(cover_mini_data)}, text_overlay_suffix={text_overlay_suffix}")

        # Синхронное сохранение обложки
        if cover_data:
            logger.info(f"Starting sync cover save for book {instance.id}")

            try:
                self._save_cover_sync(instance, cover_data, cover_editor_data, cover_mini_data, text_overlay_suffix)
                logger.info(f"Cover saved successfully for book {instance.id}")

                # Запускаем очистку старых файлов в фоне
                from books.tasks import cleanup_book_covers
                cleanup_book_covers.apply_async(args=[instance.id], countdown=300)  # 5 минут

            except Exception as e:
                logger.error(f"Error saving cover for book {instance.id}: {e}")
                raise serializers.ValidationError(f"Ошибка при сохранении обложки: {str(e)}")

            # Удаляем данные обложки из validated_data, чтобы не обрабатывать их дальше
            validated_data.pop('cover', None)

        # Синхронное сохранение обложки (старая логика)
        elif cover_file:
            # Удаляем старые файлы
            if instance.cover:
                instance.cover.delete(save=False)
            if instance.cover_mini:
                instance.cover_mini.delete(save=False)
            if instance.cover_editor:
                instance.cover_editor.delete(save=False)

            # Сохраняем основную обложку (с лейблами)
            cover_rel_path = book_cover_path(instance, cover_file.name)
            cover_file_content = cover_file.read()
            instance.cover.save(cover_rel_path, ContentFile(cover_file_content), save=False)
            logger.info(f"Saved cover to: {instance.cover.name}")

            # Создаем и сохраняем миниатюру (с лейблами)
            image = Image.open(io.BytesIO(cover_file_content))
            image = image.convert('RGB')
            image = image.resize((210, 300), Image.LANCZOS)
            buf = io.BytesIO()
            image.save(buf, format='JPEG', quality=100)
            buf.seek(0)
            mini_rel_path = book_covermini_path(instance, cover_file.name)
            instance.cover_mini.save(mini_rel_path, ContentFile(buf.getvalue()), save=False)
            logger.info(f"Saved cover_mini to: {instance.cover_mini.name}")

            # Сохраняем обложку для редактора (без лейблов) с правильным именованием
            if cover_editor_file:
                editor_rel_path = book_cover_editor_path(instance, cover_editor_file.name)
                instance.cover_editor.save(editor_rel_path, ContentFile(cover_editor_file.read()), save=False)
                logger.info(f"Saved cover_editor to: {instance.cover_editor.name}")
            else:
                # Если обложка для редактора не предоставлена, используем основную (с лейблами)
                # Создаем путь с правильным именованием для editor файла
                editor_rel_path = book_cover_editor_path(instance, cover_file.name)
                instance.cover_editor.save(editor_rel_path, ContentFile(cover_file_content), save=False)
                logger.info(f"Saved cover_editor (fallback) to: {instance.cover_editor.name}")

            instance.cover_type = 'custom'
            instance.save(update_fields=['cover', 'cover_mini', 'cover_editor', 'cover_type'])
            validated_data.pop('cover', None)

            # Запускаем задачу очистки старых обложек (асинхронно)
            # Используем threading для полной изоляции от основного потока
            from django.conf import settings
            import threading

            def schedule_cleanup():
                try:
                    if not getattr(settings, 'ENABLE_COVER_CLEANUP', True):
                        logger.info(f"Cover cleanup disabled for book {instance.id}")
                        return

                    delay = getattr(settings, 'COVER_CLEANUP_DELAY', 20)
                    from .tasks import cleanup_book_covers
                    cleanup_book_covers.apply_async(args=[instance.id], countdown=delay)
                    logger.info(f"Scheduled cleanup task for book {instance.id} in {delay} seconds")
                except Exception as e:
                    logger.error(f"Failed to schedule cleanup task for book {instance.id}: {e}")

            # Запускаем в отдельном потоке, чтобы не блокировать ответ
            cleanup_thread = threading.Thread(target=schedule_cleanup, daemon=True)
            cleanup_thread.start()
        # Сохраняем тип обложки, если он передан явно (например, system для заглушки)
        cover_type = request.data.get('cover_type') if request else None
        if cover_type == 'system':
            instance.cover_type = 'system'
            instance.cover = None
            instance.cover_mini = None
            instance.save(update_fields=['cover_type', 'cover', 'cover_mini'])
        book = super().update(instance, validated_data)
        if genre_ids is not None:
            book.genres.set(genre_ids)
        if hashtag_names is not None:
            hashtags = []
            for name in hashtag_names:
                tag, _ = Hashtag.objects.get_or_create(name=name.lower())
                hashtags.append(tag)
            book.hashtags.set(hashtags)
        return book

    def validate_title(self, value):
        if value:
            has_forbidden, forbidden_words = check_forbidden_words(value)
            if has_forbidden:
                raise serializers.ValidationError(f"Заголовок содержит запрещенные слова: {', '.join(forbidden_words)}")
        return value

    def validate_description(self, value):
        if value:
            has_forbidden, forbidden_words = check_forbidden_words(value)
            if has_forbidden:
                raise serializers.ValidationError(f"Описание содержит запрещенные слова: {', '.join(forbidden_words)}")
        return bleach.clean(value, tags=ALLOWED_TAGS, attributes=ALLOWED_ATTRS, strip=True)

    def validate_hashtag_names(self, value):
        if value:
            for hashtag in value:
                has_forbidden, forbidden_words = check_forbidden_words(hashtag)
                if has_forbidden:
                    raise serializers.ValidationError(f"Хештег '{hashtag}' содержит запрещенные слова: {', '.join(forbidden_words)}")
        return value

    def _save_cover_sync(self, instance, cover_data, cover_editor_data=None, cover_mini_data=None, text_overlay_suffix=None):
        """Синхронное сохранение обложки"""
        import base64
        import io
        from PIL import Image
        from django.core.files.base import ContentFile

        # Декодируем base64 данные основной обложки
        if cover_data.startswith('data:image'):
            cover_data = cover_data.split(',')[1]
        cover_bytes = base64.b64decode(cover_data)

        # Декодируем base64 данные обложки для редактора (если есть)
        editor_bytes = None
        if cover_editor_data:
            if cover_editor_data.startswith('data:image'):
                cover_editor_data = cover_editor_data.split(',')[1]
            editor_bytes = base64.b64decode(cover_editor_data)

        # Создаем файл основной обложки
        cover_file = ContentFile(cover_bytes)
        cover_filename = f"{instance.id}_cover.jpg"
        instance.cover.save(cover_filename, cover_file, save=False)

        # Создаем и сохраняем миниатюру
        # Проверяем, есть ли готовая миниатюра с лейблами от frontend
        if cover_mini_data:
            # Используем готовую миниатюру с лейблами
            if cover_mini_data.startswith('data:image'):
                format_str, imgstr = cover_mini_data.split(';base64,')
                mini_bytes = base64.b64decode(imgstr)
            else:
                mini_bytes = base64.b64decode(cover_mini_data)

            mini_filename = f"{instance.id}_cover_mini.jpg"
            instance.cover_mini.save(mini_filename, ContentFile(mini_bytes), save=False)
        else:
            # Создаем миниатюру обычным способом (уменьшение основной обложки)
            image = Image.open(io.BytesIO(cover_bytes))
            image = image.convert('RGB')
            image = image.resize((210, 300), Image.LANCZOS)
            buf = io.BytesIO()
            image.save(buf, format='JPEG', quality=85)  # Уменьшено с 100 до 85 для оптимизации размера
            buf.seek(0)
            mini_filename = f"{instance.id}_cover_mini.jpg"
            instance.cover_mini.save(mini_filename, ContentFile(buf.getvalue()), save=False)

        # Сохраняем обложку для редактора с правильным именованием
        # Временно сохраняем суффикс в атрибуте экземпляра для использования в upload_to
        instance._temp_text_suffix = text_overlay_suffix

        if editor_bytes:
            editor_file = ContentFile(editor_bytes)
            instance.cover_editor.save("editor_cover.jpg", editor_file, save=False)
            logger.info(f"Saved cover_editor with text suffix '{text_overlay_suffix}' to: {instance.cover_editor.name}")
        else:
            # Если обложка для редактора не предоставлена, используем основную
            instance.cover_editor.save("editor_cover.jpg", ContentFile(cover_bytes), save=False)
            logger.info(f"Saved cover_editor (fallback) to: {instance.cover_editor.name}")

        # Очищаем временный атрибут
        if hasattr(instance, '_temp_text_suffix'):
            delattr(instance, '_temp_text_suffix')

        # Устанавливаем тип обложки и сохраняем
        instance.cover_type = 'custom'
        instance.save(update_fields=['cover', 'cover_mini', 'cover_editor', 'cover_type'])

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if data.get('description'):
            data['description'] = bleach.clean(
                data['description'],
                tags=ALLOWED_TAGS,
                attributes=ALLOWED_ATTRS,
                strip=True
            )



        return data

class BookCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Book
        fields = ['id']  # только id возвращаем после создания
        read_only_fields = ['id']

    def create(self, validated_data):
        user = self.context['request'].user
        book = Book.objects.create(author=user, creation_status='creating')
        return book

class BookChapterCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = BookChapter
        fields = ['id', 'title', 'content', 'order', 'is_published', 'created_at', 'updated_at', 'published_at', 'scheduled_publish_at', 'publish_as_finished']
        read_only_fields = ['id', 'created_at', 'updated_at', 'published_at']

class ReviewSerializer(serializers.ModelSerializer):
    user = serializers.SerializerMethodField()

    class Meta:
        model = Review
        fields = ['id', 'user', 'text', 'created_at']
        read_only_fields = ['id', 'user', 'created_at']

    def get_user(self, obj):
        return {
            'id': obj.user.id,
            'username': obj.user.username,
            'display_name': obj.user.display_name,
            'avatar': obj.user.avatar.url if obj.user.avatar else None
        }

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        validated_data['book_id'] = self.context['book_id']
        return super().create(validated_data)

class CommentSerializer(serializers.ModelSerializer):
    from users.serializers import MiniUserSerializer
    user = MiniUserSerializer(read_only=True)
    replies = serializers.SerializerMethodField()
    replies_count = serializers.SerializerMethodField()
    parent_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    likes_count = serializers.SerializerMethodField()
    user_reaction = serializers.SerializerMethodField()
    # Новые поля для мягкого удаления и разрешений
    is_deleted = serializers.SerializerMethodField()
    deletion_message = serializers.SerializerMethodField()
    can_edit = serializers.SerializerMethodField()
    can_delete = serializers.SerializerMethodField()
    reply_to = serializers.PrimaryKeyRelatedField(queryset=Comment.objects.all(), required=False, allow_null=True)
    reply_to_id = serializers.SerializerMethodField()

    class Meta:
        model = Comment
        fields = ['id', 'user', 'text', 'created_at', 'updated_at', 'is_edited', 
                 'parent_id', 'replies', 'replies_count', 'likes_count', 'user_reaction',
                 'is_deleted', 'deletion_message', 'can_edit', 'can_delete', 'reply_to', 'reply_to_id']
        read_only_fields = ['id', 'user', 'created_at', 'updated_at', 'is_edited']



    def get_replies(self, obj):
        # Возвращаем только прямые ответы (не глубже одного уровня как в YouTube)
        if obj.parent is None:  # Только для основных комментариев
            # Применяем ту же логику, что и для основных комментариев:
            # 1. Показываем все неудаленные ответы
            # 2. Показываем удаленные ответы, на которые кто-то ответил (с префиксом имени)
            
            all_replies = obj.replies.all()
            filtered_replies = []
            
            for reply in all_replies:
                if reply.is_deleted:
                    # Для удаленного ответа проверяем, есть ли ответы с его именем в префиксе
                    reply_author_name = reply.user.display_name or reply.user.username
                    has_responses = obj.replies.filter(
                        deleted_by_user=False, 
                        deleted_by_admin=False,
                        text__startswith=f"{reply_author_name}  "  # Двойной пробел как в коде
                    ).exists()
                    
                    if has_responses:
                        filtered_replies.append(reply)
                    # Иначе скрываем удаленный ответ без откликов
                else:
                    # Неудаленные ответы показываем всегда
                    filtered_replies.append(reply)
            
            # Возвращаем только первые 3 для предварительного просмотра
            serializer = CommentSerializer(filtered_replies[:3], many=True, context=self.context)
            return serializer.data
        return []

    def get_replies_count(self, obj):
        if obj.parent is None:  # Только для основных комментариев
            # Применяем ту же логику подсчета, что и в get_replies
            all_replies = obj.replies.all()
            visible_count = 0
            
            for reply in all_replies:
                if reply.is_deleted:
                    # Для удаленного ответа проверяем, есть ли ответы с его именем в префиксе
                    reply_author_name = reply.user.display_name or reply.user.username
                    has_responses = obj.replies.filter(
                        deleted_by_user=False, 
                        deleted_by_admin=False,
                        text__startswith=f"{reply_author_name}  "  # Двойной пробел как в коде
                    ).exists()
                    
                    if has_responses:
                        visible_count += 1
                    # Иначе не считаем удаленный ответ без откликов
                else:
                    # Неудаленные ответы считаем всегда
                    visible_count += 1
            
            return visible_count
        return 0

    def get_likes_count(self, obj):
        """Возвращает количество лайков у комментария"""
        return obj.likes.filter(reaction='like').count()

    def get_user_reaction(self, obj):
        """Возвращает реакцию текущего пользователя на комментарий"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                reaction = obj.likes.get(user=request.user)
                return reaction.reaction
            except CommentLike.DoesNotExist:
                return None
        return None

    def get_is_deleted(self, obj):
        """Проверяет, удален ли комментарий"""
        return obj.is_deleted

    def get_deletion_message(self, obj):
        """Возвращает сообщение об удалении"""
        return obj.get_deletion_message()

    def get_can_edit(self, obj):
        """Проверяет, может ли пользователь редактировать комментарий"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            # Автор может редактировать только если комментарий не удален
            return obj.user == request.user and not obj.is_deleted
        return False

    def get_can_delete(self, obj):
        """Проверяет, может ли пользователь удалить комментарий"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            # Автор может удалить, если комментарий не удален
            # Администратор может удалить любой неудаленный комментарий
            if not obj.is_deleted:
                return obj.user == request.user or request.user.is_staff
        return False

    def get_reply_to_id(self, obj):
        """Возвращает ID комментария, на который отвечают"""
        return obj.reply_to.id if obj.reply_to else None

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        validated_data['book_id'] = self.context['book_id']
        # Обработка parent_id
        parent_id = validated_data.pop('parent_id', None)
        if parent_id:
            try:
                parent_comment = Comment.objects.get(id=parent_id, book_id=validated_data['book_id'])
                validated_data['parent'] = parent_comment
            except Comment.DoesNotExist:
                raise serializers.ValidationError({'parent_id': 'Parent comment not found'})
        # Обработка reply_to
        reply_to_id = self.initial_data.get('reply_to')
        if reply_to_id:
            try:
                reply_to_comment = Comment.objects.get(id=reply_to_id)
                validated_data['reply_to'] = reply_to_comment
            except Comment.DoesNotExist:
                raise serializers.ValidationError({'reply_to': 'Reply-to comment not found'})
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """Обновление комментария с пометкой о редактировании"""
        # Проверяем разрешения
        request = self.context.get('request')
        if not (request and request.user.is_authenticated and instance.user == request.user):
            raise serializers.ValidationError("У вас нет прав для редактирования этого комментария")
        
        if instance.is_deleted:
            raise serializers.ValidationError("Нельзя редактировать удаленный комментарий")
        
        # Если текст изменился, отмечаем как отредактированный
        if 'text' in validated_data and validated_data['text'] != instance.text:
            validated_data['is_edited'] = True
        
        return super().update(instance, validated_data)

class BookChapterUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = BookChapter
        fields = ['id', 'title', 'content', 'order', 'is_published', 'created_at', 'updated_at', 'published_at', 'scheduled_publish_at', 'publish_as_finished']
        read_only_fields = ['id', 'created_at', 'updated_at', 'published_at']

    def validate_content(self, value):
        logger.info(f"Validating content (update): length={len(value or '')}, contains color: {'color:' in (value or '')}")
        # Очищаем HTML теги и CSS свойства
        cleaned = bleach.clean(
            value, 
            tags=ALLOWED_TAGS, 
            attributes=ALLOWED_ATTRS, 
            css_sanitizer=CSS_SANITIZER,
            strip=True
        )
        logger.info(f"After bleach.clean (update): length={len(cleaned or '')}, contains color: {'color:' in (cleaned or '')}")
        return cleaned

    def update(self, instance, validated_data):
        import logging
        logger = logging.getLogger(__name__)

        scheduled_publish_at = validated_data.get('scheduled_publish_at', instance.scheduled_publish_at)
        was_published = instance.is_published

        # Автоматически очищаем устаревшие задачи планирования
        if instance.clean_expired_schedule():
            logger.info(f"Автоматически очищена устаревшая задача планирования для главы {instance.id}")
        
        # Если снимаем отложенную публикацию — умная отмена задачи
        if scheduled_publish_at is None and instance.scheduled_publish_at and hasattr(instance, 'celery_task_id') and instance.celery_task_id:
            # Находим другие главы с той же задачей
            same_task_chapters = BookChapter.objects.filter(
                book=instance.book,
                celery_task_id=instance.celery_task_id,
                is_published=False
            ).exclude(id=instance.id)

            # Отменяем старую задачу
            try:
                AsyncResult(instance.celery_task_id).revoke(terminate=True)
                logger.info(f"Отменена задача {instance.celery_task_id} при снятии отложенной публикации главы {instance.id}")
            except Exception as e:
                logger.warning(f"Не удалось отменить задачу {instance.celery_task_id}: {e}")

            # Если есть другие главы с той же задачей - создаем новую задачу для них
            if same_task_chapters.exists():
                try:
                    from books.tasks import publish_chapters_batch_task

                    # Берем параметры от первой главы
                    first_chapter = same_task_chapters.first()
                    chapter_ids = list(same_task_chapters.values_list('id', flat=True))

                    # Создаем новую массовую задачу
                    new_task = publish_chapters_batch_task.apply_async(
                        args=[instance.book.id, chapter_ids, first_chapter.publish_as_finished or False],
                        eta=first_chapter.scheduled_publish_at.astimezone(pytz.utc) if first_chapter.scheduled_publish_at.tzinfo else pytz.timezone(settings.TIME_ZONE).localize(first_chapter.scheduled_publish_at).astimezone(pytz.utc)
                    )

                    # Обновляем ID задачи для оставшихся глав
                    same_task_chapters.update(celery_task_id=new_task.id)

                    logger.info(f"Создана новая задача {new_task.id} для {same_task_chapters.count()} оставшихся глав")

                except Exception as e:
                    logger.error(f"Ошибка при пересоздании задачи для оставшихся глав: {e}")

            instance.celery_task_id = None
            # Также очищаем флаг завершения при отмене
            if 'publish_as_finished' in validated_data:
                instance.publish_as_finished = None

            logger.info(f"Отменена отложенная публикация главы {instance.id}, поля очищены")
            
        # Если назначаем новую публикацию — ставим задачу
        elif scheduled_publish_at and (not instance.scheduled_publish_at or scheduled_publish_at != instance.scheduled_publish_at):
            # Отменяем старую задачу, если была
            if hasattr(instance, 'celery_task_id') and instance.celery_task_id:
                try:
                    AsyncResult(instance.celery_task_id).revoke(terminate=True)
                except Exception:
                    pass
            
            # ОПТИМИЗИРОВАНО: Проверяем, есть ли другие главы на то же время
            try:
                # Преобразуем в UTC
                tz = pytz.timezone(settings.TIME_ZONE)
                eta = scheduled_publish_at.astimezone(pytz.utc) if scheduled_publish_at.tzinfo else tz.localize(scheduled_publish_at).astimezone(pytz.utc)

                # Проверяем, есть ли другие главы на то же время (в пределах 1 минуты)
                same_time_chapters = BookChapter.objects.filter(
                    book=instance.book,
                    scheduled_publish_at__range=(
                        scheduled_publish_at - timezone.timedelta(minutes=1),
                        scheduled_publish_at + timezone.timedelta(minutes=1)
                    ),
                    is_published=False
                ).exclude(id=instance.id)

                # Проверяем, нужно ли применить умную логику планирования
                publish_as_finished = getattr(instance, 'publish_as_finished', False)

                if publish_as_finished:
                    # УМНАЯ ЛОГИКА: Применяем ту же логику, что и в batch_schedule
                    logger.info(f"Применяем умную логику планирования для главы {instance.id} с завершением произведения")

                    # Находим все неопубликованные главы и анализируем их планирование
                    all_unpublished_chapters = BookChapter.objects.filter(
                        book=instance.book,
                        is_published=False
                    ).values('id', 'scheduled_publish_at')

                    # Разделяем главы по категориям
                    chapters_to_reschedule_ids = [instance.id]  # Начинаем с текущей главы

                    for chapter in all_unpublished_chapters:
                        if chapter['id'] == instance.id:
                            continue  # Пропускаем текущую главу

                        if chapter['scheduled_publish_at'] is None:
                            # Главы без планирования - добавляем
                            chapters_to_reschedule_ids.append(chapter['id'])
                        elif chapter['scheduled_publish_at'] > scheduled_publish_at:
                            # Главы с планированием ПОЗЖЕ - переносим
                            chapters_to_reschedule_ids.append(chapter['id'])
                        # Главы с планированием РАНЬШЕ - НЕ трогаем

                    logger.info(f"Умная логика: планируем {len(chapters_to_reschedule_ids)} глав (включая текущую)")

                    # Отменяем задачи для всех глав, которые будем перепланировать
                    chapters_to_update = BookChapter.objects.filter(id__in=chapters_to_reschedule_ids)
                    unique_task_ids = set()
                    for chapter in chapters_to_update:
                        if chapter.celery_task_id:
                            unique_task_ids.add(chapter.celery_task_id)

                    # Асинхронная отмена задач
                    if unique_task_ids:
                        import threading
                        def revoke_tasks_async():
                            for task_id in unique_task_ids:
                                try:
                                    AsyncResult(task_id).revoke(terminate=True)
                                except Exception:
                                    pass

                        revoke_thread = threading.Thread(target=revoke_tasks_async, daemon=True)
                        revoke_thread.start()

                    # Создаем массовую задачу для всех глав
                    from books.tasks import publish_chapters_batch_task
                    result = publish_chapters_batch_task.apply_async(
                        args=[instance.book.id, chapters_to_reschedule_ids, True],
                        eta=eta,
                        expires=eta + timezone.timedelta(hours=24)
                    )

                    # Обновляем все главы
                    chapters_to_update.update(
                        scheduled_publish_at=scheduled_publish_at,
                        celery_task_id=result.id,
                        publish_as_finished=True
                    )

                    logger.info(f"Создана УМНАЯ массовая задача для {len(chapters_to_reschedule_ids)} глав с завершением произведения")

                elif same_time_chapters.exists():
                    # Обычная логика для глав без завершения произведения
                    all_chapters = list(same_time_chapters) + [instance]
                    chapter_ids = [ch.id for ch in all_chapters]

                    # Отменяем существующие задачи
                    for chapter in same_time_chapters:
                        if chapter.celery_task_id:
                            try:
                                AsyncResult(chapter.celery_task_id).revoke(terminate=True)
                            except Exception:
                                pass

                    # Создаем одну массовую задачу
                    from books.tasks import publish_chapters_batch_task
                    result = publish_chapters_batch_task.apply_async(
                        args=[instance.book.id, chapter_ids, False],
                        eta=eta,
                        expires=eta + timezone.timedelta(hours=24)
                    )

                    # Обновляем все главы одним ID задачи
                    same_time_chapters.update(celery_task_id=result.id)
                    instance.celery_task_id = result.id

                    logger.info(f"Создана МАССОВАЯ задача для {len(all_chapters)} глав книги {instance.book.id} на время {eta}")
                else:
                    # Только одна глава - используем индивидуальную задачу
                    result = publish_chapter_task.apply_async(
                        args=[instance.id, getattr(instance, 'publish_as_finished', None)],
                        eta=eta,
                        expires=eta + timezone.timedelta(hours=24)
                    )
                    instance.celery_task_id = result.id

                    logger.info(f"Создана индивидуальная задача для главы {instance.id} на время {eta}")
                
            except Exception as e:
                # В случае ошибки планирования, логируем но не прерываем процесс
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Ошибка при планировании публикации главы {instance.id}: {e}")
                
        result = super().update(instance, validated_data)
        
        # Если только что опубликовали главу ВРУЧНУЮ — отправить событие в канал
        # (Для отложенной публикации WebSocket отправляется из задачи publish_chapter_task)
        if not was_published and result.is_published and not result.scheduled_publish_at:
            # Для рассказов устанавливаем статус книги
            if result.book.type == 'story':
                result.book.is_published = True
                if result.publish_as_finished is not None:
                    result.book.is_finished = result.publish_as_finished
                    # Устанавливаем новый статус
                    result.book.status = 'finished' if result.publish_as_finished else 'in_progress'
                    result.book.save(update_fields=['is_published', 'is_finished', 'status'])
                    # Очищаем поле после использования
                    result.publish_as_finished = None
                    result.save(update_fields=['publish_as_finished'])
                else:
                    result.book.status = 'in_progress'
                    result.book.save(update_fields=['is_published', 'status'])
            
            channel_layer = get_channel_layer()
            async_to_sync(channel_layer.group_send)(
                f'book_{result.book.id}',
                {
                    'type': 'chapter_published',
                    'chapter_id': result.id,
                    'title': result.title,
                    'book_is_finished': result.book.is_finished,  # Для обратной совместимости
                    'book_status': result.book.status,
                }
            )
        
        # Если только что сняли главу с публикации — обновляем статус книги для рассказов
        elif was_published and not result.is_published:
            if result.book.type == 'story':
                result.book.is_published = False
                result.book.is_finished = False
                result.book.status = 'draft'
                result.book.save(update_fields=['is_published', 'is_finished', 'status'])
                
                # Отправляем WebSocket событие о снятии с публикации
                channel_layer = get_channel_layer()
                async_to_sync(channel_layer.group_send)(
                    f'book_{result.book.id}',
                    {
                        'type': 'chapter_unpublished',
                        'chapter_id': result.id,
                        'title': result.title,
                        'book_is_finished': result.book.is_finished,  # Для обратной совместимости
                        'book_status': result.book.status,
                    }
                )
        
        return result