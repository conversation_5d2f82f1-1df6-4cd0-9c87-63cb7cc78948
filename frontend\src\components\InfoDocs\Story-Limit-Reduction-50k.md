# Снижение лимита рассказов до 50,000 символов

## Обзор изменений
Лимит для рассказов снижен с 80,000 до 50,000 символов. Добавлены проверки во всех местах: редактирование, сохранение, публикация, планирование и загрузка DOCX.

## Проблема, которую решаем

### Слишком большой лимит для рассказов
```
БЫЛО: Лимит рассказов 80,000 символов
→ Слишком большой объем для формата "рассказ"
→ Размытие границ между рассказом и повестью
→ Неоптимальная категоризация произведений

СТАЛО: Лимит рассказов 50,000 символов
→ Четкое разделение форматов произведений
→ Рассказ: до 50k символов
→ Повесть: до 2.5M символов (средний формат)
→ Роман: до 2.5M символов (крупный формат)
```

## Реализованные изменения

### 1. Обновлен лимит в StoryEditor

#### Константа лимита:
```javascript
// БЫЛО:
const MAX_STORY_LENGTH = 80000;

// СТАЛО:
const MAX_STORY_LENGTH = 50000;
```

#### Проверка при сохранении:
```javascript
// Проверяем превышение лимита символов для всех рассказов
if (totalWithNewContent > MAX_STORY_LENGTH) {
  const isPublished = localBook?.is_published || false;
  Modal.error({
    title: `Объем рассказа превышает допустимый`,
    content: (
      <div>
        <p>
          Размер рассказа <strong>{totalWithNewContent.toLocaleString()} символов</strong> превышает
          допустимый лимит <strong>{MAX_STORY_LENGTH.toLocaleString()} символов</strong>.
        </p>
        <p>
          Сохранение невозможно. Рекомендуется сократить текст рассказа{isPublished ? ' или снять его с публикации' : ''}.
        </p>
        <p style={{ color: '#666', fontSize: '14px', marginTop: '12px' }}>
          Превышение: <strong>{(totalWithNewContent - MAX_STORY_LENGTH).toLocaleString()} символов</strong>
        </p>
      </div>
    ),
    okText: 'Понятно'
  });
  return; // Блокируем сохранение
}
```

### 2. Проверка при публикации

#### Немедленная публикация:
```javascript
const handlePublish = async () => {
  // Проверяем превышение лимита символов для рассказа
  if (isStoryOverLimit) {
    message.error('Функция недоступна, превышен максимальный размер рассказа');
    return;
  }
  // ... остальная логика публикации
};
```

#### Отложенная публикация:
```javascript
const handleSchedulePublish = async () => {
  if (!scheduleDate || !safeChapters.length) return;
  
  // Проверяем превышение лимита символов для рассказа
  if (isStoryOverLimit) {
    message.error('Планирование публикации невозможно: превышен максимальный размер рассказа');
    setShowScheduleModal(false);
    return;
  }
  // ... остальная логика планирования
};
```

### 3. Обновлен backend для DOCX загрузки

#### Проверка содержимого DOCX:
```python
# Проверяем, не превышает ли общий объем лимит произведения
if book.type == 'story':
    # Для рассказов - лимит 50,000 символов
    MAX_STORY_LENGTH = 50000
    if total_new_content_length > MAX_STORY_LENGTH:
        logger.warning(f"DOCX файл превышает лимит рассказа: {total_new_content_length} > {MAX_STORY_LENGTH}")
        return Response({
            'error': f'Документ превышает максимальный размер рассказа в {MAX_STORY_LENGTH:,} символов. '
                    f'Текущий размер: {total_new_content_length:,} символов. '
                    f'Рекомендуется сократить текст или разделить на несколько рассказов.'
        }, status=400)
else:
    # Для романов, повестей и сборников - лимит 2.5M символов
    if total_new_content_length > MAX_NOVEL_LENGTH:
        # ... существующая логика для других типов
```

### 4. Обновлены сообщения и подсказки

#### Tooltip для кнопки завершения:
```javascript
// БЫЛО:
title={isStoryOverLimit ? "Превышен максимальный размер рассказа (80,000 символов)" : ""}

// СТАЛО:
title={isStoryOverLimit ? "Превышен максимальный размер рассказа (50,000 символов)" : ""}
```

#### Счетчик символов:
```javascript
{totalCharacters.toLocaleString()}/{MAX_STORY_LENGTH.toLocaleString()} символов
// Показывает: "25,000/50,000 символов"
```

## Места применения ограничений

### ✅ Все проверки обновлены:

#### 1. Редактирование и сохранение:
- **Проверка при сохранении**: блокировка сохранения при превышении 50k
- **Визуальный индикатор**: красный счетчик при превышении лимита
- **Информативное сообщение**: с указанием точного превышения

#### 2. Публикация:
- **Немедленная публикация**: блокировка кнопки при превышении лимита
- **Отложенная публикация**: блокировка планирования при превышении
- **Завершение рассказа**: блокировка установки статуса "Завершено"

#### 3. Загрузка DOCX:
- **Проверка размера файла**: до 10 МБ (без изменений)
- **Проверка содержимого**: до 50,000 символов (обновлено)
- **Информативные ошибки**: с рекомендациями по решению

#### 4. Интерфейс:
- **Счетчик символов**: показывает текущий прогресс
- **Цветовая индикация**: красный цвет при превышении
- **Подсказки**: обновленные tooltip с новым лимитом

## Пользовательские сценарии

### Сценарий 1: Сохранение рассказа с превышением лимита
```
Пользователь пишет рассказ на 55,000 символов
→ Пытается сохранить изменения
→ Система показывает модальное окно:
   "Объем рассказа превышает допустимый"
   "Размер рассказа 55,000 символов превышает лимит 50,000 символов"
   "Превышение: 5,000 символов"
→ Сохранение блокируется
→ Пользователь остается в редакторе для сокращения текста
```

### Сценарий 2: Попытка публикации большого рассказа
```
Пользователь написал рассказ на 52,000 символов
→ Пытается опубликовать рассказ
→ Кнопка "Опубликовать" неактивна
→ Tooltip показывает: "Превышен максимальный размер рассказа (50,000 символов)"
→ Счетчик показывает красным: "52,000/50,000 символов (Превышен размер рассказа)"
→ Публикация невозможна до сокращения текста
```

### Сценарий 3: Загрузка большого DOCX файла
```
Пользователь загружает DOCX файл с рассказом на 60,000 символов
→ Файл проходит проверку размера (< 10 МБ)
→ Backend обрабатывает содержимое
→ Обнаруживается превышение лимита символов
→ Возвращается ошибка:
   "Документ превышает максимальный размер рассказа в 50,000 символов"
   "Текущий размер: 60,000 символов"
   "Рекомендуется сократить текст или разделить на несколько рассказов"
→ Загрузка отклоняется
```

### Сценарий 4: Планирование публикации большого рассказа
```
Пользователь написал рассказ на 51,000 символов
→ Пытается запланировать публикацию
→ Открывает модальное окно планирования
→ Выбирает дату и время
→ Нажимает "Запланировать"
→ Система проверяет лимит и показывает ошибку:
   "Планирование публикации невозможно: превышен максимальный размер рассказа"
→ Модальное окно закрывается
→ Планирование блокируется
```

## Сравнение лимитов

### Обновленная иерархия лимитов:
```
📖 Рассказ: 50,000 символов
   └── Короткая форма, одна сюжетная линия

📝 Повесть: 2,500,000 символов  
   └── Средняя форма, развернутый сюжет

📚 Роман: 2,500,000 символов
   └── Крупная форма, сложная структура

📑 Сборник рассказов: 2,500,000 символов
   └── Коллекция отдельных произведений

🎭 Сборник стихов: 2,500,000 символов
   └── Коллекция поэтических произведений
```

### Преимущества нового лимита:

#### ✅ Четкая категоризация:
- **Рассказ**: до 50k - короткая форма
- **Повесть**: до 2.5M - средняя форма  
- **Роман**: до 2.5M - крупная форма

#### ✅ Лучшая организация:
- **Понятные границы** между форматами
- **Правильная категоризация** произведений
- **Соответствие литературным стандартам**

#### ✅ Техническая оптимизация:
- **Быстрая загрузка** рассказов
- **Эффективная обработка** небольших произведений
- **Оптимальное использование** ресурсов

## Технические детали

### Константы лимитов:
```javascript
// Frontend (StoryEditor.jsx)
const MAX_STORY_LENGTH = 50000;     // 50k символов для рассказов

// Backend (views.py)
MAX_STORY_LENGTH = 50000            # 50k символов для рассказов
MAX_NOVEL_LENGTH = 2500000          # 2.5M символов для других типов
```

### Проверки в коде:
```javascript
// Проверка превышения лимита
const isStoryOverLimit = localBook?.type === 'story' && totalCharacters > MAX_STORY_LENGTH;

// Блокировка функций при превышении
disabled={isStoryOverLimit}

// Визуальная индикация
color: isStoryOverLimit ? '#ef4444' : (theme === 'dark' ? '#a1a1aa' : '#666')
```

## Результат изменений

### 📊 Статистика обновлений:
- **Обновлена 1 константа**: MAX_STORY_LENGTH с 80k до 50k
- **Добавлена 1 проверка**: в планировании публикации
- **Обновлена 1 проверка**: в backend для DOCX
- **Исправлен 1 tooltip**: с новым лимитом

### 🎯 Улучшения:
- ✅ **Четкие границы** между форматами произведений
- ✅ **Консистентные проверки** во всех местах
- ✅ **Информативные сообщения** об ошибках
- ✅ **Правильная категоризация** литературных форм

### 🚀 Итоговый результат:
Лимит рассказов теперь составляет **50,000 символов** с полной защитой:
- ✅ **Блокировка сохранения** при превышении лимита
- ✅ **Блокировка публикации** (немедленной и отложенной)
- ✅ **Блокировка загрузки DOCX** с превышением
- ✅ **Визуальная индикация** превышения лимита
- ✅ **Информативные сообщения** с рекомендациями

**Рассказы теперь имеют оптимальный лимит в 50,000 символов!** 📖
