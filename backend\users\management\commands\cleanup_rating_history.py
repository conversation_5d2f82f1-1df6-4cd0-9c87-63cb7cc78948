"""
Команда для ручной очистки истории рейтинга.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from users.models import RatingHistory
import time


class Command(BaseCommand):
    help = 'Очистка старой истории рейтинга (старше указанного количества дней)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=100,
            help='Количество дней для хранения истории (по умолчанию: 100)'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=1000,
            help='Размер пакета для удаления (по умолчанию: 1000)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Показать количество записей для удаления без фактического удаления'
        )

    def handle(self, *args, **options):
        days = options['days']
        batch_size = options['batch_size']
        dry_run = options['dry_run']
        
        cutoff_date = timezone.now() - timedelta(days=days)
        
        self.stdout.write(
            self.style.SUCCESS(f'Поиск записей истории рейтинга старше {days} дней (до {cutoff_date.date()})')
        )
        
        # Подсчитываем количество записей
        records_to_delete = RatingHistory.objects.filter(created_at__lt=cutoff_date)
        count_to_delete = records_to_delete.count()
        
        if count_to_delete == 0:
            self.stdout.write(self.style.SUCCESS('Нет записей для удаления'))
            return
        
        self.stdout.write(
            self.style.WARNING(f'Найдено {count_to_delete} записей для удаления')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS('Режим dry-run: записи НЕ будут удалены')
            )
            
            # Показываем статистику по типам действий
            from django.db.models import Count
            stats = records_to_delete.values('action_type').annotate(
                count=Count('id')
            ).order_by('-count')
            
            self.stdout.write('\nСтатистика по типам действий:')
            for stat in stats[:10]:  # Топ 10
                self.stdout.write(f"  {stat['action_type']}: {stat['count']} записей")
            
            return
        
        # Подтверждение удаления
        confirm = input(f'Вы уверены, что хотите удалить {count_to_delete} записей? (yes/no): ')
        if confirm.lower() != 'yes':
            self.stdout.write(self.style.ERROR('Операция отменена'))
            return
        
        # Удаляем пакетами
        deleted_total = 0
        start_time = time.time()
        
        self.stdout.write(f'Начинаем удаление пакетами по {batch_size} записей...')
        
        while True:
            # Получаем ID записей для удаления
            ids_to_delete = list(
                RatingHistory.objects.filter(created_at__lt=cutoff_date)
                .values_list('id', flat=True)[:batch_size]
            )
            
            if not ids_to_delete:
                break
                
            # Удаляем пакет
            deleted_count, _ = RatingHistory.objects.filter(id__in=ids_to_delete).delete()
            deleted_total += deleted_count
            
            # Показываем прогресс
            progress = (deleted_total / count_to_delete) * 100
            self.stdout.write(
                f'Удалено {deleted_total}/{count_to_delete} записей ({progress:.1f}%)',
                ending='\r'
            )
            
            # Небольшая пауза между пакетами
            time.sleep(0.1)
        
        elapsed_time = time.time() - start_time
        
        self.stdout.write('')  # Новая строка после прогресса
        self.stdout.write(
            self.style.SUCCESS(
                f'Очистка завершена: удалено {deleted_total} записей за {elapsed_time:.2f} секунд'
            )
        )
        
        # Показываем статистику БД после очистки
        remaining_count = RatingHistory.objects.count()
        self.stdout.write(
            self.style.SUCCESS(f'Осталось записей в истории рейтинга: {remaining_count}')
        )
