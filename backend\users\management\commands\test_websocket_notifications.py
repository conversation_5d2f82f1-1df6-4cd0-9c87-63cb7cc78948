from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from users.services import NotificationService
import time

User = get_user_model()

class Command(BaseCommand):
    help = 'Test WebSocket notifications by creating test data'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Username to test with')

    def handle(self, *args, **options):
        username = options['username']
        
        if not username:
            self.stdout.write(self.style.ERROR('Please provide --username'))
            return
        
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'User {username} not found'))
            return
        
        self.stdout.write(f'Testing WebSocket notifications for user: {user.username}')
        
        # Тестируем обновление уведомлений
        self.stdout.write('Updating unread messages count...')
        NotificationService.update_unread_messages_count(user)
        
        self.stdout.write('Updating feed events count...')
        NotificationService.update_feed_events_count(user)
        
        self.stdout.write('Updating friend requests count...')
        NotificationService.update_friend_requests_count(user)
        
        # Получаем текущие уведомления
        notifications = NotificationService.get_user_notifications(user)
        
        self.stdout.write(self.style.SUCCESS('Current notifications:'))
        for notification_type, data in notifications.items():
            self.stdout.write(f'  {notification_type}: {data}')
        
        self.stdout.write(self.style.SUCCESS('Test completed! Check WebSocket connection in browser.')) 