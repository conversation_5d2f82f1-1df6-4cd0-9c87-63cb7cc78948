from django.contrib.auth import authenticate, login, logout
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated, AllowAny, IsAuthenticatedOrReadOnly
from django.shortcuts import get_object_or_404
from .models import User, Subscription, FriendRequest, FeedEvent, Dialog, Message, DialogUserState, UserHeaderImage, UserAvatar, user_avatar_path, user_avatar_thumb_path, UserNotification
from django.utils import timezone
import boto3
from .bio_models import UserBioImage, user_bio_image_path
from .serializers import UserSerializer, UserCreateSerializer, UserUpdateSerializer, DialogSerializer, MessageSerializer, UserHeaderImageSerializer, UserAvatarSerializer
from django.db.models import Count, Q, Max
from django.views.decorators.csrf import ensure_csrf_cookie
from django.http import JsonResponse
from django.middleware.csrf import get_token
from django.conf import settings
import os
from rest_framework.parsers import <PERSON>Part<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONParser
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from PIL import Image
import io
from django.utils import timezone
from datetime import timedelta
from rest_framework.decorators import api_view, permission_classes
from .utils import create_feed_event, create_avatar_thumbnail
from rest_framework.pagination import PageNumberPagination


class StandardResultsSetPagination(PageNumberPagination):
    """Стандартная пагинация для результатов."""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100
from django.core.cache import cache
import shutil
from uuid import uuid4
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from users.storage_backends import PublicMediaStorage
from django.contrib.auth.models import AnonymousUser
from .services import NotificationService
from django.db import transaction
import logging
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from rest_framework.generics import RetrieveAPIView, ListAPIView
from .rating_service import RatingService
from .serializers import (
    UserStatsSerializer, UserMetricHistorySerializer, 
    RatingCalculationRuleSerializer, LeaderboardItemSerializer
)
from .tasks import recalculate_user_rating

logger = logging.getLogger(__name__)

class LoginView(APIView):
    permission_classes = [AllowAny]
    def post(self, request):
        username = request.data.get('username')
        email = request.data.get('email')
        password = request.data.get('password')
        
        # Проверяем что переданы необходимые поля
        if not password:
            return Response({'detail': 'Пароль обязателен'}, status=status.HTTP_400_BAD_REQUEST)
        
        if not username and not email:
            return Response({'detail': 'Необходим логин или email'}, status=status.HTTP_400_BAD_REQUEST)
        
        user = None
        
        # Если передан email, ищем пользователя по email
        if email:
            try:
                user_obj = User.objects.get(email=email)
                username = user_obj.username
            except User.DoesNotExist:
                return Response({'detail': 'Неверный email или пароль'}, status=status.HTTP_401_UNAUTHORIZED)
        
        # Аутентификация по username (либо оригинальному, либо найденному по email)
        if username:
            user = authenticate(request, username=username, password=password)
        
        if user is not None:
            login(request, user)
            return Response({'success': True, 'user': UserSerializer(user).data})
        
        return Response({'detail': 'Неверный логин/email или пароль'}, status=status.HTTP_401_UNAUTHORIZED)

class UserListView(APIView):
    def get(self, request):
        users = User.objects.annotate(
            books_published=Count('books', filter=Q(books__is_published=True)),
            reviews_written=Count('reviews'),
            books_read=Count('reading_sessions', filter=Q(reading_sessions__is_finished=True)),
            followers_count=Count('subscribers'),
            following_count=Count('subscriptions')
        ).order_by('-author_rating', '-reader_rating')
        serializer = UserSerializer(users, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = UserCreateSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class UserDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        user = get_object_or_404(User, pk=pk)
        serializer = UserSerializer(user)
        return Response(serializer.data)

    def put(self, request, pk):
        user = get_object_or_404(User, pk=pk)
        if user != request.user:
            return Response({'message': 'Нет прав на редактирование'}, status=status.HTTP_403_FORBIDDEN)
        
        serializer = UserUpdateSerializer(user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class UserProfileView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data)

    def put(self, request):
        serializer = UserUpdateSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request):
        print("PATCH request data:", request.data)  # Логируем входящие данные
        serializer = UserUpdateSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            # После сохранения возвращаем полные данные пользователя
            return Response(UserSerializer(request.user).data)
        print("Serializer errors:", serializer.errors)  # Логируем ошибки сериализатора
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class CheckAuthView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        if request.user.is_authenticated:
            return Response({
                'is_authenticated': True,
                'user': UserSerializer(request.user).data
            })
        return Response({
            'is_authenticated': False,
            'user': None
        })

class LogoutView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        # Получаем текущего пользователя до выхода
        user = request.user
        
        # Выполняем выход
        logout(request)
        
        # Очищаем сессию
        request.session.flush()
        
        # Устанавливаем анонимного пользователя
        request.user = AnonymousUser()
        
        # Создаем новый CSRF токен
        get_token(request)
        
        response = Response({'detail': 'Successfully logged out'})
        
        # Очищаем все куки сессии
        response.delete_cookie('sessionid')
        response.delete_cookie('csrftoken')
        
        return response

@ensure_csrf_cookie
def get_csrf_token(request):
    token = get_token(request)
    response = JsonResponse({'detail': 'CSRF cookie set'})
    response.set_cookie('csrftoken', token, samesite='Lax', secure=False)
    return response

class CheckUsernameView(APIView):
    permission_classes = [AllowAny]
    
    # Список запрещенных слов в логинах
    FORBIDDEN_WORDS = [
        'admin', 'administrator', 'moderator', 'login', 'boss', 'director', 'litportal'
    ]
    
    def _check_forbidden_username(self, username):
        """Проверяет содержит ли логин запрещенные слова"""
        lower_username = username.lower()
        return any(word in lower_username for word in self.FORBIDDEN_WORDS)
    
    def get(self, request):
        username = request.GET.get('username', '')
        
        # Проверяем запрещенные слова
        if self._check_forbidden_username(username):
            return Response({
                'exists': True,  # Считаем что логин "занят" если содержит запрещенные слова
                'forbidden': True,
                'message': 'Данный логин содержит запрещенные слова'
            })
        
        # Проверяем существование в базе
        exists = User.objects.filter(username=username).exists()
        return Response({
            'exists': exists,
            'forbidden': False
        })

class RegisterView(APIView):
    permission_classes = [AllowAny]
    
    # Список запрещенных слов в логинах (дублируем для консистентности)
    FORBIDDEN_WORDS = [
        'admin', 'administrator', 'moderator', 'login', 'boss', 'director', 'litportal'
    ]
    
    def _check_forbidden_username(self, username):
        """Проверяет содержит ли логин запрещенные слова"""
        lower_username = username.lower()
        return any(word in lower_username for word in self.FORBIDDEN_WORDS)
    
    def post(self, request):
        try:
            data = request.data
            username = data.get('username')
            password = data.get('password')
            email = data.get('email')
            gender = data.get('gender', '')  # По умолчанию неопределённый
            
            if not all([username, password, email]):
                return Response(
                    {'error': 'All fields are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Проверяем запрещенные слова в логине
            if self._check_forbidden_username(username):
                return Response(
                    {'username': ['Данный логин содержит запрещенные слова']},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if User.objects.filter(username=username).exists():
                return Response(
                    {'error': 'Username already exists'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if User.objects.filter(email=email).exists():
                return Response(
                    {'error': 'Email already exists'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                gender=gender,
                display_name=data.get('display_name', 'Пользователь')
            )
            
            # Устанавливаем системный аватар при создании
            try:
                user.update_system_avatar()
                user.save()
                user.update_avatar_urls()  # Добавляем обновление кэшированных URL
            except Exception as avatar_error:
                logger.error(f"Ошибка при обновлении аватара: {avatar_error}")
                # Продолжаем выполнение, аватар не критичен для регистрации
            
            # НЕ входим автоматически, так как фронтенд все равно делает logout
            # и перенаправляет на страницу входа
            # login(request, user)
            
            return Response({
                'message': 'User registered successfully',
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'gender': user.gender,
                    'display_name': user.display_name,
                    'avatar_url': getattr(user, 'avatar_url_cached', None),
                    'avatar_thumbnail_url': getattr(user, 'avatar_thumbnail_url_cached', None)
                }
            })
        except Exception as e:
            import traceback
            logger.error(f"Ошибка при регистрации пользователя: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class PublicUserView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, username):
        try:
            user = User.objects.get(username=username)

            # Проверяем, удален ли аккаунт
            if user.is_deleted:
                return Response({
                    'is_deleted': True,
                    'username': username,
                    'display_name': 'Аккаунт удален',
                    'message': 'Этот аккаунт был удален пользователем.',
                    'deleted_at': user.deleted_at
                }, status=status.HTTP_200_OK)

            serializer = UserSerializer(user)
            data = serializer.data
            
            # Добавляем информацию о статусе активности
            now = timezone.now()
            last_activity = user.last_activity
            
            if last_activity:
                time_diff = now - last_activity
                
                # Если активность была менее 2 минут назад, считаем пользователя онлайн
                if time_diff < timedelta(minutes=2):
                    data['is_online'] = True
                    data['status'] = 'В сети'
                else:
                    data['is_online'] = False
                    
                    # Формируем текст статуса в зависимости от пола
                    if user.gender == 'M':
                        prefix = 'Был'
                    elif user.gender == 'F':
                        prefix = 'Была'
                    else:
                        prefix = 'Был(а)'
                    
                    # Форматируем время
                    if time_diff < timedelta(hours=1):
                        minutes = int(time_diff.total_seconds() / 60)
                        data['status'] = f'{prefix} {minutes} мин. назад'
                    elif time_diff < timedelta(days=1):
                        hours = int(time_diff.total_seconds() / 3600)
                        data['status'] = f'{prefix} {hours} ч. назад'
                    else:
                        days = int(time_diff.total_seconds() / 86400)
                        data['status'] = f'{prefix} {days} д. назад'
            else:
                data['is_online'] = False
                data['status'] = 'Статус неизвестен'
            
            return Response(data)
        except User.DoesNotExist:
            return Response({'detail': 'Пользователь не найден'}, status=status.HTTP_404_NOT_FOUND)

def header_presets_list(request):
    folder = os.path.join(settings.BASE_DIR, 'static', 'header_presets')
    files = []
    if os.path.exists(folder):
        for fname in os.listdir(folder):
            if fname.lower().endswith(('.jpg', '.jpeg', '.png')):
                files.append(f'/static/header_presets/{fname}')
    return JsonResponse({'files': files})

class UploadHeaderImageView(APIView):
    parser_classes = (MultiPartParser, FormParser)
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        file = request.FILES.get('file')
        if not file:
            return Response({'error': 'No file provided'}, status=400)
        # Сохраняем через поле header_bg_image (использует user_header_path)
        user.header_bg_image.save(file.name, file, save=True)
        user.header_bg_type = 'image'
        user.header_bg_color1 = ''
        user.header_bg_color2 = ''
        user.save()
        rel_path = str(user.header_bg_image)
        full_url = f"https://storage.yandexcloud.net/lpo-test/media/public/{rel_path}"
        return Response({
            'header_bg_image': rel_path,
            'header_bg_image_url': full_url
        })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_avatar(request):
    try:
        user = request.user
        file = request.FILES.get('file')
        if not file:
            return Response({'error': 'Не передан файл'}, status=400)

        # Генерируем пути
        user_id = user.id
        path = user_avatar_path(user_id)
        thumb_path = user_avatar_thumb_path(user_id)

        # Сохраняем полный аватар
        storage = PublicMediaStorage()
        storage.save(path, file)

        # Создаём миниатюру
        file.seek(0)
        img = Image.open(file)
        img = img.resize((100, 100), Image.Resampling.LANCZOS)
        buf = io.BytesIO()
        img.save(buf, format='WEBP', quality=90)
        buf.seek(0)
        storage.save(thumb_path, ContentFile(buf.read()))

        # Обновляем пользователя
        user.avatar = path
        user.avatar_thumbnail = thumb_path
        user.avatar_type = 2
        user.avatar_updated_at = timezone.now()
        user.save()

        # Обновляем кэшированные URL
        user.update_avatar_urls()

        # Добавляем запись в UserAvatar
        if UserAvatar.objects.filter(user=user).count() < 2:
            UserAvatar.objects.create(user=user, path=path, thumb_path=thumb_path)

        return Response({
            'avatar': f'/media/{path}',
            'avatar_thumbnail': f'/media/{thumb_path}',
            'system_avatar': f'{settings.STATIC_URL}{user.avatar_preset}',
            'system_avatar_thumbnail': f'{settings.STATIC_URL}{user.avatar_preset_mini}'
        })
    except Exception as e:
        print('Ошибка при сохранении аватара:', str(e))
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def reset_avatar(request):
    """Сбросить аватар к системному"""
    user = request.user
    # Не удаляем пользовательские файлы, только сбрасываем поля
    user.reset_to_system_avatar()
    user.avatar_type = 1  # <--- фикс: явно выставляем тип
    user.avatar_updated_at = timezone.now()
    user.save()
    user.update_avatar_urls()  # Добавляем обновление кэшированных URL
    return Response({
        'avatar': None,
        'avatar_thumbnail': None,
        'system_avatar': f'{settings.STATIC_URL}{user.avatar_preset}',
        'system_avatar_thumbnail': f'{settings.STATIC_URL}{user.avatar_preset_mini}'
    })

def header_frames_list(request):
    folder = os.path.join(settings.BASE_DIR, 'static', 'header_frames')
    files = []
    if os.path.exists(folder):
        for fname in os.listdir(folder):
            if fname.lower().endswith('.png'):
                files.append(f'/static/header_frames/{fname}')
    return JsonResponse({'frames': files})

class UserPingView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        user.last_activity = timezone.now()
        user.save(update_fields=['last_activity'])
        return Response({'success': True, 'last_activity': user.last_activity})

class SubscribeToggleView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        username = request.data.get('username')
        if not username:
            return Response({'error': 'Не передан username'}, status=400)
        if username == request.user.username:
            return Response({'error': 'Нельзя подписаться на себя'}, status=400)
        try:
            target = User.objects.get(username=username)
        except User.DoesNotExist:
            return Response({'error': 'Пользователь не найден'}, status=404)
        # TODO: Проверка на бан
        sub, created = Subscription.objects.get_or_create(from_user=request.user, to_user=target)
        if not created:
            # Уже подписан — отписываемся
            sub.delete()
            # Событие отписки
            create_feed_event(target, 'unsubscribed', request.user, status='new')
            return Response({'subscribed': False})
        else:
            # Событие подписки
            create_feed_event(target, 'subscribed', request.user, status='new')
            return Response({'subscribed': True})

class FriendRequestView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        username = request.data.get('username')
        if not username:
            return Response({'error': 'Не передан username'}, status=400)
        if username == request.user.username:
            return Response({'error': 'Нельзя отправить запрос себе'}, status=400)
        try:
            target = User.objects.get(username=username)
        except User.DoesNotExist:
            return Response({'error': 'Пользователь не найден'}, status=404)
        # Проверка: уже друзья?
        if Subscription.objects.filter(from_user=request.user, to_user=target).exists() and Subscription.objects.filter(from_user=target, to_user=request.user).exists():
            return Response({'error': 'Вы уже друзья'}, status=400)
        # Автоматическое добавление
        if target.auto_accept_friends:
            Subscription.objects.get_or_create(from_user=request.user, to_user=target)
            Subscription.objects.get_or_create(from_user=target, to_user=request.user)
            # Создаем события для обоих пользователей
            create_feed_event(target, 'friend_accepted', request.user, status='accepted')
            create_feed_event(request.user, 'friend_added', target, status='accepted')
            return Response({'friend': True, 'auto_accepted': True})
        # Проверка: уже есть ЛЮБОЙ запрос?
        if FriendRequest.objects.filter(from_user=request.user, to_user=target).exists():
            return Response({'error': 'Заявка уже существует'}, status=400)
        friend_request = FriendRequest.objects.create(from_user=request.user, to_user=target)
        # Создаем событие для получателя запроса
        create_feed_event(target, 'friend_request', request.user, friend_request, status='new')
        return Response({'friend_request_sent': True})

    def delete(self, request):
        username = request.data.get('username')
        if not username:
            return Response({'error': 'Не передан username'}, status=400)
        try:
            target = User.objects.get(username=username)
        except User.DoesNotExist:
            return Response({'error': 'Пользователь не найден'}, status=404)
        try:
            friend_request = FriendRequest.objects.get(from_user=request.user, to_user=target, accepted=False)
            friend_request.delete()
            # Меняем статус события на cancelled
            FeedEvent.objects.filter(user=target, event_type='friend_request', actor=request.user, status='new').update(status='cancelled')
            return Response({'cancelled': True})
        except FriendRequest.DoesNotExist:
            return Response({'error': 'Запрос не найден'}, status=404)

class FriendRequestAcceptView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        username = request.data.get('username')
        if not username:
            return Response({'error': 'Не передан username'}, status=400)
        try:
            from_user = User.objects.get(username=username)
        except User.DoesNotExist:
            return Response({'error': 'Пользователь не найден'}, status=404)
        try:
            fr = FriendRequest.objects.get(from_user=from_user, to_user=request.user, accepted=False)
        except FriendRequest.DoesNotExist:
            return Response({'error': 'Запрос не найден'}, status=404)
        # Создаем взаимные подписки
        Subscription.objects.get_or_create(from_user=request.user, to_user=from_user)
        Subscription.objects.get_or_create(from_user=from_user, to_user=request.user)
        fr.delete()
        # Меняем статус события на accepted
        FeedEvent.objects.filter(user=request.user, event_type='friend_request', actor=from_user, status='new').update(status='accepted')
        # Создаем события для обоих пользователей
        create_feed_event(from_user, 'friend_accepted', request.user, status='accepted')
        create_feed_event(request.user, 'friend_accepted', from_user, status='accepted')
        return Response({'friend': True})

class FriendRequestDeclineView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        username = request.data.get('username')
        if not username:
            return Response({'error': 'Не передан username'}, status=400)
        try:
            from_user = User.objects.get(username=username)
        except User.DoesNotExist:
            return Response({'error': 'Пользователь не найден'}, status=404)
        try:
            fr = FriendRequest.objects.get(from_user=from_user, to_user=request.user, accepted=False)
        except FriendRequest.DoesNotExist:
            return Response({'error': 'Запрос не найден'}, status=404)
        fr.delete()
        # Меняем статус события на declined
        FeedEvent.objects.filter(user=request.user, event_type='friend_request', actor=from_user, status='new').update(status='declined')
        return Response({'declined': True})

class RemoveFriendView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        username = request.data.get('username')
        if not username:
            return Response({'error': 'Не передан username'}, status=400)
        if username == request.user.username:
            return Response({'error': 'Нельзя удалить себя'}, status=400)
        try:
            target = User.objects.get(username=username)
        except User.DoesNotExist:
            return Response({'error': 'Пользователь не найден'}, status=404)
        # Удаляем взаимные подписки (дружбу)
        Subscription.objects.filter(from_user=request.user, to_user=target).delete()
        # Если у target включено auto_remove_on_unfriend, он отписывается от request.user
        if target.auto_remove_on_unfriend:
            Subscription.objects.filter(from_user=target, to_user=request.user).delete()
        else:
            # Если auto_remove_on_unfriend выключено, сохраняем подписку target на request.user
            pass
        # Событие удаления из друзей
        create_feed_event(target, 'removed_from_friends', request.user, status='new')
        return Response({'removed': True})

class UserStatsView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, username):
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            return Response({'error': 'Пользователь не найден'}, status=404)
        
        # Друзья (взаимные подписки)
        friends = User.objects.filter(
            user_subscribers__from_user=user,
            user_subscriptions__to_user=user
        ).distinct()
        friends_count = friends.count()
        
        # Подписчики (кто подписан НА меня, но НЕ является другом)
        subscribers_count = user.user_subscribers.exclude(
            from_user__in=friends
        ).count()
        
        # Подписки (на кого я подписан, но кто НЕ является другом) 
        subscriptions_count = user.user_subscriptions.exclude(
            to_user__in=friends
        ).count()
        
        data = {
            'subscribers_count': subscribers_count,
            'friends_count': friends_count
        }
        
        return Response(data)

class FriendRequestsInboxView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Входящие запросы (непринятые)
        requests = FriendRequest.objects.filter(to_user=request.user, accepted=False)
        data = [
            {
                'from_username': fr.from_user.username,
                'from_display_name': fr.from_user.display_name,
                'created': fr.created
            }
            for fr in requests
        ]
        return Response({'requests': data})

class UserRelationStatusView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, username):
        if username == request.user.username:
            return Response({'self': True})
        try:
            target = User.objects.get(username=username)
        except User.DoesNotExist:
            return Response({'error': 'Пользователь не найден'}, status=404)
        is_subscribed = Subscription.objects.filter(from_user=request.user, to_user=target).exists()
        is_friend = (
            Subscription.objects.filter(from_user=request.user, to_user=target).exists() and
            Subscription.objects.filter(from_user=target, to_user=request.user).exists()
        )
        outgoing_request = FriendRequest.objects.filter(from_user=request.user, to_user=target, accepted=False).exists()
        incoming_request = FriendRequest.objects.filter(from_user=target, to_user=request.user, accepted=False).exists()
        return Response({
            'is_subscribed': is_subscribed,
            'is_friend': is_friend,
            'outgoing_friend_request': outgoing_request,
            'incoming_friend_request': incoming_request
        })

# --- Новый API для списков друзей, подписок, подписчиков ---
@api_view(['GET'])
@permission_classes([AllowAny])
def friends_list(request, username):
    try:
        user = User.objects.get(username=username)
    except User.DoesNotExist:
        return Response({'error': 'Пользователь не найден'}, status=404)
    # Взаимные подписки
    friends = User.objects.filter(
        user_subscribers__from_user=user,
        user_subscriptions__to_user=user
    ).distinct()
    now = timezone.now()
    def friend_info(u):
        last_activity = u.last_activity
        is_online = False
        if last_activity and (now - last_activity) < timedelta(minutes=2):
            is_online = True
        # Только если есть кастомный аватар и миниатюра — возвращаем media
        if u.avatar and u.avatar_thumbnail and u.avatar_type == 2:
            avatar_url = u.avatar_thumbnail.url
        else:
            # Для системного аватара всегда возвращаем основной файл
            preset = u.avatar_preset or u.get_system_avatar_path()
            avatar_url = preset if preset.startswith('/') else f'{settings.STATIC_URL}{preset}'
        system_avatar_thumbnail_url = UserSerializer().get_system_avatar_thumbnail_url(u)
        system_avatar_url = UserSerializer().get_system_avatar_url(u)
        return {
            'username': u.username,
            'display_name': u.display_name,
            'avatar': avatar_url,
            'avatar_thumbnail': u.avatar_thumbnail.url if u.avatar and u.avatar_thumbnail else None,
            'system_avatar_thumbnail_url': system_avatar_thumbnail_url,
            'system_avatar_url': system_avatar_url,
            'avatar_type': u.avatar_type,
            'is_online': is_online,
            'last_activity': last_activity,
            'total_rating': getattr(u, 'reader_rating', 0) + getattr(u, 'author_rating', 0),
            'reader_rating': getattr(u, 'reader_rating', 0),
            'author_rating': getattr(u, 'author_rating', 0),
            'avatar_updated_at': u.avatar_updated_at,
            'gender': u.gender,
        }
    friends_list = [friend_info(u) for u in friends]
    # Сортировка: онлайн, потом по last_activity (сначала самые недавние)
    friends_list.sort(key=lambda x: (not x['is_online'], x['last_activity'] if x['last_activity'] else timezone.make_aware(timezone.datetime.min)), reverse=False)
    return Response({'friends': friends_list})

@api_view(['GET'])
@permission_classes([AllowAny])
def subscriptions_list(request, username):
    try:
        user = User.objects.get(username=username)
    except User.DoesNotExist:
        return Response({'error': 'Пользователь не найден'}, status=404)
    
    # Друзья (взаимные подписки)
    friends = User.objects.filter(
        user_subscribers__from_user=user,
        user_subscriptions__to_user=user
    ).distinct()
    
    # На кого подписан (исключая друзей)
    subs = User.objects.filter(user_subscribers__from_user=user).exclude(pk=user.pk).exclude(pk__in=friends).distinct()
    now = timezone.now()
    def sub_info(u):
        last_activity = u.last_activity
        is_online = False
        if last_activity and (now - last_activity) < timedelta(minutes=2):
            is_online = True
        # Только если есть кастомный аватар и миниатюра — возвращаем media
        if u.avatar and u.avatar_thumbnail and u.avatar_type == 2:
            avatar_url = u.avatar_thumbnail.url
        else:
            # Для системного аватара всегда возвращаем основной файл
            preset = u.avatar_preset or u.get_system_avatar_path()
            avatar_url = preset if preset.startswith('/') else f'{settings.STATIC_URL}{preset}'
        system_avatar_thumbnail_url = UserSerializer().get_system_avatar_thumbnail_url(u)
        system_avatar_url = UserSerializer().get_system_avatar_url(u)
        return {
            'username': u.username,
            'display_name': u.display_name,
            'avatar': avatar_url,
            'avatar_thumbnail': u.avatar_thumbnail.url if u.avatar and u.avatar_thumbnail else None,
            'system_avatar_thumbnail_url': system_avatar_thumbnail_url,
            'system_avatar_url': system_avatar_url,
            'avatar_type': u.avatar_type,
            'is_online': is_online,
            'last_activity': last_activity,
            'total_rating': getattr(u, 'reader_rating', 0) + getattr(u, 'author_rating', 0),
            'reader_rating': getattr(u, 'reader_rating', 0),
            'author_rating': getattr(u, 'author_rating', 0),
            'avatar_updated_at': u.avatar_updated_at,
            'gender': u.gender,
        }
    subs_list = [sub_info(u) for u in subs]
    subs_list.sort(key=lambda x: (not x['is_online'], x['last_activity'] if x['last_activity'] else timezone.make_aware(timezone.datetime.min)), reverse=False)
    return Response({'subscriptions': subs_list})

@api_view(['GET'])
@permission_classes([AllowAny])
def subscribers_list(request, username):
    try:
        user = User.objects.get(username=username)
    except User.DoesNotExist:
        return Response({'error': 'Пользователь не найден'}, status=404)
    
    # Друзья (взаимные подписки)
    friends = User.objects.filter(
        user_subscribers__from_user=user,
        user_subscriptions__to_user=user
    ).distinct()
    
    # Кто подписан на пользователя (исключая друзей)
    subs = User.objects.filter(user_subscriptions__to_user=user).exclude(pk=user.pk).exclude(pk__in=friends).distinct()
    now = timezone.now()
    def sub_info(u):
        last_activity = u.last_activity
        is_online = False
        if last_activity and (now - last_activity) < timedelta(minutes=2):
            is_online = True
        # Только если есть кастомный аватар и миниатюра — возвращаем media
        if u.avatar and u.avatar_thumbnail and u.avatar_type == 2:
            avatar_url = u.avatar_thumbnail.url
        else:
            # Для системного аватара всегда возвращаем основной файл
            preset = u.avatar_preset or u.get_system_avatar_path()
            avatar_url = preset if preset.startswith('/') else f'{settings.STATIC_URL}{preset}'
        system_avatar_thumbnail_url = UserSerializer().get_system_avatar_thumbnail_url(u)
        system_avatar_url = UserSerializer().get_system_avatar_url(u)
        return {
            'username': u.username,
            'display_name': u.display_name,
            'avatar': avatar_url,
            'avatar_thumbnail': u.avatar_thumbnail.url if u.avatar and u.avatar_thumbnail else None,
            'system_avatar_thumbnail_url': system_avatar_thumbnail_url,
            'system_avatar_url': system_avatar_url,
            'avatar_type': u.avatar_type,
            'is_online': is_online,
            'last_activity': last_activity,
            'total_rating': getattr(u, 'reader_rating', 0) + getattr(u, 'author_rating', 0),
            'reader_rating': getattr(u, 'reader_rating', 0),
            'author_rating': getattr(u, 'author_rating', 0),
            'avatar_updated_at': u.avatar_updated_at,
            'gender': u.gender,
        }
    subs_list = [sub_info(u) for u in subs]
    subs_list.sort(key=lambda x: (not x['is_online'], x['last_activity'] if x['last_activity'] else timezone.make_aware(timezone.datetime.min)), reverse=False)
    return Response({'subscribers': subs_list})

class FeedEventsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        user = request.user
        events = FeedEvent.objects.filter(user=user)
        # Фильтрация по настройкам пользователя
        if not user.show_unsubscribes_in_feed:
            events = events.exclude(event_type='unsubscribed')
        if not user.show_removed_from_friends_in_feed:
            events = events.exclude(event_type='removed_from_friends')
        total = events.count()
        # Пагинация
        start = (page - 1) * per_page
        end = start + per_page
        events = events[start:end]
        def event_to_dict(event):
            actor = event.actor

            # Проверяем, удален ли пользователь
            if actor.is_deleted:
                avatar_thumbnail_url = actor.get_avatar_urls_for_deleted()['avatar_thumbnail_url']
                avatar_url = actor.get_avatar_urls_for_deleted()['avatar_url']
                system_avatar_thumbnail_url = avatar_thumbnail_url
                system_avatar_url = avatar_url
            else:
                # Миниатюра: если кастомный аватар и миниатюра — всегда её url
                if actor.avatar_type == 2 and actor.avatar_thumbnail:
                    avatar_thumbnail_url = actor.avatar_thumbnail.url
                else:
                    avatar_thumbnail_url = UserSerializer().get_system_avatar_thumbnail_url(actor)
                # Основной аватар: если кастомный — его url, иначе системный
                if actor.avatar_type == 2 and actor.avatar:
                    avatar_url = actor.avatar.url
                else:
                    avatar_url = UserSerializer().get_system_avatar_url(actor)
                system_avatar_thumbnail_url = UserSerializer().get_system_avatar_thumbnail_url(actor)
                system_avatar_url = UserSerializer().get_system_avatar_url(actor)
            return {
                'id': event.id,
                'type': event.event_type,
                'actor': {
                    'username': actor.username,
                    'display_name': actor.get_display_name(),  # Используем метод, который учитывает удаление
                    'avatar': avatar_url,
                    'avatar_thumbnail': avatar_thumbnail_url,
                    'system_avatar_thumbnail_url': system_avatar_thumbnail_url,
                    'system_avatar_url': system_avatar_url,
                    'avatar_type': actor.avatar_type,
                    'gender': actor.gender,
                    'avatar_updated_at': actor.avatar_updated_at,
                },
                'created_at': event.created_at,
                'is_read': event.is_read,
                'status': event.status,
                'content': self._get_event_content(event)
            }
        return Response({
            'events': [event_to_dict(e) for e in events],
            'total': total,
            'page': page,
            'per_page': per_page,
            'has_more': end < total
        })
    
    def _get_event_content(self, event):
        """Получает контент события в зависимости от его типа"""
        # Для событий, где нужен только текст, возвращаем сразу
        if event.event_type in ['friend_accepted', 'friend_added', 'subscribed', 'unsubscribed', 'removed_from_friends']:
            if event.event_type == 'friend_accepted':
                return {'message': 'теперь ваш друг'}
            elif event.event_type == 'friend_added':
                return {'message': 'теперь ваш друг'}
            elif event.event_type == 'subscribed':
                gender = event.actor.gender
                if gender == 'M':
                    msg = 'подписался на ваши обновления'
                elif gender == 'F':
                    msg = 'подписалась на ваши обновления'
                else:
                    msg = 'подписался(лась) на ваши обновления'
                return {'message': msg}
            elif event.event_type == 'unsubscribed':
                gender = event.actor.gender
                if gender == 'M':
                    msg = 'отписался от ваших обновлений'
                elif gender == 'F':
                    msg = 'отписалась от ваших обновлений'
                else:
                    msg = 'отписался(лась) от ваших обновлений'
                return {'message': msg}
            elif event.event_type == 'removed_from_friends':
                gender = event.actor.gender
                if gender == 'M':
                    msg = 'удалил вас из списка друзей'
                elif gender == 'F':
                    msg = 'удалила вас из списка друзей'
                else:
                    msg = 'удалил(а) вас из списка друзей'
                return {'message': msg}
        # Для остальных — старая логика
        if not event.content_object:
            return None
        if event.event_type == 'friend_request':
            return {
                'message': f'отправил(а) вам запрос в друзья'
            }
        elif event.event_type == 'new_post':
            return {
                'title': event.content_object.title,
                'preview': event.content_object.content[:200] + '...' if len(event.content_object.content) > 200 else event.content_object.content
            }
        return None

class FeedEventsCountView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # Обновляем уведомления для пользователя
        NotificationService.update_feed_events_count(request.user)
        
        # Получаем актуальное количество из уведомлений
        try:
            notification = UserNotification.objects.get(
                user=request.user,
                notification_type='feed_events'
            )
            count = notification.data.get('count', 0)
        except UserNotification.DoesNotExist:
            count = 0
        
        return Response({'count': count})

class MarkFeedEventReadView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        event_id = request.data.get('event_id')
        if event_id:
            # Отметить конкретное событие как прочитанное
            try:
                with transaction.atomic():
                    event = FeedEvent.objects.select_for_update().get(id=event_id, user=request.user)
                    if not event.is_read:  # Проверяем, не помечено ли уже как прочитанное
                        event.is_read = True
                        event.save()  # Это вызовет сигнал
                        # Обновляем уведомления после изменения событий
                        NotificationService.update_feed_events_count(request.user)
            except FeedEvent.DoesNotExist:
                return Response({'error': 'Event not found'}, status=404)
        else:
            # Отметить все события как прочитанные
            with transaction.atomic():
                events = FeedEvent.objects.select_for_update().filter(
                    user=request.user, 
                    is_read=False
                )
                # Используем bulk_update для эффективности
                updated_events = []
                for event in events:
                    event.is_read = True
                    updated_events.append(event)
                
                if updated_events:
                    FeedEvent.objects.bulk_update(updated_events, ['is_read'])
                    # Обновляем уведомления после изменения событий
                    NotificationService.update_feed_events_count(request.user)
        
        return Response({'success': True})

@api_view(['GET', 'PATCH'])
@permission_classes([IsAuthenticated])
def feed_settings(request):
    user = request.user
    if request.method == 'GET':
        return Response({
            'show_unsubscribes_in_feed': user.show_unsubscribes_in_feed,
            'show_removed_from_friends_in_feed': user.show_removed_from_friends_in_feed,
        })
    elif request.method == 'PATCH':
        show_unsubscribes = request.data.get('show_unsubscribes_in_feed')
        show_removed = request.data.get('show_removed_from_friends_in_feed')
        if show_unsubscribes is not None:
            user.show_unsubscribes_in_feed = bool(show_unsubscribes)
        if show_removed is not None:
            user.show_removed_from_friends_in_feed = bool(show_removed)
        user.save(update_fields=['show_unsubscribes_in_feed', 'show_removed_from_friends_in_feed'])
        return Response({
            'show_unsubscribes_in_feed': user.show_unsubscribes_in_feed,
            'show_removed_from_friends_in_feed': user.show_removed_from_friends_in_feed,
        })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def change_gender(request):
    """Change user's gender"""
    try:
        new_gender = request.data.get('gender')
        if new_gender not in ['male', 'female']:
            return Response(
                {'error': 'Invalid gender value. Must be "male" or "female"'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        user = request.user
        user.gender = new_gender
        # Обновляем системный аватар при смене пола
        user.update_system_avatar()
        user.save()
        user.update_avatar_urls()  # Добавляем обновление кэшированных URL
        
        return Response({
            'message': 'Gender updated successfully',
            'gender': user.gender,
            'system_avatar': user.system_avatar,
            'system_avatar_thumbnail': user.system_avatar_thumbnail
        })
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
def system_avatars_list(request):
    """Возвращает список системных аватаров по полу"""
    gender = request.GET.get('gender', '').upper()
    # Поддерживаем '', 'M', 'F'
    if gender not in ('M', 'F', ''):
        gender = ''
    # Список пресетов (можно расширить при необходимости)
    presets = {
        'M': [
            {
                'url': f'{settings.STATIC_URL}ava_presets/ava_m.webp',
                'thumbnail': f'{settings.STATIC_URL}ava_presets/ava_m_s.webp',
            }
        ],
        'F': [
            {
                'url': f'{settings.STATIC_URL}ava_presets/ava_f.webp',
                'thumbnail': f'{settings.STATIC_URL}ava_presets/ava_f_s.webp',
            }
        ],
        '': [
            {
                'url': f'{settings.STATIC_URL}ava_presets/ava_n.webp',
                'thumbnail': f'{settings.STATIC_URL}ava_presets/ava_n_s.webp',
            }
        ]
    }
    return Response(presets.get(gender, presets[''])) 

class DialogListView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        if not request.user.is_authenticated:
            return Response(
                {'detail': 'Authentication credentials were not provided.'},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        # Получаем все диалоги пользователя
        user_dialogs = Dialog.objects.filter(participants=request.user)
        
        # Получаем ID диалогов, которые не удалены для текущего пользователя
        not_deleted_dialog_ids = DialogUserState.objects.filter(
            user=request.user,
            is_deleted=False
        ).values_list('dialog_id', flat=True)
        
        # Фильтруем диалоги: только те, что не удалены для текущего пользователя
        dialogs = user_dialogs.filter(
            id__in=not_deleted_dialog_ids
        ).order_by('-updated_at')
        
        paginator = PageNumberPagination()
        paginator.page_size = 6
        result_page = paginator.paginate_queryset(dialogs, request)
        serializer = DialogSerializer(result_page, many=True, context={'request': request})
        return paginator.get_paginated_response(serializer.data)

    def post(self, request):
        search_query = request.data.get('username')
        if not search_query or search_query == request.user.username:
            return Response({'error': 'Invalid username'}, status=400)
        
        # Ищем пользователя по логину или отображаемому имени
        try:
            # Сначала пробуем найти точное совпадение по username
            other = User.objects.filter(
                Q(username=search_query) | Q(display_name=search_query)
            ).exclude(id=request.user.id).first()
            
            # Если не нашли точное совпадение, ищем частичное
            if not other:
                other = User.objects.filter(
                    Q(username__icontains=search_query) | Q(display_name__icontains=search_query)
                ).exclude(id=request.user.id).first()
                
            if not other:
                return Response({'error': 'Пользователь не найден'}, status=404)
        except Exception as e:
            return Response({'error': f'Ошибка при поиске пользователя: {str(e)}'}, status=400)
        # Найти диалог между двумя пользователями (без учёта порядка)
        dialog = Dialog.objects.filter(participants=request.user).filter(participants=other).first()
        if dialog:
            # Проверяем, не удалён ли он для всех
            states = DialogUserState.objects.filter(dialog=dialog)
            if states.count() == 2 and all(s.is_deleted for s in states):
                dialog = None  # Считаем, что диалога нет
        if not dialog:
            dialog = Dialog.objects.create()
            dialog.participants.add(request.user, other)
            # Создаем записи в DialogUserState для обоих пользователей
            DialogUserState.objects.create(dialog=dialog, user=request.user)
            DialogUserState.objects.create(dialog=dialog, user=other)
            dialog.save()
        serializer = DialogSerializer(dialog, context={'request': request})
        return Response(serializer.data, status=201)

class MessageListView(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser, JSONParser)

    def get(self, request, dialog_id):
        logger.info(f"GET_MESSAGES: MessageListView.get called:")
        logger.info(f"   Dialog ID: {dialog_id}")
        logger.info(f"   User: {request.user.username} (ID: {request.user.id})")
        
        dialog = get_object_or_404(Dialog, id=dialog_id, participants=request.user)
        messages = dialog.messages.order_by('created_at')
        
        logger.info(f"   Total messages in dialog: {messages.count()}")
        
        # Проверяем состояние диалога для пользователя
        try:
            user_state = DialogUserState.objects.get(dialog=dialog, user=request.user)
            logger.info(f"   User dialog state: is_deleted={user_state.is_deleted}, is_left={user_state.is_left}")
        except DialogUserState.DoesNotExist:
            logger.info(f"   User dialog state: NOT FOUND")
        
        # Параметры пагинации
        page_size = int(request.GET.get('page_size', 15))  # Количество сообщений на страницу
        cursor = request.GET.get('cursor')  # ID сообщения, от которого загружать предыдущие
        
        if cursor:
            # Загружаем сообщения до указанного курсора (более старые)
            try:
                cursor_message = messages.get(id=cursor)
                messages = messages.filter(created_at__lt=cursor_message.created_at).order_by('-created_at')[:page_size]
                # Сортируем обратно в хронологическом порядке
                messages = list(messages)
                messages.reverse()
            except Exception as e:
                logger.error(f"   Error with cursor {cursor}: {e}")
                # Если курсор неверный, возвращаем последние сообщения
                messages = messages.order_by('-created_at')[:page_size]
                messages = list(messages)
                messages.reverse()
        else:
            # Загружаем последние сообщения (первая загрузка)
            messages = messages.order_by('-created_at')[:page_size]
            messages = list(messages)
            messages.reverse()
        
        serializer = MessageSerializer(messages, many=True)
        serialized_data = serializer.data
        
        # Определяем есть ли еще сообщения для загрузки
        has_more = False
        next_cursor = None
        if messages:
            # Проверяем есть ли сообщения старше первого в текущем наборе
            oldest_message_date = messages[0].created_at
            older_messages_count = dialog.messages.filter(created_at__lt=oldest_message_date).count()
            has_more = older_messages_count > 0
            if has_more:
                next_cursor = str(messages[0].id)
        
        return Response({
            'count': len(serialized_data),
            'has_more': has_more,
            'next_cursor': next_cursor,
            'results': serialized_data
        })

    def post(self, request, dialog_id):
        dialog = get_object_or_404(Dialog, id=dialog_id, participants=request.user)
        # Проверяем, что диалог существует для обоих пользователей
        other_user = dialog.participants.exclude(id=request.user.id).first()
        if not other_user:
            return Response({'error': 'Dialog not found'}, status=404)
            
        # Проверяем состояние диалога для обоих пользователей
        user_state = DialogUserState.objects.get(dialog=dialog, user=request.user)
        other_user_state = DialogUserState.objects.get(dialog=dialog, user=other_user)
        
        # Если диалог был удален для обоих пользователей (режим 'all')
        if user_state.is_deleted and other_user_state.is_deleted:
            # Создаем новый диалог
            new_dialog = Dialog.objects.create()
            new_dialog.participants.add(request.user, other_user)
            # Создаем записи в DialogUserState для нового диалога
            DialogUserState.objects.create(dialog=new_dialog, user=request.user)
            DialogUserState.objects.create(dialog=new_dialog, user=other_user)
            dialog = new_dialog
        # Если диалог был удален только для текущего пользователя (режим 'self')
        elif user_state.is_deleted and not other_user_state.is_deleted:
            if user_state.is_left:
                # Создаем новый диалог
                new_dialog = Dialog.objects.create()
                new_dialog.participants.add(request.user, other_user)
                # Создаем записи в DialogUserState для нового диалога
                DialogUserState.objects.create(dialog=new_dialog, user=request.user)
                DialogUserState.objects.create(dialog=new_dialog, user=other_user)
                dialog = new_dialog
            else:
                # Сбрасываем состояние удаления
                user_state.is_deleted = False
                user_state.is_left = False
                user_state.left_at = None
                user_state.deleted_by = None
                user_state.save()
        
        # Проверяем, не удален ли один из участников диалога
        if request.user.is_deleted or other_user.is_deleted:
            return Response({
                'error': 'Невозможно отправить сообщение. Один из участников диалога удален.'
            }, status=status.HTTP_403_FORBIDDEN)

        data = request.data.copy()
        data['dialog'] = dialog.id
        data['sender'] = request.user.id
        data['recipient'] = other_user.id
        print("DEBUG: data for serializer =", data)
        serializer = MessageSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            message = serializer.save()
            dialog.last_message = message
            dialog.save(update_fields=['last_message', 'updated_at'])
            # --- ВАЖНО: сбрасываем is_deleted/is_left для всех участников, у кого is_deleted=True ---
            for state in DialogUserState.objects.filter(dialog=dialog, is_deleted=True):
                state.is_deleted = False
                state.is_left = False
                state.left_at = None
                state.save()
            
            # --- ДОБАВЛЯЕМ: Отправка нового сообщения через WebSocket ---
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync
            
            channel_layer = get_channel_layer()
            message_data = MessageSerializer(message).data
            
            # Отправляем сообщение всем участникам диалога через DialogConsumer
            async_to_sync(channel_layer.group_send)(
                f'dialog_{dialog.id}',
                {
                    'type': 'new_message',
                    'message': message_data
                }
            )
            
            return Response(message_data, status=201)
        print("DEBUG: serializer.errors =", serializer.errors)
        return Response(serializer.errors, status=400)

class UnreadMessagesCountView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        # Обновляем уведомления для пользователя
        NotificationService.update_unread_messages_count(request.user)
        
        # Получаем актуальное количество из уведомлений
        try:
            notification = UserNotification.objects.get(
                user=request.user,
                notification_type='unread_messages'
            )
            count = notification.data.get('count', 0)
        except UserNotification.DoesNotExist:
            count = 0
        
        return Response({'unread_count': count})

class MarkMessagesReadView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, dialog_id):
        print("ALERT: MarkMessagesReadView.post CALLED - OUR CODE IS RUNNING!")
        logger.info("ALERT: MarkMessagesReadView.post CALLED - OUR CODE IS RUNNING!")
        dialog = get_object_or_404(Dialog, id=dialog_id, participants=request.user)
        messages = dialog.messages.filter(recipient=request.user, is_read=False)
        
        logger.info(f"DEBUG: MarkMessagesReadView.post called:")
        logger.info(f"   Dialog ID: {dialog_id}")
        logger.info(f"   User: {request.user.username} (ID: {request.user.id})")
        logger.info(f"   Found unread messages: {messages.count()}")
        
        # ВАЖНО: сохраняем message_ids ПЕРЕД обновлением is_read=True
        message_ids = list(messages.values_list('id', flat=True))
        logger.info(f"   Message IDs: {message_ids}")
        
        # Обновляем сообщения как прочитанные
        updated_count = messages.update(is_read=True, read_at=timezone.now())

        # Обновляем счетчик непрочитанных сообщений
        from .services import NotificationService
        NotificationService.update_unread_messages_count(request.user)
 
        # Создаем или обновляем DialogUserState для текущего пользователя
        user_state, created = DialogUserState.objects.get_or_create(
            dialog=dialog,
            user=request.user,
            defaults={'is_deleted': False, 'is_left': False}
        )
        
        # Если запись уже существовала и диалог был удален, но не покинут
        if not created and user_state.is_deleted and not user_state.is_left:
            user_state.is_deleted = False
            user_state.left_at = None
            user_state.save()
            
        # Обновляем last_message в диалоге
        last_message = dialog.messages.order_by('-created_at').first()
        if last_message:
            dialog.last_message = last_message
            dialog.save(update_fields=['last_message', 'updated_at'])
        # Инвалидируем кэш для общего количества непрочитанных сообщений
        cache.delete(f'unread_messages_count_{request.user.id}')
        
        logger.info(f"TARGET: Preparing to send WebSocket notification...")
        logger.info(f"   updated_count: {updated_count}")
        logger.info(f"   message_ids: {message_ids}")
        
        # --- ДОБАВЛЯЕМ: Уведомление о прочтении сообщений через WebSocket ---
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync
        
        if message_ids:  # Используем сохраненные message_ids вместо messages.exists()
            channel_layer = get_channel_layer()
            
            logger.info(f"SENDING: WebSocket messages_read notification:")
            logger.info(f"   Dialog ID: {dialog.id}")
            logger.info(f"   Message IDs: {message_ids}")
            logger.info(f"   Read by: {request.user.id} ({request.user.username})")
            logger.info(f"   Group name: dialog_{dialog.id}")
            
            # Отправляем уведомление о прочтении всем участникам диалога
            async_to_sync(channel_layer.group_send)(
                f'dialog_{dialog.id}',
                {
                    'type': 'messages_read',
                    'message_ids': message_ids,
                    'read_by': request.user.id
                }
            )
            
            logger.info(f"SUCCESS: WebSocket messages_read notification sent")
        else:
            logger.warning(f"WARNING: No messages to mark as read - WebSocket notification not sent")
        
        return Response({'status': 'ok', 'marked': updated_count})

class MessageDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def patch(self, request, message_id):
        message = get_object_or_404(Message, id=message_id, sender=request.user)
        if message.is_deleted:
            return Response({'detail': 'Сообщение уже удалено.'}, status=400)
        # Проверяем наличие вложений через связанную модель MessageAttachment
        if message.attachments.exists():
            return Response({'detail': 'Нельзя редактировать сообщения с картинкой.'}, status=400)
        if timezone.now() - message.created_at > timedelta(hours=12):
            return Response({'detail': 'Время на редактирование истекло.'}, status=400)
        serializer = MessageSerializer(message, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=400)

    def delete(self, request, message_id):
        message = get_object_or_404(Message, id=message_id, sender=request.user)
        if message.is_deleted:
            return Response({'detail': 'Сообщение уже удалено.'}, status=400)
        if timezone.now() - message.created_at > timedelta(hours=12):
            return Response({'detail': 'Время на удаление истекло.'}, status=400)
        
        # Помечаем сообщение как удалённое
        message.is_deleted = True
        message.deleted_at = timezone.now()
        
        # Сохраняем сообщение с обновленными полями
        # Не перемещаем и не удаляем файлы физически, так как по законодательству
        # мы должны хранить переписки пользователей
        message.save(update_fields=['is_deleted', 'deleted_at'])
        
        # Файлы вложений не будут отображаться в интерфейсе, так как
        # методы get_image_url и get_image_thumbnail_url в MessageSerializer
        # возвращают None для удаленных сообщений
        
        return Response({'status': 'deleted'})

class DialogLeaveView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request, dialog_id):
        dialog = get_object_or_404(Dialog, id=dialog_id, participants=request.user)
        mode = request.data.get('mode')
        
        if mode not in ['all', 'self']:
            return Response({'error': 'Invalid mode'}, status=400)
            
        # Получаем состояние диалога для текущего пользователя
        user_state, _ = DialogUserState.objects.get_or_create(
            dialog=dialog,
            user=request.user,
            defaults={'is_deleted': False, 'is_left': False}
        )
        
        # Получаем другого участника диалога
        other_user = dialog.participants.exclude(id=request.user.id).first()
        if not other_user:
            return Response({'error': 'Dialog not found'}, status=404)
            
        # Получаем состояние диалога для другого пользователя
        other_user_state, _ = DialogUserState.objects.get_or_create(
            dialog=dialog,
            user=other_user,
            defaults={'is_deleted': False, 'is_left': False}
        )
        
        if mode == 'all':
            # Помечаем диалог как удаленный для обоих пользователей
            user_state.is_deleted = True
            user_state.is_left = True
            user_state.left_at = timezone.now()
            user_state.deleted_by = request.user
            user_state.save()
            
            other_user_state.is_deleted = True
            other_user_state.is_left = True
            other_user_state.left_at = timezone.now()
            other_user_state.deleted_by = request.user
            other_user_state.save()
            
            # Отправляем системное сообщение о том, что диалог удален
            Message.objects.create(
                dialog=dialog,
                sender=request.user,
                recipient=other_user,
                text=f"Диалог удален пользователем {request.user.display_name}",
                is_read=False,
                is_deleted=False,
                emoji_only=False,
                uuid=uuid4()
            )
            
            # Обновляем last_message в диалоге
            dialog.last_message = Message.objects.filter(dialog=dialog).order_by('-created_at').first()
            dialog.save(update_fields=['last_message', 'updated_at'])
            # --- ВАЖНО: помечаем все сообщения в этом диалоге как прочитанные ---
            Message.objects.filter(dialog=dialog, is_read=False).update(is_read=True)
            
        else:  # mode == 'self'
            # Помечаем диалог как удаленный только для текущего пользователя
            user_state.is_deleted = True
            user_state.is_left = True
            user_state.left_at = timezone.now()
            user_state.deleted_by = request.user
            user_state.save()
            
            # Отправляем системное сообщение о том, что пользователь покинул диалог
            Message.objects.create(
                dialog=dialog,
                sender=request.user,
                recipient=other_user,
                text=f"Пользователь {request.user.display_name} покинул диалог",
                is_read=False,
                is_deleted=False,
                emoji_only=False,
                uuid=uuid4()
            )
            
            # Обновляем last_message в диалоге
            dialog.last_message = Message.objects.filter(dialog=dialog).order_by('-created_at').first()
            dialog.save(update_fields=['last_message', 'updated_at'])
        
        return Response({'status': 'success'})

class UserHeaderImageListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        print('UserHeaderImageListCreateView.get: user =', request.user)
        print('UserHeaderImageListCreateView.get: user.id =', getattr(request.user, 'id', None))
        print('UserHeaderImageListCreateView.get: user.username =', getattr(request.user, 'username', None))
        print('UserHeaderImageListCreateView.get: user.is_authenticated =', getattr(request.user, 'is_authenticated', None))
        if not request.user.is_authenticated:
            return Response({'detail': 'Требуется аутентификация'}, status=status.HTTP_401_UNAUTHORIZED)
        try:
            print(f"Получение хедеров для пользователя: {request.user.username}")
            headers = UserHeaderImage.objects.filter(user=request.user).order_by('-created_at')
            serialized_data = UserHeaderImageSerializer(headers, many=True).data
            print(f"Найдено хедеров: {len(serialized_data)}")
            return Response(serialized_data)
        except Exception as e:
            print(f"Ошибка при получении хедеров: {str(e)}")
            return Response({'detail': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        if not request.user.is_authenticated:
            return Response({'detail': 'Требуется аутентификация'}, status=status.HTTP_401_UNAUTHORIZED)
        try:
            if UserHeaderImage.objects.filter(user=request.user).count() >= 4:
                return Response({'error': 'Максимум 4 пользовательских хедера'}, status=400)
            # Ожидаем, что файл уже загружен и путь передан в request.data['path']
            path = request.data.get('path')
            if not path:
                return Response({'error': 'Не передан путь'}, status=400)
            header = UserHeaderImage.objects.create(user=request.user, path=path)
            return Response(UserHeaderImageSerializer(header).data, status=201)
        except Exception as e:
            print(f"Ошибка при создании хедера: {str(e)}")
            return Response({'detail': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class UserHeaderImageDeleteView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, pk):
        try:
            header = UserHeaderImage.objects.get(pk=pk, user=request.user)
        except UserHeaderImage.DoesNotExist:
            return Response({'error': 'Не найдено'}, status=404)
        # Удаляем файл с S3 через PublicMediaStorage
        storage = PublicMediaStorage()
        print(f"[UserHeaderImageDeleteView] Попытка удалить: {header.path} (storage location: {storage.location})")
        if storage.exists(header.path):
            print("[UserHeaderImageDeleteView] Файл найден, удаляю...")
            storage.delete(header.path)
        else:
            print("[UserHeaderImageDeleteView] Файл не найден в storage.exists()")
        header.delete()
        return Response({'success': True})

class UserAvatarListCreateView(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    def get(self, request):
        print('UserAvatarListCreateView.get: user =', request.user)
        print('UserAvatarListCreateView.get: user.id =', getattr(request.user, 'id', None))
        print('UserAvatarListCreateView.get: user.username =', getattr(request.user, 'username', None))
        print('UserAvatarListCreateView.get: user.is_authenticated =', getattr(request.user, 'is_authenticated', None))
        avatars = UserAvatar.objects.filter(user=request.user).order_by('-created_at')
        return Response(UserAvatarSerializer(avatars, many=True).data)

    def post(self, request):
        if UserAvatar.objects.filter(user=request.user).count() >= 2:
            return Response({'error': 'Максимум 2 пользовательских аватара'}, status=400)
        file = request.FILES.get('file')
        if not file:
            return Response({'error': 'Не передан файл'}, status=400)
        # Генерируем пути
        user_id = request.user.id
        path = user_avatar_path(user_id)
        thumb_path = user_avatar_thumb_path(user_id)
        # Сохраняем полный аватар
        storage = PublicMediaStorage()
        storage.save(path, file)
        # Создаём миниатюру
        file.seek(0)
        img = Image.open(file)
        img = img.resize((100, 100), Image.Resampling.LANCZOS)
        buf = io.BytesIO()
        img.save(buf, format='WEBP', quality=90)
        buf.seek(0)
        storage.save(thumb_path, ContentFile(buf.read()))
        # Создаём запись
        avatar = UserAvatar.objects.create(user=request.user, path=path, thumb_path=thumb_path)
        return Response(UserAvatarSerializer(avatar).data, status=201)

class UserAvatarDeleteView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, pk):
        try:
            avatar = UserAvatar.objects.get(pk=pk, user=request.user)
        except UserAvatar.DoesNotExist:
            return Response({'error': 'Не найдено'}, status=404)
        
        storage = PublicMediaStorage()
        files_deleted = True
        
        # Удаляем файлы из S3
        if storage.exists(avatar.path):
            try:
                storage.delete(avatar.path)
            except Exception as e:
                print(f"Ошибка при удалении файла {avatar.path}: {str(e)}")
                files_deleted = False
        
        if storage.exists(avatar.thumb_path):
            try:
                storage.delete(avatar.thumb_path)
            except Exception as e:
                print(f"Ошибка при удалении файла {avatar.thumb_path}: {str(e)}")
                files_deleted = False
        
        # Удаляем запись из БД
        avatar.delete()
        
        # Если удаляемый аватар был активным, применяем логику
        user = request.user
        was_active = (str(user.avatar) == avatar.path)
        
        response_data = {
            'status': 'success',
            'message': 'Аватар успешно удален',
            'data': {
                'was_active': was_active,
                'files_deleted': files_deleted
            }
        }
        
        if was_active:
            remaining = UserAvatar.objects.filter(user=user).order_by('-created_at')
            if remaining.exists():
                new_ava = remaining.first()
                # Проверяем, что файлы нового аватара существуют
                if storage.exists(new_ava.path) and storage.exists(new_ava.thumb_path):
                    user.avatar = new_ava.path
                    user.avatar_thumbnail = new_ava.thumb_path
                    user.avatar_type = 2
                else:
                    # Если файлы не найдены, сбрасываем на системный
                    user.reset_to_system_avatar()
                    user.avatar_type = 1
            else:
                user.reset_to_system_avatar()
                user.avatar_type = 1
            
            user.avatar_updated_at = timezone.now()
            user.update_avatar_urls()  # Обновляем кэшированные URL
            user.save(update_fields=['avatar', 'avatar_thumbnail', 'avatar_type', 'avatar_updated_at'])
            
            # Добавляем информацию о текущем аватаре в ответ
            response_data['data'].update({
                'current_avatar': {
                    'type': user.avatar_type,
                    'url': user.avatar_url_cached,
                    'thumbnail_url': user.avatar_thumbnail_url_cached,
                    'system_avatar': f'{settings.STATIC_URL}{user.avatar_preset}',
                    'system_avatar_thumbnail': f'{settings.STATIC_URL}{user.avatar_preset_mini}'
                }
            })
        else:
            # Даже если аватар не был активным, обновляем кэшированные URL
            user.update_avatar_urls()
            user.save(update_fields=['avatar_url_cached', 'avatar_thumbnail_url_cached'])
            
            # Добавляем информацию о текущем аватаре в ответ
            response_data['data'].update({
                'current_avatar': {
                    'type': user.avatar_type,
                    'url': user.avatar_url_cached,
                    'thumbnail_url': user.avatar_thumbnail_url_cached,
                    'system_avatar': f'{settings.STATIC_URL}{user.avatar_preset}',
                    'system_avatar_thumbnail': f'{settings.STATIC_URL}{user.avatar_preset_mini}'
                }
            })
        
        return Response(response_data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_bio_image(request):
    """Загрузка изображения для bio пользователя"""
    from PIL import Image as PilImage
    import io
    from django.core.files.base import ContentFile
    
    user = request.user
    file = request.FILES.get('image')
    if not file:
        return Response({'error': 'Нет файла изображения'}, status=status.HTTP_400_BAD_REQUEST)
    
    # Проверка размера файла (5MB лимит)
    max_file_size = 5 * 1024 * 1024  # 5MB в байтах
    if file.size > max_file_size:
        return Response({'error': 'Размер файла превышает 5MB'}, status=status.HTTP_400_BAD_REQUEST)
    
    # Конвертируем все изображения в WebP с качеством 90%
    try:
        img = PilImage.open(file)
        
        # Сохраняем прозрачность для изображений с альфа-каналом
        if img.mode in ('RGBA', 'LA'):
            # Конвертируем в RGBA для сохранения прозрачности
            img = img.convert('RGBA')
        elif img.mode == 'P':
            # Проверяем, есть ли прозрачность в палитре
            if 'transparency' in img.info:
                img = img.convert('RGBA')
            else:
                img = img.convert('RGB')
        else:
            img = img.convert('RGB')
        
        max_size = 1000
        w, h = img.size
        if max(w, h) > max_size:
            if w > h:
                new_w = max_size
                new_h = int(h * max_size / w)
            else:
                new_h = max_size
                new_w = int(w * max_size / h)
            img = img.resize((new_w, new_h), PilImage.LANCZOS)
        
        buf = io.BytesIO()
        img.save(buf, format='WEBP', quality=90)
        buf.seek(0)
        file_content = buf.getvalue()
        file_extension = 'webp'
    except Exception as e:
        return Response({'error': f'Ошибка обработки изображения: {e}'}, status=status.HTTP_400_BAD_REQUEST)
    
    # Генерация пути с расширением WebP
    s3_path = user_bio_image_path(user.id).replace('.jpg', f'.{file_extension}')
    
    # Сохранение в S3
    storage = PublicMediaStorage()
    storage.save(s3_path, ContentFile(file_content))
    
    # Создание записи в БД
    width = request.data.get('width')
    height = request.data.get('height')
    
    image_obj = UserBioImage.objects.create(
        user=user,
        path=s3_path,
        width=width,
        height=height
    )
    
    # Возвращаем прямую ссылку на S3 (публичная папка)
    full_url = f"https://storage.yandexcloud.net/lpo-test/media/public/{s3_path}"
    
    return Response({
        'url': full_url,
        'id': image_obj.id,
        'path': s3_path
    })


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_bio_image(request):
    """Удаление изображения из bio пользователя"""
    user = request.user
    image_id = request.data.get('image_id')
    
    if not image_id:
        return Response({'error': 'Не передан image_id'}, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        image = UserBioImage.objects.get(id=image_id, user=user)
    except UserBioImage.DoesNotExist:
        return Response({'error': 'Изображение не найдено'}, status=status.HTTP_404_NOT_FOUND)
    
    # Удаляем файл из S3 и запись из БД
    storage = PublicMediaStorage()
    if storage.exists(image.path):
        storage.delete(image.path)
    
    image.delete()
    return Response({'ok': True})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_bio_image_links(request):
    """Получить прямые ссылки на изображения bio (публичные)"""
    paths = request.data.get('paths', [])
    result = {}
    user = request.user
    
    for path in paths:
        # Убираем префикс, если есть
        clean_path = path
        if clean_path.startswith('/media/public/'):
            clean_path = clean_path[len('/media/public/'):]
        elif clean_path.startswith('/media/'):
            clean_path = clean_path[len('/media/'):]
        else:
            clean_path = clean_path.lstrip('/')
        
        try:
            img = UserBioImage.objects.get(path=clean_path, user=user)
            # Возвращаем прямую ссылку на S3 (публичная папка)
            url = f"https://storage.yandexcloud.net/lpo-test/media/public/{clean_path}"
            result[path] = url
        except UserBioImage.DoesNotExist:
            continue
    
    return Response(result)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cleanup_bio_images(request):
    """Очистка неиспользуемых изображений в bio пользователя"""
    from bs4 import BeautifulSoup
    
    user = request.user
    bio_html = request.data.get('html_content', '') or request.data.get('bio_html', '')
    
    # Извлекаем пути изображений из HTML
    soup = BeautifulSoup(bio_html, 'html.parser')
    used_paths = set()
    
    for img in soup.find_all('img'):
        src = img.get('src', '')
        if src:
            # Извлекаем путь из URL
            if 'storage.yandexcloud.net' in src:
                # Извлекаем путь после /media/public/
                if '/media/public/' in src:
                    path = src.split('/media/public/')[-1]
                    used_paths.add(path)
            elif src.startswith('/media/public/'):
                path = src[len('/media/public/'):]
                used_paths.add(path)
            elif not src.startswith('http'):
                # Относительный путь
                used_paths.add(src.lstrip('/'))
    
    # Находим неиспользуемые изображения
    all_user_images = UserBioImage.objects.filter(user=user)
    deleted_count = 0
    
    # Отладочная информация
    db_paths = [img.path for img in all_user_images]
    print(f"DEBUG: Used paths from HTML: {used_paths}")
    print(f"DEBUG: DB paths: {db_paths}")
    
    for image in all_user_images:
        if image.path not in used_paths:
            print(f"DEBUG: Deleting image with path: {image.path}")
            # Удаляем файл из S3
            storage = PublicMediaStorage()
            if storage.exists(image.path):
                storage.delete(image.path)
            
            # Удаляем запись из БД
            image.delete()
            deleted_count += 1
        else:
            print(f"DEBUG: Keeping image with path: {image.path}")
    
    return Response({
        'deleted_count': deleted_count,
        'used_images': len(used_paths),
        'debug_used_paths': list(used_paths),
        'debug_db_paths': db_paths
    })


class UserSearchView(APIView):
    """API для поиска пользователей без создания диалога"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        search_query = request.data.get('username')
        if not search_query or search_query == request.user.username:
            return Response({'error': 'Invalid username'}, status=400)
        
        # Ищем пользователя по логину или отображаемому имени
        try:
            # Сначала пробуем найти точное совпадение по username
            users = User.objects.filter(
                Q(username=search_query) | Q(display_name=search_query)
            ).exclude(id=request.user.id)
            
            # Если не нашли точное совпадение, ищем частичное
            if not users.exists():
                users = User.objects.filter(
                    Q(username__icontains=search_query) | Q(display_name__icontains=search_query)
                ).exclude(id=request.user.id)
                
            if not users.exists():
                return Response({'error': 'Пользователь не найден'}, status=404)
                
            # Сериализуем найденных пользователей
            serializer = UserSerializer(users, many=True, context={'request': request})
            return Response(serializer.data)
            
        except Exception as e:
            return Response({'error': f'Ошибка при поиске пользователя: {str(e)}'}, status=400)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def select_avatar(request):
    """Выбрать ранее загруженный аватар"""
    try:
        avatar_id = request.data.get('avatar_id')
        if not avatar_id:
            return Response({'error': 'Не указан ID аватара'}, status=400)
        
        user = request.user
        print(f"Selecting avatar {avatar_id} for user {user.username}")
        print(f"Current state: avatar_type={user.avatar_type}, avatar={user.avatar}, avatar_thumbnail={user.avatar_thumbnail}")
        print(f"Current cached URLs: avatar_url_cached={user.avatar_url_cached}, avatar_thumbnail_url_cached={user.avatar_thumbnail_url_cached}")
        
        try:
            avatar = UserAvatar.objects.get(pk=avatar_id, user=user)
        except UserAvatar.DoesNotExist:
            return Response({'error': 'Аватар не найден'}, status=404)
        
        # Проверяем, что файлы существуют
        storage = PublicMediaStorage()
        if not storage.exists(avatar.path) or not storage.exists(avatar.thumb_path):
            return Response({'error': 'Файлы аватара не найдены'}, status=404)
        
        # Устанавливаем выбранный аватар
        user.avatar = avatar.path
        user.avatar_thumbnail = avatar.thumb_path
        user.avatar_type = 2
        user.avatar_updated_at = timezone.now()
        
        # Сначала обновляем кэшированные URL
        user.update_avatar_urls()
        
        # Затем сохраняем пользователя
        user.save()
        
        # Возвращаем актуальные данные, включая кэшированные URL
        return Response({
            'avatar': user.avatar_url_cached,
            'avatar_thumbnail': user.avatar_thumbnail_url_cached,
            'system_avatar': f'{settings.STATIC_URL}{user.avatar_preset}',
            'system_avatar_thumbnail': f'{settings.STATIC_URL}{user.avatar_preset_mini}',
            'avatar_type': user.avatar_type
        })
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

class UserBooksBlocksOrderView(APIView):
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get(self, request, username):
        user = User.objects.get(username=username)
        order = user.books_blocks_order or ['in_progress', 'finished']
        return Response({'order': order})

    def patch(self, request, username):
        user = User.objects.get(username=username)
        if request.user != user:
            return Response({'detail': 'Permission denied.'}, status=status.HTTP_403_FORBIDDEN)
        order = request.data.get('order')
        if not isinstance(order, list) or not all(x in ['in_progress', 'finished'] for x in order):
            return Response({'detail': 'Invalid order.'}, status=status.HTTP_400_BAD_REQUEST)
        user.books_blocks_order = order
        user.save(update_fields=['books_blocks_order'])
        return Response({'order': order})

class UserNotificationsView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Получить все уведомления пользователя"""
        # Обновляем все уведомления
        NotificationService.update_all_notifications(request.user)
        
        # Получаем все уведомления
        notifications = NotificationService.get_user_notifications(request.user)
        
        return Response(notifications)

class RatingUserStatsView(RetrieveAPIView):
    """
    Получение статистики пользователя.
    """
    serializer_class = UserStatsSerializer
    permission_classes = [IsAuthenticated]
    
    def get_object(self):
        user_id = self.kwargs.get('user_id')
        if user_id:
            user = get_object_or_404(User, id=user_id)
        else:
            user = self.request.user
        
        return RatingService.get_or_create_user_stats(user)


class UserMetricHistoryView(ListAPIView):
    """
    История изменений метрик пользователя.
    """
    serializer_class = UserMetricHistorySerializer
    permission_classes = [IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    
    def get_queryset(self):
        user_id = self.kwargs.get('user_id')
        if user_id:
            # Проверяем права доступа
            target_user = get_object_or_404(User, id=user_id)
            if target_user != self.request.user and not self.request.user.is_staff:
                raise PermissionDenied("Нет доступа к истории метрик другого пользователя")
            user = target_user
        else:
            user = self.request.user
            
        return UserMetricHistory.objects.filter(user=user).order_by('-created_at')


class LeaderboardView(ListAPIView):
    """
    Лидерборд пользователей по рейтингу.
    """
    serializer_class = LeaderboardItemSerializer
    permission_classes = [AllowAny]  # Лидерборд доступен всем
    pagination_class = StandardResultsSetPagination
    
    def get_queryset(self):
        rating_type = self.request.query_params.get('type', 'total')
        limit = int(self.request.query_params.get('limit', 100))
        
        # Проверяем кэш
        from django.core.cache import cache
        cache_key = f'leaderboard:{rating_type}'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            # Возвращаем данные из кэша
            return cached_data
        
        # Получаем данные из базы
        stats = RatingService.get_leaderboard(rating_type, limit)
        
        # Добавляем позицию в рейтинге
        for index, stat in enumerate(stats, 1):
            stat.position = index
        
        return stats


class RecalculateRatingView(APIView):
    """
    Запуск пересчета рейтинга пользователя.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        user_id = request.data.get('user_id')
        
        # Только сам пользователь или админ может запустить пересчет
        if user_id:
            target_user = get_object_or_404(User, id=user_id)
            if target_user != request.user and not request.user.is_staff:
                return Response(
                    {'error': 'Нет прав для пересчета рейтинга другого пользователя'},
                    status=status.HTTP_403_FORBIDDEN
                )
            user = target_user
        else:
            user = request.user
        
        # Проверяем, не запущен ли уже пересчет
        existing_task = RatingRecalculationTask.objects.filter(
            user=user,
            status__in=['pending', 'processing']
        ).first()
        
        if existing_task:
            return Response({
                'message': 'Пересчет уже запущен',
                'task_id': existing_task.id
            })
        
        # Запускаем пересчет
        if request.data.get('async', True):
            # Асинхронный пересчет
            task = RatingService.schedule_recalculation(user)
            return Response({
                'message': 'Пересчет запланирован',
                'task_id': task.id,
                'async': True
            })
        else:
            # Синхронный пересчет (только для админов)
            if not request.user.is_staff:
                return Response(
                    {'error': 'Синхронный пересчет доступен только администраторам'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            stats = RatingService.recalculate_user_stats(user)
            return Response({
                'message': 'Рейтинг пересчитан',
                'stats': UserStatsSerializer(stats).data,
                'async': False
            })


class RatingRulesView(ListAPIView):
    """
    Список правил расчета рейтингов.
    """
    serializer_class = RatingCalculationRuleSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        rating_type = self.request.query_params.get('type')
        queryset = RatingCalculationRule.objects.filter(is_active=True)
        
        if rating_type:
            queryset = queryset.filter(rating_type=rating_type)
            
        return queryset.order_by('rating_type', '-weight')


class UserRatingPositionView(APIView):
    """
    Получение позиции пользователя в рейтинге.
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, user_id=None):
        if user_id:
            user = get_object_or_404(User, id=user_id)
        else:
            user = request.user
            
        rating_type = request.query_params.get('type', 'total')
        position = RatingService.get_user_rating_position(user, rating_type)
        
        return Response({
            'user_id': user.id,
            'username': user.username,
            'rating_type': rating_type,
            'position': position
        })


class RatingStatsView(APIView):
    """
    Общая статистика по рейтингам.
    """
    permission_classes = [AllowAny]
    
    def get(self, request):
        from django.db.models import Avg, Max, Min, Count
        
        # Агрегированная статистика
        stats_aggregate = UserStats.objects.aggregate(
            total_users=Count('id'),
            avg_total_rating=Avg('total_rating'),
            max_total_rating=Max('total_rating'),
            min_total_rating=Min('total_rating'),
            avg_reader_rating=Avg('reader_rating'),
            max_reader_rating=Max('reader_rating'),
            avg_author_rating=Avg('author_rating'),
            max_author_rating=Max('author_rating'),
        )
        
        # Топ-10 пользователей
        top_users = RatingService.get_leaderboard('total', 10)
        
        return Response({
            'aggregate': stats_aggregate,
            'top_users': LeaderboardItemSerializer(top_users, many=True).data,
            'cache_updated': timezone.now().isoformat()
        })

# Добавляем в конец файла новые views для библиотеки

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.db.models import Q
from .models import UserLibrary, ReadingSession
from .serializers import UserLibrarySerializer, ReadingSessionSerializer, UserLibraryStatsSerializer

class LibraryPagination(PageNumberPagination):
    """Пагинация для библиотеки - 25 книг на страницу (5x5)"""
    page_size = 25
    page_size_query_param = 'page_size'
    max_page_size = 100

class UserLibraryViewSet(viewsets.ModelViewSet):
    """
    ViewSet для управления библиотекой пользователя
    """
    serializer_class = UserLibrarySerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = LibraryPagination
    
    def get_queryset(self):
        """Получаем только библиотеку текущего пользователя"""
        return UserLibrary.objects.filter(
            user=self.request.user
        ).select_related('book', 'book__author').prefetch_related(
            'book__genres', 'book__hashtags'
        ).order_by('-updated_at')
    
    def get_serializer_context(self):
        """Передаем контекст в сериализатор"""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context
    
    def list(self, request, *args, **kwargs):
        """
        Получение библиотеки с фильтрацией по статусу
        
        Query параметры:
        - status: reading, want_to_read, read, all (по умолчанию all)
        - search: поиск по названию книги или автору
        """
        queryset = self.get_queryset()
        
        # Фильтрация по статусу
        status_filter = request.query_params.get('status', 'all')
        if status_filter != 'all':
            queryset = queryset.filter(status=status_filter)
        
        # Поиск
        search = request.query_params.get('search', '').strip()
        if search:
            queryset = queryset.filter(
                Q(book__title__icontains=search) |
                Q(book__author__display_name__icontains=search) |
                Q(book__description__icontains=search)
            )
        
        # Пагинация
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    def create(self, request, *args, **kwargs):
        """Добавление книги в библиотеку"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
    
    def perform_create(self, serializer):
        """Устанавливаем пользователя при создании"""
        serializer.save(user=self.request.user)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Статистика библиотеки пользователя"""
        queryset = self.get_queryset()
        serializer = UserLibraryStatsSerializer(queryset)
        return Response(serializer.data)
    
    @action(detail=True, methods=['patch'])
    def update_status(self, request, pk=None):
        """Обновление статуса книги в библиотеке"""
        library_entry = self.get_object()
        new_status = request.data.get('status')
        
        if new_status not in ['reading', 'want_to_read', 'read']:
            return Response(
                {'error': 'Неверный статус'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        old_status = library_entry.status
        
        # Обновляем временные метки
        if new_status == 'reading' and old_status != 'reading':
            library_entry.mark_as_reading()
        elif new_status == 'read' and old_status != 'read':
            library_entry.mark_as_read()
        else:
            library_entry.status = new_status
            library_entry.save()
        
        # Перезагружаем объект из базы для актуальных данных
        library_entry.refresh_from_db()
        
        serializer = self.get_serializer(library_entry)
        return Response(serializer.data)
    
    @action(detail=True, methods=['patch'])
    def update_progress(self, request, pk=None):
        """Обновление прогресса чтения"""
        library_entry = self.get_object()
        progress = request.data.get('progress', 0)
        
        try:
            progress = float(progress)
            if not 0 <= progress <= 100:
                raise ValueError()
        except (ValueError, TypeError):
            return Response(
                {'error': 'Прогресс должен быть числом от 0 до 100'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        library_entry.update_progress(progress)
        serializer = self.get_serializer(library_entry)
        return Response(serializer.data)
    
    @action(detail=True, methods=['patch'])
    def add_note(self, request, pk=None):
        """Добавление/обновление личной заметки"""
        library_entry = self.get_object()
        note = request.data.get('note', '')
        
        library_entry.private_notes = note
        library_entry.save()
        
        serializer = self.get_serializer(library_entry)
        return Response(serializer.data)
    
    @action(detail=True, methods=['patch'])
    def rate_book(self, request, pk=None):
        """Оценка книги пользователем"""
        library_entry = self.get_object()
        rating = request.data.get('rating')
        
        if rating is not None:
            try:
                rating = int(rating)
                if not 1 <= rating <= 10:
                    raise ValueError()
            except (ValueError, TypeError):
                return Response(
                    {'error': 'Оценка должна быть числом от 1 до 10'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        library_entry.user_rating = rating
        library_entry.save()
        
        serializer = self.get_serializer(library_entry)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def update_activity(self, request):
        """Обновление времени последней активности с книгой (просмотр страницы/чтение)"""
        book_id = request.data.get('book_id')
        
        if not book_id:
            return Response(
                {'error': 'book_id обязателен'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            from books.models import Book
            book = Book.objects.get(id=book_id, is_published=True)
            
            # Ищем книгу в библиотеке пользователя
            try:
                library_entry = UserLibrary.objects.get(user=request.user, book=book)
                # Обновляем updated_at для поднятия книги в списке
                library_entry.save(update_fields=['updated_at'])
                
                serializer = self.get_serializer(library_entry)
                return Response({
                    'success': True,
                    'library_entry': serializer.data,
                    'message': 'Активность обновлена'
                })
                
            except UserLibrary.DoesNotExist:
                # Книги нет в библиотеке, ничего не делаем
                return Response({
                    'success': True,
                    'message': 'Книга не в библиотеке'
                })
                
        except Book.DoesNotExist:
            return Response(
                {'error': 'Книга не найдена'},
                status=status.HTTP_404_NOT_FOUND
            )


class ReadingSessionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet для просмотра сессий чтения (только чтение)
    """
    serializer_class = ReadingSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = PageNumberPagination
    
    def get_queryset(self):
        """Получаем только сессии текущего пользователя"""
        return ReadingSession.objects.filter(
            user=self.request.user
        ).select_related('book', 'chapter').order_by('-started_at')
    
    @action(detail=False, methods=['post'])
    def start_session(self, request):
        """Начать новую сессию чтения"""
        book_id = request.data.get('book_id')
        chapter_id = request.data.get('chapter_id')
        
        if not book_id:
            return Response(
                {'error': 'book_id обязателен'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            from books.models import Book, BookChapter
            book = Book.objects.get(id=book_id, is_published=True)
            
            chapter = None
            if chapter_id:
                try:
                    chapter = BookChapter.objects.get(id=chapter_id, book=book)
                except BookChapter.DoesNotExist:
                    pass
            
            # Закрываем все активные сессии пользователя
            ReadingSession.objects.filter(
                user=request.user,
                is_active=True
            ).update(is_active=False)
            
            # Создаем новую сессию
            session = ReadingSession.objects.create(
                user=request.user,
                book=book,
                chapter=chapter
            )
            
            serializer = self.get_serializer(session)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except Book.DoesNotExist:
            return Response(
                {'error': 'Книга не найдена'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['post'])
    def update_activity(self, request, pk=None):
        """Обновить активность в сессии"""
        session = self.get_object()
        
        if not session.is_active:
            return Response(
                {'error': 'Сессия уже завершена'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        session.update_duration()
        serializer = self.get_serializer(session)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def close_session(self, request, pk=None):
        """Завершить сессию чтения"""
        session = self.get_object()
        session.close_session()
        
        serializer = self.get_serializer(session)
        return Response(serializer.data)


# API функция для быстрого добавления книги в библиотеку
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def quick_add_to_library(request):
    """
    Быстрое добавление книги в библиотеку
    Используется в карточках книг для кнопок "Читаю" и "Отложить"
    """
    book_id = request.data.get('book_id')
    status_choice = request.data.get('status', 'want_to_read')  # По умолчанию "Отложить"
    
    if not book_id:
        return Response(
            {'error': 'book_id обязателен'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    if status_choice not in ['reading', 'want_to_read', 'read']:
        return Response(
            {'error': 'Неверный статус'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    try:
        from books.models import Book
        book = Book.objects.get(id=book_id, is_published=True)
        

        
        # Создаем или обновляем запись в библиотеке
        library_entry, created = UserLibrary.objects.get_or_create(
            user=request.user,
            book=book,
            defaults={'status': status_choice}
        )
        
        if not created:
            # Обновляем статус если запись уже существует
            library_entry.status = status_choice
            if status_choice == 'reading':
                library_entry.mark_as_reading()
            elif status_choice == 'read':
                library_entry.mark_as_read()
            else:
                library_entry.save()
        
        serializer = UserLibrarySerializer(library_entry, context={'request': request})
        
        return Response({
            'success': True,
            'created': created,
            'library_entry': serializer.data,
            'message': f'Книга {"добавлена" if created else "обновлена"} в библиотеке'
        }, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)
        
    except Book.DoesNotExist:
        return Response(
            {'error': 'Книга не найдена'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def check_library_status(request, book_id):
    """
    Проверка статуса конкретной книги в библиотеке пользователя
    """
    try:
        from books.models import Book
        book = Book.objects.get(id=book_id, is_published=True)
        
        try:
            library_entry = UserLibrary.objects.get(user=request.user, book=book)
            serializer = UserLibrarySerializer(library_entry, context={'request': request})
            return Response({
                'in_library': True,
                'status': library_entry.status,
                'library_entry': serializer.data
            })
        except UserLibrary.DoesNotExist:
            return Response({
                'in_library': False,
                'status': None,
                'library_entry': None
            })
            
    except Book.DoesNotExist:
        return Response(
            {'error': 'Книга не найдена'},
            status=status.HTTP_404_NOT_FOUND
        )

# OAuth Views
from allauth.socialaccount.providers.vk.views import VKOAuth2Adapter
from allauth.socialaccount.providers.yandex.views import YandexOAuth2Adapter  
from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.odnoklassniki.views import OdnoklassnikiOAuth2Adapter
from allauth.socialaccount.providers.oauth2.client import OAuth2Client
from dj_rest_auth.registration.views import SocialLoginView

class VKLoginView(SocialLoginView):
    adapter_class = VKOAuth2Adapter
    callback_url = "http://localhost:5173/"  # Base domain for VK (стандартный порт Vite)
    client_class = OAuth2Client

class YandexLoginView(SocialLoginView):
    adapter_class = YandexOAuth2Adapter
    callback_url = "http://localhost:5173/auth/callback/"
    client_class = OAuth2Client

class GoogleLoginView(SocialLoginView):
    adapter_class = GoogleOAuth2Adapter
    callback_url = "http://localhost:5173/auth/callback/"
    client_class = OAuth2Client

class OKLoginView(SocialLoginView):
    adapter_class = OdnoklassnikiOAuth2Adapter
    callback_url = "http://localhost:5173/auth/callback/"
    client_class = OAuth2Client


class DeleteAccountView(APIView):
    """API для удаления аккаунта пользователя"""
    permission_classes = [IsAuthenticated]

    def _delete_user_files_from_s3(self, user):
        """Удаляет все файлы пользователя из S3"""
        try:
            from users.models import get_group_folder
            import boto3

            user_id = user.id
            group_folder = get_group_folder(user_id)

            # Получаем S3 клиент
            s3 = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                endpoint_url=settings.AWS_S3_ENDPOINT_URL,
                region_name=settings.AWS_S3_REGION_NAME,
            )
            bucket = settings.AWS_STORAGE_BUCKET_NAME

            # Список префиксов для удаления пользовательских файлов
            prefixes = [
                f"{settings.AWS_S3_PUBLIC_MEDIA_PREFIX}/avatars/{group_folder}/{user_id}/",
                f"{settings.AWS_S3_PUBLIC_MEDIA_PREFIX}/headers/{group_folder}/{user_id}/",
                f"{settings.AWS_S3_PUBLIC_MEDIA_PREFIX}/profile_pics/{group_folder}/{user_id}/",
                # Добавляем другие папки пользователя при необходимости
            ]

            deleted_files_count = 0
            for prefix in prefixes:
                try:
                    # Список всех объектов с данным префиксом
                    response = s3.list_objects_v2(Bucket=bucket, Prefix=prefix)

                    if 'Contents' in response:
                        # Удаляем все файлы
                        objects_to_delete = [{'Key': obj['Key']} for obj in response['Contents']]
                        if objects_to_delete:
                            s3.delete_objects(
                                Bucket=bucket,
                                Delete={'Objects': objects_to_delete}
                            )
                            deleted_files_count += len(objects_to_delete)
                            logger.info(f"Deleted {len(objects_to_delete)} files from {prefix}")

                except Exception as e:
                    logger.error(f"Error deleting files from {prefix}: {e}")

            logger.info(f"Total deleted files for user {user.username}: {deleted_files_count}")

        except Exception as e:
            logger.error(f"Error deleting user files from S3 for user {user.username}: {e}")

    def post(self, request):
        confirmation = request.data.get('confirmation', '').strip().lower()

        # Проверяем подтверждение
        if confirmation != 'удалить':
            return Response({
                'error': 'Для подтверждения удаления введите слово "удалить"'
            }, status=status.HTTP_400_BAD_REQUEST)

        user = request.user

        try:
            with transaction.atomic():
                # Помечаем аккаунт как удаленный
                user.is_deleted = True
                user.deleted_at = timezone.now()

                # Устанавливаем уникальный email для удаленного аккаунта
                user.email = f'deleted_{user.id}_{int(timezone.now().timestamp())}@deleted.local'

                # Очищаем bio
                user.bio = ''

                # Устанавливаем аватар удаленного аккаунта
                user.avatar = None
                user.avatar_thumbnail = None
                user.avatar_preset = 'ava_presets/ava_del.webp'
                user.avatar_preset_mini = 'ava_presets/ava_del_s.webp'
                user.avatar_type = 1
                user.avatar_updated_at = timezone.now()

                # Сохраняем изменения пользователя
                user.save()

                # Обновляем кэшированные URL аватаров (теперь автоматически учтет is_deleted)
                user.update_avatar_urls()

                # Логируем начало процесса удаления
                logger.info(f"Starting account deletion for user {user.username} (ID: {user.id})")

                # Импортируем модели книг
                from books.models import Book, Like as BookLike, CommentLike, Review

                # Удаляем книги пользователя (каскадно удалятся главы, комментарии к книгам)
                # При удалении книг автоматически сработает сигнал delete_book_media_folders
                user_books = Book.objects.filter(author=user)
                books_count = user_books.count()
                user_books.delete()
                logger.info(f"Deleted {books_count} books for user {user.username}")

                # Удаляем лайки книг пользователя (это влияет на рейтинги)
                book_likes_count = BookLike.objects.filter(user=user).count()
                BookLike.objects.filter(user=user).delete()
                logger.info(f"Deleted {book_likes_count} book likes for user {user.username}")

                # Отзываем влияние пользователя на рейтинги других (до удаления лайков!)
                from users.rating_service import RatingEventHandlers
                RatingEventHandlers.on_user_deleted(user)

                # Удаляем лайки комментариев пользователя БЕЗ сигналов (влияние уже отозвано выше)
                comment_likes_count = CommentLike.objects.filter(user=user).count()
                # Используем bulk_delete чтобы избежать срабатывания сигналов post_delete
                from django.db import connection
                with connection.cursor() as cursor:
                    cursor.execute("DELETE FROM books_commentlike WHERE user_id = %s", [user.id])
                logger.info(f"Deleted {comment_likes_count} comment likes for user {user.username} (without signals)")

                # Удаляем отзывы пользователя (НЕ удаляем, а оставляем с пометкой "Аккаунт удален")
                # Отзывы остаются, но их вес в рейтингах убирается
                reviews_count = Review.objects.filter(user=user).count()
                logger.info(f"User {user.username} had {reviews_count} reviews (keeping but removing weight)")

                # Удаляем подписки и друзей
                subscriptions_count = Subscription.objects.filter(
                    Q(from_user=user) | Q(to_user=user)
                ).count()
                Subscription.objects.filter(Q(from_user=user) | Q(to_user=user)).delete()
                logger.info(f"Deleted {subscriptions_count} subscriptions for user {user.username}")

                # Удаляем заявки в друзья
                friend_requests_count = FriendRequest.objects.filter(
                    Q(from_user=user) | Q(to_user=user)
                ).count()
                FriendRequest.objects.filter(Q(from_user=user) | Q(to_user=user)).delete()
                logger.info(f"Deleted {friend_requests_count} friend requests for user {user.username}")

                # Удаляем пользователя из библиотек других пользователей
                from users.models import UserLibrary
                library_count = UserLibrary.objects.filter(user=user).count()
                UserLibrary.objects.filter(user=user).delete()
                logger.info(f"Deleted {library_count} library entries for user {user.username}")

                # Удаляем пользователя из просмотров книг
                from books.models import Book
                for book in Book.objects.filter(viewed_by=user):
                    book.viewed_by.remove(user)

                # Удаляем пользовательские файлы из S3
                self._delete_user_files_from_s3(user)

                # Удаляем пользовательские изображения (записи из БД)
                # Файлы уже удалены выше
                UserHeaderImage.objects.filter(user=user).delete()
                UserAvatar.objects.filter(user=user).delete()

                # Удаляем bio изображения (записи из БД)
                # Файлы уже удалены выше
                from users.bio_models import UserBioImage
                UserBioImage.objects.filter(user=user).delete()

                # НЕ удаляем пользователя из диалогов для сохранения переписки
                # Диалоги остаются, но пользователь помечается как удаленный
                # Второй участник может продолжать видеть диалог, но не может отправлять новые сообщения
                user_dialogs_count = Dialog.objects.filter(participants=user).count()
                logger.info(f"User {user.username} participated in {user_dialogs_count} dialogs (keeping for legal compliance)")

                logger.info(f"Account deletion completed for user {user.username}")

                # Выходим из системы
                logout(request)

                return Response({
                    'success': True,
                    'message': 'Аккаунт успешно удален'
                }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error deleting account for user {user.username}: {str(e)}")
            return Response({
                'error': 'Произошла ошибка при удалении аккаунта'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserRatingStatsView(APIView):
    """API для получения статистики рейтинга пользователя"""
    permission_classes = [IsAuthenticated]

    def get(self, request, username):
        # Проверяем, что пользователь запрашивает свою статистику
        if request.user.username != username:
            return Response(
                {'error': 'Доступ запрещен. Вы можете просматривать только свою статистику.'},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            from .models import RatingHistory
            from django.db.models.functions import TruncDate, TruncWeek, TruncMonth

            user = request.user
            period = request.GET.get('period', 'day')  # day, week, month, custom
            metric = request.GET.get('metric', 'all')  # reader_rating, author_rating, all
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 10))  # Изменили по умолчанию на 10
            date_from = request.GET.get('date_from')  # для custom периода
            date_to = request.GET.get('date_to')  # для custom периода

            # Базовый queryset
            history_qs = RatingHistory.objects.filter(user=user)

            # ИСКЛЮЧАЕМ записи, связанные с главами (чтобы не влияли на пагинацию)
            # Исключаем записи с action_type, связанными с главами
            history_qs = history_qs.exclude(action_type__in=['chapter_added', 'chapter_removed', 'chapter_published', 'chapter_unpublished'])

            # Фильтрация по метрике
            if metric in ['reader_rating', 'author_rating']:
                history_qs = history_qs.filter(metric_name=metric)

            # Получаем историю для таблицы (с пагинацией)
            history_total = history_qs.count()
            start = (page - 1) * page_size
            end = start + page_size
            history_items = history_qs.order_by('-created_at')[start:end]

            # Сериализуем историю
            history_data = []
            for item in history_items:
                item_data = {
                    'id': item.id,
                    'metric_name': item.get_metric_name_display(),
                    'action_type': item.get_action_type_display(),
                    'change_delta': item.change_delta,
                    'old_value': item.old_value,
                    'new_value': item.new_value,
                    'created_at': item.created_at,
                    'created_by': item.created_by,
                    'related_object': None
                }

                # Добавляем информацию о связанном объекте
                if item.related_object:
                    try:
                        # Проверяем тип связанного объекта
                        if hasattr(item.related_object, 'book') and item.related_object.book:
                            # Это комментарий к книге
                            item_data['related_object'] = {
                                'type': 'book',
                                'title': item.related_object.book.title,
                                'id': item.related_object.book.id
                            }
                        elif hasattr(item.related_object, 'title'):
                            # Это сама книга
                            item_data['related_object'] = {
                                'type': 'book',
                                'title': item.related_object.title,
                                'id': item.related_object.id
                            }
                    except Exception:
                        # Если не удалось получить информацию об объекте
                        pass

                history_data.append(item_data)

            # Получаем данные для графиков
            chart_data = self._get_chart_data(user, period, metric, date_from, date_to)

            # Получаем текущие рейтинги
            from .rating_service import RatingService
            user_stats = RatingService.get_or_create_user_stats(user)

            current_ratings = {
                'reader_rating': user_stats.reader_rating,
                'author_rating': user_stats.author_rating,
                'total_rating': user_stats.total_rating,
            }

            return Response({
                'current_ratings': current_ratings,
                'user_registration_date': user.date_joined.isoformat(),
                'history': {
                    'items': history_data,
                    'total': history_total,
                    'page': page,
                    'page_size': page_size,
                    'has_more': end < history_total
                },
                'chart_data': chart_data,
                'period': period,
                'metric': metric,
                'date_from': date_from,
                'date_to': date_to
            })

        except Exception as e:
            logger.error(f"Ошибка при получении статистики рейтинга для {username}: {e}")
            return Response(
                {'error': f'Ошибка при получении статистики: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _get_chart_data(self, user, period, metric, date_from=None, date_to=None):
        """Получить данные для графиков"""
        from .models import RatingHistory
        from django.db.models import Sum, Count
        from django.db.models.functions import TruncDate, TruncWeek, TruncMonth
        from datetime import datetime, timedelta

        # Определяем функцию группировки и период
        if period == 'custom':
            trunc_func = TruncDate
            # Для кастомного периода используем переданные даты
            if date_from and date_to:
                start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
                end_date = datetime.strptime(date_to, '%Y-%m-%d').date()
                # Проверяем ограничения
                today = timezone.now().date()
                three_months_ago = today - timedelta(days=90)
                registration_date = user.date_joined.date()

                # Минимальная дата: позже из (дата регистрации, 3 месяца назад)
                min_allowed_date = max(registration_date, three_months_ago)

                # Применяем ограничения
                start_date = max(start_date, min_allowed_date)
                end_date = min(end_date, today)
            else:
                # Если даты не переданы, используем последние 30 дней
                end_date = timezone.now().date()
                start_date = end_date - timedelta(days=30)
        elif period == 'week':
            trunc_func = TruncWeek
            days_back = 84  # 12 недель
            start_date = timezone.now() - timedelta(days=days_back)
            end_date = timezone.now()
        elif period == 'month':
            trunc_func = TruncMonth
            days_back = 365  # 12 месяцев
            start_date = timezone.now() - timedelta(days=days_back)
            end_date = timezone.now()
        else:  # day
            trunc_func = TruncDate
            days_back = 30  # 30 дней
            start_date = timezone.now() - timedelta(days=days_back)
            end_date = timezone.now()

        # Базовый queryset за указанный период
        history_qs = RatingHistory.objects.filter(
            user=user,
            created_at__gte=start_date,
            created_at__lte=end_date
        )

        # ИСКЛЮЧАЕМ записи, связанные с главами (для графиков тоже)
        history_qs = history_qs.exclude(action_type__in=['chapter_added', 'chapter_removed', 'chapter_published', 'chapter_unpublished'])

        # Фильтрация по метрике
        if metric in ['reader_rating', 'author_rating']:
            history_qs = history_qs.filter(metric_name=metric)

        # Группируем по периодам
        chart_data = history_qs.annotate(
            period=trunc_func('created_at')
        ).values('period', 'metric_name').annotate(
            total_change=Sum('change_delta'),
            count=Count('id')
        ).order_by('period', 'metric_name')

        # Форматируем данные для фронтенда
        formatted_data = {}
        for item in chart_data:
            period_key = item['period'].strftime('%Y-%m-%d')
            metric_name = item['metric_name']

            if period_key not in formatted_data:
                formatted_data[period_key] = {}

            formatted_data[period_key][metric_name] = {
                'change': item['total_change'],
                'count': item['count']
            }

        return formatted_data