import * as React from 'react';

import { DEFAULT_REACTIONS } from '../components/Reactions/DEFAULT_REACTIONS';
import { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';
import {
  setCustomEmojis,
  emojiUrlByUnified,
} from '../dataUtils/emojiSelectors';
import {
  EmojiClickData,
  EmojiStyle,
  SkinTonePickerLocation,
  SkinTones,
  SuggestionMode,
  Theme,
} from '../types/exposedTypes';

import {
  CategoriesConfig,
  baseCategoriesConfig,
  mergeCategoriesConfig,
  Locale,
} from './categoryConfig';
import { CustomEmoji } from './customEmojiConfig';

const KNOWN_FAILING_EMOJIS = ['2640-fe0f', '2642-fe0f', '2695-fe0f'];

// Локализация текстов
const localeTexts = {
  en: {
    searchPlaceholder: 'Search',
    noResultsFound: 'No results found',
    resultsSuffix: ' found. Use up and down arrow keys to navigate.',
    oneResult: '1 result',
    multipleResults: '%n results'
  },
  ru: {
    searchPlaceholder: 'Поиск',
    noResultsFound: 'Ничего не найдено',
    resultsSuffix: ' найдено. Используйте стрелки вверх и вниз для навигации.',
    oneResult: '1 результат',
    multipleResults: '%n результатов'
  }
};

export function getLocalizedText(key: keyof typeof localeTexts.en, locale: Locale = 'en'): string {
  return localeTexts[locale]?.[key] || localeTexts.en[key];
}

export const DEFAULT_SEARCH_PLACEHOLDER = 'Search';
export const SEARCH_RESULTS_NO_RESULTS_FOUND = 'No results found';
export const SEARCH_RESULTS_SUFFIX =
  ' found. Use up and down arrow keys to navigate.';
export const SEARCH_RESULTS_ONE_RESULT_FOUND =
  '1 result' + SEARCH_RESULTS_SUFFIX;
export const SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND =
  '%n results' + SEARCH_RESULTS_SUFFIX;

export function mergeConfig(
  userConfig: PickerConfig = {}
): PickerConfigInternal {
  const base = basePickerConfig();

  const previewConfig = Object.assign(
    base.previewConfig,
    userConfig.previewConfig ?? {}
  );
  const config = Object.assign(base, userConfig);

  // Поддержка локализации
  const locale = (userConfig as any).locale || 'en';
  const categories = mergeCategoriesConfig(userConfig.categories, {
    suggestionMode: config.suggestedEmojisMode,
  });

  // Обновляем категории с локализацией
  if (locale === 'ru') {
    config.categories = baseCategoriesConfig(undefined, locale);
    config.searchPlaceholder = getLocalizedText('searchPlaceholder', locale);
    config.searchPlaceHolder = getLocalizedText('searchPlaceholder', locale);
  }

  config.hiddenEmojis.forEach((emoji) => {
    config.unicodeToHide.add(emoji);
  });

  setCustomEmojis(config.customEmojis ?? []);

  const skinTonePickerLocation = config.searchDisabled
    ? SkinTonePickerLocation.PREVIEW
    : config.skinTonePickerLocation;

  return {
    ...config,
    categories: locale === 'ru' ? config.categories : categories,
    previewConfig,
    skinTonePickerLocation,
    locale,
  };
}

export function basePickerConfig(): PickerConfigInternal {
  return {
    autoFocusSearch: true,
    categories: baseCategoriesConfig(),
    className: '',
    customEmojis: [],
    defaultSkinTone: SkinTones.NEUTRAL,
    emojiStyle: EmojiStyle.APPLE,
    emojiVersion: null,
    getEmojiUrl: emojiUrlByUnified,
    height: 450,
    lazyLoadEmojis: false,
    previewConfig: {
      ...basePreviewConfig,
    },
    searchDisabled: false,
    searchPlaceHolder: DEFAULT_SEARCH_PLACEHOLDER,
    searchPlaceholder: DEFAULT_SEARCH_PLACEHOLDER,
    skinTonePickerLocation: SkinTonePickerLocation.SEARCH,
    skinTonesDisabled: false,
    style: {},
    suggestedEmojisMode: SuggestionMode.FREQUENT,
    theme: Theme.LIGHT,
    unicodeToHide: new Set<string>(KNOWN_FAILING_EMOJIS),
    width: 350,
    reactionsDefaultOpen: false,
    reactions: DEFAULT_REACTIONS,
    open: true,
    allowExpandReactions: true,
    hiddenEmojis: [],
  };
}

export type PickerConfigInternal = {
  emojiVersion: string | null;
  searchPlaceHolder: string;
  searchPlaceholder: string;
  defaultSkinTone: SkinTones;
  skinTonesDisabled: boolean;
  autoFocusSearch: boolean;
  emojiStyle: EmojiStyle;
  categories: CategoriesConfig;
  theme: Theme;
  suggestedEmojisMode: SuggestionMode;
  lazyLoadEmojis: boolean;
  previewConfig: PreviewConfig;
  className: string;
  height: PickerDimensions;
  width: PickerDimensions;
  style: React.CSSProperties;
  getEmojiUrl: GetEmojiUrl;
  searchDisabled: boolean;
  skinTonePickerLocation: SkinTonePickerLocation;
  unicodeToHide: Set<string>;
  customEmojis: CustomEmoji[];
  reactionsDefaultOpen: boolean;
  reactions: string[];
  open: boolean;
  allowExpandReactions: boolean;
  hiddenEmojis: string[];
  locale?: Locale;
};

export type PreviewConfig = {
  defaultEmoji: string;
  defaultCaption: string;
  showPreview: boolean;
};

const basePreviewConfig: PreviewConfig = {
  defaultEmoji: '1f60a',
  defaultCaption: "What's your mood?",
  showPreview: true,
};

type ConfigExternal = {
  previewConfig: Partial<PreviewConfig>;
  onEmojiClick: MouseDownEvent;
  onReactionClick: MouseDownEvent;
  onSkinToneChange: OnSkinToneChange;
} & Omit<PickerConfigInternal, 'previewConfig' | 'unicodeToHide'>;

export type PickerConfig = Partial<ConfigExternal>;

export type PickerDimensions = string | number;

export type MouseDownEvent = (
  emoji: EmojiClickData,
  event: MouseEvent,
  api?: OnEmojiClickApi
) => void;
export type OnSkinToneChange = (emoji: SkinTones) => void;

type OnEmojiClickApi = {
  collapseToReactions: () => void;
};
