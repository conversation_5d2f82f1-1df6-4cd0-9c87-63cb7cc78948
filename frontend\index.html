<!doctype html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/webp+xml" href="/lpo96.webp" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ЛитПортал</title>
    <!-- Проверка браузера должна выполняться как можно раньше -->
    <script src="/browser-check.js"></script>
  </head>
  <body>
    <div id="root"></div>
    <script>
      // Детекция старых браузеров
      (function() {
        // Проверяем поддержку WebP
        function supportsWebP() {
          try {
            return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0;
          } catch(err) {
            return false;
          }
        }

        // Проверяем поддержку современных JS функций
        function supportsModernJS() {
          try {
            // Проверяем поддержку ES6+ функций
            return typeof Symbol !== 'undefined' &&
                   typeof Promise !== 'undefined' &&
                   typeof Map !== 'undefined' &&
                   typeof Set !== 'undefined' &&
                   typeof Array.prototype.includes !== 'undefined' &&
                   typeof Object.assign !== 'undefined';
          } catch(err) {
            return false;
          }
        }

        // Проверяем версии браузеров
        function isOldBrowser() {
          const ua = navigator.userAgent;

          // Internet Explorer (все версии)
          if (ua.indexOf('MSIE') !== -1 || ua.indexOf('Trident/') !== -1) {
            return true;
          }

          // Старые версии Chrome (< 60)
          const chromeMatch = ua.match(/Chrome\/(\d+)/);
          if (chromeMatch && parseInt(chromeMatch[1]) < 60) {
            return true;
          }

          // Старые версии Firefox (< 55)
          const firefoxMatch = ua.match(/Firefox\/(\d+)/);
          if (firefoxMatch && parseInt(firefoxMatch[1]) < 55) {
            return true;
          }

          // Старые версии Safari (< 11)
          const safariMatch = ua.match(/Version\/(\d+).*Safari/);
          if (safariMatch && parseInt(safariMatch[1]) < 11) {
            return true;
          }

          // Старые версии Edge (< 79)
          const edgeMatch = ua.match(/Edge\/(\d+)/);
          if (edgeMatch && parseInt(edgeMatch[1]) < 79) {
            return true;
          }

          return false;
        }

        // Основная проверка
        if (!supportsWebP() || !supportsModernJS() || isOldBrowser()) {
          // Редиректим на страницу для старых браузеров
          window.location.href = '/badbrowser.html';
          return;
        }
      })();

      // Подавляем конкретные предупреждения findDOMNode от Ant Design в режиме разработки
      // Эти предупреждения исходят от библиотеки Ant Design и не влияют на функциональность
      const originalWarn = console.warn;
      console.warn = function(...args) {
        const message = args[0];
        if (typeof message === 'string' &&
            (message.includes('findDOMNode is deprecated') ||
             message.includes('findDOMNode was passed an instance'))) {
          return; // Подавляем эти предупреждения
        }
        originalWarn.apply(console, args);
      };
    </script>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
