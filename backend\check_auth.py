#!/usr/bin/env python
"""
Скрипт для проверки системы авторизации
"""
import os
import sys
import django
from pathlib import Path

# Добавляем путь к проекту
sys.path.append(str(Path(__file__).parent))

# Настраиваем Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model, authenticate
from django.conf import settings

User = get_user_model()

def check_auth_system():
    print("🔍 Проверка системы авторизации")
    print("=" * 50)
    
    # Проверяем пользователей
    total_users = User.objects.count()
    print(f"👥 Всего пользователей: {total_users}")
    
    if total_users > 0:
        print("\n📋 Первые 5 пользователей:")
        for user in User.objects.all()[:5]:
            print(f"   - {user.username} ({user.email}) - активен: {user.is_active}")
    
    # Проверяем AUTHENTICATION_BACKENDS
    auth_backends = getattr(settings, 'AUTHENTICATION_BACKENDS', [])
    print(f"\n🔐 AUTHENTICATION_BACKENDS:")
    for backend in auth_backends:
        print(f"   ✅ {backend}")
    
    # Проверяем REST_FRAMEWORK auth
    rest_auth = getattr(settings, 'REST_FRAMEWORK', {}).get('DEFAULT_AUTHENTICATION_CLASSES', [])
    print(f"\n🛡️  REST_FRAMEWORK Authentication:")
    for auth_class in rest_auth:
        print(f"   ✅ {auth_class}")
    
    # Тест авторизации (если есть пользователи)
    if total_users > 0:
        print(f"\n🧪 Тест авторизации:")
        test_user = User.objects.first()
        print(f"   Тестируем пользователя: {test_user.username}")
        
        # Проверяем что пользователь активен
        if not test_user.is_active:
            print("   ❌ Пользователь неактивен!")
        else:
            print("   ✅ Пользователь активен")
        
        # Проверяем что у пользователя есть пароль
        if test_user.password:
            print("   ✅ У пользователя установлен пароль")
        else:
            print("   ❌ У пользователя нет пароля!")
    
    # Проверяем URL patterns
    print(f"\n🔗 Проверка URL авторизации:")
    try:
        from django.urls import reverse
        login_url = reverse('login')
        print(f"   ✅ Login URL: {login_url}")
    except Exception as e:
        print(f"   ❌ Ошибка Login URL: {e}")
    
    # Проверяем middleware
    middleware = getattr(settings, 'MIDDLEWARE', [])
    required_middleware = [
        'django.contrib.sessions.middleware.SessionMiddleware',
        'django.contrib.auth.middleware.AuthenticationMiddleware',
    ]
    
    print(f"\n⚙️  Проверка Middleware:")
    for req_middleware in required_middleware:
        if req_middleware in middleware:
            print(f"   ✅ {req_middleware}")
        else:
            print(f"   ❌ {req_middleware} - НЕ НАЙДЕН!")

if __name__ == "__main__":
    check_auth_system() 