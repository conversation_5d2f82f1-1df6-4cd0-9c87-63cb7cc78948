from django.utils import timezone
from datetime import timedelta
from django.utils.deprecation import MiddlewareMixin
from channels.middleware import BaseMiddleware
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from django.contrib.auth import get_user
from django.contrib.sessions.models import Session
import logging

logger = logging.getLogger(__name__)

class UserActivityMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        if request.user.is_authenticated:
            # Обновляем время последней активности
            request.user.last_activity = timezone.now()
            request.user.save(update_fields=['last_activity'])
            
        return response 

class RequestLoggingMiddleware(MiddlewareMixin):
    def process_request(self, request):
        # Логируем только ошибки и важные события
        if request.path.startswith('/admin/') or request.path.startswith('/api/'):
            logger.info(f"API Request: {request.method} {request.path} by {request.user}")
        return None

class WebSocketAuthMiddleware(BaseMiddleware):
    async def __call__(self, scope, receive, send):
        # Получаем сессию из cookies
        cookies = dict(scope.get('headers', []))
        session_key = None
        
        for name, value in cookies:
            if name == b'cookie':
                cookie_parts = value.decode().split(';')
                for part in cookie_parts:
                    if 'sessionid=' in part:
                        session_key = part.split('=')[1].strip()
                        break
                break
        
        if session_key:
            # Получаем пользователя из сессии
            user = await self.get_user_from_session(session_key)
            scope['user'] = user
            logger.info(f"WebSocket auth: User {user} authenticated")
        else:
            scope['user'] = AnonymousUser()
            logger.warning("WebSocket auth: No session found")
        
        return await super().__call__(scope, receive, send)
    
    @database_sync_to_async
    def get_user_from_session(self, session_key):
        try:
            session = Session.objects.get(session_key=session_key)
            user_id = session.get_decoded().get('_auth_user_id')
            if user_id:
                from .models import User
                return User.objects.get(id=user_id)
        except (Session.DoesNotExist, User.DoesNotExist):
            pass
        return AnonymousUser() 