/* Стили для плавающего меню изображений */
@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateX(-50%) translateY(-4px); 
  }
  to { 
    opacity: 1; 
    transform: translateX(-50%) translateY(0); 
  }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateX(-50%) translateY(8px); 
  }
  to { 
    opacity: 1; 
    transform: translateX(-50%) translateY(0); 
  }
}

.floating-image-menu {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 4px;
  border-radius: 8px;
  padding: 8px 12px;
  z-index: 1000;
  animation: fadeIn 0.25s ease-out;
  white-space: nowrap;
  pointer-events: auto;
  /* Темная тема (по умолчанию) - 20% прозрачности */
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(12px);
  /* Адаптивный размер */
  min-width: fit-content;
  max-width: 100%;
  font-size: 14px;
  transition: all 0.3s ease;
}

/* Светлая тема - менее прозрачная */
.floating-image-menu.light-theme {
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(8px);
}

/* Темная тема */
.floating-image-menu.dark-theme {
  background: rgba(30, 30, 30, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(12px);
}

.floating-image-menu.menu-top {
  bottom: calc(100% + 8px);
  animation: slideUp 0.25s ease-out;
}

.floating-image-menu.menu-bottom {
  top: calc(100% + 8px);
}

.floating-menu-button {
  background: none;
  border: none;
  border-radius: 6px;
  width: var(--btn-size, 36px);
  height: var(--btn-size, 36px);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  font-size: var(--btn-font-size, 16px);
}

/* Адаптивные размеры меню */
.floating-image-menu.compact {
  --btn-size: 28px;
  --btn-font-size: 12px;
  gap: 2px;
  padding: 6px 8px;
  border-radius: 6px;
}

.floating-image-menu.mini {
  --btn-size: 22px;
  --btn-font-size: 10px;
  gap: 1px;
  padding: 4px 6px;
  border-radius: 5px;
}

/* Адаптация разделителей в зависимости от размера */
.floating-image-menu.compact .menu-divider {
  height: 20px;
  margin: 0 2px;
}

.floating-image-menu.mini .menu-divider {
  height: 16px;
  margin: 0 1px;
}

/* Темная тема (по умолчанию) */
.floating-menu-button {
  color: #fff;
}

.floating-menu-button:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-1px);
}

.floating-menu-button.active {
  background: rgba(59, 130, 246, 0.3);
  color: #60a5fa;
}

/* Темная тема - явные стили */
.floating-image-menu.dark-theme .floating-menu-button {
  color: #e5e7eb;
}

.floating-image-menu.dark-theme .floating-menu-button:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.floating-image-menu.dark-theme .floating-menu-button.active {
  background: rgba(59, 130, 246, 0.4);
  color: #93c5fd;
}

/* Светлая тема */
.floating-image-menu.light-theme .floating-menu-button {
  color: #374151;
}

.floating-image-menu.light-theme .floating-menu-button:hover {
  background: rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.floating-image-menu.light-theme .floating-menu-button.active {
  background: rgba(59, 130, 246, 0.2);
  color: #2563eb;
}

.floating-menu-button:active {
  transform: translateY(0);
}

.floating-menu-button.delete-button {
  color: #ef4444;
}

.floating-menu-button.delete-button:hover {
  background: rgba(239, 68, 68, 0.15);
  color: #f87171;
}

/* Темная тема - кнопка удаления */
.floating-image-menu.dark-theme .floating-menu-button.delete-button {
  color: #f87171;
}

.floating-image-menu.dark-theme .floating-menu-button.delete-button:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
}

.floating-image-menu.light-theme .floating-menu-button.delete-button {
  color: #dc2626;
}

.floating-image-menu.light-theme .floating-menu-button.delete-button:hover {
  background: rgba(220, 38, 38, 0.1);
  color: #ef4444;
}

.floating-menu-button.caption-active {
  background: rgba(34, 197, 94, 0.25);
  color: #4ade80;
}

.floating-menu-button.caption-active:hover {
  background: rgba(34, 197, 94, 0.35);
}

/* Темная тема - активная кнопка подписи */
.floating-image-menu.dark-theme .floating-menu-button.caption-active {
  background: rgba(34, 197, 94, 0.3);
  color: #6ee7b7;
}

.floating-image-menu.dark-theme .floating-menu-button.caption-active:hover {
  background: rgba(34, 197, 94, 0.4);
}

.floating-image-menu.light-theme .floating-menu-button.caption-active {
  background: rgba(34, 197, 94, 0.15);
  color: #16a34a;
}

.floating-image-menu.light-theme .floating-menu-button.caption-active:hover {
  background: rgba(34, 197, 94, 0.25);
}

/* Прозрачная кнопка позиционирования меню */
.floating-menu-button.position-toggle {
  background: transparent !important;
  background-color: transparent !important;
}

.floating-menu-button.position-toggle:hover {
  background: rgba(255, 255, 255, 0.08) !important;
}

.floating-image-menu.light-theme .floating-menu-button.position-toggle:hover {
  background: rgba(0, 0, 0, 0.05) !important;
}

/* Стили для заголовка группы в выпадающем меню */
.ant-dropdown-menu-item-group-title {
  font-size: 11px !important;
  font-weight: 500 !important;
  opacity: 0.7 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  padding: 4px 12px 2px 12px !important;
}

/* Темная тема для Dropdown меню позиционирования */
.position-dropdown.dark-theme .ant-dropdown-menu {
  background: rgba(31, 41, 55, 0.95) !important;
  border: 1px solid #4b5563 !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(12px);
}

.position-dropdown.dark-theme .ant-dropdown-menu-item {
  color: #e5e7eb !important;
}

.position-dropdown.dark-theme .ant-dropdown-menu-item:hover {
  background: rgba(75, 85, 99, 0.3) !important;
}

.position-dropdown.dark-theme .ant-dropdown-menu-item-group-title {
  color: #9ca3af !important;
}

.position-dropdown.dark-theme .ant-dropdown-menu-item-divider {
  background: #4b5563 !important;
}

/* Светлая тема для Dropdown меню позиционирования */
.position-dropdown.light-theme .ant-dropdown-menu {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(8px);
}

.position-dropdown.light-theme .ant-dropdown-menu-item {
  color: #374151 !important;
}

.position-dropdown.light-theme .ant-dropdown-menu-item:hover {
  background: rgba(0, 0, 0, 0.05) !important;
}

.position-dropdown.light-theme .ant-dropdown-menu-item-group-title {
  color: #6b7280 !important;
}

/* Стили для поля ввода подписи - адаптивное по ширине изображения */
.caption-input-container {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  right: 0;
  background: var(--bg-color, #fff);
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  animation: fadeIn 0.2s ease-out;
  backdrop-filter: blur(8px);
}

.caption-input-container.dark-theme {
  --bg-color: #1f2937;
  --border-color: #374151;
  --text-color: #fff;
  --input-bg: #111827;
}

.caption-input-container.light-theme {
  --bg-color: #fff;
  --border-color: #d1d5db;
  --text-color: #000;
  --input-bg: #fff;
}

.caption-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--input-bg);
  color: #6b7280; /* Серый цвет текста */
  font-size: 14px;
  font-style: italic; /* Курсив */
  text-align: center; /* Центрирование текста */
  margin-bottom: 8px;
  transition: border-color 0.2s ease, color 0.2s ease;
  resize: none;
  font-family: inherit;
  max-length: 150; /* Максимум 150 символов */
}

.caption-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Темная тема - поле ввода подписи */
.caption-input-container.dark-theme .caption-input {
  border: 1px solid #4b5563;
  background: rgba(31, 41, 55, 0.95);
  color: #9ca3af;
}

.caption-input-container.dark-theme .caption-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Блок информации о подписи */
.caption-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  gap: 8px;
}

/* Счетчик символов */
.caption-counter {
  font-size: 11px;
  color: #9ca3af;
  font-style: normal;
  opacity: 0.7;
  white-space: nowrap;
}

/* Подсказка по управлению */
.caption-hint {
  font-size: 10px;
  color: #9ca3af;
  font-style: normal;
  opacity: 0.6;
  text-align: left;
  flex: 1;
}

/* Темная тема - счетчик и подсказки */
.caption-input-container.dark-theme .caption-counter {
  color: #6b7280;
}

.caption-input-container.dark-theme .caption-hint {
  color: #6b7280;
}

.caption-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.caption-button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.caption-button.cancel {
  border: 1px solid var(--border-color);
  background: transparent;
  color: var(--text-color);
}

.caption-button.cancel:hover {
  background: rgba(0, 0, 0, 0.05);
}

.caption-button.save {
  border: none;
  background: #2563eb;
  color: #fff;
}

.caption-button.save:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
}

/* Стили для подписи под изображением */
.image-caption {
  font-size: 13px;
  font-style: italic; /* Курсив */
  text-align: center; /* Центрирование */
  color: #6b7280; /* Серый цвет */
  margin-top: 8px;
  margin-bottom: 0;
  padding: 0 8px;
  line-height: 1.4;
  transition: opacity 0.2s ease;
  width: 100%; /* Адаптируется по ширине изображения */
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  opacity: 0.85;
  font-weight: 400;
}

.image-caption:hover {
  opacity: 0.9;
}

/* Темная тема - подпись под изображением */
.dark-theme .image-caption {
  color: #9ca3af;
}

/* Стили для блочных изображений с возможностью обтекания */
.custom-resizable-image {
  display: block;
  position: relative;
  margin: 16px auto;
  clear: both;
}

/* CSS классы для обтекания (float) */
.custom-resizable-image.float-left {
  float: left;
  margin: 0 16px 16px 0;
  clear: none;
  z-index: 10;
  position: relative;
  cursor: pointer;
}

.custom-resizable-image.float-right {
  float: right;
  margin: 0 0 16px 16px;
  clear: none;
  z-index: 10;
  position: relative;
  cursor: pointer;
}

/* Улучшение взаимодействия для floating изображений */
.custom-resizable-image.float-left img,
.custom-resizable-image.float-right img {
  pointer-events: auto;
  cursor: pointer;
  user-select: none;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* Hover эффекты для floating изображений */
.custom-resizable-image.float-left:hover img,
.custom-resizable-image.float-right:hover img {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Добавляем невидимую область для лучшего клика */
.custom-resizable-image.float-left::before,
.custom-resizable-image.float-right::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  z-index: -1;
  pointer-events: auto;
}

/* CSS классы для выравнивания без обтекания */
.custom-resizable-image.align-left {
  float: none;
  margin: 16px 0 16px 0;
  text-align: left;
  clear: both;
}

.custom-resizable-image.align-right {
  float: none;
  margin: 16px 0 16px auto;
  text-align: right;
  clear: both;
}

.custom-resizable-image.align-center {
  float: none;
  margin: 16px auto;
  text-align: center;
  clear: both;
}

/* Стили для TipTap редактора */
.ProseMirror {
  overflow: visible;
}

.ProseMirror p {
  margin: 1em 0;
  line-height: 1.6;
  overflow: visible;
}

/* Обеспечиваем правильное обтекание для параграфов после floating изображений */
.ProseMirror .custom-resizable-image.float-left + p,
.ProseMirror .custom-resizable-image.float-right + p {
  margin-top: 0;
}

/* Стили для обычного выравнивания (без обтекания) */
.ProseMirror .custom-resizable-image.align-left + p,
.ProseMirror .custom-resizable-image.align-right + p,
.ProseMirror .custom-resizable-image.align-center + p {
  margin-top: 1em;
}

/* Очистка float после последнего параграфа */
.ProseMirror p:last-child::after {
  content: '';
  display: table;
  clear: both;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
  .custom-resizable-image.float-left,
  .custom-resizable-image.float-right,
  .custom-resizable-image.align-left,
  .custom-resizable-image.align-right {
    float: none !important;
    display: block !important;
    margin: 16px auto !important;
    text-align: center !important;
    max-width: 100% !important;
  }
}

/* Стили для ресайзеров изображения */
.image-resizer {
  position: absolute;
  background: #2563eb;
  border: 2px solid #fff;
  border-radius: 4px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 1001;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.image-container-selected .image-resizer {
  opacity: 1;
}

/* Контейнер для ресайзеров - позиционируется по размерам изображения */
.image-resizers-container {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.image-resizers-container .image-resizer {
  pointer-events: auto;
}

/* Угловые ресайзеры */
.image-resizer.corner {
  width: 12px;
  height: 12px;
}

.image-resizer.top-left {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.image-resizer.top-right {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.image-resizer.bottom-left {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.image-resizer.bottom-right {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

/* Боковые ресайзеры */
.image-resizer.side {
  background: #2563eb;
}

.image-resizer.top {
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 8px;
  cursor: n-resize;
}

.image-resizer.bottom {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 8px;
  cursor: s-resize;
}

.image-resizer.left {
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 24px;
  cursor: w-resize;
}

.image-resizer.right {
  right: -4px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 24px;
  cursor: e-resize;
}

.image-resizer:hover {
  background: #1d4ed8;
  transform: scale(1.1);
}

.image-resizer.corner:hover {
  transform: scale(1.2);
}

.image-resizer.top:hover,
.image-resizer.bottom:hover {
  transform: translateX(-50%) scale(1.1);
}

.image-resizer.left:hover,
.image-resizer.right:hover {
  transform: translateY(-50%) scale(1.1);
}

/* Стили для контейнера изображения при ресайзе */
.image-container-resizing {
  user-select: none;
}

.image-container-resizing img {
  pointer-events: none;
}

/* Улучшенная адаптивность для средних экранов */
@media (max-width: 992px) {
  .floating-image-menu:not(.mini):not(.compact) {
    --btn-size: 32px;
    --btn-font-size: 14px;
    padding: 6px 10px;
    gap: 3px;
  }
}

/* Адаптивность для планшетов */
@media (max-width: 768px) {
  .floating-image-menu:not(.mini):not(.compact) {
    --btn-size: 30px;
    --btn-font-size: 13px;
    padding: 6px 8px;
    gap: 2px;
  }

  .floating-image-menu.compact {
    --btn-size: 26px;
    --btn-font-size: 11px;
    padding: 4px 6px;
    gap: 2px;
  }

  .floating-image-menu.mini {
    --btn-size: 20px;
    --btn-font-size: 9px;
    padding: 3px 5px;
    gap: 1px;
  }
  
  .caption-input-container {
    left: -4px;
    right: -4px;
    padding: 8px 10px;
  }

  .caption-input {
    font-size: 13px;
    padding: 6px 10px;
  }

  .caption-info {
    gap: 6px;
  }

  .caption-counter,
  .caption-hint {
    font-size: 10px;
  }
  

  
  /* Увеличиваем ресайзеры на мобильных */
  .image-resizer.corner {
    width: 16px;
    height: 16px;
  }
  
  .image-resizer.top-left {
    top: -8px;
    left: -8px;
  }
  
  .image-resizer.top-right {
    top: -8px;
    right: -8px;
  }
  
  .image-resizer.bottom-left {
    bottom: -8px;
    left: -8px;
  }
  
  .image-resizer.bottom-right {
    bottom: -8px;
    right: -8px;
  }
}

@media (max-width: 480px) {
  /* На очень маленьких экранах все изображения отображаются как блочные */
  .custom-resizable-image {
    display: block !important;
    float: none !important;
    margin: 16px auto !important;
    max-width: 100% !important;
  }
} 