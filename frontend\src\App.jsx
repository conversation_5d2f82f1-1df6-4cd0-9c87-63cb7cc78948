import { Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Suspense, lazy } from 'react';
import Navbar from './components/Navbar';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import NotFound from './pages/NotFound';
import UserRatingStats from './pages/UserRatingStats';
import { AuthProvider, useAuth } from './context/AuthContext';
import { MessageProvider } from './context/MessageContext';
import { UserSettingsContext } from './context/UserSettingsContext';
import { UserCacheProvider } from './context/UserCacheContext';
import { BookCountsProvider } from './context/BookCountsContext';
import WebSocketStatus from './components/WebSocketStatus';

// Lazy loading для тяжелых компонентов
const Books = lazy(() => import('./pages/Books'));
const ProfileLayout = lazy(() => import('./pages/Profile'));
const ProfileMain = lazy(() => import('./pages/ProfileMain'));
const ProfileBooks = lazy(() => import('./pages/ProfileBooks'));
const ProfileSettings = lazy(() => import('./pages/ProfileSettings'));
const OAuthCallback = lazy(() => import('./pages/OAuthCallback'));
const SearchResults = lazy(() => import('./pages/SearchResults'));
const Feed = lazy(() => import('./components/Feed/Feed'));
const UserBooks = lazy(() => import('./pages/UserBooks'));
const FeedSettings = lazy(() => import('./pages/FeedSettings'));
const EditBook = lazy(() => import('./pages/EditBook'));
const BookView = lazy(() => import('./pages/BookView'));
const BookReader = lazy(() => import('./components/BookReader'));
const Messages = lazy(() => import('./components/Messages'));
const CreateBook = lazy(() => import('./pages/CreateBook'));
const Library = lazy(() => import('./pages/Library'));


// Компонент загрузки
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-[200px]">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>
);

// Создаем экземпляр QueryClient
const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            refetchOnWindowFocus: false,
            retry: 1,
        },
    },
});

// Создаем отдельный компонент для контента приложения
function AppContent() {
    const { user, loading } = useAuth();
    
    if (loading) {
        return <div>Загрузка...</div>; // или компонент загрузки
    }
    
    return (
        <UserCacheProvider>
            <BookCountsProvider>
                <MessageProvider>
                    <UserSettingsContext.Provider value={{ timezone: user?.timezone || 'Europe/Moscow' }}>
                        <QueryClientProvider client={queryClient}>
                    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
                        <Navbar />
                        <main className="container mx-auto px-4 py-[10px]">
                            <Suspense fallback={<LoadingSpinner />}>
                                <Routes>
                                    <Route path="/" element={<Home />} />
                                    <Route path="/books" element={<Books />} />
                                    <Route path="/search" element={<SearchResults />} />
                                    <Route path="/book/:id" element={<BookView />} />
                                    <Route path="/book/:id/reader" element={<BookReader />} />
                                    <Route path="/lpu/:username/books/:id/edit" element={<EditBook />} />
                                    <Route path="/lpu/:username/stat" element={<UserRatingStats />} />
                                    <Route path="/lpu/:username" element={<ProfileLayout />}>
                                        <Route index element={<ProfileMain />} />
                                        <Route path="books" element={<UserBooks />} />
                                        <Route path="feed" element={<Feed />} />
                                        <Route path="settings" element={<ProfileSettings />} />
                                        <Route path="feed/settings" element={<FeedSettings />} />
                                        <Route path="messages" element={<Messages />} />
                                        <Route path="library" element={<Library />} />
                                    </Route>
                                    <Route path="/login" element={<Login />} />
                                    <Route path="/register" element={<Register />} />
                                    <Route path="/auth/callback" element={<OAuthCallback />} />
                                    <Route path="/lpu/:username/books/create" element={<CreateBook />} />

                                    <Route path="*" element={<NotFound />} />
                                </Routes>
                            </Suspense>
                        </main>
                        <WebSocketStatus />
                    </div>
                    </QueryClientProvider>
                </UserSettingsContext.Provider>
            </MessageProvider>
        </BookCountsProvider>
        </UserCacheProvider>
    );
}

function App() {
    return (
        <AuthProvider>
            <AppContent />
        </AuthProvider>
    );
}

export default App;
