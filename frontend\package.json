{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run generate-index && vite build", "build:analyze": "npm run generate-index && vite build --mode analyze", "build:stats": "npm run generate-index && vite build && echo 'Bundle analysis available at dist/stats.html'", "build:size": "npm run build && du -sh dist/ && echo 'Chunk sizes:' && find dist/js -name '*.js' -exec du -h {} + | sort -hr", "generate-index": "node scripts/generatePresetsIndex.cjs", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@giphy/js-fetch-api": "^5.6.0", "@giphy/react-components": "^10.0.1", "@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@tanstack/react-query": "^5.75.6", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "antd": "^5.25.1", "axios": "^1.9.0", "date-fns": "^4.1.0", "emoji-picker-react": "4.13.2", "epubjs": "^0.3.93", "mammoth": "^1.9.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "react": "^18.2.0", "react-dom": "^18.2.0", "react-easy-crop": "^5.4.1", "react-router-dom": "^6.22.1", "styled-components": "^6.1.18"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^22.15.29", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "vite": "^6.3.5"}}