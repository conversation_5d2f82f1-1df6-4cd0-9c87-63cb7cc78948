import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Container from '../components/Container';
import { useTheme } from '../theme/ThemeContext';

function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

function Register() {
  const { theme } = useTheme();
  const [formData, setFormData] = useState({
    display_name: '',
    username: '',
    email: '',
    password: '',
    password2: '',
  });
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState('');
  const [usernameStatus, setUsernameStatus] = useState({
    valid: null,
    message: '',
    color: '',
    icon: '',
    checking: false,
  });
  const [passwordRequirements, setPasswordRequirements] = useState({
    hasUpperCase: false,
    hasLowerCase: false,
    hasNumber: false,
    hasSymbol: false,
  });
  const [csrfToken, setCsrfToken] = useState('');
  const navigate = useNavigate();

  // Список запрещенных слов в логинах
  const forbiddenWords = [
    'admin', 'administrator', 'moderator', 'login', 'boss', 'director', 'litportal'
  ];

  // Функция проверки запрещенных слов в логине
  const checkForbiddenUsername = (username) => {
    const lowerUsername = username.toLowerCase();
    return forbiddenWords.some(word => lowerUsername.includes(word));
  };

  // Проверка username в реальном времени
  useEffect(() => {
    const username = formData.username;
    if (!username) {
      setUsernameStatus({ valid: null, message: '', color: '', icon: '', checking: false });
      return;
    }
    if (!/^[a-z0-9]*$/.test(username)) {
      setUsernameStatus({
        valid: false,
        message: 'Только строчные латинские буквы и цифры',
        color: 'red',
        icon: '❌',
        checking: false,
      });
      return;
    }
    if (username.length < 4) {
      setUsernameStatus({
        valid: false,
        message: 'Минимум 4 символа',
        color: 'red',
        icon: '❌',
        checking: false,
      });
      return;
    }
    if (checkForbiddenUsername(username)) {
      setUsernameStatus({
        valid: false,
        message: 'Данный логин содержит запрещенные слова',
        color: 'red',
        icon: '❌',
        checking: false,
      });
      return;
    }
    setUsernameStatus({
      valid: null,
      message: 'Проверка доступности...',
      color: '',
      icon: '',
      checking: true,
    });
    const controller = new AbortController();
    fetch(`/api/auth/check-username/?username=${encodeURIComponent(username)}`, {
      signal: controller.signal,
    })
      .then((res) => res.json())
      .then((data) => {
        if (data.exists) {
          if (data.forbidden) {
            setUsernameStatus({
              valid: false,
              message: data.message || 'Данный логин содержит запрещенные слова',
              color: 'red',
              icon: '❌',
              checking: false,
            });
          } else {
            setUsernameStatus({
              valid: false,
              message: 'Пользователь занят',
              color: 'red',
              icon: '❌',
              checking: false,
            });
          }
        } else {
          setUsernameStatus({
            valid: true,
            message: 'Логин свободен',
            color: 'green',
            icon: '✔️',
            checking: false,
          });
        }
      })
      .catch(() => {
        setUsernameStatus({
          valid: null,
          message: '',
          color: '',
          icon: '',
          checking: false,
        });
      });
    return () => controller.abort();
  }, [formData.username]);

  // Проверка требований к паролю
  useEffect(() => {
    const password = formData.password;
    setPasswordRequirements({
      hasUpperCase: /[A-Z]/.test(password),
      hasLowerCase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSymbol: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    });
  }, [formData.password]);

  useEffect(() => {
    fetch('/api/auth/csrf/', { credentials: 'include' })
      .then(() => {
        setCsrfToken(getCookie('csrftoken'));
      });
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.display_name.trim()) {
      newErrors.display_name = 'Введите имя или псевдоним';
    }
    if (!formData.username.trim()) {
      newErrors.username = 'Введите логин';
    } else if (!/^[a-z0-9]+$/.test(formData.username)) {
      newErrors.username = 'Логин: только строчные латинские буквы и цифры';
    } else if (formData.username.length < 4) {
      newErrors.username = 'Минимум 4 символа';
    } else if (checkForbiddenUsername(formData.username)) {
      newErrors.username = 'Данный логин содержит запрещенные слова';
    } else if (usernameStatus.valid === false) {
      newErrors.username = usernameStatus.message;
    }
    if (!formData.email.trim()) {
      newErrors.email = 'Введите email';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Введите корректный email';
    }
    if (!formData.password) {
      newErrors.password = 'Введите пароль';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Пароль не менее 8 символов';
    } else if (!Object.values(passwordRequirements).every(Boolean)) {
      newErrors.password = 'Пароль не соответствует требованиям';
    }
    if (!formData.password2) {
      newErrors.password2 = 'Подтвердите пароль';
    } else if (formData.password !== formData.password2) {
      newErrors.password2 = 'Пароли не совпадают';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;
    try {
      console.log('Отправляем данные регистрации:', formData);
      const response = await fetch('/api/auth/register/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify(formData),
      });

      console.log('Ответ от сервера:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        console.log('Успешная регистрация:', data);

        // Показываем сообщение об успехе и сразу перенаправляем
        setSuccessMessage('Регистрация успешна!');
        setErrors({});

        // Мгновенный переход на страницу входа
        window.location.href = '/login';
      } else {
        console.log('Ошибка регистрации, статус:', response.status);
        const data = await response.json();
        console.log('Данные ошибки:', data);
        setErrors(data);
      }
    } catch (err) {
      console.error('Ошибка сети при регистрации:', err);
      setErrors({ non_field_errors: 'Ошибка сети' });
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 overflow-y-auto">
      <div className="flex justify-center items-center min-h-screen p-4">
        <div className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-md w-full ${theme === 'dark' ? 'dark' : ''}`}>
          <button
            onClick={() => navigate('/')}
            className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 z-20 bg-transparent p-2 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Закрыть"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <div className="flex flex-col items-center mb-4">
            <img src="/lpo/logo/logolpo.webp" alt="ЛитПортал" className="h-12 mx-auto mb-2" />
            <h1 className="text-xl font-bold text-gray-900 dark:text-white text-center">Регистрация на ЛитПортале</h1>
          </div>
          {successMessage && (
            <div className="rounded-md bg-green-50 dark:bg-green-900/50 p-4 mb-4">
              <p className="text-sm text-green-600 dark:text-green-400">{successMessage}</p>
            </div>
          )}
          <form className="space-y-3" onSubmit={handleSubmit} autoComplete="on" data-form-type="register">
            {/* Скрытые поля для лучшего распознавания браузером */}
            <input type="hidden" name="action" value="register" />
            <div>
              <label htmlFor="display_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Укажите ФИО или Псевдоним
              </label>
              <input
                type="text"
                id="display_name"
                name="display_name"
                autoComplete="name"
                className={`mt-1 block w-full min-w-[260px] md:min-w-[340px] rounded-md border border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm placeholder:text-xs py-2 px-3 ${errors.display_name ? 'border-red-500' : ''}`}
                placeholder="Это имя которое будут видеть пользователи на сайте"
                value={formData.display_name}
                onChange={handleChange}
              />
              <p className="mt-1 text-[11px] text-gray-500 dark:text-gray-400 italic px-1">(Можно использовать Заглавные, прописные буквы и тире)</p>
              {errors.display_name && <p className="mt-1 text-sm text-red-600 px-1">{errors.display_name}</p>}
            </div>
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Логин (для страницы профиля)
              </label>
              {formData.username && (
                <div className="mb-2">
                  <p className="text-sm text-blue-600 dark:text-blue-400 font-medium px-1">
                    Ваша страница: {window.location.origin}/lpu/{formData.username}
                  </p>
                </div>
              )}
              <input
                type="text"
                id="username"
                name="username"
                autoComplete="nickname"
                data-lpignore="true"
                className={`mt-1 block w-full min-w-[260px] md:min-w-[340px] rounded-md border border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm placeholder:text-xs py-2 px-3 ${usernameStatus.valid === false ? 'border-red-500' : usernameStatus.valid === true ? 'border-green-500' : ''}`}
                placeholder="Создается один раз и прикрепляется к вашей странице"
                value={formData.username}
                onChange={handleChange}
              />
              <p className="mt-1 text-[11px] text-gray-500 dark:text-gray-400 italic px-1">(Длина логина не менее 4-х символов. Можно использовать только прописные латинские буквы, а также цифры, пробелы и символы недопускаются)</p>
              {usernameStatus.message && (
                <p className={`mt-1 text-sm ${usernameStatus.color === 'red' ? 'text-red-600' : usernameStatus.color === 'green' ? 'text-green-600' : 'text-gray-600'} px-1`}>
                  {usernameStatus.icon} {usernameStatus.message}
                </p>
              )}
              {errors.username && <p className="mt-1 text-sm text-red-600 px-1">{errors.username}</p>}
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email (для входа на сайт)
              </label>
              <input
                type="email"
                id="email"
                name="email"
                autoComplete="username email"
                data-form-type="register"
                className={`mt-1 block w-full min-w-[260px] md:min-w-[340px] rounded-md border border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm placeholder:text-xs py-2 px-3 ${errors.email ? 'border-red-500' : ''}`}
                placeholder="Укажите ваш email"
                value={formData.email}
                onChange={handleChange}
              />
              {errors.email && <p className="mt-1 text-sm text-red-600 px-1">{errors.email}</p>}
            </div>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Пароль
              </label>
              <input
                type="password"
                id="password"
                name="password"
                autoComplete="new-password"
                className={`mt-1 block w-full min-w-[260px] md:min-w-[340px] rounded-md border border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm placeholder:text-xs py-2 px-3 ${errors.password ? 'border-red-500' : ''}`}
                placeholder="Придумайте надежный пароль"
                value={formData.password}
                onChange={handleChange}
              />
              <div className="mt-2 space-y-1 px-1">
                <p className="text-xs text-gray-500 dark:text-gray-400">Пароль должен содержать:</p>
                <ul className="text-xs space-y-1">
                  <li className={`flex items-center ${passwordRequirements.hasUpperCase ? 'text-green-600' : 'text-gray-500 dark:text-gray-400'}`}>
                    {passwordRequirements.hasUpperCase ? '✓' : '○'} Заглавную букву
                  </li>
                  <li className={`flex items-center ${passwordRequirements.hasLowerCase ? 'text-green-600' : 'text-gray-500 dark:text-gray-400'}`}>
                    {passwordRequirements.hasLowerCase ? '✓' : '○'} Строчную букву
                  </li>
                  <li className={`flex items-center ${passwordRequirements.hasNumber ? 'text-green-600' : 'text-gray-500 dark:text-gray-400'}`}>
                    {passwordRequirements.hasNumber ? '✓' : '○'} Цифру
                  </li>
                  <li className={`flex items-center ${passwordRequirements.hasSymbol ? 'text-green-600' : 'text-gray-500 dark:text-gray-400'}`}>
                    {passwordRequirements.hasSymbol ? '✓' : '○'} Специальный символ
                  </li>
                </ul>
              </div>
              {errors.password && <p className="mt-1 text-sm text-red-600 px-1">{errors.password}</p>}
            </div>
            <div>
              <label htmlFor="password2" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Подтверждение пароля
              </label>
              <input
                type="password"
                id="password2"
                name="password2"
                autoComplete="new-password"
                className={`mt-1 block w-full min-w-[260px] md:min-w-[340px] rounded-md border border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm placeholder:text-xs py-2 px-3 ${errors.password2 ? 'border-red-500' : ''}`}
                placeholder="Повторите пароль"
                value={formData.password2}
                onChange={handleChange}
              />
              {errors.password2 && <p className="mt-1 text-sm text-red-600 px-1">{errors.password2}</p>}
            </div>
            {errors.non_field_errors && (
              <div className="rounded-md bg-red-50 dark:bg-red-900/50 p-4">
                <p className="text-sm text-red-600 dark:text-red-400">{errors.non_field_errors}</p>
              </div>
            )}
            <div>
              <button
                type="submit"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Зарегистрироваться
              </button>
            </div>
            <p className="text-center text-sm text-gray-600 dark:text-gray-400">
              Уже есть аккаунт?{' '}
              <Link to="/login" className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                Войти
              </Link>
            </p>
          </form>
        </div>
      </div>
    </div>
  );
}

export default Register; 