import React, { useState } from 'react';
import { Gip<PERSON>Fetch } from '@giphy/js-fetch-api';
import { Grid as GiphyGrid } from '@giphy/react-components';

const gf = new GiphyFetch('2b71g917gxxuKWHKWKAevQLb1xQzAxs8'); // или ваш ключ

const GiphyModal = ({ open, onClose, onSelect, theme = 'light' }) => {
  const [query, setQuery] = useState('');

  if (!open) return null;

  // fetchGifs для поиска и трендов
  const fetchGifs = (offset) =>
    query
      ? gf.search(query, { offset, limit: 15 })
      : gf.trending({ offset, limit: 15 });

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50" onClick={onClose}>
      <div className={`rounded-lg shadow-lg p-4 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`} onClick={e => e.stopPropagation()}>
        <input
          type="text"
          value={query}
          onChange={e => setQuery(e.target.value)}
          placeholder="Поиск GIF"
          className="w-full mb-2 p-2 rounded border"
          autoFocus
        />
        <div className="mt-2" style={{ maxHeight: 340, overflowY: 'auto' }}>
          <GiphyGrid
            key={query}
            fetchGifs={fetchGifs}
            onGifClick={gif => { onSelect(gif); onClose(); }}
            width={320}
            columns={3}
            noLink
          />
        </div>
      </div>
    </div>
  );
};

export default GiphyModal; 