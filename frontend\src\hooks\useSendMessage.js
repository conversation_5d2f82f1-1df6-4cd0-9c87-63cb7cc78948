import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../api';

export const useSendMessage = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (data) => {
            const dialogId = data instanceof FormData ? data.get('dialog') : data.dialog;
            const response = await api.post(`/api/dialogs/${dialogId}/messages/`, data, {
                headers: data instanceof FormData ? {} : {
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['messages'] });
        },
    });

    return {
        sendMessage: mutation.mutate,
        isLoading: mutation.isPending,
        error: mutation.error,
    };
}; 