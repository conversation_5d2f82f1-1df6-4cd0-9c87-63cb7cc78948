# 🇷🇺 Русифицированный Emoji Picker React

Это форк популярной библиотеки [emoji-picker-react](https://github.com/ealush/emoji-picker-react) с добавленной поддержкой русской локализации.

## ✨ Особенности

- 🇷🇺 **Полная русская локализация** - все категории и интерфейс переведены на русский язык
- 🎨 **Поддержка тем** - светлая и темная темы
- 🔍 **Поиск эмодзи** - с русскими и английскими названиями
- 📱 **Адаптивный дизайн** - работает на всех устройствах
- ⚡ **Высокая производительность** - оптимизированная отрисовка
- 🎯 **Полная совместимость** - с оригинальным API emoji-picker-react

## 🚀 Установка

```bash
# Этот форк уже включен в проект LitPortal
# Импортируйте компонент напрямую:
import { RussianEmojiPicker } from './emoji-picker-react-fork/src';
```

## 📖 Использование

### Базовое использование

```jsx
import React from 'react';
import { RussianEmojiPicker } from './emoji-picker-react-fork/src';

function MyComponent() {
  const handleEmojiClick = (emojiData) => {
    console.log('Выбран эмодзи:', emojiData.emoji);
  };

  return (
    <RussianEmojiPicker 
      onEmojiClick={handleEmojiClick}
      locale="ru" // По умолчанию русская локализация
    />
  );
}
```

### С настройками темы

```jsx
<RussianEmojiPicker 
  onEmojiClick={handleEmojiClick}
  theme="dark" // 'light' | 'dark'
  height={400}
  width={350}
/>
```

### Переключение локали

```jsx
<RussianEmojiPicker 
  onEmojiClick={handleEmojiClick}
  locale="en" // Переключение на английский
/>
```

## 🎛️ API

### Props

| Prop | Тип | По умолчанию | Описание |
|------|-----|--------------|----------|
| `onEmojiClick` | `(emojiData) => void` | - | Обработчик выбора эмодзи |
| `locale` | `'ru' \| 'en'` | `'ru'` | Язык интерфейса |
| `theme` | `'light' \| 'dark' \| 'auto'` | `'light'` | Тема оформления |
| `height` | `number \| string` | `450` | Высота пикера |
| `width` | `number \| string` | `350` | Ширина пикера |
| `autoFocusSearch` | `boolean` | `true` | Автофокус на поиске |
| `searchDisabled` | `boolean` | `false` | Отключить поиск |

### EmojiData объект

```typescript
{
  emoji: string;        // '😀'
  unified: string;      // '1f600'
  names: string[];      // ['grinning', 'grinning face']
  imageUrl: string;     // URL изображения эмодзи
  isCustom: boolean;    // false для стандартных эмодзи
}
```

## 🌍 Локализация

### Поддерживаемые языки

- 🇷🇺 **Русский** (`ru`) - полная локализация
- 🇺🇸 **Английский** (`en`) - оригинальная локализация

### Переведенные элементы

- ✅ Названия категорий
- ✅ Плейсхолдер поиска
- ✅ Сообщения об отсутствии результатов
- ✅ Подсказки навигации

## 📂 Структура проекта

```
emoji-picker-react-fork/
├── src/
│   ├── config/
│   │   ├── categoryConfig.ts    # Конфигурация категорий с локализацией
│   │   └── config.ts           # Основная конфигурация
│   ├── RussianEmojiPicker.tsx  # Русифицированный компонент
│   └── index.tsx               # Экспорты
├── test-russian-picker.html    # Демо-страница
└── README-RU.md               # Этот файл
```

## 🔧 Интеграция в LitPortal

Компонент уже интегрирован в следующие части проекта:

- `BookComments.jsx` - комментарии к книгам
- `EmojiMartModal.jsx` - модальное окно с эмодзи
- `MessageInput.js` - ввод сообщений

## 🎨 Темы

### Светлая тема
- Белый фон
- Темный текст
- Светлые границы

### Темная тема
- Темный фон
- Светлый текст
- Темные границы

## 🚀 Производительность

- Ленивая загрузка эмодзи
- Виртуализация для больших списков
- Оптимизированный поиск
- Кэширование результатов

## 🤝 Совместимость

Полностью совместим с оригинальным API `emoji-picker-react`, что позволяет легко заменить существующие компоненты.

## 📝 Лицензия

Наследует лицензию оригинального проекта emoji-picker-react.

## 🙏 Благодарности

- [emoji-picker-react](https://github.com/ealush/emoji-picker-react) - оригинальная библиотека
- Unicode Consortium - за стандарт эмодзи
- Сообщество разработчиков React
