const fs = require('fs');
const path = require('path');

// Укажи базовый путь S3 для production
const S3_BASE = 'https://storage.yandexcloud.net/lpo-test/dist/';

function generateIndex(dir, outFile, exts, s3SubPath) {
  const files = fs.readdirSync(dir)
    .filter(f => exts.some(ext => f.endsWith(ext)))
    .map(f => S3_BASE + s3SubPath + '/' + f);
  fs.writeFileSync(outFile, JSON.stringify(files, null, 2));
}

generateIndex('./public/header_presets', './public/header_presets/index.json', ['.webp'], 'header_presets');
generateIndex('./public/header_frames', './public/header_frames/index.json', ['.webp'], 'header_frames'); 