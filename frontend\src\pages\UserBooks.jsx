import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useParams, useNavigate, Outlet } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Button, Input, Card, message, Typography, Spin, Form, Radio, Space, Divider, Modal, Tooltip } from 'antd';
import { PlusOutlined, SearchOutlined, ArrowLeftOutlined, EyeOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useTheme } from '../theme/ThemeContext';
import { getBookCoverUrl, getBookCoverMiniUrl } from '../utils/bookCover';
import { useUserSettings } from '../context/UserSettingsContext';
import { formatDateWithTimezone } from '../utils/formatDate';
import { formatPublishedDate, formatUpdatedDate } from '../utils/dateHelpers';
import { csrfFetch } from '../utils/csrf';
import { useBookCounts } from '../context/BookCountsContext';
import { createSafeBookDescription } from '../utils/htmlUtils';
import '../styles/bookDescription.css';

const { Search } = Input;
const { Title, Text } = Typography;

const bookTypes = [
  {
    value: 'story',
    label: 'Рассказ',
    description: 'Небольшое произведения без разделения на главы (части)'
  },
  {
    value: 'novella',
    label: 'Повесть',
    description: 'Произведение от 0,5 до 4 АЛ ( АЛ - Авторский лист = 40 000 символов с пробелами) состоящее из нескольких глав (частей)'
  },
  {
    value: 'novel',
    label: 'Роман',
    description: 'Большая форма произведения, от 4 АЛ и более, состоит из нескольких глав (частей)'
  },
  {
    value: 'story_collection',
    label: 'Сборник рассказов',
    description: 'Состоит из нескольких частей. Важно для навигации и корректного отображения, одна часть сборника = один рассказ'
  },
  {
    value: 'poetry_collection',
    label: 'Сборник поэзии',
    description: 'Состоит из нескольких частей. Одна часть = одно стихотворение'
  }
];

const BOOK_TYPES = [
  { value: 'story', label: 'Рассказ' },
  { value: 'novella', label: 'Повесть' },
  { value: 'novel', label: 'Роман' },
  { value: 'story_collection', label: 'Сборник рассказов' },
  { value: 'poetry_collection', label: 'Сборник поэзии' },
];

const UserBooks = () => {
  const { username } = useParams();
  const { user } = useAuth();
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [books, setBooks] = useState([]);
  const [filteredBooks, setFilteredBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [userInfo, setUserInfo] = useState(null);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [bookToDelete, setBookToDelete] = useState(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState('');
  const [deleteForm] = Form.useForm();
  const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
  const [isCreating, setIsCreating] = useState(false);
  const [showAdultNotice, setShowAdultNotice] = useState(false);
  const { timezone } = useUserSettings();
  
  // Используем контекст для управления счетчиками
  const { getCounts, updateCountsFromBooks, removeBookFromCounts, updateBookStatusInCounts } = useBookCounts();

  // --- Состояния порядка для каждой категории ---
  const [finishedOrder, setFinishedOrder] = useState([]);
  const [inProgressOrder, setInProgressOrder] = useState([]);
  const [draftsOrder, setDraftsOrder] = useState([]);

  const [blocksOrder, setBlocksOrder] = React.useState(['in_progress', 'finished']);

  const isOwner = user?.username === username;

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Используем объединенный API endpoint - один запрос вместо трех
        const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
        const response = await fetch(`${backendUrl}/api/users/${username}/books-with-user-simple/`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (!response.ok) throw new Error('Failed to fetch data');

        const data = await response.json();
        
        // Устанавливаем данные пользователя
        setUserInfo(data.user);
        
        // Устанавливаем книги
        const booksArr = data.books.results || [];
        setBooks(booksArr);
        setFilteredBooks(booksArr);
        
        // Устанавливаем порядок блоков
        setBlocksOrder(data.books_blocks_order || ['in_progress', 'finished']);
        
        // Обновляем счетчики книг сразу при загрузке
        updateCountsFromBooks(username, booksArr);
      } catch (error) {
        console.error('Error fetching data:', error);
        message.error('Ошибка при загрузке данных');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [username, updateCountsFromBooks]);

  // --- Фильтрация книг: скрываем книги в статусе создания ---
  useEffect(() => {
    let visibleBooks = Array.isArray(books) ? books.filter(b => b.creation_status !== 'creating') : [];
    // Фильтрация 18+
    if (user && user.birth_date) {
      const birthDate = user.birth_date;
      let age = null;
      if (birthDate) {
        const today = new Date();
        const dob = new Date(birthDate);
        age = today.getFullYear() - dob.getFullYear();
        const m = today.getMonth() - dob.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
          age--;
        }
      }
      let filtered = visibleBooks;
      let hasAdult = false;
      if (!birthDate || age < 18) {
        filtered = filtered.filter(b => !b.is_adult);
        hasAdult = filtered.some(b => b.is_adult);
      }
      setFilteredBooks(filtered);
      setShowAdultNotice(!birthDate || age < 18 ? hasAdult : false);
    } else {
      setFilteredBooks(visibleBooks);
      setShowAdultNotice(false);
    }
  }, [books, user]);

  const handleSearch = useCallback((value) => {
    if (!value.trim()) {
      setFilteredBooks(books);
      return;
    }

    const searchTerm = value.toLowerCase().trim();
    const filtered = books.filter(book => {
      // Поиск по названию
      if (book.title.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // Поиск по описанию
      if (book.description && book.description.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // Поиск по жанрам
      if (book.genres && book.genres.some(genre =>
        genre.name.toLowerCase().includes(searchTerm)
      )) {
        return true;
      }

      // Поиск по хештегам (включая поиск без символа #)
      if (book.hashtags && book.hashtags.some(hashtag => {
        const hashtagName = hashtag.name.toLowerCase();
        return hashtagName.includes(searchTerm) ||
               hashtagName.includes(searchTerm.replace(/^#/, '')) ||
               searchTerm.includes(hashtagName);
      })) {
        return true;
      }

      return false;
    });
    setFilteredBooks(filtered);
  }, [books]);

  const handleCreateBook = useCallback(async () => {
    setIsCreating(true);
    try {
      const response = await csrfFetch('/api/books/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({}), // только автор, остальные поля пустые
      });
      if (!response.ok) throw new Error('Ошибка при создании книги');
      const data = await response.json();
      if (!data.id) throw new Error('Некорректный ответ от сервера');
      navigate(`/lpu/${username}/books/${data.id}/edit`);
    } catch (error) {
      message.error(error.message || 'Ошибка при создании книги');
    } finally {
      setIsCreating(false);
    }
  }, [navigate, username]);

  const handleBackToList = () => {
    // This function is no longer used
  };

  const handleDeleteClick = (e, book) => {
    e.stopPropagation();
    setBookToDelete(book);
    setIsDeleteModalVisible(true);
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalVisible(false);
    setBookToDelete(null);
    setDeleteConfirmation('');
    deleteForm.resetFields();
  };

  const handleDeleteConfirm = async () => {
    if (!bookToDelete) return;

    try {
      const csrfToken = getCookie('csrftoken');
      if (!csrfToken) {
        throw new Error('CSRF token not found');
      }

      const response = await fetch(`/api/books/${bookToDelete.id}/`, {
        method: 'DELETE',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete book');
      }

      // Удаляем книгу из состояния локально
      const updatedBooks = books.filter(book => book.id !== bookToDelete.id);
      setBooks(updatedBooks);
      // filteredBooks обновится автоматически через useEffect
      
      // Обновляем счетчики мгновенно
      removeBookFromCounts(username, bookToDelete);
      
      message.success('Произведение успешно удалено');
      handleDeleteCancel();
    } catch (error) {
      console.error('Delete error:', error);
      message.error('Ошибка при удалении произведения');
    }
  };

  // --- Мемоизированная фильтрация книг по статусу ---
  const categorizedBooks = useMemo(() => {
    const visibleBooks = filteredBooks.filter(book => book.creation_status !== 'creating');
    
    return {
      fullyPublished: visibleBooks.filter(book => book.status === 'finished'),
      partiallyPublished: visibleBooks.filter(book => book.status === 'in_progress'),
      drafts: visibleBooks.filter(book => book.status === 'draft')
    };
  }, [filteredBooks]);

  const { fullyPublished: fullyPublishedBooks, partiallyPublished: partiallyPublishedBooks, drafts: draftBooks } = categorizedBooks;

  // --- Новый статус ---
  const renderBookStatus = (book) => {
    if (book.status === 'draft') {
      return (
        <div className="text-orange-600 dark:text-orange-400 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Черновик
        </div>
      );
    } else if (book.status === 'in_progress') {
      return (
        <div className="text-blue-600 dark:text-blue-400">
          <div className="flex items-center">
            <EditOutlined style={{ color: theme === 'dark' ? '#60A5FA' : '#2563eb', fontSize: 15, marginRight: 4 }} /> В процессе публикации
          </div>
          {book.updated_at && (
            <div className="text-xs mt-1" style={{ color: theme === 'dark' ? '#9ca3af' : '#6b7280' }}>
              {formatUpdatedDate(book.updated_at, timezone)}
            </div>
          )}
        </div>
      );
    } else if (book.status === 'finished') {
      return (
        <div className="text-green-600 dark:text-green-400">
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            Завершено
          </div>
          {book.published_at && (
            <div className="text-xs mt-1" style={{ color: theme === 'dark' ? '#9ca3af' : '#6b7280' }}>
              {formatPublishedDate(book.published_at, timezone)}
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  // --- PATCH-запрос для смены статуса книги ---
  const updateBookStatus = async (book, newStatus) => {
    try {
      const res = await csrfFetch(`/api/books/${book.id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(newStatus),
      });
      if (!res.ok) throw new Error();
      
      // Обновляем книгу в состоянии локально
      const updatedBooks = books.map(b => 
        b.id === book.id ? { ...b, ...newStatus } : b
      );
      setBooks(updatedBooks);
      // filteredBooks обновится автоматически через useEffect
      
      // Обновляем счетчики мгновенно
      updateBookStatusInCounts(username, book, newStatus);
      
      message.success('Статус произведения обновлён');
    } catch {
      message.error('Ошибка при обновлении статуса');
    }
  };

  // --- Кнопки управления публикацией ---
  const renderStatusActions = (book) => {
    if (!isOwner) return null;
    // Черновик
    if (book.status === 'draft') {
      return (
        <div className="flex gap-3 mt-2">
          <button
            className="flex items-center gap-1 text-blue-600 dark:text-blue-400 font-medium hover:underline"
            onClick={() => updateBookStatus(book, { status: 'in_progress' })}
          >
            {/* Перо */}
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 01-8 0M12 11v6m0 0l-2-2m2 2l2-2" />
            </svg>
            Опубликовать частично
          </button>
          <button
            className="flex items-center gap-1 text-green-600 dark:text-green-400 font-medium hover:underline"
            onClick={() => updateBookStatus(book, { status: 'finished' })}
          >
            {/* Галочка */}
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            Завершить
          </button>
        </div>
      );
    }
    // В процессе публикации
    if (book.status === 'in_progress') {
      return (
        <div className="flex gap-3 mt-2">
          <button
            className="flex items-center gap-1 text-orange-600 dark:text-orange-400 font-medium hover:underline"
            onClick={() => updateBookStatus(book, { status: 'draft' })}
          >
            {/* Часы */}
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Убрать в черновики
          </button>
          <button
            className="flex items-center gap-1 text-green-600 dark:text-green-400 font-medium hover:underline"
            onClick={() => updateBookStatus(book, { status: 'finished' })}
          >
            {/* Галочка */}
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            Завершить
          </button>
        </div>
      );
    }
    // Завершено
    if (book.status === 'finished') {
      return (
        <div className="flex gap-3 mt-2">
          <button
            className="flex items-center gap-1 text-blue-600 dark:text-blue-400 font-medium hover:underline"
            onClick={() => updateBookStatus(book, { status: 'in_progress' })}
          >
            {/* Перо */}
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 01-8 0M12 11v6m0 0l-2-2m2 2l2-2" />
            </svg>
            В режим редактирования
          </button>
          <button
            className="flex items-center gap-1 text-orange-600 dark:text-orange-400 font-medium hover:underline"
            onClick={() => updateBookStatus(book, { status: 'draft' })}
          >
            {/* Часы */}
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Убрать в черновики
          </button>
        </div>
      );
    }
    return null;
  };

  // --- Компонент карточки книги ---
  function BookCard({ book, index, booksInCategory, isOwner, onMove, onSetPosition, globalIndex }) {
    const typeLabel = BOOK_TYPES.find(t => t.value === book.type)?.label || book.type;
    const genres = Array.isArray(book.genres) ? book.genres.map(g => g.name).join(', ') : '';
    const descriptionData = book.description ? createSafeBookDescription(book.description, 400) : { html: '', isTruncated: false };
    const isFirst = index === 0;
    const isLast = index === booksInCategory.length - 1;
    const [editPos, setEditPos] = React.useState(false);
    const [inputValue, setInputValue] = React.useState(index + 1);
    const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
    // --- Кнопки управления ---
    return (
      <div
        key={book.id}
        className={`w-full ${isOwner ? 'max-w-[885px] mx-auto' : ''} bg-white dark:bg-gray-800 rounded-lg shadow p-3 flex items-start gap-4 mb-4 transition hover:shadow-lg relative`}
      >
        {/* Абсолютный блок управления порядком */}
        <div
          className="absolute right-4 top-4 flex items-center gap-2 z-10"
        >
          {isOwner ? (
            <>
              {booksInCategory.length > 1 && (
                <button
                  className={`p-1 rounded-full transition disabled:opacity-40 ${theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'} ${isFirst ? 'cursor-not-allowed' : 'hover:bg-blue-200 hover:text-blue-700'}`}
                  disabled={isFirst}
                  onClick={() => onMove(index, index - 1)}
                  title="Переместить вверх"
                  style={{ minWidth: 32, minHeight: 32, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                >
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 5L10 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                    <path d="M6 9L10 5L14 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              )}
              {editPos ? (
                <input
                  type="number"
                  min={1}
                  max={booksInCategory.length}
                  value={inputValue}
                  onChange={e => setInputValue(Number(e.target.value))}
                  onBlur={() => { setEditPos(false); onSetPosition(index, inputValue - 1); }}
                  onKeyDown={e => {
                    if (e.key === 'Enter') { setEditPos(false); onSetPosition(index, inputValue - 1); }
                  }}
                  className={`w-10 text-center border rounded ${theme === 'dark' ? 'bg-gray-800 text-white border-gray-600' : 'bg-white text-gray-900 border-gray-300'}`}
                  style={{ margin: '0 4px' }}
                />
              ) : (
                <span
                  className={`font-bold text-base select-none px-2 py-1 rounded ${theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-900'}`}
                  title={booksInCategory.length > 1 ? "Изменить позицию" : "Позиция"}
                  onClick={() => booksInCategory.length > 1 && setEditPos(true)}
                  style={{ 
                    color: booksInCategory.length > 1 ? (theme === 'dark' ? '#fff' : '#2563eb') : '#888',
                    cursor: booksInCategory.length > 1 ? 'pointer' : 'default'
                  }}
                >{globalIndex + 1}</span>
              )}
              {booksInCategory.length > 1 && (
                <button
                  className={`p-1 rounded-full transition disabled:opacity-40 ${theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'} ${isLast ? 'cursor-not-allowed' : 'hover:bg-blue-200 hover:text-blue-700'}`}
                  disabled={isLast}
                  onClick={() => onMove(index, index + 1)}
                  title="Переместить вниз"
                  style={{ minWidth: 32, minHeight: 32, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                >
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 15L10 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                    <path d="M14 11L10 15L6 11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              )}
            </>
          ) : (
            <span className={`font-bold text-base px-2 py-1 rounded ${theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-900'}`}>{globalIndex + 1}</span>
          )}
        </div>
        {/* Блок с обложкой */}
        <div className="flex flex-col items-center flex-shrink-0" style={{ width: 180 }}>
          <div className="relative group cursor-pointer" style={{ width: 150, height: 210 }} onClick={(e) => handleBookClick(e, book.id)}>
            {/* Основная обложка книги */}
            <div className="relative h-full bg-gray-200 dark:bg-gray-700 overflow-hidden shadow-lg transform transition-transform duration-300 group-hover:scale-105" 
                 style={{
                   borderRadius: '0 8px 8px 0'
                 }}>
              <img
                alt={book.title}
                src={book.cover_mini_url || book.cover_temp_url || `${backendUrl}/media/covertemp/covertemp.jpg`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.src = `${backendUrl}/media/covertemp/covertemp.jpg`;
                }}
              />
              
              {/* Корешок книги - левая полоска */}
              <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-r from-black/30 via-black/10 to-transparent"></div>
              
              {/* Дополнительная тень для глубины */}
              <div className="absolute left-1 top-0 bottom-0 w-1 bg-gradient-to-r from-black/20 to-transparent"></div>
              
              {/* Блик на обложке */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            
            {/* Тень под книгой */}
            <div className="absolute -bottom-1 left-1 right-2 h-1 bg-black/20 rounded-full blur-sm transform transition-transform duration-300 group-hover:scale-110"></div>
          </div>
          <div className="flex items-center justify-center gap-2 text-gray-400 dark:text-gray-500 text-xs mt-1">
            <Tooltip title="Просмотры" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
              <span className="flex items-center gap-1 cursor-default">
                <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                  <circle cx="12" cy="12" r="3"/>
                </svg>
                {book.views_count || 0}
              </span>
            </Tooltip>
            <Tooltip title="Лайки" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
              <span className="flex items-center gap-1 cursor-default">
                <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path d="M4.318 6.318a4.5 4.5 0 0 1 6.364 0L12 7.636l1.318-1.318a4.5 4.5 0 1 1 6.364 6.364L12 21.682l-7.682-7.682a4.5 4.5 0 0 1 0-6.364z"/>
                </svg>
                {book.likes_count || 0}
              </span>
            </Tooltip>
            <Tooltip title="В библиотеках" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
              <span className="flex items-center gap-1 cursor-default">
                <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                  <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
                </svg>
                {book.library_count || 0}
              </span>
            </Tooltip>
            <Tooltip title="Комментарии" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
              <span className="flex items-center gap-1 cursor-default">
                <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                {book.comments_count || 0}
              </span>
            </Tooltip>
          </div>
        </div>
        {/* Блок с инфо и кнопками */}
        <div className="flex-1 min-w-0 flex flex-col">
          <div 
            className="text-lg font-bold text-gray-900 dark:text-white break-words leading-tight cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200" 
            onClick={(e) => handleBookClick(e, book.id)}
          >
            {book.title}
          </div>
          <div className="text-xs mt-1 mb-1" style={{ color: book.status === 'finished' ? '#22c55e' : (book.status === 'in_progress' ? '#2563eb' : '#f59e42') }}>{renderBookStatus(book)}</div>
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-300 mb-1">
            <span>{typeLabel}</span>
            {genres && <span className="mx-2 text-gray-300 dark:text-gray-600">|</span>}
            {genres && <span><span className="font-semibold">Жанры:</span> {genres}</span>}
          </div>
          {descriptionData.html && (
            <div
              className="book-description text-sm text-gray-700 dark:text-gray-300 mt-1 mb-1 line-clamp-4"
              dangerouslySetInnerHTML={{ __html: descriptionData.html }}
            />
          )}
          <div className="flex gap-2 mt-2 flex-wrap">
            {(book.status === 'in_progress' || book.status === 'finished') && (
              <Button
                size="small"
                icon={<EyeOutlined />}
                onClick={(e) => handleViewBook(e, book.id)}
                className="bg-blue-50 dark:bg-gray-900 text-blue-600 dark:text-blue-300 border-blue-200 dark:border-gray-700 hover:bg-blue-100 dark:hover:bg-gray-700"
              >
                Подробнее
              </Button>
            )}
            {isOwner && (
              <>
                <Button
                  size="small"
                  icon={<EditOutlined />}
                  onClick={(e) => handleEditBook(e, book.id)}
                  className="bg-green-50 dark:bg-gray-900 text-green-600 dark:text-green-300 border-green-200 dark:border-gray-700 hover:bg-green-100 dark:hover:bg-gray-700"
                >
                  Редактировать
                </Button>
                <Button
                  size="small"
                  icon={<DeleteOutlined />}
                  danger
                  onClick={(e) => handleDeleteClick(e, book)}
                  className="bg-red-50 dark:bg-gray-900 text-red-600 dark:text-red-300 border-red-200 dark:border-gray-700 hover:bg-red-100 dark:hover:bg-gray-700"
                >
                  Удалить
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  // --- Модалка подтверждения возраста ---
  const [ageConfirmVisible, setAgeConfirmVisible] = useState(false);
  const [pendingBookId, setPendingBookId] = useState(null);

  const handleViewBook = (e, bookId) => {
    e.stopPropagation();
    const book = filteredBooks.find(b => b.id === bookId);
    // Если книга 18+ и (гость или нет даты рождения)
    if (book?.is_adult && (!user || !user.birth_date)) {
      // Проверяем, подтверждал ли пользователь возраст ранее
      if (window.sessionStorage.getItem('adultConfirmed') === 'true') {
        navigate(`/book/${bookId}`);
      } else {
        setPendingBookId(bookId);
        setAgeConfirmVisible(true);
      }
      return;
    }
    navigate(`/book/${bookId}`);
  };

  const handleAgeConfirm = () => {
    window.sessionStorage.setItem('adultConfirmed', 'true');
    setAgeConfirmVisible(false);
    if (pendingBookId) {
      navigate(`/book/${pendingBookId}`);
      setPendingBookId(null);
    }
  };

  const handleAgeDecline = () => {
    setAgeConfirmVisible(false);
    setPendingBookId(null);
  };

  const handleEditBook = (e, bookId) => {
    e.stopPropagation();
    navigate(`/lpu/${username}/books/${bookId}/edit`);
  };

  const handleBookClick = (e, bookId) => {
    e.stopPropagation();
    const book = filteredBooks.find(b => b.id === bookId);
    // Если книга 18+ и (гость или нет даты рождения)
    if (book?.is_adult && (!user || !user.birth_date)) {
      // Проверяем, подтверждал ли пользователь возраст ранее
      if (window.sessionStorage.getItem('adultConfirmed') === 'true') {
        navigate(`/book/${bookId}`);
      } else {
        setPendingBookId(bookId);
        setAgeConfirmVisible(true);
      }
      return;
    }
    navigate(`/book/${bookId}`);
  };

  // Вспомогательная функция для разных сообщений
  const getEmptyMessage = (isOwner) => {
    if (isOwner) {
      return 'У вас пока нет книг. Начните создавать свои произведения!';
    }
    // Рандомное сообщение для гостей
    const guestEmptyMessages = [
      'Автор договаривается со своей музой... Ещё немного и будет результат!',
      'Нет, это не ошибка загрузки) Просто автор в процессе создания шедевра!',
      'Если вы не видите книг на этой странице, то это еще не значит, что их нет у автора. Он просто пока сомневается.',
      'А у вас много книг написано? Покажите пример, как надо!',
      'Музы они такие... Ходят где-то, а к кому-то редко заглядывают!'
    ];
    return guestEmptyMessages[Math.floor(Math.random() * guestEmptyMessages.length)];
  };

  // --- Синхронизация порядка с сервером при загрузке ---
  useEffect(() => {
    setFinishedOrder(
      fullyPublishedBooks
        .slice()
        .sort((a, b) => (a.position_finished || 0) - (b.position_finished || 0))
        .map(b => b.id)
    );
    setInProgressOrder(
      partiallyPublishedBooks
        .slice()
        .sort((a, b) => (a.position_in_progress || 0) - (b.position_in_progress || 0))
        .map(b => b.id)
    );
    setDraftsOrder(
      draftBooks
        .slice()
        .sort((a, b) => (a.position_draft || 0) - (b.position_draft || 0))
        .map(b => b.id)
    );
  }, [filteredBooks]);

  // --- Универсальная функция сохранения порядка ---
  const saveOrder = async (category, order) => {
    try {
      const csrfToken = getCookie('csrftoken');
      const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      const res = await fetch(`${backendUrl}/api/users/${username}/books/reorder/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({ category, order }),
      });
      if (!res.ok) throw new Error();
      // message.success('Порядок сохранён'); // Можно раскомментировать для уведомления
    } catch {
      message.error('Ошибка при сохранении порядка');
    }
  };

  // --- Функции перемещения и установки позиции ---
  const handleMove = (category, order, setOrder) => (from, to) => {
    if (to < 0 || to >= order.length) return;
    const newOrder = [...order];
    const [moved] = newOrder.splice(from, 1);
    newOrder.splice(to, 0, moved);
    setOrder(newOrder);
    saveOrder(category, newOrder);
  };
  const handleSetPosition = (category, order, setOrder) => (from, to) => {
    if (to < 0 || to >= order.length) return;
    const newOrder = [...order];
    const [moved] = newOrder.splice(from, 1);
    newOrder.splice(to, 0, moved);
    setOrder(newOrder);
    saveOrder(category, newOrder);
  };

  // --- Получение книг в нужном порядке ---
  const getOrderedBooks = (books, order) => {
    if (!order || order.length === 0) return books;
    const idToBook = Object.fromEntries(books.map(b => [b.id, b]));
    return order.map(id => idToBook[id]).filter(Boolean);
  };

  // --- Счетчики книг для профиля ---
  // Используем только книги, не находящиеся в статусе создания
  const visibleBooks = React.useMemo(() => Array.isArray(books) ? books.filter(b => b.creation_status !== 'creating') : [], [books]);

  // Получаем актуальные счетчики из контекста
  const bookCounts = getCounts(username);

  // Порядок блоков теперь приходит с основными данными, дополнительный запрос не нужен

  // Смена порядка блоков (только для автора)
  const swapBlocks = () => {
    if (!isOwner) return;
    const newOrder = [...blocksOrder].reverse();
    setBlocksOrder(newOrder);
    // PATCH на сервер
    const csrfToken = getCookie('csrftoken');
    const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
    fetch(`${backendUrl}/api/users/${username}/books-blocks-order/`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrfToken,
      },
      credentials: 'include',
      body: JSON.stringify({ order: newOrder }),
    })
      .then(res => {
        if (!res.ok) throw new Error();
        return res.json();
      })
      .then(() => message.success('Порядок блоков сохранён'))
      .catch(() => message.error('Ошибка при сохранении порядка блоков'));
  };

  if (loading) {
    return (
      <div className="p-5 max-w-[1200px] mx-auto">
        <div className="text-center py-[50px]">
          <Spin size="large" />
        </div>
      </div>
    );
  }

  // Если нет ни одной видимой книги, показываем пустое состояние
  if (visibleBooks.length === 0) {
    return (
      <div className="p-5 max-w-[1200px] mx-auto">
        <Outlet />
        <>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white m-0">
              {userInfo
                ? `Автор книг: ${userInfo.display_name || userInfo.username}`
                : 'Автор книг: ...'}
            </h2>
            {isOwner && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                onClick={handleCreateBook}
                className="ml-4"
              >
                Добавить книгу
              </Button>
            )}
          </div>
          <p className="text-base mt-2 text-gray-500 dark:text-gray-400">
            {getEmptyMessage(isOwner)}
          </p>
        </>
      </div>
    );
  }

  return (
    <div className="p-5 max-w-[1200px] mx-auto">
      <Outlet />
      <>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white m-0">
            {userInfo
              ? `Автор книг: ${userInfo.display_name || userInfo.username}`
              : 'Автор книг: ...'}
          </h2>
          {isOwner && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="large"
              onClick={handleCreateBook}
              className="ml-4"
            >
              Добавить книгу
            </Button>
          )}
        </div>
        {showAdultNotice && (
          <div className="mb-4 text-sm text-yellow-700 dark:text-yellow-300 bg-yellow-50 dark:bg-yellow-900 rounded p-3">
            Некоторые произведения скрыты, так как доступны только пользователям старше 18 лет.
          </div>
        )}

        {books.length === 0 ? (
          <p className="text-base mt-2 text-gray-500 dark:text-gray-400">
            {getEmptyMessage(isOwner)}
          </p>
        ) : (
          <>
            <div className="max-w-[500px] mb-5">
              <Search
                placeholder="Поиск по названию, описанию, жанру или хештегу..."
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onChange={(e) => handleSearch(e.target.value)}
                className="bg-white dark:bg-gray-800"
              />
            </div>

            {blocksOrder.map((block, idx) => {
              // Определяем есть ли книги в текущем блоке
              const currentBooks = block === 'in_progress' ? partiallyPublishedBooks : fullyPublishedBooks;
              const hasBooks = currentBooks.length > 0;
              
              // Для автора: скрываем пустые блоки
              if (isOwner && !hasBooks) {
                return null;
              }
              
              // Показываем стрелочки только если есть книги в обоих блоках
              const hasInProgressBooks = partiallyPublishedBooks.length > 0;
              const hasFinishedBooks = fullyPublishedBooks.length > 0;
              const showSwapButtons = isOwner && hasInProgressBooks && hasFinishedBooks;
              
              // Вычисляем глобальный offset для этого блока
              let globalOffset = 0;
              for (let i = 0; i < idx; i++) {
                const prevBlock = blocksOrder[i];
                if (prevBlock === 'in_progress') {
                  globalOffset += partiallyPublishedBooks.length;
                } else {
                  globalOffset += fullyPublishedBooks.length;
                }
              }
              
              return (
                <div key={block} className="mb-8">
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    {isOwner && (
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white m-0 pr-2">
                        {block === 'in_progress' ? 'В процессе публикации' : 'Завершенные произведения'}
                      </h2>
                    )}
                    {isOwner && showSwapButtons && idx === 0 && (
                      <button
                        onClick={swapBlocks}
                        title="Переместить вниз"
                        className={`p-1 rounded-full transition disabled:opacity-40 ${theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'} hover:bg-blue-200 hover:text-blue-700`}
                        style={{ minWidth: 32, minHeight: 32, display: 'flex', alignItems: 'center', justifyContent: 'center', marginLeft: 4 }}
                      >
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M10 15L10 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                          <path d="M14 11L10 15L6 11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </button>
                    )}
                    {isOwner && showSwapButtons && idx === 1 && (
                      <button
                        onClick={swapBlocks}
                        title="Переместить вверх"
                        className={`p-1 rounded-full transition disabled:opacity-40 ${theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'} hover:bg-blue-200 hover:text-blue-700`}
                        style={{ minWidth: 32, minHeight: 32, display: 'flex', alignItems: 'center', justifyContent: 'center', marginLeft: 4 }}
                      >
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M10 5L10 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                          <path d="M6 9L10 5L14 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </button>
                    )}
                  </div>
                {block === 'in_progress' ? (
                  inProgressOrder.length > 0 ? (
                    <div>
                      {getOrderedBooks(partiallyPublishedBooks, inProgressOrder).map((book, index) => <BookCard
                        key={book.id}
                        book={book}
                        index={index}
                        booksInCategory={getOrderedBooks(partiallyPublishedBooks, inProgressOrder)}
                        isOwner={isOwner}
                        onMove={handleMove('in_progress', inProgressOrder, setInProgressOrder)}
                        onSetPosition={handleSetPosition('in_progress', inProgressOrder, setInProgressOrder)}
                        globalIndex={globalOffset + index}
                      />)}
                    </div>
                  ) : (
                    isOwner && (
                      <p className="text-base text-gray-500 dark:text-gray-400">
                        Здесь будут располагаться ваши произведения, которые вы опубликовали частично.
                      </p>
                    )
                  )
                ) : (
                  finishedOrder.length > 0 ? (
                    <div>
                      {getOrderedBooks(fullyPublishedBooks, finishedOrder).map((book, index) => <BookCard
                        key={book.id}
                        book={book}
                        index={index}
                        booksInCategory={getOrderedBooks(fullyPublishedBooks, finishedOrder)}
                        isOwner={isOwner}
                        onMove={handleMove('finished', finishedOrder, setFinishedOrder)}
                        onSetPosition={handleSetPosition('finished', finishedOrder, setFinishedOrder)}
                        globalIndex={globalOffset + index}
                      />)}
                    </div>
                  ) : (
                    isOwner && (
                      <p className="text-base text-gray-500 dark:text-gray-400">
                        У вас пока нет завершенных книг, но есть черновики или произведения в процессе публикации!
                      </p>
                    )
                  )
                )}
              </div>
                );
              })}
            {/* Черновики всегда в конце */}
            {isOwner && draftBooks.length > 0 && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white m-0 pr-2">
                  Черновики <span className="italic font-normal align-baseline" style={{ fontSize: '1rem' }}>(только для автора)</span>
                </h2>
                {draftsOrder.length > 0 ? (
                  <div>
                    {getOrderedBooks(draftBooks, draftsOrder).map((book, index) => {
                      // Вычисляем глобальный offset для черновиков
                      let draftsGlobalOffset = 0;
                      for (const block of blocksOrder) {
                        if (block === 'in_progress') {
                          draftsGlobalOffset += partiallyPublishedBooks.length;
                        } else {
                          draftsGlobalOffset += fullyPublishedBooks.length;
                        }
                      }
                      
                      return <BookCard
                        key={book.id}
                        book={book}
                        index={index}
                        booksInCategory={getOrderedBooks(draftBooks, draftsOrder)}
                        isOwner={isOwner}
                        onMove={handleMove('drafts', draftsOrder, setDraftsOrder)}
                        onSetPosition={handleSetPosition('drafts', draftsOrder, setDraftsOrder)}
                        globalIndex={draftsGlobalOffset + index}
                      />
                    })}
                  </div>
                ) : (
                  <p className="text-base text-gray-500 dark:text-gray-400">
                    У вас пока нет черновиков
                  </p>
                )}
              </div>
            )}
          </>
        )}
      </>

      <Modal
        open={isDeleteModalVisible}
        onCancel={handleDeleteCancel}
        footer={null}
        className={theme === 'dark' ? 'dark-modal' : ''}
        styles={{
          content: {
            background: theme === 'dark' ? '#23272f' : '#ffffff',
            borderRadius: '12px',
            padding: '24px',
          },
          mask: {
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        <div className="mb-6">
          <h3 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Подтверждение удаления
          </h3>
          <p className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>
            Вы точно уверены, что хотите удалить произведение: <strong className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>{bookToDelete?.title}</strong>?
          </p>
          <p className="text-red-600 dark:text-red-400 mt-2">
            Операция необратима.
          </p>
        </div>
        <Form
          form={deleteForm}
          onFinish={handleDeleteConfirm}
          layout="vertical"
        >
          <Form.Item
            name="confirmation"
            rules={[
              { required: true, message: 'Пожалуйста, введите слово "удалить"' },
              {
                validator: (_, value) =>
                  value === 'удалить'
                    ? Promise.resolve()
                    : Promise.reject(new Error('Введите слово "удалить" для подтверждения')),
              },
            ]}
          >
            <Input
              placeholder="Введите слово 'удалить' для подтверждения"
              value={deleteConfirmation}
              onChange={(e) => setDeleteConfirmation(e.target.value)}
              className={theme === 'dark' ? 'bg-[#23272f] text-white border-[#4b5563] placeholder-[#9ca3af]' : 'bg-white text-gray-900 border-gray-300 placeholder-gray-400'}
              style={theme === 'dark' ? { borderColor: '#4b5563', color: '#fff', background: '#23272f' } : {}}
            />
          </Form.Item>
          <div className="flex justify-end space-x-4 mt-4">
            <Button 
              onClick={handleDeleteCancel}
              className={theme === 'dark' ? 'bg-[#23272f] text-white border-[#4b5563] hover:bg-[#374151]' : 'bg-white text-gray-900 border-gray-300 hover:bg-gray-100'}
              style={theme === 'dark' ? { borderColor: '#4b5563', color: '#fff', background: '#23272f' } : {}}
            >
              Отмена
            </Button>
            <Button
              type="primary"
              danger
              onClick={handleDeleteConfirm}
              disabled={deleteConfirmation !== 'удалить'}
              className={theme === 'dark' ? 'bg-red-600 hover:bg-red-700 border-none text-white' : 'bg-red-600 hover:bg-red-700 border-none text-white'}
            >
              Подтверждаю удаление
            </Button>
          </div>
        </Form>
      </Modal>

      <Modal
        title={<span className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>Подтверждение возраста</span>}
        open={ageConfirmVisible}
        onCancel={handleAgeDecline}
        footer={null}
        className={theme === 'dark' ? 'dark-modal' : ''}
        styles={{
          header: {
            background: theme === 'dark' ? '#23272f' : '#ffffff',
            borderBottom: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`,
          },
          content: {
            background: theme === 'dark' ? '#23272f' : '#ffffff',
          },
          mask: {
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        <div className="mb-4">
          <p className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>
            Для просмотра этого контента необходимо подтвердить, что вам исполнилось 18 лет.
          </p>
        </div>
        <div className="flex justify-end space-x-4 mt-4">
          <Button
            type="primary"
            onClick={handleAgeConfirm}
            className={theme === 'dark' ? 'bg-green-600 hover:bg-green-700 border-none text-white' : 'bg-green-600 hover:bg-green-700 border-none text-white'}
          >
            Да, мне исполнилось 18 лет
          </Button>
          <Button
            danger
            onClick={handleAgeDecline}
            className={theme === 'dark' ? 'bg-red-600 hover:bg-red-700 border-none text-white' : 'bg-red-600 hover:bg-red-700 border-none text-white'}
          >
            Нет, мне меньше 18 лет
          </Button>
        </div>
      </Modal>

      {/* Кастомные стили для модалки в dark mode */}
      <style>{`
        .dark-modal .ant-modal-content {
          background: #23272f !important;
          border-radius: 12px !important;
          border: none !important;
        }
        .dark-modal .ant-modal-close-x {
          color: #fff !important;
        }
        .ant-modal-close {
          top: 8px !important;
          right: 8px !important;
        }
        .ant-modal-close .ant-modal-close-x {
          font-size: 16px !important;
          line-height: 1 !important;
        }
        .ant-modal-content {
          border-radius: 12px !important;
          overflow: hidden;
        }
        .annotation-preview {
          line-height: 1.8;
        }
        .annotation-preview p {
          margin-top: 0;
          margin-bottom: 2em;
        }
      `}</style>
    </div>
  );
};

// Вспомогательная функция для получения CSRF токена
function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

export default UserBooks; 