import os
from django.db import models
from django.conf import settings
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from users.models import get_user_media_path, get_group_folder, random_prefix
from PIL import Image, ImageDraw, ImageFont, ImageFilter
import textwrap
from django.core.exceptions import ValidationError
from django.db.models import Max
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
import boto3
from users.storage_backends import PublicMediaStorage, PrivateMediaStorage
import logging

logger = logging.getLogger(__name__)

# --- Новые upload_to для книг ---
def book_cover_path(instance, filename):
    book_id = instance.id
    group_folder = get_group_folder(book_id)
    rand = random_prefix()
    return f"book_covers/{group_folder}/{book_id}/{book_id}_cover_{rand}.jpg"

def book_covermini_path(instance, filename):
    book_id = instance.id
    group_folder = get_group_folder(book_id)
    rand = random_prefix()
    # Используем префикс mini_ для отличия от editor_ файлов
    return f"book_covers/{group_folder}/{book_id}/{book_id}_mini_{rand}.jpg"

def book_cover_editor_path(instance, filename):
    book_id = instance.id
    group_folder = get_group_folder(book_id)
    rand = random_prefix()

    # Проверяем есть ли временный суффикс текста
    if hasattr(instance, '_temp_text_suffix') and instance._temp_text_suffix:
        # Суффикс идет ПЕРЕД editor, чтобы Django не затер его
        path = f"book_covers/{group_folder}/{book_id}/{book_id}_{instance._temp_text_suffix}_editor_{rand}.jpg"
        logger.info(f"Generated editor path WITH temp suffix '{instance._temp_text_suffix}': {path}")
        return path
    else:
        # Обычная обложка без текста
        path = f"book_covers/{group_folder}/{book_id}/{book_id}_editor_{rand}.jpg"
        logger.info(f"Generated editor path WITHOUT suffix: {path}")
        return path

def book_cover_editor_path_with_suffix(instance, filename, text_suffix=None):
    """Генерирует путь для обложки редактора с суффиксом текста"""
    book_id = instance.id
    group_folder = get_group_folder(book_id)
    rand = random_prefix()

    if text_suffix:
        # Для обложек с наложением текста: txtw (светлый) или txtb (темный)
        # Суффикс идет ПЕРЕД editor, чтобы Django не затер его
        path = f"book_covers/{group_folder}/{book_id}/{book_id}_{text_suffix}_editor_{rand}.jpg"
        logger.info(f"Generated editor path WITH suffix '{text_suffix}': {path}")
        return path
    else:
        # Обычная обложка без текста
        path = f"book_covers/{group_folder}/{book_id}/{book_id}_editor_{rand}.jpg"
        logger.info(f"Generated editor path WITHOUT suffix: {path}")
        return path

def book_pic_path(instance, filename):
    book_id = instance.book.id
    group_folder = get_group_folder(book_id)
    chapter = instance.chapter_number if hasattr(instance, 'chapter_number') else 0
    rand = random_prefix()
    return f"book_pics/{group_folder}/{book_id}/{chapter}/{book_id}_{rand}.jpg"

def create_cover_thumbnail_from_file(image_file, mini_path):
    from PIL import Image
    import io
    image = Image.open(image_file)
    image = image.convert('RGB')
    image = image.resize((210, 300), Image.LANCZOS)
    buf = io.BytesIO()
    image.save(buf, format='JPEG', quality=100)
    buf.seek(0)
    if default_storage.exists(mini_path):
        default_storage.delete(mini_path)
    default_storage.save(mini_path, ContentFile(buf.getvalue()))

class Genre(models.Model):
    name = models.CharField(max_length=100, unique=True)
    parent = models.ForeignKey('self', null=True, blank=True, related_name='subgenres', on_delete=models.CASCADE)

    def __str__(self):
        return self.name

class Hashtag(models.Model):
    name = models.CharField(max_length=50, unique=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"#{self.name}"

class Book(models.Model):
    BOOK_TYPES = [
        ('story', 'Рассказ'),
        ('novella', 'Повесть'),
        ('novel', 'Роман'),
        ('story_collection', 'Сборник рассказов'),
        ('poetry_collection', 'Сборник поэзии'),
    ]
    COVER_TYPES = [
        ('generated', 'Сгенерированная'),
        ('custom', 'Пользовательская'),
        ('system', 'Системная'),
    ]
    CREATION_STATUS = [
        ('creating', 'В процессе создания'),
        ('draft', 'Черновик'),
        ('published', 'Опубликована'),
    ]
    # Новые статусы книги
    BOOK_STATUS = [
        ('draft', 'Черновик'),
        ('in_progress', 'В процессе публикации'),
        ('finished', 'Завершено'),
    ]
    # Возрастные ограничения
    AGE_RATINGS = [
        ('0+', '0+'),
        ('6+', '6+'),
        ('12+', '12+'),
        ('16+', '16+'),
        ('18+', '18+'),
    ]

    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='books'
    )
    title = models.CharField(max_length=100, blank=True)
    description = models.TextField(blank=True)
    cover = models.ImageField(storage=PublicMediaStorage(), upload_to=book_cover_path, blank=True)
    cover_mini = models.ImageField(storage=PublicMediaStorage(), upload_to=book_covermini_path, blank=True)
    is_published = models.BooleanField(default=False)
    is_finished = models.BooleanField(default=False)
    # Новое поле статуса книги
    status = models.CharField(max_length=20, choices=BOOK_STATUS, default='draft')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(null=True, blank=True, help_text='Дата первой публикации книги')
    type = models.CharField(max_length=20, choices=BOOK_TYPES, blank=True, null=True, default=None)
    cover_type = models.CharField(max_length=16, choices=COVER_TYPES, default='generated')
    genres = models.ManyToManyField(Genre, related_name='books', blank=True)
    is_adult = models.BooleanField(default=False, help_text='Книга для взрослых (18+)')
    # Новые поля для возрастных ограничений
    age_rating = models.CharField(max_length=3, choices=AGE_RATINGS, default='0+', help_text='Возрастное ограничение')
    has_profanity = models.BooleanField(default=False, help_text='Содержит ненормативную лексику')
    cover_editor = models.ImageField(storage=PublicMediaStorage(), upload_to=book_cover_editor_path, blank=True, help_text='Обложка без лейблов для редактора')
    hashtags = models.ManyToManyField('Hashtag', blank=True, related_name='books')
    auto_indent = models.BooleanField(default=False, help_text='Автоматическая красная строка для абзацев')
    position_finished = models.PositiveIntegerField(default=0)
    position_in_progress = models.PositiveIntegerField(default=0)
    position_draft = models.PositiveIntegerField(default=0)
    creation_status = models.CharField(max_length=16, choices=CREATION_STATUS, default='creating', help_text='Статус процесса создания книги')
    abandon_at = models.DateTimeField(null=True, blank=True)
    
    # Счетчик уникальных просмотров - сохраняем ID пользователей, которые заходили на страницу книги
    viewed_by = models.ManyToManyField(settings.AUTH_USER_MODEL, blank=True, related_name='viewed_books')

    def clean(self):
        super().clean()
        if self.genres.count() > 3:
            raise ValidationError('Можно выбрать не более 3 жанров для одной книги.')

    def save(self, *args, **kwargs):
        # Автоматически устанавливаем is_adult=True для 18+ или при наличии ненормативной лексики
        if self.age_rating == '18+' or self.has_profanity:
            self.is_adult = True
        else:
            self.is_adult = False
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title or f"Книга {self.id} (создаётся)"

    def is_visible_for(self, user):
        if self.author == user:
            return True
        return self.status in ['in_progress', 'finished']

    def save(self, *args, **kwargs):
        # Определяем старый статус до сохранения
        old_status = None
        if self.pk is not None:
            try:
                orig = Book.objects.get(pk=self.pk)
                old_status = orig.status
                logger.info(f"ОТЛАДКА Book.save: Книга {self.pk}, старый статус: {old_status}, новый статус: {self.status}")
                logger.info(f"ОТЛАДКА Book.save: Старые значения is_published={orig.is_published}, is_finished={orig.is_finished}")
                logger.info(f"ОТЛАДКА Book.save: Новые значения is_published={self.is_published}, is_finished={self.is_finished}")
                
                # Для обратной совместимости обновляем is_published и is_finished
                if self.status != orig.status:
                    if self.status == 'draft':
                        self.is_published = False
                        self.is_finished = False
                        logger.info(f"ОТЛАДКА Book.save: Установлен статус draft, обновлены флаги is_published=False, is_finished=False")
                    elif self.status == 'in_progress':
                        self.is_published = True
                        self.is_finished = False
                        logger.info(f"ОТЛАДКА Book.save: Установлен статус in_progress, обновлены флаги is_published=True, is_finished=False")
                    elif self.status == 'finished':
                        self.is_published = True
                        self.is_finished = True
                        logger.info(f"ОТЛАДКА Book.save: Установлен статус finished, обновлены флаги is_published=True, is_finished=True")
            except Book.DoesNotExist:
                pass
        
        # Для новых книг или при изменении is_published/is_finished обновляем статус
        # для обратной совместимости
        if self.pk is None or (old_status is not None and 
                              (self.is_published != orig.is_published or 
                               self.is_finished != orig.is_finished)):
            old_status_from_flags = self.status
            if not self.is_published:
                self.status = 'draft'
            elif self.is_published and not self.is_finished:
                self.status = 'in_progress'
            else:
                self.status = 'finished'
            logger.info(f"ОТЛАДКА Book.save: Статус изменен на основе флагов с {old_status_from_flags} на {self.status}")
        
        # Проверяем статус взрослого контента при сохранении
        if self.pk is not None and old_status is not None:
            if orig.is_adult and not self.is_adult and not kwargs.pop('force_adult_reset', False):
                self.is_adult = True
        
        # Устанавливаем дату первой публикации при переходе в статус "в процессе" или "завершено"
        if (self.status in ['in_progress', 'finished']) and not self.published_at:
            self.published_at = timezone.now()
            logger.info(f"ОТЛАДКА Book.save: Установлена дата первой публикации {self.published_at}")
            
        super().save(*args, **kwargs)
        
        # Если статус изменился, вызываем обработчик рейтинга
        # Но только если не установлен флаг _skip_rating_update
        if old_status is not None and old_status != self.status and not getattr(self, '_skip_rating_update', False):
            # Импортируем здесь, чтобы избежать циклических импортов
            from users.rating_service import RatingEventHandlers
            logger.info(f"ОТЛАДКА Book.save: Вызов RatingEventHandlers.on_book_status_changed с параметрами: book.id={self.id}, old_status={old_status}, new_status={self.status}")
            RatingEventHandlers.on_book_status_changed(self, old_status, self.status)
        elif old_status is not None and old_status == self.status:
            logger.info(f"ОТЛАДКА Book.save: Статус не изменился ({old_status}), обработчик рейтинга не вызывается")
        elif getattr(self, '_skip_rating_update', False):
            logger.info(f"ОТЛАДКА Book.save: Установлен флаг _skip_rating_update, обработчик рейтинга не вызывается")
            
        # Сбрасываем флаг _skip_rating_update после использования
        if hasattr(self, '_skip_rating_update'):
            delattr(self, '_skip_rating_update')
            logger.info(f"ОТЛАДКА Book.save: Флаг _skip_rating_update сброшен")

    def sync_status(self):
        published_chapters = self.chapters.filter(is_published=True).count()
        total_chapters = self.chapters.count()
        
        # Определяем старый статус до изменений
        old_status = self.status
        
        # Флаг для отслеживания изменений
        status_changed = False
        
        if total_chapters == 0:
            if self.status != 'draft':
                status_changed = True
                self.status = 'draft'
                self.is_published = False
                self.is_finished = False
                self.updated_at = timezone.now()
                self.save(update_fields=['status', 'is_published', 'is_finished', 'updated_at'])
            return
            
        if published_chapters == 0:
            if self.status != 'draft':
                status_changed = True
                self.status = 'draft'
                self.is_published = False
                self.is_finished = False
                self.updated_at = timezone.now()
                self.save(update_fields=['status', 'is_published', 'is_finished', 'updated_at'])
            return
        
        # Для рассказов статус зависит от publish_as_finished последней опубликованной главы
        if self.type == 'story':
            # Если есть хотя бы одна опубликованная глава, книга должна быть опубликована
            if not self.is_published or self.status == 'draft':
                # Проверяем, нужно ли отметить как завершенную
                latest_chapter = self.chapters.filter(is_published=True).order_by('-updated_at').first()
                if latest_chapter and latest_chapter.publish_as_finished:
                    self.status = 'finished'
                    self.is_published = True
                    self.is_finished = True
                else:
                    self.status = 'in_progress'
                    self.is_published = True
                    self.is_finished = False
                self.updated_at = timezone.now()
                self.save(update_fields=['status', 'is_published', 'is_finished', 'updated_at'])
        else:
            # Для романов/повестей/сборников полная логика статусов
            if published_chapters == total_chapters and published_chapters > 0:
                # Все главы опубликованы - книга завершена
                if self.status != 'finished':
                    self.status = 'finished'
                    self.is_published = True
                    self.is_finished = True
                    self.updated_at = timezone.now()
                    self.save(update_fields=['status', 'is_published', 'is_finished', 'updated_at'])
            elif published_chapters > 0:
                # Есть опубликованные главы, но не все - в процессе публикации
                if self.status != 'in_progress':
                    self.status = 'in_progress'
                    self.is_published = True
                    self.is_finished = False
                    self.updated_at = timezone.now()
                    self.save(update_fields=['status', 'is_published', 'is_finished', 'updated_at'])

    def publish_all_chapters(self):
        import time
        start_time = time.time()
        now = timezone.now()

        # Определяем старый статус до изменений
        old_status = self.status

        logger.info(f"ОТЛАДКА: Начало publish_all_chapters для книги {self.id}")

        # КРИТИЧЕСКАЯ ОПТИМИЗАЦИЯ: Публикуем все главы ОДНИМ SQL-запросом без циклов
        unpublished_count = self.chapters.filter(is_published=False).count()
        if unpublished_count > 0:
            # Используем update() вместо bulk_update для максимальной скорости
            from django.db import transaction
            with transaction.atomic():
                # Обновляем все неопубликованные главы одним SQL-запросом
                updated_count = self.chapters.filter(is_published=False).update(
                    is_published=True,
                    published_at=now
                )

            logger.info(f"МАССОВО опубликовано {updated_count} глав книги {self.id} одним SQL-запросом (без циклов)")
            logger.info(f"ОТЛАДКА: publish_all_chapters использует update() - метод save() глав НЕ вызывается")
        
        # Если были обновления глав, обновляем статус книги
        if unpublished_count > 0:
            # Обновляем статус книги
            if self.status == 'draft':
                self.status = 'in_progress'
            
            # Для обратной совместимости обновляем старые поля
            self.is_published = True
            if not self.published_at:
                self.published_at = now
            
            # Если все главы опубликованы, отмечаем книгу как завершенную
            if self.chapters.count() == self.chapters.filter(is_published=True).count():
                self.status = 'finished'
                self.is_finished = True
                
            self.updated_at = now
            self.save()  # Используем обычный save для обработки изменения статуса
            
            # Если статус изменился, вызываем обработчик рейтинга
            if old_status != self.status:
                rating_start = time.time()
                # Импортируем здесь, чтобы избежать циклических импортов
                from users.rating_service import RatingEventHandlers
                RatingEventHandlers.on_book_status_changed(self, old_status, self.status)
                rating_time = time.time() - rating_start
                logger.info(f"ОТЛАДКА: Обработка рейтинга заняла {rating_time:.2f} сек")

        total_time = time.time() - start_time
        logger.info(f"ОТЛАДКА: Полное выполнение publish_all_chapters заняло {total_time:.2f} сек")

    def unpublish_all_chapters(self):
        import time
        start_time = time.time()

        # Определяем старый статус до изменений
        old_status = self.status

        logger.info(f"ОТЛАДКА: Начало unpublish_all_chapters для книги {self.id}")

        # Снимаем с публикации все главы
        updated_count = self.chapters.update(is_published=False)

        operation_time = time.time() - start_time
        logger.info(f"МАССОВО снято с публикации {updated_count} глав книги {self.id} одним SQL-запросом за {operation_time:.2f} сек")
        
        # Обновляем статус книги
        was_not_draft = self.status != 'draft'
        
        # Устанавливаем новый статус
        self.status = 'draft'
        
        # Для обратной совместимости обновляем старые поля
        self.is_published = False
        self.is_finished = False
        self.updated_at = timezone.now()
        self.save(update_fields=['status', 'is_published', 'is_finished', 'updated_at'])

        # Если статус изменился, вызываем обработчик рейтинга
        if was_not_draft:  # Если книга была не в черновиках
            rating_start = time.time()
            # Импортируем здесь, чтобы избежать циклических импортов
            from users.rating_service import RatingEventHandlers
            RatingEventHandlers.on_book_status_changed(self, old_status, self.status)
            rating_time = time.time() - rating_start
            logger.info(f"ОТЛАДКА: Обработка рейтинга заняла {rating_time:.2f} сек")

        total_time = time.time() - start_time
        logger.info(f"ОТЛАДКА: Полное выполнение unpublish_all_chapters заняло {total_time:.2f} сек")

@receiver(post_delete, sender=Book)
def delete_book_media_folders(sender, instance, **kwargs):
    """
    При удалении книги удаляет все связанные с ней файлы из S3 (обложки, миниатюры, вложения к главам и т.д.)
    """
    book_id = instance.id
    group_folder = get_group_folder(book_id)
    s3 = boto3.resource(
        's3',
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        endpoint_url=settings.AWS_S3_ENDPOINT_URL,
        region_name=settings.AWS_S3_REGION_NAME,
    )
    bucket = s3.Bucket(settings.AWS_STORAGE_BUCKET_NAME)
    # Префиксы для удаления (добавьте свои, если нужно)
    prefixes = [
        f"{settings.AWS_S3_PUBLIC_MEDIA_PREFIX}/book_covers/{group_folder}/{book_id}/",
        f"{settings.AWS_S3_PRIVATE_MEDIA_PREFIX}/book_pics/{group_folder}/{book_id}/",
        # Добавьте другие связанные с книгой папки, если нужно
    ]
    for prefix in prefixes:
        bucket.objects.filter(Prefix=prefix).delete()

class BookChapter(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='chapters')
    title = models.CharField(max_length=200)
    content = models.TextField(blank=True)
    order = models.PositiveIntegerField(default=0)
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(null=True, blank=True, help_text='Дата публикации главы')
    scheduled_publish_at = models.DateTimeField(null=True, blank=True)
    celery_task_id = models.CharField(max_length=100, null=True, blank=True)
    publish_as_finished = models.BooleanField(null=True, blank=True, help_text='Статус завершения книги при публикации (только для рассказов)')

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"{self.book.title} — {self.title}"

    def clean_expired_schedule(self):
        """Очищает устаревшие задачи планирования"""
        if self.scheduled_publish_at and self.scheduled_publish_at < timezone.now():
            import logging
            logger = logging.getLogger(__name__)

            logger.info(f"Очищаем устаревшую задачу планирования для главы {self.id}")

            # Отменяем задачу если она есть
            if self.celery_task_id:
                try:
                    from celery.result import AsyncResult
                    AsyncResult(self.celery_task_id).revoke(terminate=True)
                    logger.info(f"Отменена устаревшая задача {self.celery_task_id}")
                except Exception as e:
                    logger.warning(f"Не удалось отменить устаревшую задачу {self.celery_task_id}: {e}")

            # Очищаем поля
            self.scheduled_publish_at = None
            self.celery_task_id = None
            self.publish_as_finished = None
            self.save(update_fields=['scheduled_publish_at', 'celery_task_id', 'publish_as_finished'])

            return True
        return False

    def save(self, *args, **kwargs):
        # Автоматически устанавливаем дату публикации при первой публикации главы
        if self.is_published and not self.published_at:
            self.published_at = timezone.now()
        
        # Проверяем, нужно ли отметить книгу как завершенную при публикации главы
        mark_as_finished = False
        if self.is_published and self.publish_as_finished:
            # Получаем тип книги с проверкой на None
            book_type = self.book.type or 'story'
            # Только для рассказов и сборников рассказов можно автоматически завершать
            if book_type in ['story', 'story_collection']:
                mark_as_finished = True
                logger.info(f"Отмечаем книгу {self.book.id} как завершенную при публикации главы {self.id}")
            else:
                logger.warning(f"Не отмечаем книгу {self.book.id} как завершенную, т.к. тип книги '{book_type}' не подходит")
        
        super().save(*args, **kwargs)
        
        # Если нужно отметить книгу как завершенную
        if mark_as_finished and self.book.status != 'finished':
            # Определяем старый статус книги
            old_status = self.book.status
                
            # Обновляем статус книги
            self.book.status = 'finished'
            
            # Для обратной совместимости обновляем старые поля
            self.book.is_published = True
            self.book.is_finished = True
            self.book.updated_at = timezone.now()
            
            # Обновляем статус книги (рейтинг будет обновлен автоматически в Book.save())
            self.book.save(update_fields=['status', 'is_published', 'is_finished', 'updated_at'])
            logger.info(f"Книга {self.book.id} отмечена как завершенная, рейтинг автора будет обновлен автоматически")
        
        # Синхронизируем статус книги после сохранения главы
        # НО НЕ во время выполнения задач (чтобы не переопределить ручные изменения)
        elif not getattr(self, '_skip_sync_status', False):
            self.book.sync_status()

    def delete(self, *args, **kwargs):
        book = self.book
        super().delete(*args, **kwargs)
        book.sync_status()

class Like(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='likes')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='likes')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Like for {self.book.title} by {self.user.username}"

class Review(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='reviews')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='reviews')
    text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

class Comment(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='comments')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='comments')
    text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    # Поля для системы threading (как в YouTube)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies')
    reply_to = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='reply_targets')
    is_edited = models.BooleanField(default=False)
    # Поля для мягкого удаления
    deleted_by_user = models.BooleanField(default=False, help_text='Комментарий удален пользователем')
    deleted_by_admin = models.BooleanField(default=False, help_text='Комментарий удален администратором')
    deleted_at = models.DateTimeField(null=True, blank=True, help_text='Время удаления')
    
    class Meta:
        ordering = ['-created_at']  # Новые комментарии вверху
    
    def __str__(self):
        return f'Comment by {self.user.username} on {self.book.title}'
    
    @property
    def is_reply(self):
        """Проверяет, является ли комментарий ответом на другой комментарий"""
        return self.parent is not None
    
    def get_replies_count(self):
        """Возвращает количество ответов на комментарий (только неудаленные)"""
        return self.replies.filter(deleted_by_user=False, deleted_by_admin=False).count()
    
    @property
    def is_deleted(self):
        """Проверяет, удален ли комментарий"""
        return self.deleted_by_user or self.deleted_by_admin
    
    def get_deletion_message(self):
        """Возвращает сообщение об удалении"""
        if self.deleted_by_admin:
            return "Комментарий удален администратором"
        elif self.deleted_by_user:
            return "Комментарий удален пользователем"
        return None
    
    def soft_delete(self, by_admin=False):
        """Мягкое удаление комментария"""
        if by_admin:
            self.deleted_by_admin = True
        else:
            self.deleted_by_user = True
        self.deleted_at = timezone.now()
        self.save(update_fields=['deleted_by_user', 'deleted_by_admin', 'deleted_at'])

class CommentLike(models.Model):
    """Модель для лайков/дизлайков комментариев"""
    REACTION_CHOICES = [
        ('like', 'Лайк'),
        ('dislike', 'Дизлайк'),
    ]

    comment = models.ForeignKey(Comment, on_delete=models.CASCADE, related_name='likes')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='comment_likes')
    reaction = models.CharField(max_length=10, choices=REACTION_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['comment', 'user']  # Один пользователь может оставить только одну реакцию на комментарий



    def __str__(self):
        return f'{self.user.username} {self.reaction}d comment {self.comment.id}'

class Purchase(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='purchases')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='purchases')
    amount = models.DecimalField(max_digits=10, decimal_places=2)  # сумма в рублях
    created_at = models.DateTimeField(auto_now_add=True)

class Award(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='awards')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='awards')
    amount = models.DecimalField(max_digits=10, decimal_places=2)  # сумма награды
    created_at = models.DateTimeField(auto_now_add=True)

class Subscription(models.Model):
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='subscribers')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='subscriptions')
    created_at = models.DateTimeField(auto_now_add=True)

class ReaderRatingHistory(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='reader_rating_history')
    date = models.DateTimeField(auto_now_add=True)
    action = models.CharField(max_length=50)
    points = models.IntegerField()
    total = models.IntegerField()

class AuthorRatingHistory(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='author_rating_history')
    date = models.DateTimeField(auto_now_add=True)
    action = models.CharField(max_length=50)
    points = models.IntegerField()
    total = models.IntegerField()

def move_book_to_category(book, user, new_category):
    category_fields = {
        'finished': 'position_finished',
        'in_progress': 'position_in_progress',
        'drafts': 'position_draft',
    }
    # Сбросить позицию в старых категориях
    for cat, field in category_fields.items():
        if cat != new_category:
            setattr(book, field, 0)
    # Найти максимальную позицию в новой категории
    field = category_fields[new_category]
    max_pos = Book.objects.filter(
        author=user,
        **{f"{field}__gt": 0}
    ).aggregate(Max(field))[f"{field}__max"] or 0
    setattr(book, field, max_pos + 1)
    book.save(update_fields=[field] + [f for c, f in category_fields.items() if c != new_category])
    # Пересчитать позиции в старой категории (чтобы не было дыр)
    for cat, f in category_fields.items():
        if cat != new_category:
            books = Book.objects.filter(author=user, **{f"{f}__gt": 0}).order_by(f)
            for idx, b in enumerate(books):
                setattr(b, f, idx + 1)
                b.save(update_fields=[f])

class BookChapterImage(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='images')
    chapter = models.ForeignKey('BookChapter', on_delete=models.SET_NULL, null=True, blank=True, related_name='images')
    path = models.CharField(max_length=512)
    inserted_at = models.DateTimeField(auto_now_add=True)
    position = models.PositiveIntegerField(default=0)
    align = models.CharField(max_length=16, default='center')
    width = models.CharField(max_length=16, blank=True, null=True)
    height = models.CharField(max_length=16, blank=True, null=True)
    # Можно добавить alt, original_width, original_height при необходимости

    def __str__(self):
        return f"Image for book {self.book_id} (chapter {self.chapter_id}): {self.path}"

@receiver(post_delete, sender=BookChapterImage)
def delete_chapter_image_file(sender, instance, **kwargs):
    from users.storage_backends import PrivateMediaStorage
    storage = PrivateMediaStorage()
    if instance.path and storage.exists(instance.path):
        storage.delete(instance.path)
