import React, { useState, useRef, useEffect } from 'react';
import { Button, message, Tooltip } from 'antd';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import { useTheme } from '../theme/ThemeContext';

const BioEditor = ({ initialContent = '', onSave, onCancel, userId, username }) => {
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
    ],
    content: initialContent,
  });

  const handleSave = async () => {
    if (!editor) return;
    
    setLoading(true);
    try {
      const content = editor.getHTML();
      await onSave(content);
    } catch (error) {
      message.error('Ошибка сохранения');
    } finally {
      setLoading(false);
    }
  };

  // Функция для создания кастомного тултипа
  const renderTooltip = (title, shortcut) => (
    <div className="tooltip-content">
      <div>{title}</div>
      {shortcut && <div className="editor-shortcut">{shortcut}</div>}
    </div>
  );

  // Панель инструментов с 4 кнопками
  const toolbarBg = theme === 'dark' ? '#23272f' : '#f3f4f6';
  const iconColor = theme === 'dark' ? '#fff' : '#222';
  const iconActiveBg = '#2563eb';
  const iconActiveColor = '#fff';

  const Toolbar = () => editor && (
    <div className="flex flex-wrap gap-1 mb-2 rounded px-2 py-1 items-center"
      style={{
        background: toolbarBg,
        border: '1.5px solid #e5e7eb',
        minHeight: 40,
        justifyContent: 'flex-start',
      }}
    >
      <div className="flex items-center gap-1">
        <Tooltip 
          title={renderTooltip("Жирный", "Ctrl+B")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().toggleBold().run();
            }}
            className={`px-2 py-1 rounded ${editor.isActive('bold') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('bold') ? iconActiveBg : 'transparent',
              color: editor.isActive('bold') ? iconActiveColor : iconColor,
            }}
          >
            <span style={{ fontWeight: 'bold' }}>Ж</span>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("Курсив", "Ctrl+I")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().toggleItalic().run();
            }}
            className={`px-2 py-1 rounded ${editor.isActive('italic') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('italic') ? iconActiveBg : 'transparent',
              color: editor.isActive('italic') ? iconActiveColor : iconColor,
            }}
          >
            <span style={{ fontStyle: 'italic' }}>К</span>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("Подчёркнутый", "Ctrl+U")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().toggleUnderline().run();
            }}
            className={`px-2 py-1 rounded ${editor.isActive('underline') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('underline') ? iconActiveBg : 'transparent',
              color: editor.isActive('underline') ? iconActiveColor : iconColor,
            }}
          >
            <span style={{ textDecoration: 'underline' }}>П</span>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("Зачёркнутый", null)} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().toggleStrike().run();
            }}
            className={`px-2 py-1 rounded ${editor.isActive('strike') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('strike') ? iconActiveBg : 'transparent',
              color: editor.isActive('strike') ? iconActiveColor : iconColor,
            }}
          >
            <span style={{ textDecoration: 'line-through' }}>З</span>
          </button>
        </Tooltip>
      </div>
    </div>
  );

  return (
    <div 
      className="p-4 rounded-lg mb-4" 
      style={{ 
        background: theme === 'dark' ? '#181c23' : '#f9fafb', 
        boxShadow: theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22',
      }}
    >
      <div className="mb-3 flex flex-col gap-2">
        <Toolbar />
        <div
          className="tiptap-editor"
          style={{
            minHeight: 200,
            maxHeight: 400,
            background: theme === 'dark' ? '#111827' : '#fff',
            color: theme === 'dark' ? '#fff' : '#222',
            border: theme === 'dark' ? '2px solid #374151' : '2px solid #d1d5db',
            borderRadius: 8,
            boxShadow: theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22',
            padding: '12px 16px',
            fontSize: '1rem',
            transition: 'background 0.2s, color 0.2s',
            overflowY: 'auto',
          }}
          onClick={() => {
            if (editor && !editor.isFocused) {
              editor.commands.focus('end');
            }
          }}
        >
          <EditorContent editor={editor} />
        </div>
      </div>
      <div className="flex gap-2 mt-2">
        <Button type="primary" onClick={handleSave} loading={loading}>
          Сохранить
        </Button>
        <Button 
          onClick={onCancel} 
          disabled={loading} 
          style={{ 
            background: theme === 'dark' ? '#23272f' : '#fff', 
            color: theme === 'dark' ? '#fff' : '#222', 
            border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb' 
          }}
        >
          Отмена
        </Button>
      </div>
    </div>
  );
};

export default BioEditor; 