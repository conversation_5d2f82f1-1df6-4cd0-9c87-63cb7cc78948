# Скрытие кнопок во время редактирования рассказа

## Обзор изменений
Кнопки "К основным настройкам" и "Опубликовать" теперь скрываются во время редактирования текста рассказа в обычном и полноэкранном режиме.

## Проблема, которую решаем

### Отвлекающие элементы интерфейса
```
БЫЛО: Кнопки всегда видны
→ Отвлекают от процесса написания
→ Занимают место на экране
→ Могут случайно нажиматься
→ Нарушают фокус на тексте

СТАЛО: Кнопки скрыты во время редактирования
→ Чистый интерфейс для написания
→ Больше места для текста
→ Фокус на творческом процессе
→ Кнопки появляются после выхода из редактора
```

## Реализованное изменение

### Условное отображение кнопок:
```javascript
// БЫЛО: Кнопки всегда отображались
<div style={{ marginTop: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
  <Button onClick={onBack}>К основным настройкам</Button>
  {safeChapters.length > 0 && (
    <Button onClick={handlePublish}>Опубликовать</Button>
  )}
</div>

// СТАЛО: Кнопки скрыты во время редактирования
{/* Скрываем кнопки "К основным настройкам" и "Опубликовать" во время редактирования */}
{!editMode && (
  <div style={{ marginTop: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
    <Button onClick={onBack}>К основным настройкам</Button>
    {safeChapters.length > 0 && (
      <Button onClick={handlePublish}>Опубликовать</Button>
    )}
  </div>
)}
```

### Логика скрытия:
```javascript
// Состояние редактирования
const [editMode, setEditMode] = useState(null);

// editMode принимает значения:
// - null: не редактируем (кнопки видны)
// - chapter.id: редактируем главу (кнопки скрыты)
// - 'new': создаем новую главу (кнопки скрыты)
// - 'preface': редактируем вступление (кнопки скрыты)

// Условие отображения кнопок
{!editMode && (
  // Блок с кнопками отображается только когда editMode === null
)}
```

## Пользовательские сценарии

### Сценарий 1: Начало редактирования
```
Пользователь на странице рассказа
→ Видит кнопки "К основным настройкам" и "Опубликовать" внизу ✅
→ Нажимает "Редактировать" на главе
→ Открывается редактор текста
→ Кнопки внизу исчезают ✅
→ Интерфейс становится чище и сфокусирован на тексте
```

### Сценарий 2: Работа в обычном режиме
```
Пользователь редактирует текст рассказа
→ Кнопки "К основным настройкам" и "Опубликовать" скрыты ✅
→ Больше места для текста и инструментов редактора
→ Ничего не отвлекает от написания
→ Фокус полностью на творческом процессе
```

### Сценарий 3: Работа в полноэкранном режиме
```
Пользователь переключается в полноэкранный режим
→ Кнопки остаются скрытыми ✅
→ Максимум места для текста
→ Чистый интерфейс без отвлекающих элементов
→ Полное погружение в процесс написания
```

### Сценарий 4: Выход из редактирования
```
Пользователь завершает редактирование
→ Нажимает "Сохранить" или "Отмена"
→ Редактор закрывается (editMode = null)
→ Кнопки "К основным настройкам" и "Опубликовать" появляются ✅
→ Возвращается к обычному интерфейсу управления рассказом
```

### Сценарий 5: Создание новой главы
```
Пользователь нажимает "Добавить главу"
→ Открывается редактор для новой главы (editMode = 'new')
→ Кнопки внизу скрыты ✅
→ Фокус на создании нового контента
→ После сохранения кнопки появляются снова
```

## Преимущества изменения

### ✅ Улучшенный UX:
- **Чистый интерфейс** во время написания
- **Больше места** для текста и инструментов
- **Меньше отвлекающих элементов**
- **Фокус на творческом процессе**

### ✅ Лучшая эргономика:
- **Предотвращение случайных нажатий** на кнопки
- **Оптимальное использование** экранного пространства
- **Логичное поведение** интерфейса

### ✅ Консистентность:
- **Единое поведение** в обычном и полноэкранном режиме
- **Предсказуемый интерфейс** для пользователей
- **Соответствие ожиданиям** от редактора текста

## Техническая реализация

### Условие отображения:
```javascript
// Проверяем состояние редактирования
{!editMode && (
  <div style={{ /* стили блока кнопок */ }}>
    {/* Кнопки отображаются только когда не редактируем */}
  </div>
)}
```

### Состояния editMode:
```javascript
// Возможные значения editMode:
null          // Не редактируем - кнопки видны
chapter.id    // Редактируем существующую главу - кнопки скрыты
'new'         // Создаем новую главу - кнопки скрыты
'preface'     // Редактируем вступление - кнопки скрыты
```

### Управление состоянием:
```javascript
// Вход в режим редактирования
const handleEdit = (chapter) => {
  setEditMode(chapter.id);  // Кнопки скрываются
  // ... остальная логика
};

// Выход из режима редактирования
const handleSave = async (data) => {
  // ... логика сохранения
  setEditMode(null);  // Кнопки появляются
};

const handleCancel = () => {
  setEditMode(null);  // Кнопки появляются
};
```

## Затронутые компоненты

### StoryEditor.jsx:
```javascript
// Основной блок с кнопками обернут в условие
{!editMode && (
  <div style={{ marginTop: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
    <Button onClick={onBack}>К основным настройкам</Button>
    {safeChapters.length > 0 && (
      <>
        {isFullyPublished ? (
          <Button onClick={handleToDraft}>Снять с публикации</Button>
        ) : (
          <Button onClick={handlePublish}>Опубликовать</Button>
        )}
      </>
    )}
  </div>
)}
```

## Поведение в разных режимах

### 📝 Обычный режим редактирования:
- **Кнопки скрыты**: ✅
- **Редактор отображается**: ✅
- **Больше места для текста**: ✅

### 🖥️ Полноэкранный режим редактирования:
- **Кнопки скрыты**: ✅
- **Максимум места для текста**: ✅
- **Чистый интерфейс**: ✅

### 📋 Режим просмотра (не редактируем):
- **Кнопки видны**: ✅
- **Полный функционал управления**: ✅
- **Доступ ко всем действиям**: ✅

## Совместимость

### ✅ Обратная совместимость:
- **Существующая функциональность** сохранена
- **Все кнопки работают** как прежде
- **Логика публикации** не изменена

### ✅ Новое поведение:
- **Кнопки скрываются** только во время редактирования
- **Автоматическое появление** после выхода из редактора
- **Работает во всех режимах** (обычный/полноэкранный)

## Результат изменения

### 📊 Улучшения UX:
- ✅ **Чистый интерфейс** во время написания
- ✅ **Больше места** для творчества
- ✅ **Меньше отвлекающих элементов**
- ✅ **Фокус на тексте**

### 🎯 Техническая реализация:
- ✅ **Простое условие** `{!editMode && (...)}`
- ✅ **Использование существующего состояния** editMode
- ✅ **Минимальные изменения** в коде
- ✅ **Стабильная работа** во всех режимах

### 🚀 Итоговый результат:
Кнопки "К основным настройкам" и "Опубликовать" теперь **автоматически скрываются** во время редактирования рассказа:
- ✅ **В обычном режиме** редактирования
- ✅ **В полноэкранном режиме** редактирования  
- ✅ **При создании новых глав**
- ✅ **При редактировании существующих глав**
- ✅ **Автоматически появляются** после выхода из редактора

**Интерфейс редактирования рассказов стал чище и удобнее!** ✨📝
