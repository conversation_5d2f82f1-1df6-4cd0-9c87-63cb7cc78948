# Оглавление

- [1. Общее описание](#1-общее-описание)
- [2. Структура проекта](#2-структура-проекта)
- [3. Взаимосвязь фронта и бэка](#3-взаимосвязь-фронта-и-бэка)
- [4. Структура стилей и UI](#4-структура-стилей-и-ui)
- [5. Бэкенд: структура и логика](#5-бэкенд-структура-и-логика)
- [6. Как запускать проект](#6-как-запускать-проект)
- [7. Бэкапы и восстановление](#7-бэкапы-и-восстановление)
- [8. Рекомендации](#8-рекомендации)
- [9. UI/UX и layout: правила для страниц с сайдбаром](#9-uiux-и-layout-правила-для-страниц-с-сайдбаром)
- [10. Работа с обложками книг и этапами редактирования](#10-работа-с-обложками-книг-и-этапами-редактирования)
- [11. Жанры, хештеги, аннотации, публикация глав](#11-жанры-хештеги-аннотации-публикация-глав)
- [12. Логика публикации глав и книги](#12-логика-публикации-глав-и-книги)
- [13. Изменения и доработки (май 2025)](#13-изменения-и-доработки-май-2025)
- [14. Логика работы с хедером профиля пользователя](#14-логика-работы-с-хедером-профиля-пользователя)

---

# Аудит и структура проекта

## 1. Общее описание
- **Проект**: Литературный портал (рабочее название)
- **Назначение**: Платформа для работы с книгами, профилями пользователей, лентой событий и т.д.
- **Технологии**:  
  - **Backend**: Django 5, Django REST Framework, MariaDB/MySQL  
  - **Frontend**: React 18, Vite, TailwindCSS, Ant Design, React Query

---

## 2. Структура проекта

```
/
├── backend/
│   ├── books/         # Приложение для работы с книгами
│   ├── users/         # Приложение для пользователей, профилей, друзей, ленты
│   ├── config/        # Основные настройки Django, маршруты, wsgi/asgi
│   ├── litportal/     # Базовые настройки, возможно, точка входа
│   ├── static/, media/ # Статика и медиафайлы
│   └── requirements.txt
├── frontend/
│   ├── src/
│   │   ├── components/   # UI-компоненты
│   │   ├── pages/        # Страницы (роуты)
│   │   ├── context/      # Контексты (например, AuthContext)
│   │   ├── hooks/        # Кастомные хуки
│   │   ├── theme/, assets/ # Темы, ассеты
│   │   └── index.css, App.css # Стили
│   ├── public/
│   ├── package.json
│   └── README.md
└── requirements.txt, package-lock.json, .gitignore и др.
```

---

## 3. Взаимосвязь фронта и бэка

- **API**:  
  - Все основные данные (книги, профили, лента, авторизация) получаются через REST API.
  - Префиксы API:  
    - `/api/` — книги (books, chapters и др.)
    - `/api/auth/` — аутентификация (login, logout, check, register и др.)
    - `/api/users/` — профили, друзья, подписки, лента и др.

- **Аутентификация**:  
  - Используется сессия (cookie, credentials: 'include' во фронте).
  - Проверка авторизации — запрос на `/api/auth/check/`.
  - Логин/логаут — POST-запросы на `/api/auth/login/` и `/api/auth/logout/`.

- **Фронтенд**:
  - React Router управляет страницами.
  - React Query для работы с API (кэширование, запросы).
  - AuthContext хранит состояние пользователя и методы login/logout.

---

## 4. Структура стилей и UI

- **TailwindCSS** — основной фреймворк для стилей.
- **Ant Design** и **styled-components** — для отдельных UI-элементов.
- Стили лежат в `frontend/src/index.css`, `App.css`, а также в компонентах.
- Используются современные подходы: utility-first CSS, адаптивность.

---

## 5. Бэкенд: структура и логика

- **Django** + **Django REST Framework**.
- Приложения:
  - `books` — модели, сериализаторы, вьюхи для книг и глав.
  - `users` — модели, сериализаторы, вьюхи для пользователей, профилей, друзей, ленты.
- **Маршрутизация**:
  - Корневой роутер (`backend/config/urls.py`) делит API на книги и пользователей.
  - Внутри приложений — DRF ViewSets и отдельные вьюхи для спец. операций.
- **БД**: MariaDB/MySQL, миграции через Django.
- **Медиа**: Загрузка аватаров, обложек и др. через Django.

---

## 6. Как запускать проект

- **Backend**:
  1. `cd backend`
  2. `python -m venv venv && venv\Scripts\activate`
  3. `pip install -r requirements.txt`
  4. `python manage.py migrate`
  5. `python manage.py runserver`

- **Frontend**:
  1. `cd frontend`
  2. `npm install`
  3. `npm run dev`

---

## 7. Бэкапы и восстановление

- **БД**:  
  - Для MariaDB/MySQL — через `mysqldump` или phpMyAdmin.
  - Для восстановления — импорт дампа в новую БД.
- **Медиа**:  
  - Копировать папку `backend/media/`.

---

## 8. Рекомендации

- Для новых разработчиков — ознакомьтесь с этим файлом перед началом работы.
- Для новых сессий с ИИ — отправляйте этот файл для быстрого погружения в проект.
- Обновляйте файл при изменениях в архитектуре или технологиях.

---

## 9. UI/UX и layout: правила для страниц с сайдбаром

- Для страниц профиля и других, где используется сайдбар справа, применяется flex-верстка:
  - Родительский контейнер: `flex flex-row w-full`
  - Основной контент: `flex-1 w-full pr-[10px]` (занимает всю ширину своей колонки, отступает от сайдбара на 10px)
  - Сайдбар: фиксированной ширины, например, `w-[250px] ml-[10px] px-[10px]`
- **Внутри основного контента не использовать max-w-*, только w-full и pr-[10px]** — это предотвращает разрывы между контентом и сайдбаром.
- Если нужен левый отступ — используйте `pl-2.5` или `pl-[10px]`.

**Пример:**
```jsx
<div className="flex flex-row w-full">
  <div className="flex-1 w-full pr-[10px]">
    {/* Основной контент */}
  </div>
  <aside className="w-[250px] ml-[10px] px-[10px]">
    {/* Сайдбар */}
  </aside>
</div>
```
- Такой подход обеспечивает адаптивность, отсутствие разрывов и визуальное единство страниц. 

## Система аватаров и статуса пользователя

### Аватары

#### Типы аватаров
1. **Системные аватары** (`avatar_type = 1`):
   - Хранятся в `/static/avatars/`
   - Размеры: основной (200x200px), миниатюра (50x50px)
   - Форматы: `.webp` для оптимизации
   - Пути формируются по шаблону: `/media/users/{username[0]}/{username[0:2]}/{username}/avatar/`

2. **Пользовательские аватары** (`avatar_type = 2`):
   - Загружаются пользователем
   - Автоматически конвертируются в `.webp`
   - Создаются миниатюры
   - Хранятся в структуре: `/media/users/{username[0]}/{username[0:2]}/{username}/avatar/`

#### Обработка аватаров
- **Загрузка**:
  - Валидация формата (jpg, png, webp)
  - Проверка размера (max 5MB)
  - Конвертация в webp
  - Создание миниатюр
  - Удаление старых файлов

- **Отображение**:
  - Функция `getUserAvatar(user, size, backendUrl, imageVersion)`:
    - `size`: 'full' или 'mini'
    - `imageVersion`: для кэширования
    - Fallback на системный аватар при ошибках

### Статус пользователя

#### Определение онлайн-статуса
1. **Механизм определения**:
   - Поле `last_activity` в модели User
   - Обновляется при каждом действии пользователя
   - Считается онлайн если активен последние 5 минут

2. **Обновление статуса**:
   - Middleware `UpdateLastActivityMiddleware`:
     - Отслеживает все запросы
     - Обновляет `last_activity`
     - Исключает статические файлы и API-запросы

3. **Кэширование**:
   - Redis для хранения статусов
   - TTL = 5 минут
   - Автоматическое обновление при активности

#### Отображение статуса
1. **В профиле**:
   ```jsx
   <span className={`w-3 h-3 rounded-full ${userData?.is_online ? 'bg-green-500' : 'bg-red-500'}`}></span>
   <span className="text-sm text-gray-700">{userData?.status}</span>
   ```

2. **В списках**:
   - Индикатор в списке друзей
   - Индикатор в диалогах
   - Индикатор в подписчиках/подписках

3. **Форматирование времени**:
   - "В сети" - если онлайн
   - "Был(а) в сети {time}" - если оффлайн
   - Форматы времени:
     - Сегодня: "в {HH:mm}"
     - Вчера: "вчера в {HH:mm}"
     - Этот год: "{DD.MM} в {HH:mm}"
     - Прошлые годы: "{DD.MM.YYYY} в {HH:mm}"

#### API для статуса
- `GET /api/users/{username}/status/`:
  ```json
  {
    "is_online": true,
    "last_activity": "2024-03-20T15:30:00Z",
    "status": "В сети"
  }
  ```

### Оптимизация
1. **Аватары**:
   - Ленивая загрузка изображений
   - Кэширование на клиенте
   - CDN для статических файлов
   - Оптимизация размеров

2. **Статус**:
   - WebSocket для real-time обновлений
   - Кэширование на клиенте
   - Batch-обновления для списков
   - Оптимизация запросов к БД

### Безопасность
1. **Аватары**:
   - Валидация MIME-типов
   - Проверка размера файлов
   - Защита от XSS
   - Безопасные пути к файлам

2. **Статус**:
   - Защита от спама
   - Rate limiting
   - Валидация данных
   - Безопасное хранение

## Система сообщений

### Основные компоненты
- `MessageContext` - глобальный контекст для управления состоянием сообщений
- `MessageDialog` - компонент диалога с сообщениями
- `MessageInput` - компонент ввода сообщений
- `MessageList` - компонент списка сообщений
- `MessageItem` - компонент отдельного сообщения

### Функциональность
1. **Отправка сообщений**
   - Поддержка текстовых сообщений
   - Поддержка файлов (изображения, документы)
   - Мгновенное обновление списка сообщений
   - Индикация статуса отправки

2. **Уведомления о непрочитанных сообщениях**
   - Глобальный счетчик непрочитанных сообщений
   - Бейдж в навигационной панели
   - Бейдж на кнопке сообщений в профиле
   - Автоматическое обновление при получении новых сообщений

3. **Отображение статуса пользователя**
   - Индикатор онлайн/оффлайн
   - Время последнего посещения
   - Статус "печатает..."
   - Обновление в реальном времени

4. **Быстрый доступ к сообщениям**
   - Кнопка сообщений в профиле пользователя (для владельца профиля)
   - Кнопка сообщений в сайдбаре профиля (для просмотра чужого профиля)
   - Кнопки сообщений в списках друзей/подписчиков/подписок

### API Endpoints
- `GET /api/dialogs/` - получение списка диалогов
- `GET /api/dialogs/{id}/` - получение информации о диалоге
- `GET /api/dialogs/{id}/messages/` - получение сообщений диалога
- `POST /api/dialogs/{id}/messages/` - отправка сообщения
- `GET /api/dialogs/unread_count/` - получение количества непрочитанных сообщений
- `POST /api/dialogs/{id}/read/` - отметка сообщений как прочитанных

### Безопасность
- Проверка прав доступа к диалогам
- Валидация входящих сообщений
- Защита от XSS в сообщениях
- Безопасная обработка файлов

### UI/UX особенности
- Адаптивный дизайн для мобильных устройств
- Поддержка темной темы
- Анимации при отправке/получении сообщений
- Форматирование дат (сегодня, в этом году, предыдущие годы)
- Отображение статуса доставки сообщений

### Интеграция с профилем
- Кнопка сообщений в профиле (для владельца)
- Кнопка сообщений в сайдбаре (для просмотра чужого профиля)
- Кнопки сообщений в списках друзей/подписчиков
- Отображение аватаров и статусов пользователей

### Оптимизация
- Пагинация сообщений
- Кэширование диалогов
- Оптимизированная загрузка изображений
- Эффективное обновление состояния 

## 10. Работа с обложками книг и этапами редактирования (обновлено: 2024-06-XX)

### Обложки книг
- С 06.2024 полностью убран тип обложки 'system' и вся логика с ним. Теперь поддерживается только пользовательская обложка ('custom').
- Если пользователь не загрузил/не сгенерировал обложку, в предпросмотре и на карточке книги показывается placeholder.
- На фронте всегда отправляется только оригинал обложки (700x1000), миниатюра генерируется на бэкенде и хранится на S3. Путь к миниатюре возвращается в поле `cover_mini_url`.
- Вся логика с полями `cover_temp_url`, `cover_type: system`, `miniFile` и пр. удалена из фронта и бэка.
- В редакторе книги предпросмотр обложки и названия обновляется в реальном времени по мере ввода.
- Вся синхронизация между формой и предпросмотром (название, обложка, автор) теперь мгновенная.

### Этапы создания и редактирования книги
- Книга создаётся с полем `creation_status = "creating"` и пустыми обязательными полями.
- После заполнения всех обязательных полей (название, аннотация, жанры, обложка) кнопка "Сохранить" становится активной.
- При нажатии "Сохранить" все поля отправляются на бэкенд, и если книга была в статусе `creating`, статус меняется на `draft`.
- После успешного сохранения и смены статуса происходит автоматический переход на второй этап — редактор текста (выбор редактора зависит от типа книги: рассказ, повесть, роман и т.д.).
- После перехода в статус `draft` черновик уже не удаляется при отмене — только на этапе создания (creation_status = 'creating') при отмене или закрытии вкладки черновик удаляется.
- Вся логика перехода между этапами реализована в компоненте `EditBook.jsx`.
- Устаревший компонент `EditBook2.jsx` больше не используется.

### Примечания по UX
- При отмене на этапе создания (creation_status = 'creating') появляется модалка с подтверждением удаления черновика. Если пользователь подтверждает — черновик удаляется, иначе остаётся.
- После перехода в статус `draft` отмена не приводит к удалению книги.
- Все предпросмотры и редакторы работают в реальном времени, без необходимости ручного обновления страницы.

## 11. Жанры, хештеги, аннотации, публикация глав

### Аннотация к книге: редактор и отображение

- Для аннотации книги используется редактор TipTap с минимальным тулбаром (жирный, курсив, подчёркнутый), ограничением 1000 символов и счётчиком.
- Аннотация сохраняется в формате HTML (разрешены только теги: <b>, <strong>, <i>, <em>, <u>, <br>, <p>).
- На бэкенде (Django) аннотация фильтруется через bleach при валидации и при выдаче через API (метод to_representation сериализатора BookSerializer).
- На фронте предпросмотр и отображение аннотации реализованы через dangerouslySetInnerHTML с классом annotation-preview.
- На странице списка книг автора (UserBooks) и на странице одной книги аннотация отображается как HTML, а не как текст.
- Для безопасного отображения аннотации всегда используйте dangerouslySetInnerHTML только с очищенными данными (bleach).

### Визуальные интервалы между абзацами

- В редакторе аннотации (TipTap) и при отображении аннотации между абзацами (<p>) установлен увеличенный интервал (margin-bottom: 0.6em), а обычная межстрочная высота — 1.5.
- CSS для редактора (AnnotationEditor.jsx):
  ```css
  .ProseMirror {
    line-height: 1.5;
  }
  .ProseMirror p {
    margin-top: 0;
    margin-bottom: 0.6em;
  }
  ```
- CSS для предпросмотра и отображения аннотации (UserBooks.jsx):
  ```css
  .annotation-preview {
    line-height: 1.7;
  }
  .annotation-preview p {
    margin-top: 0;
    margin-bottom: 2em;
  }
  ```
- Это обеспечивает современный, удобный и визуально чистый вид аннотаций как при вводе, так и при чтении.

### Безопасность

- На всех этапах (создание, редактирование, отображение) аннотация проходит очистку через bleach (разрешены только безопасные теги).
- На фронте HTML-аннотация выводится только через dangerouslySetInnerHTML и только в тех местах, где данные гарантированно очищены на бэке.
- Это предотвращает XSS и другие уязвимости, связанные с пользовательским HTML.

### UX-ограничения и рекомендации

- При выборе жанров в редакторе книги действует ограничение: не более 3 жанров (1 основной, 2 дополнительных).
- Все стили редактора, тулбара и предпросмотра аннотации приведены к единому виду, адаптированы под светлую и тёмную тему.
- Для всех кастомных стилей используйте отдельные CSS-файлы или <style> внутри компонентов.

## 12. Логика публикации глав и книги

- Публикация хотя бы одной главы переводит книгу в статус "опубликовано"
- Удаление всех глав переводит книгу в "черновик"
- Завершение книги публикует все главы
- Для рассказа (одна глава): только два статуса — "черновик" и "опубликовано полностью"
- При переводе в черновики снимается публикация со всех глав
- Кнопки смены статуса доступны только в нужных местах (нет на странице всех книг автора)
- Фильтрация книг по возрасту: гостям и другим пользователям не показываются "для взрослых"

## 13. Изменения и доработки (май 2025)

### UX, сортировка и визуал книг
- Исправлена и улучшена сортировка книг в профиле автора:
  - Порядок книг теперь всегда определяется на бэкенде по полям позиции (position_finished, position_in_progress, position_draft).
  - После reorder и обновления страницы порядок сохраняется и отображается корректно.
  - Фронтенд не сортирует книги самостоятельно, а использует порядок с бэка.
- Для гостей и пользователей без даты рождения показываются все книги, но при попытке открыть 18+ — модалка подтверждения возраста.
- В редакторе глав реализован подсчёт знаков, загрузка docx, автоотступ, синхронизация с сервером.
- Исправлены баги с сохранением, удалением глав, стили редактора, баги с типами данных и pagination.

### Визуал карточек книг
- Блок управления порядком (стрелки и номер позиции) теперь находится в правом верхнем углу карточки книги.
- Для гостей отображается только номер позиции, для автора — стрелки и возможность редактировать номер.
- Стрелки реализованы SVG-иконками, стили адаптированы под светлую и тёмную тему.
- Вся остальная информация (обложка, жанры, описание, кнопки) не смещается и остаётся на своих местах.

### Счетчик книг в профиле
- Счетчик книг в кнопке "Книги" теперь всегда актуален:
  - Для автора: "опубликовано + в процессе / черновики"
  - Для других пользователей: только "опубликовано + в процессе"
- Счетчик берётся из Outlet context, если страница UserBooks уже загружена.
- Если context не пришёл (или равен нулю), используется fallback: делается fetch и вычисляется локально.
- Это гарантирует работу счетчика при любых переходах и обновлениях.

### Плавная загрузка обложек
- Для обложек книг внедрён lazy loading (`loading="lazy"` у `<img>`).
- Добавлено плавное появление (opacity transition): обложка появляется мягко, когда загрузилась.
- Пока обложка не загружена, показывается аккуратная заглушка (серый прямоугольник).
- Это устраняет "подбросы" и мигание при загрузке списка книг.

### Прочее
- Внесены правки для корректной работы pagination, фильтрации, поиска и отображения книг.
- В планах — внедрение серверных миниатюр для обложек (thumbnails) для ещё большей оптимизации.

## Правила оформления глобальных и компонентных стилей для темы (dark/light)

- Класс темы (`dark` или `light`) всегда применяется к тегу `<html>`, а не к `<body>`.
- Все **глобальные стили** для темы (например, тултипы, модальные окна, глобальные инпуты) должны использовать только селекторы `html.dark ...` и `html.light ...`.
- Не использовать селекторы `body.dark ...` или `body:not(.dark) ...` — они не будут работать, так как класс темы всегда на `<html>`.
- Для компонентных/локальных стилей (жанры, хештеги, отдельные секции) допускается использовать scoped-классы (`.dark-select-dropdown`, `.custom-hashtag-input-wrapper.dark` и т.д.), которые явно задаются компонентом через props или className.
- Для глобальных компонентов, рендерящихся через portal (Ant Design Tooltip, Modal, Popconfirm и др.), всегда используйте глобальные селекторы с `html.dark`/`html.light` для корректной поддержки темы.
- В документации и новых компонентах всегда придерживаться этих правил для единообразия и предотвращения багов с темой.

---

## 14. Логика работы с хедером профиля пользователя

## Типы фонов хедера
- **Пользовательский хедер**: изображение, загруженное пользователем, хранится на S3 по шаблону:
  `https://storage.yandexcloud.net/lpo-test/users/{username[0]}/{username[0:2]}/{username}/pics/header/header_{username}.jpg`
- **Пресеты**: статические изображения, лежащие в public (и на S3 после деплоя) по пути `/header_presets/`. Выбор пресета сохраняет относительный путь (`/header_presets/xxx.jpg`).
- **Цвет/градиент**: задаётся двумя цветами, хранится как параметры профиля.

## Рамки хедера
- Рамки — PNG-файлы с прозрачностью, лежат в `/header_frames/` (public/S3).
- Выбор рамки сохраняет относительный путь (`/header_frames/xxx.png`).
- Рамка накладывается поверх любого типа фона (цвет, градиент, картинка, пресет, пользовательский).

## Алгоритм выбора и отображения
1. **Пользователь выбирает фон:**
   - Загружает своё изображение (валидируется по размеру, обрезается, сохраняется на S3 по шаблону).
   - Или выбирает пресет (сохраняется относительный путь).
   - Или выбирает цвет/градиент.
2. **Пользователь может выбрать рамку:**
   - Сохраняется относительный путь к PNG-рамке.
   - Можно убрать рамку (пустое значение).
3. **Отображение хедера:**
   - Если выбран пользовательский хедер — используется S3-путь по шаблону.
   - Если выбран пресет — используется относительный путь из public/S3.
   - Если выбран цвет/градиент — формируется CSS-градиент.
   - Если выбрана рамка — она накладывается поверх фона через CSS `backgroundImage` (два слоя).
4. **Миниатюра пользовательского хедера:**
   - Всегда вычисляется по шаблону S3-адреса (см. выше), не зависит от состояния профиля.
   - Если файл есть — миниатюра показывается, если нет — пустое поле.
   - Кнопка "Использовать это изображение" отправляет PATCH с этим путём.

## Добавление новых пресетов и рамок (для разработчика/админа)
1. **Добавить пресет:**
   - Положить новый JPG-файл в папку `frontend/public/header_presets/`.
   - При билде фронта автоматически сгенерируется `index.json` со списком файлов.
   - После деплоя на S3 пресет будет доступен по адресу `/header_presets/имя_файла.jpg`.
   - Не требуется менять код — пресеты подхватываются автоматически.
2. **Добавить рамку:**
   - Положить новый PNG-файл в папку `frontend/public/header_frames/`.
   - При билде фронта автоматически сгенерируется `index.json` со списком файлов.
   - После деплоя на S3 рамка будет доступна по адресу `/header_frames/имя_файла.png`.
   - Не требуется менять код — рамки подхватываются автоматически.
3. **Удаление/замена:**
   - Просто удалить/заменить файл в соответствующей папке и пересобрать фронт.

## Принципы и нюансы
- Все пути к статике (пресеты, рамки) — относительные, чтобы работало и с public, и с S3.
- Пользовательский хедер всегда по одному и тому же пути на S3, что позволяет легко обновлять и отображать миниатюру.
- Не требуется хранить lastUserHeader в состоянии — путь всегда вычисляется по username.
- Фронт не зависит от ответа бэка для отображения миниатюры пользовательского хедера.
- При смене фона на пресет/цвет миниатюра пользовательского хедера не пропадает, всегда можно выбрать её обратно.
- Вся логика выбора, отображения и обновления хедера реализована на фронте, бэкенд только сохраняет выбранные значения.

---

## [2024-06-27] Баг: не вставляются изображения в редакторе Tiptap

### Симптомы
- После загрузки изображения через редактор главы (Tiptap + @tiptap/extension-image) картинка не появляется в тексте, хотя текстовые вставки работают.
- В консоли видно, что editor и schema корректны, ошибок нет, но в HTML редактора <img> не появляется.

### Причина
- Для вставки изображения использовался метод:
  ```js
  editor.chain().focus().insertContent({
    type: 'image',
    attrs: { src: url, width }
  }).run();
  ```
- После обновлений/чистки расширений Tiptap схема стала строже, и insertContent с объектом node image не срабатывает в большинстве контекстов (например, если курсор не в пустом параграфе).
- Ранее, возможно, использовалась команда setImage, либо схема была иной.

### Решение
- Для вставки изображения в Tiptap v2 с @tiptap/extension-image нужно использовать команду:
  ```js
  editor.chain().focus().setImage({ src: url, width }).run();
  ```
- После замены insertContent на setImage вставка изображений снова работает корректно.

### Вывод
- Для вставки изображений всегда используйте editor.chain().focus().setImage(...), а не insertContent с node image.
- Если изображения перестали вставляться после обновлений — проверьте, не используется ли insertContent вместо setImage.

---

## Нюансы работы с изображениями (Tiptap, S3, presigned URL)

### Основные принципы
- В редакторе Tiptap поддерживается вставка изображений с возможностью ресайза и управления.
- Все изображения книги хранятся в приватном S3-бакете.
- В БД сохраняются **только относительные пути** к изображениям (например, `book_pics/000a/47/chapics/47_cc5ec274.jpg`).
- Для отображения в редакторе автору всегда подгружаются новые presigned URL для всех изображений главы.

### Как работает подмена ссылок
- При открытии редактора фронтенд отправляет POST-запрос на `/api/books/get_image_links/` с массивом относительных путей (иногда с префиксом `/media/private/` или `/media/`).
- Backend теперь **автоматически убирает префиксы** `/media/private/` и `/media/` из начала пути перед поиском в базе и генерацией presigned URL.
- В ответе ключом остаётся оригинальный путь из запроса, чтобы фронт мог корректно заменить ссылки в тексте.
- При сохранении главы все presigned URL заменяются обратно на относительные пути (без префиксов).

### Почему это важно
- Это гарантирует, что:
  - Автор всегда видит рабочие ссылки на изображения.
  - В БД и при сохранении остаются только относительные пути (без временных ссылок и префиксов).
  - Загрузка новых изображений и их отображение не нарушаются.
- Если потребуется поддержка других ролей или новых типов файлов, аналогичная логика может быть расширена.

### Пример пути
- В БД: `book_pics/000a/47/chapics/47_cc5ec274.jpg`
- С фронта может прийти: `/media/private/book_pics/000a/47/chapics/47_cc5ec274.jpg`
- Backend приведёт путь к нужному формату и найдёт нужную запись.

---

## Автоочистка изображений при сохранении главы

### Проблема (исторически)
- При добавлении изображения в редактор Tiptap оно сначала вставляется с временной (presigned) ссылкой.
- После сохранения главы фронт заменяет все ссылки на относительные (`/media/private/...`), и только тогда в базе появляется корректный путь.
- Если автоочистка запускалась сразу после сохранения, но до того как в базе гарантированно лежит "чистый" текст, нужные картинки могли быть ошибочно удалены (из-за несовпадения путей).

### Решение
- Теперь после сохранения главы (update/partial_update) на бэке происходит:
  1. Сначала глава сохраняется в базу (`super().update()` / `super().partial_update()`).
  2. Затем **принудительно перечитывается** из базы (`instance.refresh_from_db()`), чтобы получить именно тот текст, который реально лежит в базе.
  3. Только после этого запускается автоочистка (`self.cleanup_unused_images(instance)`), которая сравнивает пути из текста главы и из базы изображений.
- Это полностью исключает баги с преждевременным удалением нужных картинок.

### Итог
- Автоочистка всегда работает только по реально сохранённому тексту главы.
- Картинки не удаляются, если они используются в тексте.
- Вся логика устойчива к гонкам и асинхронности между фронтом и бэком.

---

### Пользовательские аватары (2025)

#### Структура хранения
- Каждый пользователь может загрузить до 2 собственных аватара.
- Для каждого аватара создаётся миниатюра (thumb).
- Файлы хранятся в S3/Yandex Cloud по схеме с группировкой по 500 пользователей:
  - `media/public/avatars/{group_folder}/{user_id}/{user_id}_ava_xxx.jpg` (основной)
  - `media/public/avatars/{group_folder}/{user_id}/{user_id}_avathumb_xxx.jpg` (миниатюра)
  - Где group_folder = буква+три цифры, например, `a000`, `a001`, ...
- В базе есть таблица UserAvatar (user_id, path, thumb_path, created_at).

#### Системные аватары
- Хранятся в `/static/ava_presets/`:
  - `ava_m.jpg` и `ava_m_s.jpg` - мужской аватар и его миниатюра
  - `ava_f.jpg` и `ava_f_s.jpg` - женский аватар и его миниатюра
  - `ava_u.jpg` и `ava_u_s.jpg` - аватар по умолчанию и его миниатюра
- При выборе пола пользователя автоматически применяется соответствующий системный аватар
- Пути к системным аватарам формируются по шаблону: `/static/ava_presets/ava_{gender}.jpg`

#### API
- `GET /api/auth/user-avatars/` — список пользовательских аватаров (до 2).
- `POST /api/auth/user-avatars/` — загрузка нового аватара (если их < 2).
- `DELETE /api/auth/user-avatars/<id>/` — удаление аватара (удаляет и миниатюру, и запись).
- `PATCH /api/auth/profile/` — применение выбранного аватара (отправляется относительный путь к файлам).

#### Логика применения и удаления
- При применении аватара через миниатюру отправляется PATCH с полями `avatar` и `avatar_thumbnail` (оба относительный путь).
- Если удаляется активный аватар и есть ещё один — он применяется автоматически.
- Если удаляется последний — сбрасывается на системный аватар, соответствующий полу пользователя.
- После всех операций вызывается глобальный refreshUser (useAuth), чтобы все компоненты мгновенно отразили изменения.

#### Синхронизация и UX
- Все компоненты (Navbar, Sidebar, Feed, Messages и т.д.) используют только глобальный user из useAuth, поэтому миниатюра и имя пользователя всегда актуальны.
- После смены/удаления аватара не требуется перезагрузка страницы.
- Кнопка "Загрузить свой" становится неактивной при лимите (2 аватара), появляется модалка с подсказкой.
- При клике на активную миниатюру ничего не происходит.
- При смене пола пользователя системный аватар обновляется мгновенно, без перезагрузки страницы.

---

### Пользовательские хедеры профиля (2025)

#### Структура хранения
- Каждый пользователь может загрузить до 4 собственных хедеров.
- Файлы хранятся в S3/Yandex Cloud по схеме с группировкой по 500 пользователей:
  - `media/public/headers/{group_folder}/{user_id}/header_{user_id}_xxx.jpg`
- В базе есть таблица UserHeaderImage (user_id, path, created_at).

#### API
- `GET /api/auth/user-header-images/` — список пользовательских хедеров (до 4).
- `POST /api/auth/user-header-images/` — загрузка нового хедера (если их < 4).
- `DELETE /api/auth/user-header-images/<id>/` — удаление хедера.
- `PATCH /api/auth/profile/` — применение выбранного хедера (отправляется относительный путь).

#### Логика применения и удаления
- При применении хедера через миниатюру отправляется PATCH с относительным путём.
- Если удаляется активный хедер и есть ещё один — он применяется автоматически.
- Если удаляется последний — сбрасывается на системный (градиент).
- После всех операций вызывается глобальный refreshUser (useAuth), чтобы все компоненты мгновенно отразили изменения.

#### Синхронизация и UX
- Все компоненты используют только глобальный user из useAuth, поэтому фон профиля всегда актуален.
- После смены/удаления хедера не требуется перезагрузка страницы.
- Кнопка "Загрузить свой" становится неактивной при лимите (4 хедера), появляется модалка с подсказкой.
- При клике на активную миниатюру ничего не происходит.

---

## Система пользовательских аватаров и хедеров профиля: схема, БД, API, взаимодействия

### 1. Схема таблиц базы данных

#### UserAvatar

| Поле         | Тип         | Описание                                 |
|--------------|-------------|------------------------------------------|
| id           | int, PK     | Уникальный идентификатор                 |
| user_id      | int, FK     | Владелец (User)                          |
| path         | varchar     | Относительный путь к файлу аватара       |
| thumb_path   | varchar     | Относительный путь к миниатюре           |
| created_at   | datetime    | Дата загрузки                            |

- **Ограничение:** максимум 2 записи на пользователя.

#### UserHeaderImage

| Поле         | Тип         | Описание                                 |
|--------------|-------------|------------------------------------------|
| id           | int, PK     | Уникальный идентификатор                 |
| user_id      | int, FK     | Владелец (User)                          |
| path         | varchar     | Относительный путь к файлу хедера        |
| created_at   | datetime    | Дата загрузки                            |

- **Ограничение:** максимум 4 записи на пользователя.

#### User (фрагмент, относящийся к аватарам/хедерам)

| Поле               | Тип      | Описание                                 |
|--------------------|----------|------------------------------------------|
| avatar             | varchar  | Относительный путь к активному аватару   |
| avatar_thumbnail   | varchar  | Относительный путь к миниатюре           |
| header_image       | varchar  | Относительный путь к активному хедеру    |
| header_frame       | varchar  | Относительный путь к PNG-рамке           |
| header_gradient    | json     | Цвета для градиента (если выбран)        |

---

### 2. Схема хранения файлов

- **Аватары:**  
  `media/public/avatars/{group_folder}/{user_id}/{user_id}_ava_xxx.jpg`  
  `media/public/avatars/{group_folder}/{user_id}/{user_id}_avathumb_xxx.jpg`
- **Хедеры:**  
  `media/public/headers/{group_folder}/{user_id}/header_{user_id}_xxx.jpg`
- **Группировка:**  
  `{group_folder}` = буква+три цифры, например, `a000`, `a001`, ... (по 500 пользователей в папке)

---

### 3. API эндпоинты

#### Аватары

| Метод | URL                              | Описание                                 | Тело запроса/ответа                |
|-------|----------------------------------|------------------------------------------|------------------------------------|
| GET   | /api/auth/user-avatars/          | Получить список пользовательских аватаров| [{id, path, thumb_path, ...}]      |
| POST  | /api/auth/user-avatars/          | Загрузить новый аватар                   | file                               |
| DELETE| /api/auth/user-avatars/{id}/     | Удалить аватар                           |                                    |
| PATCH | /api/auth/profile/               | Применить аватар (и миниатюру)           | {avatar, avatar_thumbnail}         |

#### Хедеры

| Метод | URL                                  | Описание                                 | Тело запроса/ответа                |
|-------|--------------------------------------|------------------------------------------|------------------------------------|
| GET   | /api/auth/user-header-images/        | Получить список пользовательских хедеров | [{id, path, ...}]                  |
| POST  | /api/auth/user-header-images/        | Загрузить новый хедер                    | file                               |
| DELETE| /api/auth/user-header-images/{id}/   | Удалить хедер                            |                                    |
| PATCH | /api/auth/profile/                   | Применить хедер                          | {header_image}                     |

---

### 4. Логика взаимодействия и синхронизации

#### Загрузка

- **Аватар:**  
  1. Пользователь выбирает файл (jpg/png/webp, ≤5MB).
  2. POST на `/api/auth/user-avatars/`.
  3. Сервер конвертирует в webp, создаёт миниатюру, сохраняет файлы, добавляет запись в UserAvatar.
  4. Если лимит (2) превышен — ошибка.

- **Хедер:**  
  1. Пользователь выбирает файл (jpg/png/webp, ≤5MB).
  2. POST на `/api/auth/user-header-images/`.
  3. Сервер сохраняет файл, добавляет запись в UserHeaderImage.
  4. Если лимит (4) превышен — ошибка.

#### Применение

- **Аватар:**  
  1. Пользователь кликает на миниатюру.
  2. PATCH на `/api/auth/profile/` с `{avatar, avatar_thumbnail}` (относительные пути).
  3. Сервер обновляет профиль.
  4. На фронте вызывается `refreshUser()` — все компоненты мгновенно обновляются.

- **Хедер:**  
  1. Пользователь кликает на миниатюру/пресет/цвет/рамку.
  2. PATCH на `/api/auth/profile/` с `{header_image}` (или `{header_frame}`, `{header_gradient}`).
  3. Сервер обновляет профиль.
  4. На фронте вызывается `refreshUser()`.

#### Удаление

- **Аватар:**  
  1. Пользователь жмёт "удалить".
  2. DELETE на `/api/auth/user-avatars/{id}/`.
  3. Сервер удаляет файлы и запись.
  4. Если удалён активный аватар:
     - Если остался ещё один — он применяется автоматически (PATCH).
     - Если не осталось — сбрасывается на системный (PATCH).
  5. `refreshUser()`.

- **Хедер:**  
  1. Пользователь жмёт "удалить".
  2. DELETE на `/api/auth/user-header-images/{id}/`.
  3. Сервер удаляет файл и запись.
  4. Если удалён активный хедер:
     - Если остался ещё один — он применяется автоматически (PATCH).
     - Если не осталось — сбрасывается на системный (градиент, PATCH).
  5. `refreshUser()`.

---

### 5. UX и синхронизация

- Все компоненты используют только глобальный user из AuthContext (`useAuth`).
- После любого изменения (загрузка, удаление, применение) вызывается `refreshUser()`, что мгновенно обновляет все миниатюры, имена, фоны и т.д.
- Кнопка "Загрузить свой" неактивна при лимите (2/4), появляется модалка с подсказкой.
- При клике на активную миниатюру/хедер ничего не происходит.
- После удаления активного аватара/хедера оставшийся применяется автоматически, всё обновляется мгновенно.
- Не требуется перезагрузка страницы.

---

### 6. Диаграмма взаимодействий (sequence)

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant Storage

    User->>Frontend: Выбрать/удалить/применить аватар/хедер
    Frontend->>Backend: POST/DELETE/PATCH (API)
    Backend->>Storage: Сохраняет/удаляет файл(ы)
    Backend->>DB: Обновляет записи
    Backend-->>Frontend: Ответ (успех/ошибка)
    Frontend->>Frontend: refreshUser()
    Frontend-->>User: Мгновенное обновление UI
```

---

### 7. Примеры запросов и ответов

#### Получить список аватаров

```http
GET /api/auth/user-avatars/
```
```json
[
  {
    "id": 12,
    "path": "media/public/avatars/a000/123/123_ava_1.webp",
    "thumb_path": "media/public/avatars/a000/123/123_avathumb_1.webp",
    "created_at": "2025-06-20T12:34:56Z"
  }
]
```

#### Применить аватар

```http
PATCH /api/auth/profile/
Content-Type: application/json

{
  "avatar": "media/public/avatars/a000/123/123_ava_1.webp",
  "avatar_thumbnail": "media/public/avatars/a000/123/123_avathumb_1.webp"
}
```

#### Удалить аватар

```http
DELETE /api/auth/user-avatars/12/
```

---

### 8. Примечания

- Все пути — относительные, чтобы работало и с public, и с S3.
- Миниатюры всегда вычисляются по шаблону, не зависят от состояния профиля.
- При смене фона на пресет/цвет миниатюра пользовательского хедера не пропадает, всегда можно выбрать её обратно.
- Вся логика выбора, отображения и обновления реализована на фронте, бэкенд только сохраняет выбранные значения.

---

**Этот блок можно вставлять в документацию как есть. Если нужно — добавлю ER-диаграмму или расширю по вашей задаче!**
