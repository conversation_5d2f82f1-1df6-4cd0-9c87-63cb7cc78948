from celery import shared_task
from books.models import BookChapter, Book
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
import logging
from django.utils import timezone
from datetime import timedelta
from django.conf import settings
import boto3
from botocore.exceptions import ClientError
import io
import base64
from PIL import Image
from django.core.files.base import ContentFile
from books.models import book_cover_path, book_covermini_path, book_cover_editor_path
from storages.backends.s3boto3 import S3Boto3Storage

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def save_book_cover_async(self, book_id, cover_data, cover_editor_data=None):
    """
    Асинхронное сохранение обложки книги на S3

    Args:
        book_id: ID книги
        cover_data: Base64 данные основной обложки (с лейблами)
        cover_editor_data: Base64 данные обложки для редактора (без лейблов)

    Returns:
        dict: Результат операции с URL обложек
    """
    try:
        logger.info(f"Starting async cover save for book {book_id}")
        # Обновляем статус задачи
        self.update_state(
            state='PROGRESS',
            meta={'current': 50, 'total': 100, 'status': 'Сохраняем обложку...'}
        )

        # Получаем книгу
        try:
            book = Book.objects.get(id=book_id)
        except Book.DoesNotExist:
            raise Exception(f"Книга с ID {book_id} не найдена")



        # Декодируем base64 данные основной обложки
        if cover_data.startswith('data:image'):
            cover_data = cover_data.split(',')[1]
        cover_bytes = base64.b64decode(cover_data)

        # Декодируем base64 данные обложки для редактора (если есть)
        editor_bytes = None
        if cover_editor_data:
            if cover_editor_data.startswith('data:image'):
                cover_editor_data = cover_editor_data.split(',')[1]
            editor_bytes = base64.b64decode(cover_editor_data)



        # Создаем файл основной обложки
        logger.info(f"Saving main cover for book {book_id}")
        cover_file = ContentFile(cover_bytes)
        cover_filename = f"{book_id}_cover.jpg"
        book.cover.save(cover_filename, cover_file, save=False)
        logger.info(f"Main cover saved: {book.cover.name}")



        # Создаем и сохраняем миниатюру
        image = Image.open(io.BytesIO(cover_bytes))
        image = image.convert('RGB')
        image = image.resize((210, 300), Image.LANCZOS)
        buf = io.BytesIO()
        image.save(buf, format='JPEG', quality=100)
        buf.seek(0)
        mini_filename = f"{book_id}_cover_mini.jpg"
        book.cover_mini.save(mini_filename, ContentFile(buf.getvalue()), save=False)



        # Сохраняем обложку для редактора
        if editor_bytes:
            editor_file = ContentFile(editor_bytes)
            editor_filename = f"{book_id}_editor_cover.jpg"
            book.cover_editor.save(editor_filename, editor_file, save=False)
        else:
            # Если обложка для редактора не предоставлена, используем основную
            editor_filename = f"{book_id}_editor_cover.jpg"
            book.cover_editor.save(editor_filename, ContentFile(cover_bytes), save=False)

        # Устанавливаем тип обложки и сохраняем
        book.cover_type = 'custom'
        book.save(update_fields=['cover', 'cover_mini', 'cover_editor', 'cover_type'])

        # Запускаем задачу очистки старых обложек в фоне (через 5 минут)
        from django.conf import settings
        if getattr(settings, 'ENABLE_COVER_CLEANUP', True):
            cleanup_delay = getattr(settings, 'COVER_CLEANUP_DELAY', 300)  # 5 минут по умолчанию
            cleanup_book_covers.apply_async(args=[book_id], countdown=cleanup_delay)

        # Получаем URL обложек
        cover_url = book.cover.url if book.cover else None
        cover_mini_url = book.cover_mini.url if book.cover_mini else None
        cover_editor_url = book.cover_editor.url if book.cover_editor else None

        # Завершаем успешно
        return {
            'current': 100,
            'total': 100,
            'status': 'Обложка успешно сохранена!',
            'cover_url': cover_url,
            'cover_mini_url': cover_mini_url,
            'cover_editor_url': cover_editor_url
        }

    except Exception as exc:
        logger.error(f"Ошибка при сохранении обложки для книги {book_id}: {exc}")
        self.update_state(
            state='FAILURE',
            meta={'current': 0, 'total': 100, 'status': f'Ошибка: {str(exc)}'}
        )
        raise exc

@shared_task(bind=True, ignore_result=True)
def publish_chapters_batch_task(self, book_id, chapter_ids, publish_as_finished=False):
    """
    ОПТИМИЗИРОВАННАЯ задача для массовой публикации глав в запланированное время
    """
    start_time = timezone.now()
    logger.info(f"Начало выполнения МАССОВОЙ задачи публикации {len(chapter_ids)} глав книги {book_id} в {start_time}")

    try:
        from books.models import Book, BookChapter

        book = Book.objects.get(id=book_id)
        chapters = BookChapter.objects.filter(id__in=chapter_ids, book=book, is_published=False)

        if not chapters.exists():
            logger.info(f"Все главы книги {book_id} уже опубликованы, пропускаем")
            return

        # Используем оптимизированную массовую публикацию
        if publish_as_finished:
            # Если нужно завершить произведение - публикуем только выбранные главы
            # Остальные неопубликованные главы БЕЗ планирования должны быть запланированы отдельно
            logger.info(f"МАССОВАЯ отложенная публикация завершает произведение {book_id} - публикуем только выбранные главы")

            from django.db import transaction
            now = timezone.now()

            with transaction.atomic():
                # Публикуем только выбранные главы
                updated_count = chapters.update(
                    is_published=True,
                    published_at=now,
                    scheduled_publish_at=None,
                    celery_task_id=None,
                    publish_as_finished=None
                )

            # Проверяем, остались ли неопубликованные главы
            remaining_unpublished = BookChapter.objects.filter(
                book=book,
                is_published=False
            ).count()

            if remaining_unpublished == 0:
                # Если все главы опубликованы - завершаем произведение
                book.status = 'finished'
                book.is_published = True
                book.is_finished = True
                book.updated_at = timezone.now()
                book.save(update_fields=['status', 'is_published', 'is_finished', 'updated_at'])
                logger.info(f"Произведение {book_id} ЗАВЕРШЕНО - все главы опубликованы")
            else:
                # Если есть неопубликованные главы - переводим в процесс публикации
                if book.status == 'draft':
                    book.status = 'in_progress'
                    book.is_published = True
                    if not book.published_at:
                        book.published_at = now
                    book.updated_at = now
                    book.save(update_fields=['status', 'is_published', 'published_at', 'updated_at'])
                logger.info(f"Произведение {book_id} в процессе публикации - остается {remaining_unpublished} неопубликованных глав")

            logger.info(f"МАССОВО опубликовано {updated_count} глав книги {book_id} одним SQL-запросом через отложенную публикацию")
        else:
            # Обычная массовая публикация выбранных глав
            from django.db import transaction
            now = timezone.now()

            with transaction.atomic():
                updated_count = chapters.update(
                    is_published=True,
                    published_at=now,
                    scheduled_publish_at=None,
                    celery_task_id=None,
                    publish_as_finished=None
                )

            # Обновляем статус книги
            if book.status == 'draft':
                book.status = 'in_progress'
                book.is_published = True
                if not book.published_at:
                    book.published_at = now
                book.updated_at = now
                book.save(update_fields=['status', 'is_published', 'published_at', 'updated_at'])

            logger.info(f"МАССОВО опубликовано {updated_count} глав книги {book_id} одним SQL-запросом через отложенную публикацию")

        # Отправляем WebSocket события для всех опубликованных глав
        try:
            channel_layer = get_channel_layer()

            # Получаем обновленные данные глав после публикации
            published_chapters = BookChapter.objects.filter(id__in=chapter_ids, book=book)

            # Подготавливаем данные для массового события
            chapter_data = []
            for chapter in published_chapters:
                chapter_data.append({
                    'id': chapter.id,
                    'title': chapter.title,
                    'order': chapter.order
                })

            # НЕ отправляем индивидуальные события - только массовое событие

            # Отправляем специальное событие массовой публикации
            async_to_sync(channel_layer.group_send)(
                f'book_{book_id}',
                {
                    'type': 'chapters_batch_published',
                    'chapters': chapter_data,
                    'count': len(chapter_data),
                    'book_is_finished': book.is_finished,
                    'book_status': book.status,
                    'was_scheduled': True  # Указываем, что это была отложенная публикация
                }
            )

            logger.info(f"WebSocket события отправлены для {published_chapters.count()} глав книги {book_id}")
        except Exception as e:
            logger.error(f"Ошибка при отправке WebSocket событий для книги {book_id}: {e}")

        # Логируем время выполнения
        end_time = timezone.now()
        execution_time = (end_time - start_time).total_seconds()
        logger.info(f"МАССОВАЯ задача публикации {len(chapter_ids)} глав книги {book_id} завершена за {execution_time:.3f} секунд")

    except Book.DoesNotExist:
        logger.error(f"Книга {book_id} не найдена")
    except Exception as e:
        logger.error(f"Ошибка при МАССОВОЙ публикации глав книги {book_id}: {e}")
        raise self.retry(countdown=60, max_retries=2)


@shared_task(bind=True, ignore_result=True)
def publish_chapter_task(self, chapter_id, publish_as_finished=None):
    """
    Задача для автоматической публикации главы в запланированное время
    """
    start_time = timezone.now()
    logger.info(f"Начало выполнения задачи публикации главы {chapter_id} в {start_time}")
    
    try:
        chapter = BookChapter.objects.get(id=chapter_id)
        
        # Проверяем, не опубликована ли уже глава
        if chapter.is_published:
            logger.info(f"Глава {chapter_id} уже опубликована, пропускаем")
            return
            
        # Проверяем, не отменена ли публикация
        if chapter.scheduled_publish_at is None:
            logger.info(f"Публикация главы {chapter_id} отменена, пропускаем")
            return
            
        # Проверяем, не просрочена ли публикация (допускаем задержку до 30 секунд)
        if chapter.scheduled_publish_at and chapter.scheduled_publish_at < timezone.now():
            delay_seconds = (timezone.now() - chapter.scheduled_publish_at).total_seconds()
            if delay_seconds > 30:
                logger.warning(f"Публикация главы {chapter_id} просрочена на {delay_seconds:.0f} секунд")
            else:
                logger.info(f"Публикация главы {chapter_id} выполняется с допустимой задержкой {delay_seconds:.0f} секунд")
        
        # КРИТИЧЕСКИ ВАЖНО: Проверяем, что все предыдущие главы опубликованы
        # Это обеспечивает строгий порядок публикации
        previous_chapters = chapter.book.chapters.filter(
            order__lt=chapter.order,
            is_published=False
        )
        
        if previous_chapters.exists():
            # Есть неопубликованные предыдущие главы - откладываем публикацию
            unpublished_orders = list(previous_chapters.values_list('order', flat=True))
            logger.warning(f"Глава {chapter_id} (order: {chapter.order}) отложена - ждем публикации предыдущих глав с order: {unpublished_orders}")
            
            # Перепланируем задачу через 5 секунд
            eta = timezone.now() + timezone.timedelta(seconds=5)
            result = publish_chapter_task.apply_async(
                args=[chapter_id, publish_as_finished],
                eta=eta,
                priority=10 - min(chapter.order, 10)  # Сохраняем приоритет
            )
            chapter.celery_task_id = result.id
            chapter.save(update_fields=['celery_task_id'])
            
            logger.info(f"Глава {chapter_id} перепланирована на {eta}")
            return
        
        # Сохраняем флаг завершения до публикации главы
        should_finish_book = None
        if chapter.publish_as_finished is not None:
            should_finish_book = chapter.publish_as_finished
            logger.info(f"Для {chapter.book.type} {chapter.book.id}: статус завершения = {chapter.publish_as_finished}")
        
        # ОПТИМИЗИРОВАНО: Используем массовую логику для отложенных публикаций
        book = chapter.book

        # Проверяем, завершает ли эта глава произведение
        # Приоритет: параметр задачи > поле главы
        should_finish_book = publish_as_finished if publish_as_finished is not None else getattr(chapter, 'publish_as_finished', False)

        if should_finish_book:
            # Если нужно завершить произведение - используем оптимизированную логику
            logger.info(f"Отложенная публикация завершает произведение {book.id}")

            # Устанавливаем флаг для избежания двойного обновления рейтинга
            book._skip_rating_update = True
            book.publish_all_chapters()

            # Устанавливаем статус "завершено"
            old_status = book.status
            book.status = 'finished'
            book.is_published = True
            book.is_finished = True
            book.updated_at = timezone.now()

            # Сбрасываем флаг для обработки рейтинга при сохранении
            book._skip_rating_update = False
            book.save(update_fields=['status', 'is_published', 'is_finished', 'updated_at'])

            logger.info(f"Произведение {book.id} завершено через отложенную публикацию")
        else:
            # Обычная публикация одной главы
            chapter.is_published = True
            chapter.published_at = timezone.now()
            chapter.scheduled_publish_at = None
            chapter.celery_task_id = None
            chapter.publish_as_finished = None

            # Сохраняем главу БЕЗ вызова sync_status (используем флаг для отключения)
            chapter._skip_sync_status = True
            chapter.save(update_fields=['is_published', 'published_at', 'scheduled_publish_at', 'celery_task_id', 'publish_as_finished'])

            # Обновляем статус книги для обычной публикации
            if book.status == 'draft':
                book.status = 'in_progress'

            # Для обратной совместимости обновляем старые поля
            book.is_published = True

            # Устанавливаем дату первой публикации, если её еще нет
            if not book.published_at:
                book.published_at = chapter.published_at
                logger.info(f"Установлена дата первой публикации для книги {book.id}")

            book.updated_at = timezone.now()
            book.save(update_fields=['status', 'is_published', 'published_at', 'updated_at'])
            logger.info(f"Статус книги {book.id} обновлен: status={book.status}, published={book.is_published}")
        
        logger.info(f"Глава {chapter_id} (порядок: {chapter.order}) успешно опубликована")
        
        # Отправляем событие в WebSocket канал
        try:
            logger.info(f"Попытка отправить событие в канал для книги {chapter.book.id}, главы {chapter.id}")
            channel_layer = get_channel_layer()
            async_to_sync(channel_layer.group_send)(
                f'book_{chapter.book.id}',
                {
                    'type': 'chapter_published',
                    'chapter_id': chapter.id,
                    'title': chapter.title,
                    'book_is_finished': book.is_finished,  # Передаем статус завершения книги для обратной совместимости
                    'book_status': book.status,  # Передаем новый статус книги
                }
            )
            logger.info(f"Событие отправлено в канал для книги {chapter.book.id}, главы {chapter.id}")
        except Exception as e:
            logger.error(f"Ошибка при отправке события в канал для главы {chapter_id}: {e}")
            
        # Логируем время выполнения
        end_time = timezone.now()
        execution_time = (end_time - start_time).total_seconds()
        logger.info(f"Задача публикации главы {chapter_id} завершена за {execution_time:.3f} секунд")
        
    except BookChapter.DoesNotExist:
        logger.error(f"Глава {chapter_id} не найдена")
    except Exception as e:
        logger.error(f"Ошибка при публикации главы {chapter_id}: {e}")
        # Повторяем задачу через 1 минуту в случае ошибки
        raise self.retry(countdown=60, max_retries=2)


# ===== ФУНКЦИИ ДЛЯ РАБОТЫ С S3 =====

def get_s3_client():
    """Получить клиент S3 для работы с Yandex Cloud"""
    return boto3.client(
        's3',
        endpoint_url='https://storage.yandexcloud.net',
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        region_name='ru-central1'
    )

def get_book_cover_folder(book_id):
    """Получить путь к папке обложек книги"""
    from users.models import get_group_folder
    group_folder = get_group_folder(book_id)
    # Добавляем префикс media/public/ как в настройках S3
    return f"media/public/book_covers/{group_folder}/{book_id}/"

def list_s3_objects(s3_client, bucket, prefix):
    """Получить список объектов в S3 по префиксу"""
    try:
        response = s3_client.list_objects_v2(
            Bucket=bucket,
            Prefix=prefix
        )
        return response.get('Contents', [])
    except ClientError as e:
        logger.error(f"Error listing S3 objects: {e}")
        return []

def delete_s3_object(s3_client, bucket, key):
    """Удалить объект из S3"""
    try:
        s3_client.delete_object(Bucket=bucket, Key=key)
        logger.info(f"Deleted S3 object: {key}")
        return True
    except ClientError as e:
        logger.error(f"Error deleting S3 object {key}: {e}")
        return False

def get_current_cover_files(book):
    """Получить список текущих файлов обложек книги"""
    current_files = set()

    if book.cover:
        # Извлекаем путь из поля модели и добавляем префикс media/public/
        cover_path = book.cover.name
        if cover_path:
            # Добавляем префикс media/public/ если его нет
            if not cover_path.startswith('media/public/'):
                cover_path = f"media/public/{cover_path}"
            current_files.add(cover_path)
            logger.info(f"Current cover file: {cover_path}")

    if book.cover_mini:
        cover_mini_path = book.cover_mini.name
        if cover_mini_path:
            # Добавляем префикс media/public/ если его нет
            if not cover_mini_path.startswith('media/public/'):
                cover_mini_path = f"media/public/{cover_mini_path}"
            current_files.add(cover_mini_path)
            logger.info(f"Current cover_mini file: {cover_mini_path}")

    if book.cover_editor:
        cover_editor_path = book.cover_editor.name
        if cover_editor_path:
            # Добавляем префикс media/public/ если его нет
            if not cover_editor_path.startswith('media/public/'):
                cover_editor_path = f"media/public/{cover_editor_path}"
            current_files.add(cover_editor_path)
            logger.info(f"Current cover_editor file: {cover_editor_path}")

    return current_files


# ===== ЗАДАЧИ ОЧИСТКИ ОБЛОЖЕК =====

@shared_task(bind=True, max_retries=3)
def cleanup_book_covers(self, book_id):
    """
    Очистка старых обложек книги через 5 секунд после сохранения.
    Оставляет только текущие используемые файлы.
    """
    try:
        logger.info(f"Starting cover cleanup for book {book_id}")

        # Получаем книгу
        try:
            book = Book.objects.get(id=book_id)
        except Book.DoesNotExist:
            logger.warning(f"Book {book_id} not found, skipping cleanup")
            return

        # Получаем клиент S3
        s3_client = get_s3_client()
        bucket = settings.AWS_STORAGE_BUCKET_NAME

        # Получаем папку обложек книги
        cover_folder = get_book_cover_folder(book_id)
        logger.info(f"Looking for files in folder: {cover_folder}")

        # Получаем все файлы в папке
        all_files = list_s3_objects(s3_client, bucket, cover_folder)
        logger.info(f"Found {len(all_files)} files in S3")

        if not all_files:
            logger.info(f"No files found in {cover_folder}")
            return

        # Логируем найденные файлы
        for file_obj in all_files:
            logger.info(f"Found S3 file: {file_obj['Key']}")

        # Получаем текущие используемые файлы
        current_files = get_current_cover_files(book)
        logger.info(f"Current cover files for book {book_id}: {current_files}")

        # Удаляем неиспользуемые файлы
        deleted_count = 0
        for file_obj in all_files:
            file_key = file_obj['Key']

            # Проверяем, используется ли файл
            if file_key not in current_files:
                if delete_s3_object(s3_client, bucket, file_key):
                    deleted_count += 1
                    logger.info(f"Deleted unused cover file: {file_key}")
            else:
                logger.info(f"Keeping current cover file: {file_key}")

        logger.info(f"Cover cleanup completed for book {book_id}. Deleted {deleted_count} unused files.")

    except Exception as exc:
        logger.error(f"Error in cleanup_book_covers for book {book_id}: {exc}")
        # Повторяем задачу с экспоненциальной задержкой
        raise self.retry(exc=exc, countdown=60 * (2 ** self.request.retries))


@shared_task(bind=True, max_retries=2)
def cleanup_abandoned_books():
    """
    Периодическая очистка заброшенных книг.
    Удаляет все обложки книг, которые более 2 дней находятся в статусе 'creating'.
    """
    try:
        logger.info("Starting cleanup of abandoned books")

        # Находим заброшенные книги (более 2 дней в статусе 'creating')
        cutoff_date = timezone.now() - timedelta(days=2)
        abandoned_books = Book.objects.filter(
            creation_status='creating',
            created_at__lt=cutoff_date
        )

        logger.info(f"Found {abandoned_books.count()} abandoned books")

        if not abandoned_books.exists():
            logger.info("No abandoned books found")
            return

        # Получаем клиент S3
        s3_client = get_s3_client()
        bucket = settings.AWS_STORAGE_BUCKET_NAME

        total_deleted = 0
        processed_books = 0

        for book in abandoned_books:
            try:
                logger.info(f"Processing abandoned book {book.id} (created: {book.created_at})")

                # Получаем папку обложек книги
                cover_folder = get_book_cover_folder(book.id)

                # Получаем все файлы в папке
                all_files = list_s3_objects(s3_client, bucket, cover_folder)

                if not all_files:
                    logger.info(f"No cover files found for abandoned book {book.id}")
                    continue

                # Удаляем все файлы обложек
                deleted_count = 0
                for file_obj in all_files:
                    file_key = file_obj['Key']
                    if delete_s3_object(s3_client, bucket, file_key):
                        deleted_count += 1

                total_deleted += deleted_count
                processed_books += 1

                logger.info(f"Deleted {deleted_count} cover files for abandoned book {book.id}")

                # Очищаем поля обложек в базе данных
                book.cover = None
                book.cover_mini = None
                book.cover_editor = None
                book.save(update_fields=['cover', 'cover_mini', 'cover_editor'])

            except Exception as e:
                logger.error(f"Error processing abandoned book {book.id}: {e}")
                continue

        logger.info(f"Abandoned books cleanup completed. Processed {processed_books} books, deleted {total_deleted} files.")

    except Exception as exc:
        logger.error(f"Error in cleanup_abandoned_books: {exc}")
        raise self.retry(exc=exc, countdown=300)  # Повтор через 5 минут