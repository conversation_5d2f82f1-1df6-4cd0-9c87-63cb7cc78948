# WebSocket Система Уведомлений

## Обзор

Мы заменили циклические запросы на WebSocket соединения для получения уведомлений в реальном времени. Теперь все уведомления (непрочитанные сообщения, события ленты, запросы в друзья) обновляются мгновенно через WebSocket.

## Архитектура

### Backend

1. **Модель UserNotification** (`backend/users/models.py`)
   - Хранит уведомления для каждого пользователя
   - Поддерживает типы: `unread_messages`, `feed_events`, `friend_requests`
   - Данные хранятся в JSON поле

2. **WebSocket Consumers** (`backend/users/consumers.py`)
   - `NotificationConsumer` - для общих уведомлений
   - `DialogConsumer` - для уведомлений диалогов

3. **Сервис уведомлений** (`backend/users/services.py`)
   - `NotificationService` - управляет обновлением уведомлений
   - Автоматически отправляет уведомления через WebSocket

4. **Сигналы** (`backend/users/signals.py`)
   - Автоматически обновляют уведомления при изменении данных
   - Срабатывают при создании/изменении/удалении сообщений, событий, запросов

### Frontend

1. **WebSocket Hook** (`frontend/src/hooks/useWebSocketNotifications.js`)
   - Управляет WebSocket соединением
   - Обрабатывает входящие уведомления

2. **Обновленные хуки**
   - `useUnreadMessagesCount` - теперь использует WebSocket
   - `useFeedCount` - теперь использует WebSocket
   - `useFeedEventsCount` - новый хук для событий ленты
   - `useFriendRequestsCount` - новый хук для запросов в друзья

3. **MessageContext** (`frontend/src/context/MessageContext.jsx`)
   - Интегрирован с WebSocket
   - Предоставляет уведомления всем компонентам

## API Endpoints

### Новые endpoints

- `GET /api/users/notifications/` - получить все уведомления пользователя
- `WS /ws/notifications/` - WebSocket для уведомлений
- `WS /ws/dialog/{dialog_id}/` - WebSocket для диалогов

### Обновленные endpoints

- `GET /api/dialogs/unread_count/` - теперь использует систему уведомлений
- `GET /api/users/feed/events/count/` - теперь использует систему уведомлений

## Использование

### Backend

```python
from users.services import NotificationService

# Обновить уведомления для пользователя
NotificationService.update_unread_messages_count(user)
NotificationService.update_feed_events_count(user)
NotificationService.update_friend_requests_count(user)
NotificationService.update_all_notifications(user)
```

### Frontend

```javascript
import { useMessage } from '../context/MessageContext';
import { useUnreadMessagesCount } from '../hooks/useUnreadMessagesCount';
import { useFeedCount } from '../hooks/useFeedCount';

// В компоненте
const { notifications, isWebSocketConnected } = useMessage();
const { data: unreadCount } = useUnreadMessagesCount();
const { data: feedCount } = useFeedCount();
```

## Преимущества

1. **Реальное время** - уведомления обновляются мгновенно
2. **Меньше нагрузки** - нет циклических запросов каждые 15 секунд
3. **Централизованность** - все уведомления в одном месте
4. **Масштабируемость** - легко добавлять новые типы уведомлений

## Миграция

### Что изменилось

1. Убраны циклические запросы (`refetchInterval: 15000`)
2. Убраны `invalidateQueries(['feedCount'])`
3. Добавлены WebSocket соединения
4. Обновлены хуки для использования WebSocket

### Что осталось

1. Fallback запросы в случае проблем с WebSocket
2. API endpoints для совместимости
3. Логика обработки уведомлений

## Отладка

### Проверка WebSocket соединения

В правом нижнем углу отображается статус WebSocket соединения:
- 🟢 Зеленый - подключен
- 🔴 Красный - отключен

### Логи

- Backend: логи в консоли Django
- Frontend: логи в консоли браузера

## Будущие улучшения

1. Добавление новых типов уведомлений
2. Персистентность WebSocket соединений
3. Очередь уведомлений для офлайн пользователей
4. Push уведомления 