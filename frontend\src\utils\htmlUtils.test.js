/**
 * Тесты для утилит работы с HTML
 */

import { createSafeBookDescription, createShortDescription, stripHtmlTags, containsHtmlTags } from './htmlUtils';

// Мокаем DOM для тестов
global.document = {
  createElement: (tag) => {
    const element = {
      tagName: tag.toUpperCase(),
      innerHTML: '',
      textContent: '',
      innerText: '',
      childNodes: [],
      attributes: [],
      nodeType: 1, // ELEMENT_NODE
    };
    
    Object.defineProperty(element, 'innerHTML', {
      get() { return this._innerHTML || ''; },
      set(value) { 
        this._innerHTML = value;
        this.textContent = value.replace(/<[^>]*>/g, '');
        this.innerText = this.textContent;
      }
    });
    
    return element;
  }
};

global.Node = {
  TEXT_NODE: 3,
  ELEMENT_NODE: 1
};

describe('htmlUtils', () => {
  describe('containsHtmlTags', () => {
    test('должен определять наличие HTML тегов', () => {
      expect(containsHtmlTags('<b>текст</b>')).toBe(true);
      expect(containsHtmlTags('обычный текст')).toBe(false);
      expect(containsHtmlTags('')).toBe(false);
      expect(containsHtmlTags(null)).toBe(false);
    });
  });

  describe('stripHtmlTags', () => {
    test('должен удалять HTML теги', () => {
      expect(stripHtmlTags('<b>жирный</b> и <i>курсив</i>')).toBe('жирный и курсив');
      expect(stripHtmlTags('обычный текст')).toBe('обычный текст');
      expect(stripHtmlTags('')).toBe('');
      expect(stripHtmlTags(null)).toBe('');
    });
  });

  describe('createShortDescription', () => {
    test('должен создавать короткое описание', () => {
      const longText = 'Это очень длинный текст, который должен быть обрезан до определенной длины для отображения в карточке книги';
      const result = createShortDescription(longText, 50);
      expect(result.length).toBeLessThanOrEqual(60); // Учитываем HTML теги
    });
  });

  describe('createSafeBookDescription', () => {
    test('должен обрабатывать пустой контент', () => {
      expect(createSafeBookDescription('')).toEqual({ html: '', isTruncated: false });
      expect(createSafeBookDescription(null)).toEqual({ html: '', isTruncated: false });
    });

    test('должен обрабатывать обычный текст без HTML', () => {
      const text = 'Обычный текст без HTML тегов';
      const result = createSafeBookDescription(text, 100);
      expect(result.html).toBe('<p>Обычный текст без HTML тегов</p>');
      expect(result.isTruncated).toBe(false);
    });

    test('должен обрабатывать текст с переносами строк', () => {
      const text = 'Первый абзац\n\nВторой абзац\nС переносом';
      const result = createSafeBookDescription(text, 100);
      expect(result.html).toBe('<p>Первый абзац</p><p>Второй абзац<br>С переносом</p>');
      expect(result.isTruncated).toBe(false);
    });

    test('должен обрезать длинный текст', () => {
      const longText = 'Это очень длинный текст, который должен быть обрезан до определенной длины для отображения в карточке книги и не должен превышать заданный лимит символов';
      const result = createSafeBookDescription(longText, 50);
      expect(result.isTruncated).toBe(true);
      expect(result.html).toContain('…');
    });
  });
});
