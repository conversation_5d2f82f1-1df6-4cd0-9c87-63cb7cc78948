# Generated by Django 4.2.7 on 2025-05-05 13:58

from django.db import migrations, models
import users.models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0005_user_motto'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='header_bg_color1',
            field=models.CharField(blank=True, default='', help_text='Основной цвет фона (hex)', max_length=16),
        ),
        migrations.AddField(
            model_name='user',
            name='header_bg_color2',
            field=models.CharField(blank=True, default='', help_text='Второй цвет для градиента (hex)', max_length=16),
        ),
        migrations.AddField(
            model_name='user',
            name='header_bg_image',
            field=models.ImageField(blank=True, help_text='Пользовательский фон хедера', null=True, upload_to=users.models.user_header_path),
        ),
        migrations.AddField(
            model_name='user',
            name='header_bg_type',
            field=models.CharField(blank=True, default='', help_text='Тип фона: solid, gradient, image', max_length=16),
        ),
    ]
