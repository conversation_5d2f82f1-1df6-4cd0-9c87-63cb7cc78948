"""
Команда для мониторинга состояния базы данных.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import connection
from books.models import Book, Comment, CommentLike
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class Command(BaseCommand):
    help = 'Мониторинг состояния базы данных'

    def add_arguments(self, parser):
        parser.add_argument(
            '--check-integrity',
            action='store_true',
            help='Проверить целостность данных'
        )
        parser.add_argument(
            '--show-stats',
            action='store_true',
            help='Показать статистику'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== МОНИТОРИНГ БАЗЫ ДАННЫХ ===\n'))
        
        # Основная статистика
        self.show_basic_stats()
        
        if options['show_stats']:
            self.show_detailed_stats()
        
        if options['check_integrity']:
            self.check_data_integrity()

    def show_basic_stats(self):
        """Показать базовую статистику."""
        try:
            user_count = User.objects.count()
            active_users = User.objects.filter(is_active=True).count()
            deleted_users = User.objects.filter(is_deleted=True).count() if hasattr(User, 'is_deleted') else 0
            
            book_count = Book.objects.count()
            comment_count = Comment.objects.count()
            like_count = CommentLike.objects.count()
            
            self.stdout.write("📊 ОСНОВНАЯ СТАТИСТИКА:")
            self.stdout.write(f"  👥 Пользователи: {user_count} (активных: {active_users}, удаленных: {deleted_users})")
            self.stdout.write(f"  📚 Книги: {book_count}")
            self.stdout.write(f"  💬 Комментарии: {comment_count}")
            self.stdout.write(f"  👍 Лайки комментариев: {like_count}")
            
            # Проверяем критические состояния
            if user_count == 0:
                self.stdout.write(self.style.ERROR("🚨 КРИТИЧЕСКАЯ ОШИБКА: НЕТ ПОЛЬЗОВАТЕЛЕЙ!"))
            elif user_count < 5:
                self.stdout.write(self.style.WARNING(f"⚠️  ПРЕДУПРЕЖДЕНИЕ: Очень мало пользователей ({user_count})"))
            else:
                self.stdout.write(self.style.SUCCESS("✅ Количество пользователей в норме"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Ошибка при получении статистики: {e}"))

    def show_detailed_stats(self):
        """Показать детальную статистику."""
        self.stdout.write("\n📈 ДЕТАЛЬНАЯ СТАТИСТИКА:")
        
        try:
            # Топ авторов по количеству книг
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT u.username, COUNT(b.id) as book_count
                    FROM users_user u
                    LEFT JOIN books_book b ON u.id = b.author_id
                    GROUP BY u.id, u.username
                    ORDER BY book_count DESC
                    LIMIT 5
                """)

                self.stdout.write("  📚 Топ авторов по книгам:")
                for row in cursor.fetchall():
                    self.stdout.write(f"    - {row[0]}: {row[1]} книг")

            # Активность комментариев
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT u.username, COUNT(c.id) as comment_count
                    FROM users_user u
                    LEFT JOIN books_comment c ON u.id = c.user_id
                    GROUP BY u.id, u.username
                    ORDER BY comment_count DESC
                    LIMIT 5
                """)
                
                self.stdout.write("  💬 Топ комментаторов:")
                for row in cursor.fetchall():
                    self.stdout.write(f"    - {row[0]}: {row[1]} комментариев")
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Ошибка при получении детальной статистики: {e}"))

    def check_data_integrity(self):
        """Проверить целостность данных."""
        self.stdout.write("\n🔍 ПРОВЕРКА ЦЕЛОСТНОСТИ ДАННЫХ:")
        
        issues_found = 0
        
        try:
            # Проверяем orphaned комментарии (без пользователя)
            orphaned_comments = Comment.objects.filter(user__isnull=True).count()
            if orphaned_comments > 0:
                self.stdout.write(self.style.WARNING(f"⚠️  Найдено {orphaned_comments} комментариев без пользователя"))
                issues_found += 1
            
            # Проверяем orphaned лайки (без пользователя)
            orphaned_likes = CommentLike.objects.filter(user__isnull=True).count()
            if orphaned_likes > 0:
                self.stdout.write(self.style.WARNING(f"⚠️  Найдено {orphaned_likes} лайков без пользователя"))
                issues_found += 1
            
            # Проверяем orphaned книги (без автора)
            orphaned_books = Book.objects.filter(author__isnull=True).count()
            if orphaned_books > 0:
                self.stdout.write(self.style.WARNING(f"⚠️  Найдено {orphaned_books} книг без автора"))
                issues_found += 1
            
            # Проверяем пользователей с некорректными данными
            users_no_email = User.objects.filter(email='').count()
            if users_no_email > 0:
                self.stdout.write(self.style.WARNING(f"⚠️  Найдено {users_no_email} пользователей без email"))
                issues_found += 1
            
            if issues_found == 0:
                self.stdout.write(self.style.SUCCESS("✅ Проблем с целостностью данных не найдено"))
            else:
                self.stdout.write(self.style.WARNING(f"⚠️  Найдено {issues_found} проблем с целостностью данных"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Ошибка при проверке целостности: {e}"))
