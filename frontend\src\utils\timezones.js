// Список часовых поясов РФ для выбора в профиле
// Для городов с одним часовым поясом value уникальный, tz — реальный timezone

const timezones = [
  // GMT+2 (Калининградское время)
  { value: 'Europe/Kaliningrad', tz: 'Europe/Kaliningrad', label: 'Калининград (GMT+2)' },

  // GMT+3 (Московское время) - несколько городов в одном поясе
  { value: 'Europe/Moscow_msk', tz: 'Europe/Moscow', label: 'Москва (GMT+3)' },
  { value: 'Europe/Moscow_spb', tz: 'Europe/Moscow', label: 'Санкт-Петербург (GMT+3)' },
  { value: 'Europe/Moscow_nng', tz: 'Europe/Moscow', label: 'Нижний Новгород (GMT+3)' },
  { value: 'Europe/Moscow_ros', tz: 'Europe/Moscow', label: 'Ростов-на-Дону (GMT+3)' },
  { value: 'Europe/Moscow_vol', tz: 'Europe/Moscow', label: 'Волгоград (GMT+3)' },

  // GMT+4 (Самарское время)
  { value: 'Europe/Samara', tz: 'Europe/Samara', label: 'Самара (GMT+4)' },
  { value: 'Europe/Samara_sar', tz: 'Europe/Samara', label: 'Саратов (GMT+4)' },
  { value: 'Europe/Samara_ast', tz: 'Europe/Samara', label: 'Астрахань (GMT+4)' },

  // GMT+5 (Екатеринбургское время)
  { value: 'Asia/Yekaterinburg', tz: 'Asia/Yekaterinburg', label: 'Екатеринбург (GMT+5)' },
  { value: 'Asia/Yekaterinburg_chel', tz: 'Asia/Yekaterinburg', label: 'Челябинск (GMT+5)' },
  { value: 'Asia/Yekaterinburg_ufa', tz: 'Asia/Yekaterinburg', label: 'Уфа (GMT+5)' },

  // GMT+6 (Омское время)
  { value: 'Asia/Omsk', tz: 'Asia/Omsk', label: 'Омск (GMT+6)' },

  // GMT+7 (Красноярское время) - несколько городов в одном поясе
  { value: 'Asia/Novosibirsk', tz: 'Asia/Novosibirsk', label: 'Новосибирск (GMT+7)' },
  { value: 'Asia/Krasnoyarsk', tz: 'Asia/Krasnoyarsk', label: 'Красноярск (GMT+7)' },
  { value: 'Asia/Krasnoyarsk_tom', tz: 'Asia/Krasnoyarsk', label: 'Томск (GMT+7)' },
  { value: 'Asia/Krasnoyarsk_kem', tz: 'Asia/Krasnoyarsk', label: 'Кемерово (GMT+7)' },

  // GMT+8 (Иркутское время)
  { value: 'Asia/Irkutsk', tz: 'Asia/Irkutsk', label: 'Иркутск (GMT+8)' },
  { value: 'Asia/Irkutsk_uub', tz: 'Asia/Irkutsk', label: 'Улан-Удэ (GMT+8)' },

  // GMT+9 (Якутское время) - несколько городов в одном поясе
  { value: 'Asia/Chita', tz: 'Asia/Chita', label: 'Чита (GMT+9)' },
  { value: 'Asia/Yakutsk', tz: 'Asia/Yakutsk', label: 'Якутск (GMT+9)' },
  { value: 'Asia/Yakutsk_bla', tz: 'Asia/Yakutsk', label: 'Благовещенск (GMT+9)' },

  // GMT+10 (Владивостокское время)
  { value: 'Asia/Vladivostok', tz: 'Asia/Vladivostok', label: 'Владивосток (GMT+10)' },
  { value: 'Asia/Vladivostok_hab', tz: 'Asia/Vladivostok', label: 'Хабаровск (GMT+10)' },

  // GMT+11 (Магаданское время) - несколько городов в одном поясе
  { value: 'Asia/Sakhalin', tz: 'Asia/Sakhalin', label: 'Южно-Сахалинск (GMT+11)' },
  { value: 'Asia/Magadan', tz: 'Asia/Magadan', label: 'Магадан (GMT+11)' },

  // GMT+12 (Камчатское время)
  { value: 'Asia/Kamchatka', tz: 'Asia/Kamchatka', label: 'Петропавловск-Камчатский (GMT+12)' },
];

export default timezones; 