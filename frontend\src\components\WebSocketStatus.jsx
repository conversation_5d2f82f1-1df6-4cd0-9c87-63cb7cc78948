import React from 'react';
import { useMessage } from '../context/MessageContext';

const WebSocketStatus = () => {
    const { isWebSocketConnected } = useMessage();

    if (isWebSocketConnected) {
        return (
            <div className="fixed bottom-4 right-4 z-50">
                <div className="flex items-center bg-green-500 text-white px-3 py-2 rounded-full shadow-lg text-sm">
                    <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                    WebSocket подключен
                </div>
            </div>
        );
    }

    return (
        <div className="fixed bottom-4 right-4 z-50">
            <div className="flex items-center bg-red-500 text-white px-3 py-2 rounded-full shadow-lg text-sm">
                <div className="w-2 h-2 bg-white rounded-full mr-2"></div>
                WebSocket отключен
            </div>
        </div>
    );
};

export default WebSocketStatus; 