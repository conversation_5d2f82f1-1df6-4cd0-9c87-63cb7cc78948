from django.core.management.base import BaseCommand
from django.utils import timezone
from books.models import BookChapter
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Очищает устаревшие задачи планирования публикации'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Показать что будет очищено, но не выполнять очистку',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # Находим главы с устаревшими задачами
        expired_chapters = BookChapter.objects.filter(
            scheduled_publish_at__lt=timezone.now(),
            scheduled_publish_at__isnull=False
        )
        
        count = expired_chapters.count()
        
        if count == 0:
            self.stdout.write(
                self.style.SUCCESS('Устаревших задач планирования не найдено.')
            )
            return
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'Найдено {count} устаревших задач планирования:')
            )
            for chapter in expired_chapters:
                self.stdout.write(
                    f'  - Глава {chapter.id} "{chapter.title}" (книга: {chapter.book.title})'
                    f' - запланирована на {chapter.scheduled_publish_at}'
                )
            self.stdout.write(
                self.style.WARNING('Используйте команду без --dry-run для очистки.')
            )
        else:
            cleaned_count = 0
            for chapter in expired_chapters:
                if chapter.clean_expired_schedule():
                    cleaned_count += 1
                    self.stdout.write(
                        f'Очищена устаревшая задача для главы {chapter.id} "{chapter.title}"'
                    )
            
            self.stdout.write(
                self.style.SUCCESS(f'Очищено {cleaned_count} устаревших задач планирования.')
            )
