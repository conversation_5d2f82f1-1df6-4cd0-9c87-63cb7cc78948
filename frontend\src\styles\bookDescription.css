/* Стили для HTML контента в описаниях книг */

.book-description {
  /* Базовые стили для контейнера */
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.book-description p {
  margin: 0 0 0.5em 0;
}

.book-description p:last-child {
  margin-bottom: 0;
}

/* Стили для форматирования текста */
.book-description b,
.book-description strong {
  font-weight: 600;
}

.book-description i,
.book-description em {
  font-style: italic;
}

.book-description u {
  text-decoration: underline;
  text-underline-offset: 2px;
}

/* Стили для заголовков */
.book-description h1,
.book-description h2,
.book-description h3,
.book-description h4,
.book-description h5,
.book-description h6 {
  font-weight: 600;
  margin: 0.5em 0 0.25em 0;
  line-height: 1.3;
}

.book-description h1 { font-size: 1.1em; }
.book-description h2 { font-size: 1.05em; }
.book-description h3,
.book-description h4,
.book-description h5,
.book-description h6 { font-size: 1em; }

/* Стили для списков */
.book-description ul,
.book-description ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.book-description li {
  margin: 0.25em 0;
}

/* Стили для цитат */
.book-description blockquote {
  margin: 0.5em 0;
  padding-left: 1em;
  border-left: 3px solid #e5e7eb;
  font-style: italic;
}

/* Темная тема */
.dark .book-description blockquote {
  border-left-color: #4b5563;
}

/* Стили для ссылок */
.book-description a {
  color: #3b82f6;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.book-description a:hover {
  color: #1d4ed8;
}

.dark .book-description a {
  color: #60a5fa;
}

.dark .book-description a:hover {
  color: #93c5fd;
}

/* Стили для кода */
.book-description code {
  background-color: #f3f4f6;
  padding: 0.125em 0.25em;
  border-radius: 0.25rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.875em;
}

.dark .book-description code {
  background-color: #374151;
}

/* Стили для разделителей */
.book-description hr {
  margin: 1em 0;
  border: none;
  border-top: 1px solid #e5e7eb;
}

.dark .book-description hr {
  border-top-color: #4b5563;
}

/* Стили для таблиц (если есть) */
.book-description table {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5em 0;
}

.book-description th,
.book-description td {
  border: 1px solid #e5e7eb;
  padding: 0.25em 0.5em;
  text-align: left;
}

.book-description th {
  background-color: #f9fafb;
  font-weight: 600;
}

.dark .book-description th,
.dark .book-description td {
  border-color: #4b5563;
}

.dark .book-description th {
  background-color: #374151;
}

/* Стили для выделения цветом */
.book-description span[style*="color"] {
  /* Цвета уже заданы inline стилями */
}

.book-description span[style*="background"] {
  padding: 0.125em 0.25em;
  border-radius: 0.125rem;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 640px) {
  .book-description {
    font-size: 0.875rem;
  }
  
  .book-description h1,
  .book-description h2,
  .book-description h3,
  .book-description h4,
  .book-description h5,
  .book-description h6 {
    font-size: 1rem;
  }
}
