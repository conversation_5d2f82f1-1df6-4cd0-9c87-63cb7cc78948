import { message } from 'antd';

// Глобальная конфигурация для всех уведомлений
message.config({
  duration: 4, // Автоматическое исчезновение через 4 секунды
  maxCount: 3, // Максимум 3 уведомления одновременно
});

// Переопределяем стандартные методы с автоматическим исчезновением
const originalSuccess = message.success;
const originalError = message.error;
const originalInfo = message.info;
const originalWarning = message.warning;

message.success = (content, duration = 4, onClose) => {
  return originalSuccess(content, duration, onClose);
};

message.error = (content, duration = 4, onClose) => {
  return originalError(content, duration, onClose);
};

message.info = (content, duration = 4, onClose) => {
  return originalInfo(content, duration, onClose);
};

message.warning = (content, duration = 4, onClose) => {
  return originalWarning(content, duration, onClose);
};

export default message;