import React from 'react';
import { useOutletContext } from 'react-router-dom';
import moment from 'moment-timezone';
import { useUserSettings } from '../context/UserSettingsContext';

function getAgeString(birthDate, timezone) {
  if (!birthDate) return null;
  const today = moment().tz(timezone);
  const dob = moment(birthDate).tz(timezone);
  let age = today.year() - dob.year();
  if (
    today.month() < dob.month() ||
    (today.month() === dob.month() && today.date() < dob.date())
  ) {
    age--;
  }
  // Склонение
  const lastDigit = age % 10;
  const lastTwo = age % 100;
  let word = 'лет';
  if (lastDigit === 1 && lastTwo !== 11) word = 'год';
  else if ([2,3,4].includes(lastDigit) && ![12,13,14].includes(lastTwo)) word = 'года';
  return `${age} ${word}`;
}

function isBirthdayToday(birthDate, timezone) {
  if (!birthDate) return false;
  const today = moment().tz(timezone);
  const dob = moment(birthDate).tz(timezone);
  return today.date() === dob.date() && today.month() === dob.month();
}

function ProfileMain() {
  return null;
}

export default ProfileMain; 