import React from 'react';

export default class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  componentDidCatch(error, errorInfo) {
    console.error('App error:', error, errorInfo);
  }
  render() {
    if (this.state.hasError) {
      return <div className="bg-red-100 text-red-700 p-4 text-center">Глобальная ошибка приложения. Попробуйте обновить страницу.</div>;
    }
    return this.props.children;
  }
} 