import time
from django.core.management.base import BaseCommand
from django.test import RequestFactory
from django.contrib.auth import get_user_model
from books.models import Book
from books.serializers import BookSerializer
from django.core.files.uploadedfile import SimpleUploadedFile

User = get_user_model()


class Command(BaseCommand):
    help = 'Test cover save performance'

    def add_arguments(self, parser):
        parser.add_argument(
            '--book-id',
            type=int,
            required=True,
            help='Book ID to test with',
        )
        parser.add_argument(
            '--disable-cleanup',
            action='store_true',
            help='Disable cleanup for testing',
        )

    def handle(self, *args, **options):
        book_id = options['book_id']
        
        try:
            book = Book.objects.get(id=book_id)
        except Book.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Book {book_id} not found")
            )
            return
        
        self.stdout.write(f"=== Cover Save Performance Test ===")
        self.stdout.write(f"Book: {book_id} - {book.title}")
        
        if options['disable_cleanup']:
            from django.conf import settings
            settings.ENABLE_COVER_CLEANUP = False
            self.stdout.write("Cover cleanup DISABLED for this test")
        
        # Создаем тестовый файл
        test_image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
        test_file = SimpleUploadedFile(
            "test_cover.png",
            test_image_content,
            content_type="image/png"
        )
        
        # Создаем request
        factory = RequestFactory()
        request = factory.patch(f'/api/books/{book_id}/')
        request.FILES = {'cover': test_file}
        
        # Измеряем время
        start_time = time.time()
        
        serializer = BookSerializer(book, context={'request': request})
        serializer.update(book, {'cover': test_file})
        
        end_time = time.time()
        duration = end_time - start_time
        
        self.stdout.write(f"\n=== Results ===")
        self.stdout.write(f"Save duration: {duration:.3f} seconds")
        
        if duration > 2.0:
            self.stdout.write(
                self.style.WARNING(f"⚠️  Slow save detected ({duration:.3f}s)")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f"✓ Fast save ({duration:.3f}s)")
            )
        
        self.stdout.write(f"\nCover saved to: {book.cover.name if book.cover else 'None'}")
        
        if not options['disable_cleanup']:
            self.stdout.write(f"Cleanup scheduled in 20 seconds...")
        
        # Восстанавливаем настройки
        if options['disable_cleanup']:
            from django.conf import settings
            settings.ENABLE_COVER_CLEANUP = True
