# Тестирование WebSocket Уведомлений

## Проверка подключения

### 1. Проверка статуса WebSocket
- Откройте браузер и войдите в систему
- В правом нижнем углу должен быть зеленый индикатор "WebSocket подключен"
- Если красный - проверьте консоль браузера на ошибки

### 2. Проверка логов сервера
```bash
# В терминале с сервером должны быть логи:
INFO WebSocket connected: user username
INFO WebSocket auth: User username authenticated
```

### 3. Проверка консоли браузера
- Откройте F12 -> Console
- Не должно быть ошибок WebSocket
- Могут быть предупреждения о переподключении

## Тестирование уведомлений

### 1. Тест через Django команду
```bash
cd backend
python manage.py test_websocket_notifications --username your_username
```

### 2. Ручное тестирование

#### Тест непрочитанных сообщений:
1. Отправьте сообщение другому пользователю
2. Проверьте, что счетчик обновился мгновенно
3. В логах должно быть: `INFO New message created: X from user1 to user2`

#### Тест событий ленты:
1. Создайте событие в ленте (дружба, подписка и т.д.)
2. Проверьте, что счетчик обновился мгновенно
3. В логах должно быть: `INFO New feed event created: event_type for user username`

#### Тест запросов в друзья:
1. Отправьте запрос в друзья
2. Проверьте, что счетчик обновился мгновенно
3. В логах должно быть: `INFO New friend request: user1 -> user2`

## Проверка отсутствия циклических запросов

### 1. Проверка Network tab
- Откройте F12 -> Network
- Не должно быть повторяющихся запросов к:
  - `/api/dialogs/unread_count/`
  - `/api/users/feed/events/count/`

### 2. Проверка логов сервера
- В терминале не должно быть повторяющихся запросов к этим endpoints

## Отладка проблем

### WebSocket не подключается:
1. Проверьте, что Redis запущен: `redis-cli ping`
2. Проверьте настройки CHANNEL_LAYERS в settings.py
3. Проверьте CORS настройки

### Уведомления не обновляются:
1. Проверьте логи сигналов
2. Проверьте, что NotificationService работает
3. Проверьте WebSocket соединение

### Циклические запросы все еще есть:
1. Проверьте все файлы на `refetchInterval`
2. Проверьте все файлы на `setInterval`
3. Проверьте все файлы на `fetchUnreadCount`

## Команды для отладки

```bash
# Проверка Redis
redis-cli ping

# Проверка логов Django
tail -f logs/django.log

# Тест уведомлений
python manage.py test_websocket_notifications --username test_user

# Проверка миграций
python manage.py showmigrations users
```

## Ожидаемое поведение

### ✅ Правильно:
- WebSocket подключается при входе в систему
- Уведомления обновляются мгновенно при событиях
- Нет циклических HTTP запросов
- Логи показывают события в реальном времени

### ❌ Неправильно:
- WebSocket не подключается
- Уведомления обновляются только при обновлении страницы
- Есть циклические HTTP запросы
- Нет логов событий 