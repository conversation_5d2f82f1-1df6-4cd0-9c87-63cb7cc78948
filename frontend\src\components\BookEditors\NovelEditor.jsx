import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON>po<PERSON>, Popconfirm, message, Tooltip, Dropdown, Menu, Switch, Modal, Checkbox, Spin, DatePicker, ConfigProvider, Form, Input } from 'antd';
import { EyeInvisibleOutlined, EyeOutlined, EditOutlined, DeleteOutlined, PlusOutlined, FileAddOutlined, ExclamationCircleOutlined, ClockCircleOutlined, MoreOutlined, CloseCircleFilled, CheckOutlined, MinusOutlined, HourglassOutlined, ArrowUpOutlined, ArrowDownOutlined, FormOutlined, QuestionCircleOutlined, StopOutlined } from '@ant-design/icons';
import ChapterEditorV2 from '../ChapterEditorV2';
import { useTheme } from '../../theme/ThemeContext';
import { useMessage } from '../../context/MessageContext';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { EditorContent } from '@tiptap/react';
import { Tooltip as AntdTooltip } from 'antd';
import dayjs from 'dayjs';
import { useUserSettings } from '../../context/UserSettingsContext';
import { formatDateWithTimezone } from '../../utils/formatDate';
import { formatPublishedDate, formatUpdatedDate } from '../../utils/dateHelpers';
import { message as antdMessage } from 'antd';
import { useCustomMessage } from '../../hooks/useCustomMessage';
import ru_RU from 'antd/locale/ru_RU';
import 'dayjs/locale/ru';
dayjs.locale('ru');

// Стили для убирания синего фокуса в DatePicker
const datePickerStyles = `
  .no-focus-outline .ant-picker-input > input:focus {
    box-shadow: none !important;
    border-color: #d9d9d9 !important;
    outline: none !important;
  }
  .no-focus-outline .ant-picker:focus,
  .no-focus-outline .ant-picker-focused {
    box-shadow: none !important;
    border-color: #d9d9d9 !important;
    outline: none !important;
  }
`;

// Добавляем стили в head
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = datePickerStyles;
  document.head.appendChild(styleElement);
}

const { Title } = Typography;

// Ограничения для романов/повестей
const MAX_CHAPTER_LENGTH = 100000;  // 100k символов на главу
const MAX_NOVEL_LENGTH = 2500000;   // 2.5M символов на произведение

const NovelEditor = ({
  bookId,
  book,
  chapters,
  handleSaveChapter,
  chapterModal,
  setChapterModal,
  chapterLoading,
  setChapters,
  onBack,
  fetchChapters,
  isOwner = false,
  isFullWidth = false,
  onToggleFullWidth
}) => {
  const [editMode, setEditMode] = useState(null); // id главы или 'new'/'preface'
  const [isPreface, setIsPreface] = useState(false);
  const [editorKey, setEditorKey] = useState(0); // для сброса редактора
  const [localAutoIndent, setLocalAutoIndent] = useState(book?.auto_indent || false);
  const [localAutoChapterSplit, setLocalAutoChapterSplit] = useState(true);
  const { theme } = useTheme();
  const { showMessage } = useMessage();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { showSuccessWithClose, showErrorWithClose, showInfoWithClose } = useCustomMessage();
  const prevChaptersCount = useRef(Array.isArray(chapters) ? chapters.length : 0);
  const editorRef = useRef(null);
  const [isFocused, setIsFocused] = useState(false);
  const [selectedChapters, setSelectedChapters] = useState([]);
  const [isContiguous, setIsContiguous] = useState(true);
  const [showBatchDeleteConfirm, setShowBatchDeleteConfirm] = useState(false);
  const [batchDeleteWarning, setBatchDeleteWarning] = useState('');
  const [batchDeleteLoading, setBatchDeleteLoading] = useState(false);
  const [batchPublishLoading, setBatchPublishLoading] = useState(false);
  
  // Глобальное состояние блокировки для всех массовых операций
  const [isProcessing, setIsProcessing] = useState(false);
  const [showPublishWarning, setShowPublishWarning] = useState(false);
  const [publishConfirmChecked, setPublishConfirmChecked] = useState(false);
  const [pendingPublishChapters, setPendingPublishChapters] = useState([]); // ids глав для публикации после подтверждения
  const [publishAsFinishedConfirm, setPublishAsFinishedConfirm] = useState(false); // Статус завершения для модального окна подтверждения
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [scheduleDate, setScheduleDate] = useState(null);
  const [schedulingChapters, setSchedulingChapters] = useState([]); // ids глав для планирования
  const [schedulingLoading, setSchedulingLoading] = useState(false);
  const [minScheduleTime, setMinScheduleTime] = useState(null); // Фиксированное минимальное время
  const [schedulingAsFinished, setSchedulingAsFinished] = useState(false); // Статус завершения для планирования
  const { timezone } = useUserSettings();
  const [editOrderId, setEditOrderId] = useState(null);
  const [editOrderValue, setEditOrderValue] = useState(1);
  const [buttonStates, setButtonStates] = useState({
    finish: { hover: false, focus: false },
    partial: { hover: false, focus: false },
    draft: { hover: false, focus: false }
  });
  // Локальное состояние для книги для мгновенного обновления UI
  const [localBook, setLocalBook] = useState(book);
  
  // Состояния для модального окна публикации последней главы
  const [showPublishLastChapterModal, setShowPublishLastChapterModal] = useState(false);
  const [publishAsFinished, setPublishAsFinished] = useState(false);
  const [publishAction, setPublishAction] = useState(null); // 'immediate' или 'scheduled'
  const [chapterToPublish, setChapterToPublish] = useState(null);

  // Состояние для показа/скрытия подсказки по автоматическому разделению
  const [showAutoSplitHelp, setShowAutoSplitHelp] = useState(false);

  // Состояния для удаления произведения
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState('');
  const [deleteForm] = Form.useForm();

  // Синхронизируем локальное состояние с пропсом
  useEffect(() => {
    setLocalBook(book);
    setLocalAutoIndent(book?.auto_indent || false);
  }, [book]);

  const username = localBook?.author?.username || '';
  const coverUrl =
    localBook?.cover_type === 'custom'
      ? (localBook?.cover_mini_url || '/static/cover_placeholder.jpg')
      : (localBook?.cover_temp_url || '/static/cover_placeholder.jpg');

  const safeChapters = Array.isArray(chapters) ? chapters : [];
  const canAddChapter = true;
  const nextChapterNumber = safeChapters.length === 0 || (isPreface && !safeChapters.some(ch => ch.order === 0)) ? 1 : Math.max(...safeChapters.map(ch => ch.order || 1)) + 1;

  const mainChapters = safeChapters.filter(ch => ch.order > 0 && ch.order !== 99999);
  const mainChaptersCount = mainChapters.length;

  function swapChapters(chapters, fromOrder, toOrder) {
    // Меняет местами главы с order=fromOrder и order=toOrder
    const idxA = chapters.findIndex(ch => ch.order === fromOrder);
    const idxB = chapters.findIndex(ch => ch.order === toOrder);
    if (idxA === -1 || idxB === -1) return chapters;
    const newChapters = [...chapters];
    [newChapters[idxA].order, newChapters[idxB].order] = [newChapters[idxB].order, newChapters[idxA].order];
    return normalizeChaptersOrderAndTitles(newChapters);
  }

  const handleMoveUp = async (chapter) => {
    if (chapter.order <= 1) return;
    const newChapters = swapChapters(safeChapters, chapter.order, chapter.order - 1);
    setChapters(newChapters);

    // Обновляем порядок на сервере
    await updateChaptersOrder(bookId, newChapters.filter(ch => ch.order > 0 && ch.order !== 99999));

    // Обновляем заголовки на сервере для затронутых глав
    const affectedChapters = newChapters.filter(ch =>
      ch.order === chapter.order - 1 || ch.order === chapter.order
    );
    if (affectedChapters.length > 0) {
      await updateChapterTitlesOnServer(affectedChapters);
    }
  };

  const handleMoveDown = async (chapter) => {
    if (chapter.order >= mainChaptersCount) return;
    const newChapters = swapChapters(safeChapters, chapter.order, chapter.order + 1);
    setChapters(newChapters);

    // Обновляем порядок на сервере
    await updateChaptersOrder(bookId, newChapters.filter(ch => ch.order > 0 && ch.order !== 99999));

    // Обновляем заголовки на сервере для затронутых глав
    const affectedChapters = newChapters.filter(ch =>
      ch.order === chapter.order || ch.order === chapter.order + 1
    );
    if (affectedChapters.length > 0) {
      await updateChapterTitlesOnServer(affectedChapters);
    }
  };
  const handleOrderEdit = (chapter) => {
    setEditOrderId(chapter.id);
    setEditOrderValue(chapter.order);
  };
  const handleOrderInputChange = (e) => {
    let value = e.target.value.replace(/^0+/, '');
    // Если поле стало пустым после удаления нулей, оставить пустым
    setEditOrderValue(value);
  };
  const handleOrderInputBlur = async (chapter) => {
    if (editOrderValue === null || isNaN(editOrderValue) || editOrderValue < 1 || editOrderValue > mainChaptersCount) {
      setEditOrderId(null);
      return;
    }
    let chapters = safeChapters.filter(ch => ch.order > 0 && ch.order !== 99999);
    chapters = chapters.sort((a, b) => a.order - b.order);
    const moving = chapters.find(ch => ch.id === chapter.id);
    chapters = chapters.filter(ch => ch.id !== chapter.id);
    chapters.splice(editOrderValue - 1, 0, moving);
    chapters.forEach((ch, idx) => { ch.order = idx + 1; });
    const newChapters = normalizeChaptersOrderAndTitles([
      ...safeChapters.filter(ch => ch.order === 0 || ch.order === 99999),
      ...chapters
    ]);
    setChapters(newChapters);
    setEditOrderId(null);

    // Обновляем порядок на сервере
    await updateChaptersOrder(bookId, newChapters.filter(ch => ch.order > 0 && ch.order !== 99999));

    // Обновляем заголовки на сервере для всех затронутых глав
    const affectedChapters = newChapters.filter(ch => ch.order > 0 && ch.order !== 99999);
    if (affectedChapters.length > 0) {
      await updateChapterTitlesOnServer(affectedChapters);
    }
  };

  const getNextImgIndex = () => {
    let maxIdx = 0;
    safeChapters.forEach(ch => {
      const matches = (ch.content || '').match(/\/media\/users\/[^"']+\.(jpg|jpeg|png|webp)/g);
      if (matches) {
        matches.forEach(url => {
          const m = url.match(/chapter\/(\d+)_\d+_\d+\.(jpg|jpeg|png|webp)/);
          if (m && m[1]) {
            const idx = parseInt(m[1], 10);
            if (idx > maxIdx) maxIdx = idx;
          }
        });
      }
    });
    return maxIdx + 1;
  };

  const handleAddChapter = (preface = false) => {
    setIsPreface(preface);
    setEditMode(preface ? 'preface' : 'new');
    setEditorKey(prev => prev + 1);
  };

  useEffect(() => {
    if (Array.isArray(chapters) && chapters.length > prevChaptersCount.current) {
      setEditMode(null);
      setIsPreface(false);
    }
    prevChaptersCount.current = Array.isArray(chapters) ? chapters.length : 0;
  }, [chapters]);

  // Функция для нормализации порядка и названий глав (только локально)
  function normalizeChaptersOrderAndTitles(chapters) {
    const intro = chapters.filter(ch => ch.order === 0);
    const finals = chapters.filter(ch => ch.order === 99999);
    const main = chapters.filter(ch => ch.order !== 0 && ch.order !== 99999)
      .sort((a, b) => a.order - b.order);
    let order = 1;
    const newMain = main.map(ch => {
      let newTitle = ch.title;
      if (/^Глава \d+(:|$)/.test(ch.title)) {
        const match = ch.title.match(/^Глава \d+(?::\s*(.*))?$/);
        if (match && match[1]) {
          newTitle = `Глава ${order}: ${match[1]}`;
        } else {
          newTitle = `Глава ${order}`;
        }
      }
      const newCh = { ...ch, order, title: newTitle };
      order++;
      return newCh;
    });
    return [
      ...intro.map(ch => ({ ...ch })),
      ...newMain,
      ...finals.map(ch => ({ ...ch })),
    ];
  }

  // Функция обновления номера главы в заголовке (аналогично ChapterEditorV2)
  const updateChapterTitleNumber = (title, newOrder) => {
    if (!title) return `Глава ${newOrder}`;

    // Если заголовок начинается с "Глава X:", заменяем номер
    const chapterPattern = /^Глава\s+\d+:\s*/i;
    if (chapterPattern.test(title)) {
      return title.replace(chapterPattern, `Глава ${newOrder}: `);
    }

    // Если это предисловие или послесловие, не трогаем
    if (title.toLowerCase().includes('предисловие') || title.toLowerCase().includes('послесловие')) {
      return title;
    }

    // Иначе добавляем номер главы
    return `Глава ${newOrder}: ${title}`;
  };

  // Функция для обновления заголовков глав на сервере
  const updateChapterTitlesOnServer = async (chaptersToUpdate) => {
    console.log('=== UPDATING CHAPTER TITLES ON SERVER ===');
    console.log('Chapters to update:', chaptersToUpdate);

    const csrfToken = getCookie('csrftoken');
    const updatePromises = [];

    for (const chapter of chaptersToUpdate) {
      const updatePromise = fetch(`/api/books/${bookId}/chapters/${chapter.id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({
          order: chapter.order,
          title: chapter.title
        }),
      }).then(async (response) => {
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Failed to update chapter ${chapter.id}:`, errorText);
          throw new Error(`Failed to update chapter ${chapter.id}: ${response.status}`);
        }
        const updatedChapter = await response.json();
        console.log(`Successfully updated chapter ${chapter.id}:`, updatedChapter);
        return updatedChapter;
      });

      updatePromises.push(updatePromise);
    }

    try {
      const results = await Promise.all(updatePromises);
      console.log('All chapter titles updated successfully');
      return { success: true, updatedChapters: results };
    } catch (error) {
      console.error('Error updating chapter titles:', error);
      return { success: false, error };
    }
  };

  // Автоматическая нормализация после любого обновления массива глав
  useEffect(() => {
    if (!Array.isArray(chapters) || chapters.length === 0) return;
    const normalized = normalizeChaptersOrderAndTitles(chapters);
    // Сравниваем массивы по порядку и названию, чтобы не зациклить useEffect
    const isSame = chapters.length === normalized.length && chapters.every((ch, i) => ch.order === normalized[i].order && ch.title === normalized[i].title);
    if (!isSame) {
      setChapters(normalized);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chapters]);

  const handleSave = async (data) => {
    const isNewChapter = editMode === 'new' || editMode === 'preface';
    const chapterToSaveId = isNewChapter ? null : editMode;

    // Проверяем размер главы перед сохранением
    const newContent = data.content || '';
    const plainNewContent = newContent.replace(/<[^>]*>/g, '');

    // Вычисляем потенциальный объем опубликованного контента с учетом сохраняемой главы
    let potentialPublishedContent = 0;

    // Считаем текущий опубликованный объем
    const currentPublishedStats = safeChapters.reduce((total, chapter) => {
      if (chapter.is_published) {
        const plainText = (chapter.content || '').replace(/<[^>]*>/g, '');
        return total + plainText.length;
      }
      return total;
    }, 0);

    if (isNewChapter) {
      // Для новой главы: текущий опубликованный объем + новая глава
      potentialPublishedContent = currentPublishedStats + plainNewContent.length;
    } else {
      // Для редактируемой главы: учитываем новый контент, если глава опубликована
      const currentChapter = safeChapters.find(ch => ch.id === editMode);
      if (currentChapter?.is_published) {
        // Если редактируемая глава опубликована, заменяем её контент
        potentialPublishedContent = safeChapters.reduce((total, chapter) => {
          if (chapter.is_published) {
            const content = chapter.id === editMode ? newContent : (chapter.content || '');
            const plainText = content.replace(/<[^>]*>/g, '');
            return total + plainText.length;
          }
          return total;
        }, 0);
      } else {
        // Если редактируемая глава НЕ опубликована, но может быть опубликована в будущем
        potentialPublishedContent = currentPublishedStats + plainNewContent.length;
      }
    }

    // Проверяем превышение лимита для потенциального опубликованного контента
    if (potentialPublishedContent > MAX_NOVEL_LENGTH) {
      Modal.error({
        title: 'Объем произведения превышает допустимый',
        content: (
          <div>
            <p>
              После сохранения потенциальный размер опубликованного контента составит <strong>{potentialPublishedContent.toLocaleString()} символов</strong>,
              что превышает допустимый лимит <strong>{MAX_NOVEL_LENGTH.toLocaleString()} символов</strong>.
            </p>
            <p>
              Сохранение невозможно. Рекомендуется сократить текст главы или снять с публикации часть других глав.
            </p>
          </div>
        ),
        okText: 'Понятно',
        onOk: () => {
          // Пользователь остается в редакторе для внесения изменений
        }
      });
      return; // Блокируем сохранение
    }

    // Проверяем размер главы, если общий объем в норме
    await handleChapterSizeAndSave(data, isNewChapter, chapterToSaveId, plainNewContent);
  };

  // Функция для проверки размера главы и сохранения
  const handleChapterSizeAndSave = async (data, isNewChapter, chapterToSaveId, plainNewContent) => {
    if (plainNewContent.length > MAX_CHAPTER_LENGTH) {
      // Находим текущую главу для проверки статуса публикации
      const currentChapter = !isNewChapter ? safeChapters.find(ch => ch.id === editMode) : null;
      const isCurrentlyPublished = currentChapter?.is_published || false;

      // Показываем предупреждение с подтверждением
      Modal.confirm({
        title: 'Объем главы превышает допустимый',
        content: (
          <div>
            <p>
              Размер главы <strong>{plainNewContent.length.toLocaleString()} символов</strong> превышает
              допустимый лимит <strong>{MAX_CHAPTER_LENGTH.toLocaleString()} символов</strong>.
            </p>
            <p>
              После сохранения такую главу нельзя будет опубликовать.
              {isCurrentlyPublished && (
                <span style={{ color: '#ef4444', fontWeight: 600 }}>
                  {' '}Поскольку глава была опубликована ранее, она будет снята с публикации.
                </span>
              )}
            </p>
          </div>
        ),
        okText: 'Сохранить',
        cancelText: 'Отмена',
        onOk: async () => {
          // Если глава была опубликована, снимаем с публикации
          if (isCurrentlyPublished && currentChapter) {
            await updateChapterPublishStatus(currentChapter, false);
          }

          // Сохраняем главу
          await performSave(data, isNewChapter, chapterToSaveId);
        },
        onCancel: () => {
          // Пользователь отменил сохранение
        }
      });
      return; // Прерываем выполнение, ждем подтверждения
    }

    // Если размер главы в пределах нормы, сохраняем без подтверждения
    await performSave(data, isNewChapter, chapterToSaveId);
  };

  // Вынесенная функция сохранения
  const performSave = async (data, isNewChapter, chapterToSaveId) => {
    const ok = await handleSaveChapter({
      ...data,
      order: isPreface ? 0 : (isNewChapter ? getNextChapterNumber() : safeChapters.find(ch => ch.id === editMode)?.order),
      chapterId: chapterToSaveId,
      isEdit: !isNewChapter,
    });
    if (ok) {
      // Проверяем общий объем произведения после сохранения
      setTimeout(() => {
        const updatedStats = getTotalStats();
        if (updatedStats.total > MAX_NOVEL_LENGTH) {
          message.warning({
            content: `Произведение превышает рекомендуемый размер в ${MAX_NOVEL_LENGTH.toLocaleString()} символов (${updatedStats.total.toLocaleString()} символов). Рекомендуется сократить объем перед завершением.`,
            duration: 8,
          });
        }
      }, 500); // Небольшая задержка для обновления статистики

      // Если редактор в полноэкранном режиме, выходим из него
      if (isFullWidth && onToggleFullWidth) {
        onToggleFullWidth();
      }
      setEditMode(null);
      setIsPreface(false);
      // После сохранения нормализуем порядок и названия
      setChapters(prev => normalizeChaptersOrderAndTitles(prev));
    }
  };

  // Функция для изменения статуса произведения
  const updateBookStatus = async (book, newStatus) => {
    try {
      const csrfToken = getCookie('csrftoken');

      const bookResponse = await fetch(`/api/books/${bookId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({ status: newStatus }),
      });

      if (!bookResponse.ok) {
        message.error('Ошибка при изменении статуса произведения');
        return false;
      }

      // Обновляем локальное состояние
      setLocalBook(prev => ({ ...prev, status: newStatus, is_published: newStatus === 'finished' }));

      if (newStatus === 'draft') {
        // Снимаем все главы с публикации при переводе в черновик
        setChapters(prev => prev.map(ch => ({ ...ch, is_published: false })));
        message.success('Произведение переведено в статус черновика');
      }

      return true;
    } catch (error) {
      console.error('Ошибка при изменении статуса произведения:', error);
      message.error('Ошибка при изменении статуса произведения');
      return false;
    }
  };

  // Функция для снятия главы с публикации
  const updateChapterPublishStatus = async (chapter, shouldPublish) => {
    if (!shouldPublish) {
      // Снимаем с публикации
      try {
        const csrfToken = getCookie('csrftoken');
        const res = await fetch(`/api/books/${bookId}/chapters/${chapter.id}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          body: JSON.stringify({ is_published: false }),
          credentials: 'include',
        });

        if (res.ok) {
          // Мгновенно обновляем локальное состояние главы
          setChapters(prev => prev.map(ch =>
            ch.id === chapter.id ? { ...ch, is_published: false } : ch
          ));

          message.success('Глава снята с публикации');
          return true;
        } else {
          message.error('Ошибка при снятии главы с публикации');
          return false;
        }
      } catch (error) {
        console.error('Ошибка при снятии главы с публикации:', error);
        message.error('Ошибка при снятии главы с публикации');
        return false;
      }
    }
    return true;
  };

  const handleEdit = (chapter) => {
    setIsPreface(chapter.order === 0);
    setEditMode(chapter.id);
    setEditorKey(prev => prev + 1);
  };

  // Функция для массовой публикации глав (используется в модальном окне подтверждения)
  const handleBatchPublishConfirm = async (chaptersToPublish) => {
    try {
      setIsProcessing(true);
      const csrfToken = getCookie('csrftoken');

      // Пытаемся использовать batch API
      let success = false;
      let bookStatus = null;

      try {
        const batchResponse = await fetch(`/api/books/${book.id}/chapters/batch_publish/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
          },
          credentials: 'include',
          body: JSON.stringify({
            chapter_ids: chaptersToPublish.map(ch => ch.id),
            is_published: true
          })
        });

        if (batchResponse.ok) {
          const data = await batchResponse.json();
          success = true;
          bookStatus = data.book_status;
          message.success('Выбранные главы опубликованы');
        } else if (batchResponse.status === 404) {
          console.log('Batch publish API not available, falling back to individual updates');
        } else {
          throw new Error('Batch publish failed');
        }
      } catch (batchError) {
        console.log('Batch publish failed, falling back to individual updates:', batchError);
      }

      // Fallback: публикуем по одной главе
      if (!success) {
        for (const ch of chaptersToPublish) {
          await handleTogglePublish(ch);
        }
      } else {
        // Мгновенно обновляем состояние глав при успешном batch запросе
        setChapters(prev => prev.map(ch =>
          chaptersToPublish.some(publishCh => publishCh.id === ch.id)
            ? { ...ch, is_published: true }
            : ch
        ));

        // Обновляем состояние книги если получили новый статус
        if (bookStatus) {
          setLocalBook(prev => ({
            ...prev,
            is_published: true,
            is_finished: bookStatus === 'finished',
            status: bookStatus
          }));
        }
      }

      // Обновляем данные с сервера
      if (typeof fetchChapters === 'function') {
        fetchChapters();
      }

    } catch (error) {
      console.error('Error in batch publish confirm:', error);
      message.error('Ошибка при массовой публикации');
    } finally {
      setIsProcessing(false);
    }
  };

  // Функция для проверки возможности завершения произведения
  const canFinishBook = () => {
    // Вычисляем текущий опубликованный объем
    const currentPublishedStats = safeChapters.reduce((total, chapter) => {
      if (chapter.is_published) {
        const plainText = (chapter.content || '').replace(/<[^>]*>/g, '');
        return total + plainText.length;
      }
      return total;
    }, 0);

    // Вычисляем объем запланированных к публикации глав
    const scheduledStats = safeChapters.reduce((total, chapter) => {
      if (chapter.scheduled_publish_at && !chapter.is_published) {
        const plainText = (chapter.content || '').replace(/<[^>]*>/g, '');
        return total + plainText.length;
      }
      return total;
    }, 0);

    // Вычисляем объем всех неопубликованных глав (которые будут опубликованы при завершении)
    const unpublishedStats = safeChapters.reduce((total, chapter) => {
      if (!chapter.is_published && !chapter.scheduled_publish_at && chapter.order > 0) {
        const plainText = (chapter.content || '').replace(/<[^>]*>/g, '');
        return total + plainText.length;
      }
      return total;
    }, 0);

    const totalAfterFinish = currentPublishedStats + scheduledStats + unpublishedStats;

    return {
      canFinish: totalAfterFinish <= MAX_NOVEL_LENGTH,
      totalAfterFinish,
      currentPublished: currentPublishedStats,
      scheduled: scheduledStats,
      unpublished: unpublishedStats
    };
  };

  // Функция для проверки лимитов при публикации
  const checkPublishLimits = (chaptersToPublish) => {
    // Вычисляем текущий опубликованный объем
    const currentPublishedStats = safeChapters.reduce((total, chapter) => {
      if (chapter.is_published) {
        const plainText = (chapter.content || '').replace(/<[^>]*>/g, '');
        return total + plainText.length;
      }
      return total;
    }, 0);

    // Вычисляем объем запланированных к публикации глав
    const scheduledStats = safeChapters.reduce((total, chapter) => {
      if (chapter.scheduled_publish_at && !chapter.is_published) {
        const plainText = (chapter.content || '').replace(/<[^>]*>/g, '');
        return total + plainText.length;
      }
      return total;
    }, 0);

    // Вычисляем объем глав, которые мы хотим опубликовать
    const toPublishStats = chaptersToPublish.reduce((total, chapter) => {
      if (!chapter.is_published && !chapter.scheduled_publish_at) {
        const plainText = (chapter.content || '').replace(/<[^>]*>/g, '');
        return total + plainText.length;
      }
      return total;
    }, 0);

    const totalPotentialPublished = currentPublishedStats + scheduledStats + toPublishStats;

    if (totalPotentialPublished > MAX_NOVEL_LENGTH) {
      Modal.error({
        title: 'Объем произведения превышает допустимый',
        content: (
          <div>
            <p>
              После публикации общий размер опубликованного и запланированного контента составит <strong>{totalPotentialPublished.toLocaleString()} символов</strong>,
              что превышает допустимый лимит <strong>{MAX_NOVEL_LENGTH.toLocaleString()} символов</strong>.
            </p>
            <p>
              Публикация невозможна. Рекомендуется сократить текст глав или снять с публикации/планирования часть других глав.
            </p>
          </div>
        ),
        okText: 'Понятно',
      });
      return false;
    }
    return true;
  };

  const handleTogglePublish = async (chapter) => {
    const newPublishStatus = !chapter.is_published;

    if (newPublishStatus) {
      // Проверяем лимиты общего объема перед публикацией
      if (!checkPublishLimits([chapter])) {
        return; // Блокируем публикацию при превышении лимитов
      }

      // Проверяем ограничения перед публикацией
      const publishCheck = canPublishChapter(chapter);
      if (!publishCheck.canPublish) {
        if (publishCheck.reason === 'chapter_over_limit') {
          Modal.warning({
            title: 'Превышен лимит размера главы',
            content: (
              <div>
                <p>{publishCheck.message}</p>
                <p>Рекомендуется сократить главу или разделить на несколько глав меньших размеров перед публикацией.</p>
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    Modal.destroyAll();
                    setEditMode(chapter.id);
                    message.info('Выделите текст в месте разделения и используйте функцию "Разделить главу"');
                  }}
                >
                  Перейти в редактор
                </Button>
              </div>
            ),
          });
        } else {
          Modal.warning({
            title: 'Превышен лимит произведения',
            content: publishCheck.message,
          });
        }
        return;
      }

      // Если публикуем - используем умную публикацию
      await handleSmartPublish(chapter);
    } else {
      // Если снимаем с публикации - используем старую логику
      try {
        const csrfToken = getCookie('csrftoken');
        const res = await fetch(`/api/books/${book.id}/chapters/${chapter.id}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          body: JSON.stringify({ is_published: false }),
          credentials: 'include',
        });

        if (res.ok) {
          // Мгновенно обновляем локальное состояние главы
          setChapters(prev => prev.map(ch =>
            ch.id === chapter.id ? { ...ch, is_published: false } : ch
          ));

          // Проверяем, остались ли опубликованные главы после снятия текущей
          const remainingPublishedChapters = safeChapters.filter(ch =>
            ch.id !== chapter.id && ch.is_published && ch.order > 0
          );

          if (remainingPublishedChapters.length === 0) {
            // Если опубликованных глав не осталось - переводим в черновики
            setLocalBook(prev => ({
              ...prev,
              is_published: false,
              is_finished: false,
              status: 'draft'
            }));

            // Синхронизируем статус с сервером
            try {
              await fetch(`/api/books/${book.id}/`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                  'X-CSRFToken': csrfToken,
                },
                credentials: 'include',
                body: JSON.stringify({ status: 'draft' }),
              });
            } catch (error) {
              console.error('Error updating book status:', error);
            }
          } else {
            // Если есть опубликованные главы - переводим в процесс публикации
            setLocalBook(prev => ({
              ...prev,
              is_published: true,
              is_finished: false,
              status: 'in_progress'
            }));

            // Синхронизируем статус с сервером
            try {
              await fetch(`/api/books/${book.id}/`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                  'X-CSRFToken': csrfToken,
                },
                credentials: 'include',
                body: JSON.stringify({ status: 'in_progress' }),
              });
            } catch (error) {
              console.error('Error updating book status:', error);
            }
          }

          if (typeof fetchChapters === 'function') {
            fetchChapters();
          }
          message.success('Глава снята с публикации');
        } else {
          message.error('Ошибка при снятии с публикации');
        }
      } catch (error) {
        console.error('Error unpublishing chapter:', error);
        message.error('Ошибка при снятии с публикации');
      }
    }
  };

  const handleDelete = async (chapter, showNotification = true) => {
    try {
      const csrfToken = getCookie('csrftoken');
      const res = await fetch(`/api/books/${book.id}/chapters/${chapter.id}/`, {
        method: 'DELETE',
        headers: { 'X-CSRFToken': csrfToken },
        credentials: 'include',
      });
      if (res.ok) {
        if (showNotification) {
          showSuccessWithClose('Глава удалена', { key: `delete-${chapter.id}`, duration: 0 });
        }

        // Локально удаляем главу и нормализуем порядок и названия
        let remaining = safeChapters.filter(ch => ch.id !== chapter.id);
        const normalizedRemaining = normalizeChaptersOrderAndTitles(remaining);
        setChapters([...normalizedRemaining]);

        // Находим главы, которые нужно обновить на сервере (изменился order или title)
        const chaptersToUpdate = [];
        for (const normalizedChapter of normalizedRemaining) {
          const originalChapter = remaining.find(ch => ch.id === normalizedChapter.id);
          if (originalChapter && (
            originalChapter.order !== normalizedChapter.order ||
            originalChapter.title !== normalizedChapter.title
          )) {
            chaptersToUpdate.push(normalizedChapter);
          }
        }

        // Обновляем заголовки на сервере, если есть изменения
        if (chaptersToUpdate.length > 0) {
          console.log(`Updating ${chaptersToUpdate.length} chapters after deletion`);
          await updateChapterTitlesOnServer(chaptersToUpdate);
        }
        
        // Проверяем и обновляем статус книги в реальном времени
        if (chapter.is_published) {
          const remainingPublishedChapters = remaining.filter(ch => ch.is_published && ch.order > 0);
          
          if (remainingPublishedChapters.length === 0) {
            // Если не осталось опубликованных глав - переводим в черновики
            setLocalBook(prev => ({
              ...prev,
              is_published: false,
              is_finished: false,
              status: 'draft'
            }));
            
            // Отправляем обновление статуса книги на сервер
            try {
              await fetch(`/api/books/${bookId}/`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                  'X-CSRFToken': csrfToken,
                },
                credentials: 'include',
                body: JSON.stringify({ status: 'draft' }),
              });
            } catch (error) {
              console.error('Error updating book status:', error);
            }
          } else {
            // Если есть опубликованные главы - переводим в процесс публикации (не завершено)
            setLocalBook(prev => ({
              ...prev,
              is_published: true,
              is_finished: false,
              status: 'in_progress'
            }));
            
            // Отправляем обновление статуса книги на сервер
            try {
              await fetch(`/api/books/${bookId}/`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                  'X-CSRFToken': csrfToken,
                },
                credentials: 'include',
                body: JSON.stringify({ status: 'in_progress' }),
              });
            } catch (error) {
              console.error('Error updating book status:', error);
            }
          }
        } else if (localBook?.status === 'finished') {
          // Если книга завершена и удаляется любая глава (даже неопубликованная) - переводим в процесс
          setLocalBook(prev => ({
            ...prev,
            is_finished: false,
            status: 'in_progress'
          }));
          
          // Отправляем обновление статуса книги на сервер
          try {
            await fetch(`/api/books/${bookId}/`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
              },
              credentials: 'include',
              body: JSON.stringify({ status: 'in_progress' }),
            });
          } catch (error) {
            console.error('Error updating book status:', error);
          }
        }
        
        // fetchChapters(); // Можно оставить для синхронизации, но UI обновляется сразу
        return true;
      } else {
        if (showNotification) {
          showErrorWithClose('Ошибка при удалении главы', { key: `delete-${chapter.id}`, duration: 0 });
        }
        return false;
      }
    } catch {
      if (showNotification) {
        showErrorWithClose('Ошибка при удалении главы', { key: `delete-${chapter.id}`, duration: 0 });
      }
      return false;
    }
  };

  const getNextChapterNumber = () => {
    const nums = safeChapters.filter(ch => ch.order !== 0).map(ch => ch.order || 1);
    return nums.length === 0 ? 1 : Math.max(...nums) + 1;
  };

  // Функция для определения, является ли глава последней
  const isLastChapter = (chapter) => {
    const mainChapters = safeChapters.filter(ch => ch.order > 0 && ch.order !== 99999);
    if (mainChapters.length === 0) return false;
    const maxOrder = Math.max(...mainChapters.map(ch => ch.order));
    return chapter.order === maxOrder;
  };

  const BOOK_TYPE_LABELS = {
    novel: 'Роман',
  };

  // --- Статистика текста ---
  function getTextStats(text) {
    if (!text) return { count: 0, formatted: '0', al: '0.00' };
    // Удаляем HTML теги и считаем только текст
    const plainText = text.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ');
    const count = plainText.length;
    const formatted = count.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
    const al = (count / 40000).toFixed(2);
    return { count, formatted, al };
  }

  const getTotalStats = () => {
    if (!safeChapters) return { total: 0, published: 0, totalFormatted: '0', publishedFormatted: '0', totalAl: '0.00', publishedAl: '0.00' };
    const stats = safeChapters.reduce((acc, chapter) => {
      const { count } = getTextStats(chapter.content);
      acc.total += count;
      if (chapter.is_published) {
        acc.published += count;
      }
      return acc;
    }, { total: 0, published: 0 });

    const totalAl = (stats.total / 40000).toFixed(2);
    const publishedAl = (stats.published / 40000).toFixed(2);

    return {
      ...stats,
      totalFormatted: stats.total.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' '),
      publishedFormatted: stats.published.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' '),
      totalAl,
      publishedAl
    };
  };

  const stats = getTotalStats();

  // --- Функции проверки ограничений ---

  // Проверка превышения лимита для отдельной главы
  const isChapterOverLimit = (chapter) => {
    const { count } = getTextStats(chapter.content);
    return count > MAX_CHAPTER_LENGTH;
  };

  // Проверка превышения общего лимита произведения
  const isNovelOverLimit = () => {
    return stats.total > MAX_NOVEL_LENGTH;
  };

  // Получение количества символов в главе
  const getChapterCharacterCount = (chapter) => {
    const { count } = getTextStats(chapter.content);
    return count;
  };

  // Проверка возможности публикации главы
  const canPublishChapter = (chapter) => {
    const chapterLength = getChapterCharacterCount(chapter);

    // Проверка 1: Глава не превышает лимит
    if (chapterLength > MAX_CHAPTER_LENGTH) {
      return {
        canPublish: false,
        reason: 'chapter_over_limit',
        message: `Глава превышает лимит в ${MAX_CHAPTER_LENGTH.toLocaleString()} символов (${chapterLength.toLocaleString()} символов)`
      };
    }

    // Проверка 2: После публикации общий объем не превысит лимит
    const newPublishedLength = stats.published + chapterLength;
    if (newPublishedLength > MAX_NOVEL_LENGTH) {
      return {
        canPublish: false,
        reason: 'novel_would_exceed_limit',
        message: `Публикация приведет к превышению лимита произведения в ${MAX_NOVEL_LENGTH.toLocaleString()} символов`
      };
    }

    return { canPublish: true };
  };

  // Проверка возможности массовой публикации
  const canBatchPublish = (chapters) => {
    const overLimitChapters = chapters.filter(ch =>
      getChapterCharacterCount(ch) > MAX_CHAPTER_LENGTH
    );

    if (overLimitChapters.length > 0) {
      return {
        canPublish: false,
        reason: 'chapters_over_limit',
        overLimitChapters,
        message: `${overLimitChapters.length} глав превышают лимит в ${MAX_CHAPTER_LENGTH.toLocaleString()} символов`
      };
    }

    // Проверка общего лимита после публикации
    const newContentLength = chapters.reduce((sum, ch) =>
      sum + getChapterCharacterCount(ch), 0
    );

    if (stats.published + newContentLength > MAX_NOVEL_LENGTH) {
      return {
        canPublish: false,
        reason: 'novel_would_exceed_limit',
        message: `Массовая публикация превысит лимит произведения в ${MAX_NOVEL_LENGTH.toLocaleString()} символов`
      };
    }

    return { canPublish: true };
  };

  // Проверка возможности завершения произведения
  const canFinishNovel = () => {
    const overLimitChapters = safeChapters.filter(ch =>
      getChapterCharacterCount(ch) > MAX_CHAPTER_LENGTH
    );

    if (overLimitChapters.length > 0) {
      return {
        canFinish: false,
        reason: 'chapters_over_limit',
        overLimitChapters,
        message: `${overLimitChapters.length} глав превышают лимит в ${MAX_CHAPTER_LENGTH.toLocaleString()} символов`
      };
    }

    if (stats.total > MAX_NOVEL_LENGTH) {
      return {
        canFinish: false,
        reason: 'novel_over_limit',
        message: `Произведение превышает лимит в ${MAX_NOVEL_LENGTH.toLocaleString()} символов (${stats.total.toLocaleString()} символов)`
      };
    }

    return { canFinish: true };
  };

  // --- DOCX загрузка ---
  const handleBackendDocxUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    if (!/\.docx$/.test(file.name.toLowerCase())) {
      message.error('Поддерживаются только файлы .docx');
      return;
    }

    // Жесткая проверка размера файла
    const fileSizeInMB = file.size / (1024 * 1024);
    if (fileSizeInMB > 40) { // Жесткий лимит 40 МБ
      Modal.error({
        title: 'Файл слишком большой',
        content: (
          <div>
            <p>Размер файла <strong>{fileSizeInMB.toFixed(1)} МБ</strong> превышает максимально допустимый размер <strong>40 МБ</strong>.</p>
            <p>Рекомендуется:</p>
            <ul style={{ marginTop: 8, marginBottom: 8 }}>
              <li>Разделить документ на несколько частей</li>
              <li>Удалить лишние изображения из документа</li>
              <li>Сжать изображения в документе</li>
            </ul>
          </div>
        ),
      });
      // Очищаем input для возможности выбора другого файла
      e.target.value = '';
      return;
    }

    message.loading({ content: 'Загрузка и обработка файла...', key: 'docx' });
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('auto_chapter_split', localAutoChapterSplit);
      const csrfToken = getCookie('csrftoken');
      const res = await fetch(`/api/books/${book.id}/upload_docx/`, {
        method: 'POST',
        headers: { 'X-CSRFToken': csrfToken },
        body: formData,
        credentials: 'include',
      });
      let data = {};
      try {
        data = await res.json();
      } catch {}
      if (!res.ok) {
        const errorMsg = data.error || data.detail || data.message || 'Ошибка загрузки или обработки файла';
        showErrorWithClose(errorMsg, { key: 'docx', duration: 0 });
        return;
      }

      // Проверяем тип ответа и выводим сообщение
      if (data.chapters) {
        if (typeof fetchChapters === 'function') {
          await fetchChapters();

          // Проверяем ограничения после загрузки
          const uploadedChapters = data.chapters;
          const overLimitChapters = uploadedChapters.filter(ch => {
            const plainText = (ch.content || '').replace(/<[^>]*>/g, '');
            return plainText.length > MAX_CHAPTER_LENGTH;
          });

          // Показываем предупреждения о превышении лимитов глав
          if (overLimitChapters.length > 0) {
            Modal.warning({
              title: 'Обнаружены главы с превышением лимита',
              content: (
                <div>
                  <p>Следующие главы превышают лимит в {MAX_CHAPTER_LENGTH.toLocaleString()} символов:</p>
                  <ul style={{ marginTop: 8, marginBottom: 8 }}>
                    {overLimitChapters.map(ch => {
                      const plainText = (ch.content || '').replace(/<[^>]*>/g, '');
                      return (
                        <li key={ch.order}>
                          {ch.title}: {plainText.length.toLocaleString()} символов
                        </li>
                      );
                    })}
                  </ul>
                  <p>Рекомендуется разделить эти главы перед публикацией.</p>
                </div>
              ),
            });
          }

          // Находим минимальный и максимальный порядковый номер для обычных глав
          const regularChapters = data.chapters.filter(ch => ch.order > 0 && ch.order < 99999);
          const regularOrders = regularChapters.map(ch => ch.order).sort((a, b) => a - b);
          
          // Находим предисловия (order=0)
          const prefaceChapters = data.chapters.filter(ch => ch.order === 0);
          const hasPrefaceChapters = prefaceChapters.length > 0;
          
          // Находим эпилоги (order=99999 или с соответствующим названием)
          const epilogueChapters = data.chapters.filter(ch => {
            const title = ch.title?.toLowerCase() || '';
            const isEpilogueTitle = title === 'эпилог' || title === 'послесловие' || 
                                   title === 'эпилог.' || title === 'послесловие.';
            return ch.order === 99999 || isEpilogueTitle;
          });
          const hasEpilogueChapters = epilogueChapters.length > 0;
          
          // Формируем сообщение в едином стиле
          let parts = [];
          
          // Добавляем предисловия
          if (hasPrefaceChapters) {
            parts.push('Предисловие');
          }
          
          // Добавляем обычные главы
          if (regularOrders.length > 0) {
            // Если только одна обычная глава
            if (regularOrders.length === 1) {
              parts.push(`Глава ${regularOrders[0]}`);
            } else {
              // Если несколько обычных глав
              const minOrder = regularOrders[0];
              const maxOrder = regularOrders[regularOrders.length - 1];
              parts.push(`Главы ${minOrder}-${maxOrder}`);
            }
          }
          
          // Добавляем эпилог
          if (hasEpilogueChapters) {
            parts.push('Эпилог');
          }
          
          let message = parts.join(', ');
          if (message) {
            message += ' успешно созданы';
          } else {
            message = `Главы успешно созданы`;
          }
          
          showSuccessWithClose(message, { key: 'docx', duration: 0 });
        }
      } else if (data.message) {
        showInfoWithClose(data.message, { key: 'docx', duration: 0 });
      } 
      else {
        showErrorWithClose('Неизвестный формат ответа от сервера', { key: 'docx', duration: 10 });
      }
    } catch (err) {
      showErrorWithClose('Ошибка: ' + (err?.message || err), { key: 'docx', duration: 0 });
    }
  };

  const onSwitchChange = async (checked) => {
    setLocalAutoIndent(checked);
    try {
      const response = await fetch(`/api/books/${bookId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCookie('csrftoken'),
        },
        body: JSON.stringify({ auto_indent: checked }),
      });
      if (!response.ok) throw new Error('Failed to update auto-indent setting');
    } catch (error) {
      console.error('Error updating auto-indent:', error);
      showMessage('error', 'Не удалось обновить настройку автоматической красной строки');
    }
  };

  const onAutoChapterSplitChange = (checked) => {
    setLocalAutoChapterSplit(checked);
  };

  // Проверка на смежность выбранных глав
  const checkContiguous = (orders) => {
    if (orders.length <= 1) return true;
    const sorted = [...orders].sort((a, b) => a - b);
    for (let i = 1; i < sorted.length; i++) {
      if (sorted[i] !== sorted[i - 1] + 1) return false;
    }
    return true;
  };

  const handleSelectChapter = (chapter) => {
    let newSelected;
    if (selectedChapters.includes(chapter.id)) {
      newSelected = selectedChapters.filter(id => id !== chapter.id);
    } else {
      newSelected = [...selectedChapters, chapter.id];
    }
    // Получаем order выбранных глав
    const selectedOrders = safeChapters.filter(ch => newSelected.includes(ch.id)).map(ch => ch.order);
    const contiguous = checkContiguous(selectedOrders);
    setIsContiguous(contiguous);
    setSelectedChapters(newSelected);
  };

  // Для итоговой строки
  const selectedOrders = safeChapters.filter(ch => selectedChapters.includes(ch.id)).map(ch => ch.order).sort((a, b) => a - b);
  let selectionText = '';
  if (selectedOrders.length === 1) {
    selectionText = `Выбрана: Глава ${selectedOrders[0]}`;
  } else if (selectedOrders.length > 1) {
    if (isContiguous) {
      selectionText = `Выбраны главы ${selectedOrders[0]}-${selectedOrders[selectedOrders.length - 1]}`;
    } else {
      selectionText = `Выбраны главы ${selectedOrders.join(', ')}`;
    }
  }

  const selectionColor = isContiguous ? '#22c55e' : '#ef4444';

  // Флаг: все выбранные главы опубликованы
  const allSelectedPublished = safeChapters.filter(ch => selectedChapters.includes(ch.id)).length > 0 &&
    safeChapters.filter(ch => selectedChapters.includes(ch.id)).every(ch => ch.is_published);

  // Флаг: все выбранные главы запланированы к публикации
  const allSelectedScheduled = safeChapters.filter(ch => selectedChapters.includes(ch.id)).length > 0 &&
    safeChapters.filter(ch => selectedChapters.includes(ch.id)).every(ch => ch.scheduled_publish_at && !ch.is_published);

  // Компонент панели массовых операций
  const BatchOperationsPanel = ({ style = {} }) => (
    <div className="chapter-row-summary" style={{
      display: 'flex', alignItems: 'center', padding: 12, borderRadius: 12,
      background: theme === 'dark' ? '#23272f' : '#f9fafb',
      color: selectionColor, fontWeight: 600, fontSize: 16,
      border: `1.5px solid ${selectionColor}`,
      opacity: isProcessing ? 0.7 : 1,
      position: 'relative',
      ...style
    }}>
      {isProcessing && (
        <div style={{
          position: 'absolute',
          top: 0, left: 0, right: 0, bottom: 0,
          background: theme === 'dark' ? '#23272f88' : '#f9fafb88',
          display: 'flex', alignItems: 'center', justifyContent: 'center',
          borderRadius: 12,
          backdropFilter: 'blur(2px)',
          zIndex: 10
        }}>
          <Spin size="small" />
          <span style={{ marginLeft: 8, color: theme === 'dark' ? '#fff' : '#222', fontSize: 14 }}>
            Выполняется операция...
          </span>
        </div>
      )}
      <span style={{ flex: 1 }}>{selectionText}</span>
      <div style={{ display: 'flex', gap: 8 }}>
        <Tooltip title={allSelectedPublished ? 'Снять с публикации выбранные главы' : 'Опубликовать выбранные главы'}>
          <Button
            icon={allSelectedPublished
              ? <EyeInvisibleOutlined style={{ color: '#f59e42', fontSize: 20 }} />
              : <EyeOutlined style={{ color: '#22c55e', fontSize: 20 }} />}
            onClick={handleBatchTogglePublish}
            loading={batchPublishLoading}
            disabled={isProcessing}
            className={theme === 'dark' ? 'dark-action-btn' : ''}
            style={{
              background: theme === 'dark' ? '#23272f' : '#fff',
              border: `1.5px solid ${allSelectedPublished ? '#f59e42' : '#22c55e'}`,
              color: allSelectedPublished ? '#f59e42' : '#22c55e',
              boxShadow: 'none',
              display: 'flex', alignItems: 'center', justifyContent: 'center',
            }}
          />
        </Tooltip>
        {allSelectedScheduled ? (
          <Tooltip title="Отменить запланированную публикацию выбранных глав">
            <Button
              icon={
                <div style={{
                  position: 'relative',
                  display: 'inline-block',
                  fontSize: 20
                }}>
                  <HourglassOutlined style={{ color: '#f59e42', fontSize: 20 }} />
                  <div style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%) rotate(-45deg)',
                    width: '24px',
                    height: '2px',
                    backgroundColor: '#f59e42',
                    borderRadius: '1px'
                  }} />
                </div>
              }
              onClick={handleBatchCancelSchedule}
              loading={batchPublishLoading}
              disabled={isProcessing}
              className={theme === 'dark' ? 'dark-action-btn' : ''}
              style={{
                background: theme === 'dark' ? '#23272f' : '#fff',
                border: '1.5px solid #f59e42',
                color: '#f59e42',
                boxShadow: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            />
          </Tooltip>
        ) : (
          <Tooltip title="Запланировать публикацию выбранных глав">
            <Button
              icon={<HourglassOutlined style={{ color: '#60A5FA', fontSize: 20 }} />}
              onClick={() => openScheduleModal(selectedChapters)}
              className={theme === 'dark' ? 'dark-action-btn' : ''}
              disabled={isProcessing || allSelectedPublished}
              style={{ background: theme === 'dark' ? '#23272f' : '#fff', border: '1.5px solid #60A5FA', color: '#60A5FA', boxShadow: 'none', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
            />
          </Tooltip>
        )}
        <Tooltip title="Удалить выбранные главы">
          <Button
            icon={<DeleteOutlined style={{ color: '#ef4444', fontSize: 20 }} />}
            onClick={handleBatchDeleteClick}
            danger
            loading={batchDeleteLoading}
            disabled={isProcessing}
            className={theme === 'dark' ? 'dark-action-btn' : ''}
          />
        </Tooltip>
      </div>
    </div>
  );

  // Проверка: нужно ли предупреждение о смене номеров глав
  const isWarningNeeded = () => {
    if (selectedChapters.length === 0) return false;
    const selectedOrders = safeChapters
      .filter(ch => selectedChapters.includes(ch.id))
      .map(ch => ch.order)
      .sort((a, b) => a - b);
    const minOrder = selectedOrders[0];
    const maxOrder = Math.max(...safeChapters.map(ch => ch.order));
    for (let order = minOrder; order <= maxOrder; order++) {
      if (!selectedOrders.includes(order)) {
        return true; // есть невыбранная глава после minOrder
      }
    }
    return false; // все подряд до конца — предупреждение не нужно
  };

  const handleBatchDeleteClick = () => {
    if (isWarningNeeded()) {
      setBatchDeleteWarning('Удаление глав в середине книги приведет к изменению порядка и названий последующих глав. Продолжить?');
      setShowBatchDeleteConfirm(true);
    } else {
      handleBatchDelete();
    }
  };

  const handleBatchDeleteConfirmed = async () => {
    setShowBatchDeleteConfirm(false);
    setBatchDeleteLoading(true);
    setIsProcessing(true);
    await handleBatchDelete();
    setBatchDeleteLoading(false);
    setIsProcessing(false);
  };

  // После удаления глав обновить порядок и названия локально
  const handleBatchDelete = async () => {
    const chaptersToDelete = safeChapters.filter(ch => selectedChapters.includes(ch.id));
    const chapterIds = chaptersToDelete.map(ch => ch.id);
    
    // Формируем читаемый текст для уведомления
    const formatChaptersMessage = (chapters) => {
      if (chapters.length === 0) return '';
      
      const orders = chapters.map(ch => ch.order).sort((a, b) => a - b);
      const ranges = [];
      let start = orders[0];
      let end = start;
      
      for (let i = 1; i < orders.length; i++) {
        if (orders[i] === end + 1) {
          end = orders[i];
        } else {
          if (start === end) {
            ranges.push(`${start}`);
          } else {
            ranges.push(`${start}-${end}`);
          }
          start = orders[i];
          end = start;
        }
      }
      
      // Добавляем последний диапазон
      if (start === end) {
        ranges.push(`${start}`);
      } else {
        ranges.push(`${start}-${end}`);
      }
      
      return ranges.join(', ');
    };

    const chaptersText = formatChaptersMessage(chaptersToDelete);
    
    try {
      const csrfToken = getCookie('csrftoken');
      
      // Пытаемся использовать batch API если он есть
      let success = false;
      try {
        const batchResponse = await fetch(`/api/books/${book.id}/chapters/batch_delete/`, {
          method: 'DELETE',
          headers: { 
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken 
          },
          credentials: 'include',
          body: JSON.stringify({ chapter_ids: chapterIds })
        });
        
        if (batchResponse.ok) {
          success = true;
          showSuccessWithClose(`Главы ${chaptersText} удалены`, { key: 'batch-delete', duration: 0 });
        } else if (batchResponse.status === 404) {
          // Batch API не существует, fallback к старому методу
          console.log('Batch delete API not available, falling back to individual deletes');
        } else {
          throw new Error('Batch delete failed');
        }
      } catch (batchError) {
        console.log('Batch delete failed, falling back to individual deletes:', batchError);
      }
      
      // Fallback: удаляем по одной главе, но без отдельных уведомлений
      if (!success) {
        let deletedCount = 0;
        let failedCount = 0;
        
        for (const chapter of chaptersToDelete) {
          try {
            const res = await fetch(`/api/books/${book.id}/chapters/${chapter.id}/`, {
              method: 'DELETE',
              headers: { 'X-CSRFToken': csrfToken },
              credentials: 'include',
            });
            
            if (res.ok) {
              deletedCount++;
            } else {
              failedCount++;
            }
          } catch {
            failedCount++;
          }
        }
        
        if (deletedCount > 0) {
          if (failedCount === 0) {
            showSuccessWithClose(`Главы ${chaptersText} удалены`, { key: 'batch-delete', duration: 0 });
          } else {
            showInfoWithClose(`Удалено глав: ${deletedCount}, ошибок: ${failedCount}`, { key: 'batch-delete', duration: 0 });
          }
        } else {
          showErrorWithClose('Ошибка при удалении глав', { key: 'batch-delete', duration: 0 });
          return;
        }
      }
      
      // После успешного удаления нормализуем локально
      const originalRemaining = safeChapters.filter(ch => !selectedChapters.includes(ch.id));
      const normalizedRemaining = normalizeChaptersOrderAndTitles(originalRemaining);
      setChapters([...normalizedRemaining]);
      setSelectedChapters([]);

      // Находим главы, которые нужно обновить на сервере (изменился order или title)
      const chaptersToUpdate = [];
      for (const normalizedChapter of normalizedRemaining) {
        const originalChapter = originalRemaining.find(ch => ch.id === normalizedChapter.id);
        if (originalChapter && (
          originalChapter.order !== normalizedChapter.order ||
          originalChapter.title !== normalizedChapter.title
        )) {
          chaptersToUpdate.push(normalizedChapter);
        }
      }

      // Обновляем заголовки на сервере, если есть изменения
      if (chaptersToUpdate.length > 0) {
        console.log(`Updating ${chaptersToUpdate.length} chapters after batch deletion`);
        await updateChapterTitlesOnServer(chaptersToUpdate);
      }
      
      // Проверяем и обновляем статус книги в реальном времени
      const remainingPublishedChapters = normalizedRemaining.filter(ch => ch.is_published && ch.order > 0);
      const hasDeletedPublishedChapters = chaptersToDelete.some(ch => ch.is_published);
      
      if (remainingPublishedChapters.length === 0) {
        // Если не осталось опубликованных глав - переводим в черновики
        setLocalBook(prev => ({
          ...prev,
          is_published: false,
          is_finished: false
        }));
      } else if (hasDeletedPublishedChapters || localBook?.is_finished) {
        // Если удалена хотя бы одна опубликованная глава ИЛИ книга была завершена
        // переводим в процесс публикации
        setLocalBook(prev => ({
          ...prev,
          is_published: true,
          is_finished: false
        }));
      }
      
      // Отправляем обновление статуса книги на сервер, если она была завершена
      if (localBook?.is_finished) {
        try {
          await fetch(`/api/books/${bookId}/`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': csrfToken,
            },
            credentials: 'include',
            body: JSON.stringify({ is_finished: false }),
          });
        } catch (error) {
          console.error('Error updating book status:', error);
        }
      }
      
      // Обновляем главы с сервера для синхронизации
      if (typeof fetchChapters === 'function') {
        // Используем setTimeout, чтобы дать серверу время обработать изменения
        setTimeout(() => {
          fetchChapters();
        }, 500);
      }
      
    } catch (error) {
      console.error('Error in batch delete:', error);
      showErrorWithClose('Ошибка при удалении глав', { key: 'batch-delete', duration: 0 });
    }
  };

  // После одиночного удаления главы (используем обновленную handleDelete)
  const handleDeleteAndNormalize = async (chapter) => {
    // handleDelete уже обновлена и включает обновление заголовков на сервере
    await handleDelete(chapter);
  };

  // Проверка: есть ли перед главой (или первой из выбранных) неопубликованные
  const hasUnpublishedBeforeOrder = (order) => {
    return safeChapters.some(ch => ch.order < order && ch.order !== 0 && ch.order !== 99999 && !ch.is_published);
  };

  // Массовая отмена запланированной публикации
  const handleBatchCancelSchedule = async () => {
    try {
      setBatchPublishLoading(true);
      setIsProcessing(true);

      const selectedChaptersData = safeChapters.filter(ch => selectedChapters.includes(ch.id));

      // Группируем главы по задачам для умной отмены
      const taskGroups = new Map();

      for (const chapter of selectedChaptersData) {
        if (chapter.celery_task_id) {
          if (!taskGroups.has(chapter.celery_task_id)) {
            taskGroups.set(chapter.celery_task_id, []);
          }
          taskGroups.get(chapter.celery_task_id).push(chapter);
        }
      }

      const csrfToken = getCookie('csrftoken');

      // Используем массовую отмену планирования
      try {
        const cancelResponse = await fetch(`/api/books/${book.id}/chapters/batch_cancel_schedule/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({
            chapter_ids: selectedChaptersData.map(ch => ch.id)
          })
        });

        if (!cancelResponse.ok) {
          throw new Error('Ошибка массовой отмены планирования');
        }

        const cancelData = await cancelResponse.json();
        console.log(`Массово отменено планирование для ${cancelData.cancelled_count} глав`);

      } catch (error) {
        console.error('Ошибка при массовой отмене планирования:', error);
        message.error('Ошибка при отмене запланированной публикации');
        return;
      }

      message.success(`Отложенная публикация отменена для ${selectedChaptersData.length} глав. Задачи пересозданы для оставшихся запланированных глав.`);

      // Мгновенно обновляем локальное состояние
      setChapters(prev => prev.map(ch =>
        selectedChaptersData.some(canceledCh => canceledCh.id === ch.id)
          ? { ...ch, scheduled_publish_at: null, celery_task_id: null, publish_as_finished: null }
          : ch
      ));

      setSelectedChapters([]);

      if (typeof fetchChapters === 'function') fetchChapters();

    } catch (error) {
      console.error('Error in batch cancel schedule:', error);
      message.error('Ошибка при отмене запланированной публикации');
    } finally {
      setBatchPublishLoading(false);
      setIsProcessing(false);
    }
  };

  // Умная массовая публикация с пересозданием задач
  const handleSmartBatchPublish = async (chaptersToPublish) => {
    try {
      setIsProcessing(true);

      // Группируем главы по их задачам планирования
      const taskGroups = new Map();
      const chaptersWithoutTasks = [];

      for (const chapter of chaptersToPublish) {
        if (chapter.scheduled_publish_at && chapter.celery_task_id) {
          if (!taskGroups.has(chapter.celery_task_id)) {
            taskGroups.set(chapter.celery_task_id, []);
          }
          taskGroups.get(chapter.celery_task_id).push(chapter);
        } else {
          chaptersWithoutTasks.push(chapter);
        }
      }

      const csrfToken = getCookie('csrftoken');

      // Публикуем главы без задач обычным способом
      for (const chapter of chaptersWithoutTasks) {
        await fetch(`/api/books/${book.id}/chapters/${chapter.id}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          body: JSON.stringify({
            is_published: true,
            scheduled_publish_at: null,
            celery_task_id: null,
            publish_as_finished: null
          }),
          credentials: 'include',
        });
      }

      // Обрабатываем группы с задачами
      for (const [taskId, chaptersInTask] of taskGroups) {
        // Публикуем главы из этой группы
        for (const chapter of chaptersInTask) {
          await fetch(`/api/books/${book.id}/chapters/${chapter.id}/`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': csrfToken,
            },
            body: JSON.stringify({
              is_published: true,
              scheduled_publish_at: null,
              celery_task_id: null,
              publish_as_finished: null
            }),
            credentials: 'include',
          });
        }

        // Находим оставшиеся главы с той же задачей
        const remainingChapters = safeChapters.filter(ch =>
          ch.celery_task_id === taskId &&
          !chaptersInTask.some(publishedCh => publishedCh.id === ch.id) &&
          !ch.is_published
        );

        // Если есть оставшиеся главы - пересоздаем задачу
        if (remainingChapters.length > 0) {
          const scheduleTime = remainingChapters[0].scheduled_publish_at;
          const publishAsFinished = remainingChapters[0].publish_as_finished;
          const scheduleTimeISO = new Date(scheduleTime).toISOString();

          await fetch(`/api/books/${book.id}/chapters/batch_schedule/`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': csrfToken,
            },
            credentials: 'include',
            body: JSON.stringify({
              chapter_ids: remainingChapters.map(ch => ch.id),
              scheduled_publish_at: scheduleTimeISO,
              publish_as_finished: publishAsFinished || false
            })
          });
        }
      }

      message.success(`${chaptersToPublish.length} глав опубликовано. Задачи пересозданы для оставшихся запланированных глав.`);

      // Мгновенно обновляем локальное состояние
      setChapters(prev => prev.map(ch =>
        chaptersToPublish.some(publishedCh => publishedCh.id === ch.id)
          ? { ...ch, is_published: true, scheduled_publish_at: null, celery_task_id: null, publish_as_finished: null }
          : ch
      ));

      if (typeof fetchChapters === 'function') fetchChapters();

    } catch (error) {
      console.error('Error in smart batch publish:', error);
      message.error('Ошибка при массовой публикации');
    } finally {
      setIsProcessing(false);
    }
  };

  // Для массовой публикации
  const handleBatchTogglePublish = async () => {
    if (!allSelectedPublished) {
      // Проверяем ограничения перед публикацией
      const selectedChaptersData = safeChapters.filter(ch => selectedChapters.includes(ch.id) && !ch.is_published);
      const batchCheck = canBatchPublish(selectedChaptersData);

      if (!batchCheck.canPublish) {
        if (batchCheck.reason === 'chapters_over_limit') {
          Modal.warning({
            title: 'Превышен лимит размера глав',
            content: (
              <div>
                <p>{batchCheck.message}</p>
                <p>Главы, превышающие лимит:</p>
                <ul>
                  {batchCheck.overLimitChapters.map(ch => (
                    <li key={ch.id}>
                      {ch.title}: {getChapterCharacterCount(ch).toLocaleString()} символов
                    </li>
                  ))}
                </ul>
                <p>Рекомендуется разделить эти главы перед публикацией.</p>
              </div>
            ),
          });
        } else {
          Modal.warning({
            title: 'Превышен лимит произведения',
            content: batchCheck.message,
          });
        }
        return;
      }

      // Проверяем, есть ли среди выбранных неопубликованная последняя глава (только для публикации)
      const hasUnpublishedLastChapter = selectedChaptersData.some(ch => !ch.is_published && isLastChapter(ch));
      
      if (hasUnpublishedLastChapter) {
        // Если выбрана неопубликованная последняя глава - показываем специальное модальное окно
        const lastChapter = selectedChaptersData.find(ch => !ch.is_published && isLastChapter(ch));
        setChapterToPublish(lastChapter);
        setPublishAction('immediate');
        setPublishAsFinished(false);
        setShowPublishLastChapterModal(true);
        return;
      }
      
      // Находим минимальный order среди выбранных
      const selectedOrders = selectedChaptersData.map(ch => ch.order);
      const minOrder = Math.min(...selectedOrders);
      if (hasUnpublishedBeforeOrder(minOrder)) {
        setPendingPublishChapters(selectedChapters);
        setShowPublishWarning(true);
        setPublishConfirmChecked(false);
        return;
      }
    }
    
    if (!allSelectedPublished) {
      // Если публикуем - проверяем лимиты и используем умную массовую публикацию
      const selectedChaptersData = safeChapters.filter(ch => selectedChapters.includes(ch.id) && !ch.is_published);

      // Проверяем лимиты общего объема перед публикацией
      if (!checkPublishLimits(selectedChaptersData)) {
        return; // Блокируем публикацию при превышении лимитов
      }

      await handleBatchPublishConfirm(selectedChaptersData);
      setSelectedChapters([]);
      return;
    }

    setBatchPublishLoading(true);
    setIsProcessing(true);
    try {
      const csrfToken = getCookie('csrftoken');
      
      // Пытаемся использовать batch API
      let success = false;
      let bookStatus = null;
      
      try {
        const batchResponse = await fetch(`/api/books/${book.id}/chapters/batch_publish/`, {
          method: 'PATCH',
          headers: { 
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken 
          },
          credentials: 'include',
          body: JSON.stringify({ 
            chapter_ids: selectedChapters,
            is_published: !allSelectedPublished,
            publish_as_finished: false // По умолчанию не завершаем
          })
        });
        
        if (batchResponse.ok) {
          const data = await batchResponse.json();
          success = true;
          bookStatus = data.book_status;
          showSuccessWithClose(
            allSelectedPublished ? 'Выбранные главы сняты с публикации' : 'Выбранные главы опубликованы',
            { key: 'batch-publish', duration: 0 }
          );
        } else if (batchResponse.status === 404) {
          console.log('Batch publish API not available, falling back to individual updates');
        } else {
          throw new Error('Batch publish failed');
        }
      } catch (batchError) {
        console.log('Batch publish failed, falling back to individual updates:', batchError);
      }
      
      // Fallback: обновляем по одной главе
      if (!success) {
        for (const ch of safeChapters.filter(ch => selectedChapters.includes(ch.id))) {
          if (allSelectedPublished && ch.is_published) {
            const response = await fetch(`/api/books/${book.id}/chapters/${ch.id}/`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
              },
              body: JSON.stringify({ is_published: false, scheduled_publish_at: ch.scheduled_publish_at }),
              credentials: 'include',
            });
            if (!response.ok) {
              throw new Error(`Ошибка при снятии главы ${ch.id} с публикации`);
            }
          } else if (!allSelectedPublished && !ch.is_published) {
            const response = await fetch(`/api/books/${book.id}/chapters/${ch.id}/`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
              },
              body: JSON.stringify({ is_published: true, scheduled_publish_at: null }),
              credentials: 'include',
            });
            if (!response.ok) {
              throw new Error(`Ошибка при публикации главы ${ch.id}`);
            }
          }
        }
        
        showSuccessWithClose(
          allSelectedPublished ? 'Выбранные главы сняты с публикации' : 'Выбранные главы опубликованы',
          { key: 'batch-publish', duration: 0 }
        );
      }
      
      // Мгновенно обновляем состояние глав
      setChapters(prev => prev.map(ch => {
        if (selectedChapters.includes(ch.id)) {
          return {
            ...ch,
            is_published: allSelectedPublished ? false : true,
            scheduled_publish_at: allSelectedPublished ? ch.scheduled_publish_at : null
          };
        }
        return ch;
      }));

      // Обновляем статус книги (либо из batch API ответа, либо вычисляем локально)
      if (bookStatus) {
        setLocalBook(prev => ({
          ...prev,
          is_published: bookStatus.is_published,
          is_finished: bookStatus.is_finished
        }));
      } else {
        // Локальный расчет статуса книги
        if (allSelectedPublished) {
          // При снятии с публикации проверяем, остались ли опубликованные главы
          const selectedChapterIds = selectedChapters;
          const remainingPublishedChapters = safeChapters.filter(ch => 
            !selectedChapterIds.includes(ch.id) && ch.is_published && ch.order > 0
          );
          
          if (remainingPublishedChapters.length === 0) {
            // Если опубликованных глав не осталось - переводим в черновики
            setLocalBook(prev => ({
              ...prev,
              is_published: false,
              is_finished: false
            }));
          } else {
            // Если есть опубликованные главы - переводим в процесс публикации
            setLocalBook(prev => ({
              ...prev,
              is_published: true,
              is_finished: false
            }));
          }
        } else {
          // При публикации глав книга становится опубликованной
          setLocalBook(prev => ({
            ...prev,
            is_published: true
          }));
        }
      }
      
      // Очищаем выбранные главы после успешного выполнения
      setSelectedChapters([]);
      
      // Обновляем для синхронизации с сервером
      if (typeof fetchChapters === 'function') {
        fetchChapters();
      }
      
    } catch (error) {
      console.error('Error in batch toggle publish:', error);
      showErrorWithClose('Ошибка при изменении статуса публикации глав', { key: 'batch-publish', duration: 0 });
    } finally {
      setBatchPublishLoading(false);
      setIsProcessing(false);
    }
  };

  // Для одиночной публикации
  const handleTogglePublishWithCheck = async (chapter) => {
    // Проверяем, является ли глава последней и НЕ опубликована (только для публикации, не для снятия)
    if (!chapter.is_published && isLastChapter(chapter)) {
      setChapterToPublish(chapter);
      setPublishAction('immediate');
      setPublishAsFinished(false); // По умолчанию "В процессе публикации"
      setShowPublishLastChapterModal(true);
      return;
    }
    
    if (!chapter.is_published && hasUnpublishedBeforeOrder(chapter.order)) {
      setPendingPublishChapters([chapter.id]);
      setShowPublishWarning(true);
      setPublishConfirmChecked(false);
      return;
    }
    await handleTogglePublish(chapter);
  };

  // Открыть модалку для одной или нескольких глав
  const openScheduleModal = (chapterIds) => {
    // Проверяем ограничения перед планированием публикации
    const chapters = safeChapters.filter(ch => chapterIds.includes(ch.id) && !ch.is_published);

    // Проверяем лимиты общего объема перед планированием
    if (!checkPublishLimits(chapters)) {
      return; // Блокируем планирование при превышении лимитов
    }

    if (chapters.length === 1) {
      const publishCheck = canPublishChapter(chapters[0]);
      if (!publishCheck.canPublish) {
        if (publishCheck.reason === 'chapter_over_limit') {
          Modal.warning({
            title: 'Превышен лимит размера главы',
            content: (
              <div>
                <p>{publishCheck.message}</p>
                <p>Рекомендуется сократить главу или разделить на несколько глав меньших размеров перед планированием публикации.</p>
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    Modal.destroyAll();
                    setEditMode(chapters[0].id);
                    message.info('Выделите текст в месте разделения и используйте функцию "Разделить главу"');
                  }}
                >
                  Перейти в редактор
                </Button>
              </div>
            ),
          });
        } else {
          Modal.warning({
            title: 'Превышен лимит произведения',
            content: publishCheck.message,
          });
        }
        return;
      }
    } else if (chapters.length > 1) {
      const batchCheck = canBatchPublish(chapters);
      if (!batchCheck.canPublish) {
        if (batchCheck.reason === 'chapters_over_limit') {
          Modal.warning({
            title: 'Превышен лимит размера глав',
            content: (
              <div>
                <p>{batchCheck.message}</p>
                <p>Главы, превышающие лимит:</p>
                <ul>
                  {batchCheck.overLimitChapters.map(ch => (
                    <li key={ch.id}>
                      {ch.title}: {getChapterCharacterCount(ch).toLocaleString()} символов
                    </li>
                  ))}
                </ul>
                <p>Рекомендуется разделить эти главы перед планированием публикации.</p>
              </div>
            ),
          });
        } else {
          Modal.warning({
            title: 'Превышен лимит произведения',
            content: batchCheck.message,
          });
        }
        return;
      }
    }

    // Проверяем, является ли одна из глав неопубликованной последней главой
    const hasUnpublishedLastChapter = chapters.some(ch => !ch.is_published && isLastChapter(ch));
    
    if (hasUnpublishedLastChapter) {
      // Если среди выбранных есть неопубликованная последняя глава - показываем специальное модальное окно
      const lastChapter = chapters.find(ch => !ch.is_published && isLastChapter(ch));
      setChapterToPublish(lastChapter);
      setPublishAction('scheduled');
      setPublishAsFinished(false); // По умолчанию "В процессе публикации"
      setShowPublishLastChapterModal(true);
      return;
    }
    
    const minTime = dayjs().add(5, 'minute').second(0).millisecond(0);
    setMinScheduleTime(minTime);
    setSchedulingChapters(chapterIds);
    setSchedulingAsFinished(false); // По умолчанию "В процессе публикации" для обычного планирования
    setScheduleDate(dayjs().add(30, 'minute').second(0).millisecond(0));
    setShowScheduleModal(true);
  };

  // Умная публикация с пересозданием задач для оставшихся запланированных глав
  const handleSmartPublish = async (chapter) => {
    try {
      setIsProcessing(true);

      // Если глава была запланирована, находим другие главы с тем же расписанием
      let sameScheduleChapters = [];
      if (chapter.scheduled_publish_at && chapter.celery_task_id) {
        sameScheduleChapters = safeChapters.filter(ch =>
          ch.scheduled_publish_at &&
          ch.celery_task_id === chapter.celery_task_id &&
          ch.id !== chapter.id &&
          !ch.is_published // Только неопубликованные
        );
      }

      // Публикуем главу
      const csrfToken = getCookie('csrftoken');
      const res = await fetch(`/api/books/${book.id}/chapters/${chapter.id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        body: JSON.stringify({
          is_published: true,
          scheduled_publish_at: null,
          celery_task_id: null,
          publish_as_finished: null
        }),
        credentials: 'include',
      });

      if (!res.ok) {
        message.error('Ошибка при публикации главы');
        return;
      }

      // Если были другие главы с тем же расписанием - пересоздаем задачу для них
      if (sameScheduleChapters.length > 0) {
        const scheduleTime = sameScheduleChapters[0].scheduled_publish_at;
        const publishAsFinished = sameScheduleChapters[0].publish_as_finished;
        const scheduleTimeISO = new Date(scheduleTime).toISOString();

        const response = await fetch(`/api/books/${book.id}/chapters/batch_schedule/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({
            chapter_ids: sameScheduleChapters.map(ch => ch.id),
            scheduled_publish_at: scheduleTimeISO,
            publish_as_finished: publishAsFinished || false
          })
        });

        if (response.ok) {
          message.success(`Глава опубликована. Задача пересоздана для оставшихся ${sameScheduleChapters.length} запланированных глав.`);
        } else {
          message.warning('Глава опубликована, но возникла ошибка при пересоздании задачи для оставшихся запланированных глав.');
        }
      } else {
        message.success('Глава опубликована');
      }

      // Мгновенно обновляем локальное состояние
      setChapters(prev => prev.map(ch =>
        ch.id === chapter.id
          ? { ...ch, is_published: true, scheduled_publish_at: null, celery_task_id: null, publish_as_finished: null }
          : ch
      ));

      // Обновляем статус книги
      const allChaptersAfterPublish = safeChapters.map(ch =>
        ch.id === chapter.id ? { ...ch, is_published: true } : ch
      );
      const publishedChaptersCount = allChaptersAfterPublish.filter(ch => ch.is_published && ch.order > 0).length;
      const totalChaptersCount = allChaptersAfterPublish.filter(ch => ch.order > 0).length;

      if (publishedChaptersCount === totalChaptersCount && totalChaptersCount > 0) {
        setLocalBook(prev => ({
          ...prev,
          is_published: true,
          is_finished: true,
          status: 'finished'
        }));
      } else {
        setLocalBook(prev => ({
          ...prev,
          is_published: true,
          is_finished: false,
          status: 'in_progress'
        }));
      }

      if (typeof fetchChapters === 'function') fetchChapters();

    } catch (error) {
      console.error('Error in smart publish:', error);
      message.error('Ошибка при публикации главы');
    } finally {
      setIsProcessing(false);
    }
  };

  // Умная отмена отложенной публикации с пересозданием задачи для оставшихся глав
  const handleSmartCancelSchedule = async (chapterToCancel) => {
    try {
      setIsProcessing(true);

      // Находим все главы с тем же временем публикации и task_id
      const sameScheduleChapters = safeChapters.filter(ch =>
        ch.scheduled_publish_at &&
        ch.celery_task_id === chapterToCancel.celery_task_id &&
        ch.id !== chapterToCancel.id
      );

      // Отменяем публикацию для выбранной главы
      const cancelResponse = await fetch(`/api/books/${book.id}/chapters/${chapterToCancel.id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCookie('csrftoken'),
        },
        credentials: 'include',
        body: JSON.stringify({
          scheduled_publish_at: null,
          celery_task_id: null,
          publish_as_finished: null
        })
      });

      if (!cancelResponse.ok) {
        message.error('Ошибка при отмене отложенной публикации');
        return;
      }

      // Если есть другие главы с тем же расписанием - пересоздаем задачу
      if (sameScheduleChapters.length > 0) {
        const scheduleTime = sameScheduleChapters[0].scheduled_publish_at;
        const publishAsFinished = sameScheduleChapters[0].publish_as_finished;

        // Преобразуем время в правильный формат ISO
        const scheduleTimeISO = new Date(scheduleTime).toISOString();

        // Используем API для пересоздания задачи
        const response = await fetch(`/api/books/${book.id}/chapters/batch_schedule/`, {
          method: 'PATCH', // Используем PATCH вместо POST
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken'),
          },
          credentials: 'include',
          body: JSON.stringify({
            chapter_ids: sameScheduleChapters.map(ch => ch.id),
            scheduled_publish_at: scheduleTimeISO,
            publish_as_finished: publishAsFinished || false
          })
        });

        if (response.ok) {
          message.success(`Отложенная публикация отменена для главы. Задача пересоздана для оставшихся ${sameScheduleChapters.length} глав.`);
        } else {
          // Получаем детали ошибки
          let errorMessage = 'Отложенная публикация отменена, но возникла ошибка при пересоздании задачи для оставшихся глав.';
          try {
            const errorData = await response.json();
            if (errorData.error) {
              errorMessage += ` Ошибка: ${errorData.error}`;
            }
            console.error('Ошибка API batch_schedule:', errorData);
          } catch (e) {
            console.error('Ошибка парсинга ответа API:', e);
          }
          message.warning(errorMessage);
        }
      } else {
        message.success('Отложенная публикация отменена');
      }

      // Мгновенно обновляем локальное состояние для отмененной главы
      setChapters(prev => prev.map(ch =>
        ch.id === chapterToCancel.id
          ? { ...ch, scheduled_publish_at: null, celery_task_id: null, publish_as_finished: null }
          : ch
      ));

      if (typeof fetchChapters === 'function') fetchChapters();

    } catch (error) {
      console.error('Error canceling scheduled publication:', error);
      message.error('Ошибка при отмене отложенной публикации');
    } finally {
      setIsProcessing(false);
    }
  };

  // PATCH запрос для планирования публикации
  const handleSchedulePublish = async () => {
    if (!scheduleDate || schedulingChapters.length === 0) return;

    // Проверяем, не прошло ли уже выбранное время относительно фиксированного минимума
    if (minScheduleTime && scheduleDate.isBefore(minScheduleTime)) {
      message.error('Выбранное время уже прошло. Пожалуйста, выберите корректное время для публикации.');
      return;
    }

    // Дополнительная проверка лимитов перед планированием
    const chaptersToSchedule = safeChapters.filter(ch => schedulingChapters.includes(ch.id) && !ch.is_published);
    if (!checkPublishLimits(chaptersToSchedule)) {
      setShowScheduleModal(false);
      return; // Блокируем планирование при превышении лимитов
    }
    
    setSchedulingLoading(true);
    setIsProcessing(true);
    try {
      const csrfToken = getCookie('csrftoken');
      
      // Получаем главы с их порядком для правильного планирования
      const chaptersToSchedule = safeChapters
        .filter(ch => schedulingChapters.includes(ch.id))
        .sort((a, b) => a.order - b.order); // Сортируем по порядку
      
      // Функция форматирования сообщения о главах
      const formatChapterMessage = (chapters) => {
        if (chapters.length === 1) {
          return `Главы ${chapters[0].order}`;
        } else if (chapters.length > 1) {
          const orders = chapters.map(ch => ch.order).sort((a, b) => a - b);
          const isContiguous = orders.every((order, index) => index === 0 || order === orders[index - 1] + 1);
          if (isContiguous) {
            return `Глав ${orders[0]}-${orders[orders.length - 1]}`;
          } else {
            return `Глав ${orders.join(', ')}`;
          }
        }
        return '';
      };
      
      // Пытаемся использовать batch API
      let success = false;
      
      try {
        const batchResponse = await fetch(`/api/books/${book.id}/chapters/batch_schedule/`, {
          method: 'PATCH',
          headers: { 
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken 
          },
          credentials: 'include',
          body: JSON.stringify({ 
            chapter_ids: schedulingChapters,
            scheduled_publish_at: scheduleDate.toISOString(),
            publish_as_finished: schedulingAsFinished
          })
        });
        
        if (batchResponse.ok) {
          const data = await batchResponse.json();
          success = true;
          
          // Формируем правильное сообщение
          const chapterMessage = formatChapterMessage(chaptersToSchedule);
          const statusText = schedulingAsFinished ? '"Завершить произведение"' : '"В процессе"';
          showSuccessWithClose(
            `Публикация ${chapterMessage} запланирована со статусом: ${statusText}`,
            { key: 'batch-schedule', duration: 0 }
          );
        } else if (batchResponse.status === 404) {
          console.log('Batch schedule API not available, falling back to individual updates');
        } else {
          throw new Error('Batch schedule failed');
        }
      } catch (batchError) {
        console.log('Batch schedule failed, falling back to individual updates:', batchError);
      }
      
      // Fallback: выполняем запросы параллельно для ускорения
      if (!success) {
        const promises = chaptersToSchedule.map(async (chapter, index) => {
          // Добавляем значительную задержку между главами для гарантированного порядка
          const delayMinutes = index * 0.25; // 15 секунд между главами для надежности
          const chapterScheduleDate = scheduleDate.add(delayMinutes, 'minute');
          
          const response = await fetch(`/api/books/${book.id}/chapters/${chapter.id}/`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': csrfToken,
            },
            credentials: 'include',
            body: JSON.stringify({ 
              scheduled_publish_at: chapterScheduleDate.second(0).millisecond(0).toISOString(),
              // Добавляем информацию о порядке для бэкенда
              order: chapter.order,
              // Добавляем информацию о статусе завершения для последней главы
              publish_as_finished: schedulingAsFinished && index === chaptersToSchedule.length - 1
            }),
          });
          
          if (!response.ok) {
            throw new Error(`Ошибка при планировании главы ${chapter.id}`);
          }
          
          return { chapterId: chapter.id, success: true };
        });
        
        // Ждем завершения всех запросов
        const results = await Promise.all(promises);
        
        // Проверяем результаты
        const failedChapters = results.filter(r => !r.success);
        if (failedChapters.length > 0) {
          throw new Error(`Ошибка при планировании ${failedChapters.length} глав`);
        }
        
        // Формируем правильное сообщение
        const chapterMessage = formatChapterMessage(chaptersToSchedule);
        const statusText = schedulingAsFinished ? '"Завершить произведение"' : '"В процессе"';
        showSuccessWithClose(
          `Публикация ${chapterMessage} запланирована со статусом: ${statusText}`,
          { key: 'batch-schedule', duration: 0 }
        );
      }
      
      if (typeof fetchChapters === 'function') fetchChapters();
      setShowScheduleModal(false);
      setSchedulingChapters([]);
      setSchedulingAsFinished(false);
      setScheduleDate(null);
      setMinScheduleTime(null);
    } catch (error) {
      console.error('Error scheduling chapters:', error);
      showErrorWithClose('Ошибка при планировании публикации', { key: 'batch-schedule', duration: 0 });
    } finally {
      setSchedulingLoading(false);
      setIsProcessing(false);
    }
  };

  // Функции для обработки публикации последней главы
  const handleLastChapterPublishConfirm = async () => {
    if (!chapterToPublish) return;
    
    try {
      const csrfToken = getCookie('csrftoken');
      
      if (publishAction === 'immediate') {
        // Немедленная публикация
        if (publishAsFinished) {
          // Если выбрано "Завершено" - публикуем все неопубликованные главы и завершаем книгу
          const unpublishedChapters = safeChapters.filter(ch => !ch.is_published && ch.order > 0);
          const chapterIds = unpublishedChapters.map(ch => ch.id);
          
          // Пытаемся использовать batch API
          let success = false;
          let bookStatus = null;
          
          try {
            const batchResponse = await fetch(`/api/books/${book.id}/chapters/batch_publish/`, {
              method: 'PATCH',
              headers: { 
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken 
              },
              credentials: 'include',
              body: JSON.stringify({ 
                chapter_ids: chapterIds,
                is_published: true,
                publish_as_finished: true
              })
            });
            
            if (batchResponse.ok) {
              const data = await batchResponse.json();
              success = true;
              bookStatus = data.book_status;
              showSuccessWithClose('Произведение завершено и опубликовано', { key: 'finish-book', duration: 0 });
            }
          } catch (batchError) {
            console.log('Batch publish failed, falling back to individual updates:', batchError);
          }
          
          // Fallback: публикуем по одной главе
          if (!success) {
            for (const chapter of unpublishedChapters) {
              const chapterRes = await fetch(`/api/books/${bookId}/chapters/${chapter.id}/`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                  'X-CSRFToken': csrfToken,
                },
                body: JSON.stringify({ 
                  is_published: true,
                  publish_as_finished: true
                }),
                credentials: 'include',
              });

              if (!chapterRes.ok) {
                showErrorWithClose('Ошибка при публикации главы', { key: 'finish-book', duration: 0 });
                return;
              }
            }
            showSuccessWithClose('Произведение завершено и опубликовано', { key: 'finish-book', duration: 0 });
          }

          // Обновляем состояние
          setChapters(prev => prev.map(ch => 
            unpublishedChapters.some(uch => uch.id === ch.id) ? { ...ch, is_published: true } : ch
          ));
          
          // Мгновенно обновляем локальное состояние книги
          if (bookStatus) {
            setLocalBook(prev => ({
              ...prev,
              is_published: bookStatus.is_published,
              is_finished: bookStatus.is_finished,
              status: bookStatus.status || (bookStatus.is_finished ? 'finished' : 'in_progress')
            }));
          } else {
            setLocalBook(prev => ({
              ...prev,
              is_published: true,
              is_finished: true,
              status: 'finished'
            }));
          }
        } else {
          // Если выбрано "В процессе публикации" - публикуем все выбранные главы
          // Определяем, какие главы нужно опубликовать
          const chaptersToPublish = selectedChapters.length > 0 
            ? safeChapters.filter(ch => selectedChapters.includes(ch.id) && !ch.is_published)
            : [chapterToPublish];
          
          // Используем batch API для публикации всех глав
          let success = false;
          let bookStatus = null;
          
          try {
            const chapterIds = chaptersToPublish.map(ch => ch.id);
            if (chapterIds.length > 0) {
              const batchResponse = await fetch(`/api/books/${book.id}/chapters/batch_publish/`, {
                method: 'PATCH',
                headers: { 
                  'Content-Type': 'application/json',
                  'X-CSRFToken': csrfToken 
                },
                credentials: 'include',
                body: JSON.stringify({ 
                  chapter_ids: chapterIds,
                  is_published: true,
                  publish_as_finished: false
                })
              });
              
              if (batchResponse.ok) {
                const data = await batchResponse.json();
                success = true;
                bookStatus = data.book_status;
                showSuccessWithClose(
                  chapterIds.length === 1 ? 'Глава опубликована' : 'Главы опубликованы', 
                  { key: 'publish-chapter', duration: 0 }
                );
              }
            }
          } catch (batchError) {
            console.log('Batch publish failed, falling back to individual updates:', batchError);
          }
          
          // Fallback: публикуем по одной главе
          if (!success && chaptersToPublish.length > 0) {
            for (const chapter of chaptersToPublish) {
              const chapterRes = await fetch(`/api/books/${bookId}/chapters/${chapter.id}/`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                  'X-CSRFToken': csrfToken,
                },
                body: JSON.stringify({ 
                  is_published: true,
                  publish_as_finished: false
                }),
                credentials: 'include',
              });

              if (!chapterRes.ok) {
                showErrorWithClose('Ошибка при публикации главы', { key: 'publish-chapter', duration: 0 });
                return;
              }
            }
            showSuccessWithClose(
              chaptersToPublish.length === 1 ? 'Глава опубликована' : 'Главы опубликованы', 
              { key: 'publish-chapter', duration: 0 }
            );
          }

          // Обновляем состояние
          if (chaptersToPublish.length > 0) {
            const publishedIds = chaptersToPublish.map(ch => ch.id);
            setChapters(prev => prev.map(ch => 
              publishedIds.includes(ch.id) ? { ...ch, is_published: true } : ch
            ));
            
            // Мгновенно обновляем локальное состояние книги
            if (bookStatus) {
              setLocalBook(prev => ({
                ...prev,
                is_published: bookStatus.is_published,
                is_finished: bookStatus.is_finished,
                status: bookStatus.status || (bookStatus.is_finished ? 'finished' : 'in_progress')
              }));
            } else {
              setLocalBook(prev => ({
                ...prev,
                is_published: true,
                is_finished: false,
                status: 'in_progress'
              }));
            }
          }
        }
      } else if (publishAction === 'scheduled') {
        // Запланированная публикация
        if (publishAsFinished) {
          // Если выбрано "Завершено" - планируем выбранные главы с завершением произведения
          // Бэкенд сам добавит остальные неопубликованные главы по умной логике
          setShowPublishLastChapterModal(false);
          const minTime = dayjs().add(5, 'minute').second(0).millisecond(0);
          setMinScheduleTime(minTime);
          // Используем выбранные главы, а не все неопубликованные
          const chaptersToSchedule = selectedChapters.length > 0 ? selectedChapters : [chapterToPublish.id];
          setSchedulingChapters(chaptersToSchedule);
          setSchedulingAsFinished(true); // Устанавливаем статус завершения
          setScheduleDate(dayjs().add(30, 'minute').second(0).millisecond(0));
          setShowScheduleModal(true);
          return;
        } else {
          // Если выбрано "В процессе публикации" - планируем только выбранную главу
          setShowPublishLastChapterModal(false);
          const minTime = dayjs().add(5, 'minute').second(0).millisecond(0);
          setMinScheduleTime(minTime);
          // Включаем все выбранные главы, включая последнюю
          const chaptersToSchedule = selectedChapters.length > 0 ? selectedChapters : [chapterToPublish.id];
          setSchedulingChapters(chaptersToSchedule);
          setSchedulingAsFinished(false); // Устанавливаем статус НЕ завершения
          setScheduleDate(dayjs().add(30, 'minute').second(0).millisecond(0));
          setShowScheduleModal(true);
          return;
        }
      }
      
      fetchChapters();
      setShowPublishLastChapterModal(false);
      setChapterToPublish(null);
      setPublishAction(null);
      setPublishAsFinished(false);
      // Очищаем выбранные главы после публикации
      setSelectedChapters([]);
      
    } catch (error) {
      console.error('Error publishing last chapter:', error);
      message.error('Ошибка при публикации');
    }
  };

  useEffect(() => {
    if (!bookId) return;
    
    let wsProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
    let wsHost = window.location.hostname;
    let wsPort = 8000; // порт Daphne
    let wsUrl = `${wsProtocol}://${wsHost}:${wsPort}/ws/books/${bookId}/`;
    let socket = null;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 3;
    let reconnectTimeout = null;

    const connectWebSocket = () => {
      // Задержка перед первой попыткой подключения
      const delay = reconnectAttempts === 0 ? 1000 : 2000 * reconnectAttempts;
      
      reconnectTimeout = setTimeout(() => {
        try {
          socket = new window.WebSocket(wsUrl);

          // Устанавливаем обработчики сразу, до подключения
          socket.onerror = () => {
            // Полностью подавляем ошибки - они нормальны при разработке
          };

          socket.onopen = () => {
            // console.log('WebSocket connected successfully');
            reconnectAttempts = 0; // Сбрасываем счетчик при успешном подключении
          };

                    socket.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);

              if (data.type === 'chapters_batch_published') {
                // Обработка массовой публикации (отложенная публикация)
                const publishedChapterIds = data.chapters.map(ch => ch.id);

                setChapters(prevChapters => prevChapters.map(ch =>
                  publishedChapterIds.includes(ch.id) ? {
                    ...ch,
                    is_published: true,
                    scheduled_publish_at: null,
                    celery_task_id: null,
                    publish_as_finished: null,
                    published_at: new Date().toISOString()
                  } : ch
                ));

                // Обновляем статус книги
                const bookIsFinished = data.book_is_finished || false;
                setLocalBook(prev => ({
                  ...prev,
                  is_published: true,
                  is_finished: bookIsFinished,
                  status: data.book_status || (bookIsFinished ? 'finished' : 'in_progress'),
                  published_at: prev.published_at || new Date().toISOString(),
                  updated_at: new Date().toISOString()
                }));

                // Показываем уведомление о запланированной публикации
                if (data.was_scheduled) {
                  if (bookIsFinished) {
                    antdMessage.success('Произведение завершено! Запланированные главы успешно опубликованы');
                  } else {
                    antdMessage.success('Запланированные главы успешно опубликованы');
                  }
                }

              } else if (data.type === 'chapters_batch_unpublished') {
                // Обработка массового снятия с публикации
                const unpublishedChapterIds = data.chapters.map(ch => ch.id);

                setChapters(prevChapters => prevChapters.map(ch =>
                  unpublishedChapterIds.includes(ch.id) ? {
                    ...ch,
                    is_published: false,
                    scheduled_publish_at: null,
                    celery_task_id: null,
                    publish_as_finished: null,
                    published_at: null
                  } : ch
                ));

                // Обновляем статус книги
                const bookIsFinished = data.book_is_finished || false;
                setLocalBook(prev => ({
                  ...prev,
                  is_published: data.book_status !== 'draft',
                  is_finished: bookIsFinished,
                  status: data.book_status,
                  updated_at: new Date().toISOString()
                }));

                // НЕ показываем уведомление здесь - оно показывается в handleBatchTogglePublish

              } else if (data.type === 'chapter_published') {
                // Обработка одиночной публикации
                setChapters(prevChapters => prevChapters.map(ch =>
                  ch.id === data.chapter_id ? {
                    ...ch,
                    is_published: true,
                    scheduled_publish_at: null,
                    celery_task_id: null,
                    publish_as_finished: null,
                    published_at: new Date().toISOString()
                  } : ch
                ));

                // Обновляем статус книги для произведений (роман/повесть)
                const bookIsFinished = data.book_is_finished || false;

                setLocalBook(prev => ({
                  ...prev,
                  is_published: true,
                  is_finished: bookIsFinished,
                  status: data.book_status || (bookIsFinished ? 'finished' : 'in_progress'),
                  published_at: prev.published_at || new Date().toISOString(),
                  updated_at: new Date().toISOString()
                }));

                // Показываем уведомление только если это НЕ часть массовой операции
                if (!data.is_batch_operation) {
                  if (bookIsFinished) {
                    antdMessage.success(`Произведение завершено! Глава опубликована: ${data.title}`);
                  } else {
                    antdMessage.success(`Глава опубликована: ${data.title}`);
                  }
                }
              } else if (data.type === 'chapter_unpublished') {
                 setChapters(prevChapters => {
                   const updatedChapters = prevChapters.map(ch =>
                     ch.id === data.chapter_id ? { ...ch, is_published: false, scheduled_publish_at: null } : ch
                   );
                   
                   // Проверяем, остались ли опубликованные главы после снятия с публикации
                   const remainingPublishedChapters = updatedChapters.filter(ch => 
                     ch.is_published && ch.order > 0
                   );
                   
                   // Обновляем статус книги
                   setLocalBook(prev => ({
                     ...prev,
                     is_published: remainingPublishedChapters.length > 0,
                     is_finished: remainingPublishedChapters.length > 0 ? prev.is_finished : false,
                     updated_at: new Date().toISOString()
                   }));
                   
                   return updatedChapters;
                 });
                 
                 // Показываем уведомление только если это НЕ часть массовой операции
                 if (!data.is_batch_operation) {
                   antdMessage.success(`Глава снята с публикации: ${data.title}`);
                 }
              }
            } catch (e) {
              // ignore invalid JSON
            }
          };

          socket.onclose = (event) => {
            // Автоматическое переподключение только при необходимости
            if (reconnectAttempts < maxReconnectAttempts && !event.wasClean) {
              reconnectAttempts++;
              // console.log(`WebSocket disconnected, attempting to reconnect... (${reconnectAttempts}/${maxReconnectAttempts})`);
              connectWebSocket(); // Рекурсивный вызов с задержкой внутри
            }
          };

        } catch (error) {
          // Полностью подавляем ошибки создания WebSocket
          if (reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            connectWebSocket();
          }
        }
      }, delay);
    };

    connectWebSocket();

    return () => {
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }
      if (socket) {
        socket.close();
      }
    };
  }, [bookId]);

  // Добавить функцию для обновления порядка глав на сервере
  // Функции управления статусом произведения
  const handleToDraft = async () => {
    if (!bookId || !safeChapters.length) return;
    const csrfToken = getCookie('csrftoken');
    
    try {
      // ОПТИМИЗИРОВАНО: Используем прямое изменение статуса книги на черновик
      // Бэкенд автоматически снимет все главы с публикации одним запросом при изменении статуса на 'draft'
      const bookResponse = await fetch(`/api/books/${bookId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({ status: 'draft' }),
      });

      if (!bookResponse.ok) {
        message.error('Ошибка при изменении статуса книги');
        return;
      }

      // Обновляем главы и книгу - все главы теперь сняты с публикации
      setChapters(prev => prev.map(ch => ({ ...ch, is_published: false })));
      
      // Мгновенно обновляем локальное состояние книги
      setLocalBook(prev => ({
        ...prev,
        is_published: false,
        is_finished: false,
        status: 'draft'
      }));
      
      // Обновляем книгу
      fetchChapters();
      message.success('Произведение переведено в черновики');
      
    } catch (error) {
      console.error('Error unpublishing book:', error);
      message.error('Ошибка');
    }
  };

  const handleChangeToInProgress = async () => {
    if (!bookId || !safeChapters.length) return;
    const csrfToken = getCookie('csrftoken');
    
    try {
      // Если произведение не опубликовано (черновик), публикуем первые главы как "в процессе"
      if (localBook?.status === 'draft') {
        // Публикуем первую главу или несколько первых глав
        const chaptersToPublish = safeChapters.filter(ch => ch.order > 0 && ch.order <= 1);
        
        for (const chapter of chaptersToPublish) {
          const chapterRes = await fetch(`/api/books/${bookId}/chapters/${chapter.id}/`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': csrfToken,
            },
            body: JSON.stringify({ 
              is_published: true,
              publish_as_finished: false
            }),
            credentials: 'include',
          });

          if (!chapterRes.ok) {
            message.error('Ошибка при публикации главы');
            return;
          }
        }

        // Обновляем состояние
        setChapters(prev => prev.map(ch => 
          chaptersToPublish.some(ctp => ctp.id === ch.id) ? { ...ch, is_published: true } : ch
        ));
        
        // Мгновенно обновляем локальное состояние книги
        setLocalBook(prev => ({
          ...prev,
          is_published: true,
          is_finished: false,
          status: 'in_progress'
        }));
        
        // Также обновляем статус на сервере
        await fetch(`/api/books/${bookId}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({ status: 'in_progress' }),
        });
        
        fetchChapters();
        message.success('Произведение опубликовано частично');
      } else {
        // Если уже опубликован и завершен, просто меняем статус на "в процессе"
        const response = await fetch(`/api/books/${bookId}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({ status: 'in_progress' }),
        });
        
        if (response.ok) {
          // Мгновенно обновляем локальное состояние книги
          setLocalBook(prev => ({
            ...prev,
            is_finished: false,
            status: 'in_progress'
          }));
          fetchChapters();
          message.success('Произведение переведено в процесс публикации');
        } else {
          message.error('Ошибка при изменении статуса');
        }
      }
    } catch (error) {
      console.error('Error changing status:', error);
      message.error('Ошибка при изменении статуса');
    }
  };

  const handleFinishBook = async () => {
    if (!bookId || !safeChapters.length) return;

    // Проверяем ограничения перед завершением произведения
    const finishCheck = canFinishNovel();
    if (!finishCheck.canFinish) {
      if (finishCheck.reason === 'chapters_over_limit') {
        Modal.warning({
          title: 'Превышен лимит размера глав',
          content: (
            <div>
              <p>{finishCheck.message}</p>
              <p>Главы, превышающие лимит:</p>
              <ul>
                {finishCheck.overLimitChapters.map(ch => (
                  <li key={ch.id}>
                    {ch.title}: {getChapterCharacterCount(ch).toLocaleString()} символов
                  </li>
                ))}
              </ul>
              <p>Необходимо разделить эти главы перед завершением произведения.</p>
            </div>
          ),
        });
      } else {
        Modal.warning({
          title: 'Превышен лимит произведения',
          content: finishCheck.message,
        });
      }
      return;
    }

    const csrfToken = getCookie('csrftoken');

    try {
      // Если произведение не опубликовано (черновик), публикуем все главы как завершенные
      if (localBook?.status === 'draft') {
        // ОПТИМИЗИРОВАНО: Используем прямое изменение статуса книги вместо поштучной публикации глав
        // Бэкенд автоматически опубликует все главы одним запросом при изменении статуса на 'finished'
        const response = await fetch(`/api/books/${bookId}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({ status: 'finished' }),
        });

        if (!response.ok) {
          message.error('Ошибка при завершении произведения');
          return;
        }

        // Обновляем состояние - все главы теперь опубликованы
        const chaptersToPublish = safeChapters.filter(ch => ch.order > 0);
        setChapters(prev => prev.map(ch =>
          chaptersToPublish.some(ctp => ctp.id === ch.id) ? { ...ch, is_published: true } : ch
        ));
        
        // Мгновенно обновляем локальное состояние книги
        setLocalBook(prev => ({
          ...prev,
          is_published: true,
          is_finished: true,
          status: 'finished'
        }));
        
        fetchChapters();
        message.success('Произведение завершено и опубликовано');
      } else {
        // Если уже опубликован в процессе, просто завершаем произведение
        // ОПТИМИЗИРОВАНО: Бэкенд автоматически опубликует все неопубликованные главы при изменении статуса на 'finished'
        const unpublishedChapters = safeChapters.filter(ch => ch.order > 0 && !ch.is_published);

        // Устанавливаем произведение как завершенное - бэкенд опубликует все главы одним запросом
        const response = await fetch(`/api/books/${bookId}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({ status: 'finished' }),
        });
        
        if (response.ok) {
          setChapters(prev => prev.map(ch => 
            unpublishedChapters.some(uc => uc.id === ch.id) ? { ...ch, is_published: true } : ch
          ));
          // Мгновенно обновляем локальное состояние книги
          setLocalBook(prev => ({
            ...prev,
            is_finished: true,
            status: 'finished'
          }));
          fetchChapters();
          message.success('Произведение завершено');
        } else {
          message.error('Ошибка при завершении произведения');
        }
      }
    } catch (error) {
      console.error('Error finishing book:', error);
      message.error('Ошибка при завершении произведения');
    }
  };

  // Функции для удаления произведения
  const handleDeleteClick = () => {
    setIsDeleteModalVisible(true);
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalVisible(false);
    setDeleteConfirmation('');
    deleteForm.resetFields();
  };

  const handleDeleteConfirm = async (values) => {
    if (!localBook) return;

    try {
      const csrfToken = getCookie('csrftoken');
      if (!csrfToken) {
        throw new Error('CSRF token not found');
      }

      const response = await fetch(`/api/books/${localBook.id}/`, {
        method: 'DELETE',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete book');
      }

      showSuccessWithClose('Произведение успешно удалено', { key: 'delete-book', duration: 0 });
      handleDeleteCancel();
      // Переходим к странице со списком книг пользователя
      navigate(`/lpu/${username}/books`);
    } catch (error) {
      showErrorWithClose('Ошибка при удалении произведения', { key: 'delete-book', duration: 0 });
    }
  };

  const handleButtonState = (buttonKey, stateType, value) => {
    setButtonStates(prev => ({
      ...prev,
      [buttonKey]: {
        ...prev[buttonKey],
        [stateType]: value
      }
    }));
  };

  const renderStatusActions = () => {
    if (!isOwner) return null;
    
    const btnStyle = { 
      fontSize: 13, 
      fontWeight: 500, 
      padding: '4px 8px', 
      display: 'flex', 
      alignItems: 'center', 
      height: 32, 
      justifyContent: 'flex-start',
      border: `1px solid ${theme === 'dark' ? '#374151' : '#d1d5db'}`,
      borderRadius: 6
    };

    const getButtonStyle = (buttonKey, baseColor, isDisabled = false) => {
      const state = buttonStates[buttonKey];
      const isActive = state?.hover || state?.focus;
      
      return {
        ...btnStyle,
        color: isDisabled ? '#9ca3af' : baseColor,
        cursor: isDisabled ? 'not-allowed' : 'pointer',
        borderColor: isActive && !isDisabled ? baseColor : (isDisabled ? '#e5e7eb' : (theme === 'dark' ? '#374151' : '#d1d5db')),
        borderWidth: isActive && !isDisabled ? '2px' : '1px',
        transition: 'all 0.2s ease'
      };
    };
    
    const buttons = [];
    
    // Кнопка "Завершить произведение" - для черновиков и в процессе публикации (только если есть главы)
    if (safeChapters.length > 0 && (localBook?.status === 'draft' || localBook?.status === 'in_progress')) {
      buttons.push(
        <Button
          key="finish-book"
          type="link"
          style={getButtonStyle('finish', '#22c55e')}
          icon={<CheckOutlined style={{ color: '#22c55e', fontSize: 18, marginRight: 8 }} />}
          onClick={handleFinishBook}
          onMouseEnter={() => handleButtonState('finish', 'hover', true)}
          onMouseLeave={() => handleButtonState('finish', 'hover', false)}
          onFocus={() => handleButtonState('finish', 'focus', true)}
          onBlur={() => handleButtonState('finish', 'focus', false)}
        >
          Завершить произведение
        </Button>
      );
    }
    
    // Кнопка "В процесс публикации" - для черновиков и полностью опубликованных (только если есть главы)
    if (safeChapters.length > 0 && (localBook?.status === 'draft' || localBook?.status === 'finished')) {
      const buttonText = localBook?.status === 'finished' ? 'В процесс публикации' : 'Опубликовать частично';
      buttons.push(
        <Button
          key="change-to-progress"
          type="link"
          style={getButtonStyle('partial', '#60A5FA')}
          icon={<EditOutlined style={{ color: '#60A5FA', fontSize: 18, marginRight: 8 }} />}
          onClick={handleChangeToInProgress}
          onMouseEnter={() => handleButtonState('partial', 'hover', true)}
          onMouseLeave={() => handleButtonState('partial', 'hover', false)}
          onFocus={() => handleButtonState('partial', 'focus', true)}
          onBlur={() => handleButtonState('partial', 'focus', false)}
        >
          {buttonText}
        </Button>
      );
    }
    
    // Кнопка "Убрать в черновики" - для в процессе публикации и полностью опубликованных
    if (localBook?.status === 'in_progress' || localBook?.status === 'finished') {
      buttons.push(
        <Button
          key="to-draft"
          type="link"
          style={getButtonStyle('draft', '#ef4444')}
          icon={<CloseCircleFilled style={{ color: '#ef4444', fontSize: 18, marginRight: 8 }} />}
          onClick={handleToDraft}
          onMouseEnter={() => handleButtonState('draft', 'hover', true)}
          onMouseLeave={() => handleButtonState('draft', 'hover', false)}
          onFocus={() => handleButtonState('draft', 'focus', true)}
          onBlur={() => handleButtonState('draft', 'focus', false)}
        >
          Убрать в черновики
        </Button>
      );
    }
    
    if (buttons.length === 0) return null;
    
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: 6, marginTop: 8 }}>
        {buttons}
      </div>
    );
  };

  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  async function updateChaptersOrder(bookId, chapters) {
    const csrfToken = getCookie('csrftoken');
    // Массовый PATCH для order
    const response = await fetch(`/api/books/${bookId}/chapters/reorder/`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrfToken,
      },
      credentials: 'include',
      body: JSON.stringify(chapters.map(ch => ({ id: ch.id, order: ch.order }))),
    });
    if (!response.ok) {
      message.error('Ошибка при сохранении порядка глав');
    }
    // PATCH для title (всегда для автогенерируемых)
    for (const ch of chapters) {
      if (/^Глава \d+(:|$)/.test(ch.title)) {
        await fetch(`/api/books/${bookId}/chapters/${ch.id}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({ title: ch.title, order: ch.order }),
        });
      }
    }
    if (typeof fetchChapters === 'function') {
      fetchChapters();
    }
  }

  // Очищаем selectedChapters от несуществующих id после любого изменения safeChapters
  useEffect(() => {
    setSelectedChapters(prev => prev.filter(id => safeChapters.some(ch => ch.id === id)));
  }, [safeChapters]);

  // После fetchChapters (если используется), также очищать selectedChapters
  useEffect(() => {
    if (typeof fetchChapters === 'function') {
      setSelectedChapters([]);
    }
  }, [fetchChapters]);

  const isFinishedTooltipText = 'Произведение в статусе "Завершено"! Для выполнения этого действия переведите произведение в статус: "В процессе публикации" или "Черновик".';

  // Функция для обработки клика вне области подсказки
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showAutoSplitHelp && !event.target.closest('.auto-split-help-container')) {
        setShowAutoSplitHelp(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showAutoSplitHelp]);

  return (
    <div className={theme === 'dark' ? 'bg-gray-900 text-gray-100' : 'bg-gray-100 text-gray-900'} style={{ borderRadius: isFullWidth ? 0 : 16, padding: isFullWidth ? 0 : 24, position: 'relative' }}>
      {/* Кнопка удаления в правом верхнем углу */}
      {isOwner && !isFullWidth && (
        <div style={{ position: 'absolute', top: 16, right: 16, zIndex: 10 }}>
          <Tooltip title="Удалить произведение" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
            <Button
              icon={<DeleteOutlined style={{ fontSize: '14px' }} />}
              size="small"
              onClick={handleDeleteClick}
              className="delete-book-button"
              style={{
                borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                padding: 0,
                transition: 'all 0.2s ease'
              }}
            />
          </Tooltip>
        </div>
      )}
      <div style={{ textAlign: 'center', marginBottom: 32, display: isFullWidth && editMode ? 'none' : 'block' }}>
        <Title
          level={3}
          style={{
            margin: 0,
            color: theme === 'dark' ? '#fff' : '#222',
            transition: 'color 0.2s'
          }}
        >
          2. Работа с текстом романа
        </Title>
      </div>
      {!isFullWidth && (
        <div className="flex flex-row items-start mb-6">
          <div className="relative group cursor-pointer mr-4" style={{ width: 160, height: 240 }}>
          {/* Основная обложка книги */}
          <div className="relative h-full bg-gray-200 dark:bg-gray-700 overflow-hidden shadow-lg transform transition-transform duration-300 group-hover:scale-105" 
               style={{
                 borderRadius: '0 8px 8px 0'
               }}>
            <img
              src={coverUrl}
              alt={localBook?.title}
              className="w-full h-full object-cover"
            />
            
            {/* Корешок книги - левая полоска */}
            <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-r from-black/30 via-black/10 to-transparent"></div>
            
            {/* Дополнительная тень для глубины */}
            <div className="absolute left-1 top-0 bottom-0 w-1 bg-gradient-to-r from-black/20 to-transparent"></div>
            
            {/* Блик на обложке */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
          
          {/* Тень под книгой */}
          <div className="absolute -bottom-1 left-1 right-2 h-1 bg-black/20 rounded-full blur-sm transform transition-transform duration-300 group-hover:scale-110"></div>
        </div>
        <div>
          <div className="text-xl font-bold mb-1" style={{ color: theme === 'dark' ? '#fff' : '#222' }}>{localBook?.title}</div>
          <div className="text-sm mb-1" style={{ color: theme === 'dark' ? '#a1a1aa' : '#888', fontStyle: 'italic' }}>
            {BOOK_TYPE_LABELS[localBook?.type] || ''}
          </div>
          {stats.published > 0 && (
            <div className="text-sm mb-1" style={{ color: theme === 'dark' ? '#fff' : '#222' }}>
              Опубликовано: {stats.publishedFormatted} зн.
              <Tooltip title="Объем текста в АЛ (Авторский лист = 40 000 знаков)" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                <span className="ml-4 text-sm font-semibold" style={{ color: theme === 'dark' ? '#22c55e' : '#16a34a' }}>
                  АЛ: {stats.publishedAl}
                </span>
              </Tooltip>
            </div>
          )}
          {stats.total > 0 && (
            <div className="text-sm mb-1" style={{ color: theme === 'dark' ? '#fff' : '#222' }}>
              Общее кол-во знаков:{' '}
              <Tooltip
                title={isNovelOverLimit() ? `Превышен лимит произведения в ${MAX_NOVEL_LENGTH.toLocaleString()} символов` : `Лимит произведения: ${MAX_NOVEL_LENGTH.toLocaleString()} символов`}
                classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
              >
                <span style={{
                  color: isNovelOverLimit() ? '#ef4444' : (theme === 'dark' ? '#fff' : '#222'),
                  fontWeight: isNovelOverLimit() ? 600 : 400,
                  cursor: 'help'
                }}>
                  {stats.totalFormatted} зн.
                </span>
              </Tooltip>
              <Tooltip title="Объем текста в АЛ (Авторский лист = 40 000 знаков)" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                <span className="ml-4 text-sm font-semibold" style={{ color: theme === 'dark' ? '#60A5FA' : '#2563eb' }}>
                  АЛ: {stats.totalAl}
                </span>
              </Tooltip>
            </div>
          )}
          <div className="text-xs mt-1" style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
              {localBook?.status === 'finished' ? (
                <><span style={{ color: theme === 'dark' ? '#22c55e' : '#22c55e' }}>Завершено</span> <span style={{ color: '#22c55e', fontSize: 18 }}>✔</span></>
              ) : safeChapters.some(ch => ch.is_published) ? (
                <span style={{ color: theme === 'dark' ? '#60A5FA' : '#2563eb', fontWeight: 600, display: 'flex', alignItems: 'center', gap: 4, fontSize: 14, marginTop: 2 }}>
                  <EditOutlined style={{ color: theme === 'dark' ? '#60A5FA' : '#2563eb', fontSize: 15, marginRight: 4 }} /> В процессе публикации
                </span>
              ) : (
                <span style={{ color: '#f59e42', fontWeight: 600, display: 'flex', alignItems: 'center', gap: 4, fontSize: 14, marginTop: 2 }}>
                  <ClockCircleOutlined style={{ color: '#f59e42', fontSize: 15, marginRight: 4 }} /> Черновик
                </span>
              )}
            </div>
            {localBook?.status === 'in_progress' && localBook?.updated_at && (
              <div className="text-xs" style={{ color: theme === 'dark' ? '#9ca3af' : '#6b7280' }}>
                {formatUpdatedDate(localBook.updated_at, timezone)}
              </div>
            )}
            {localBook?.status === 'finished' && localBook?.published_at && (
              <div className="text-xs" style={{ color: theme === 'dark' ? '#9ca3af' : '#6b7280' }}>
                {formatPublishedDate(localBook.published_at, timezone)}
              </div>
            )}
          </div>
          {/* Кнопки управления статусом произведения */}
          {renderStatusActions()}
          </div>
        </div>
      )}
      {!isFullWidth && (
        <div className="mb-4 flex items-center gap-4">
          {localBook?.type !== 'story' && localBook?.status !== 'finished' && ( // Загрузка DOCX только для романов и повестей, и НЕ для завершенных произведений
            <div className="mb-4 flex items-center gap-2 ml-1">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Загрузить свой файл (docx):</span>
              <input
                type="file"
                accept=".docx"
                onChange={handleBackendDocxUpload}
                className="block text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>
          )}
        </div>
      )}
      {isOwner && !isFullWidth && localBook?.status !== 'finished' && ( // Скрываем тумблер для завершенных произведений
        <div className="flex flex-col gap-4 mb-4 ml-1">
          <div className="flex items-center gap-3">
            <Switch
              checked={localAutoChapterSplit}
              onChange={onAutoChapterSplitChange}
              className={theme === 'dark' ? 'bg-[#374151]' : 'bg-gray-300'}
              style={{ minWidth: 44 }}
            />
            <span className="text-sm font-medium" style={{ color: theme === 'dark' ? '#fff' : '#222' }}>
              Использовать автоматическое разделение текста на главы
            </span>
            <div className="auto-split-help-container" style={{ position: 'relative', display: 'inline-block' }}>
              <QuestionCircleOutlined
                style={{
                  color: theme === 'dark' ? '#9ca3af' : '#6b7280',
                  fontSize: '14px',
                  cursor: 'pointer',
                  marginLeft: '4px'
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  setShowAutoSplitHelp(!showAutoSplitHelp);
                }}
              />
              {showAutoSplitHelp && (
                <div
                  style={{
                    position: 'absolute',
                    top: '20px',
                    left: '-200px',
                    width: '400px',
                    background: theme === 'dark' ? '#23272f' : '#ffffff',
                    border: theme === 'dark' ? '1px solid #374151' : '1px solid #e5e7eb',
                    borderRadius: '8px',
                    padding: '12px',
                    boxShadow: theme === 'dark' ? '0 4px 12px rgba(0,0,0,0.3)' : '0 4px 12px rgba(0,0,0,0.15)',
                    zIndex: 1000,
                    fontSize: '12px',
                    lineHeight: '1.4',
                    color: theme === 'dark' ? '#9ca3af' : '#6b7280',
                    fontStyle: 'italic'
                  }}
                >
                  {safeChapters.length > 0 ? (
                    <>
                      Если уже есть главы, будьте внимательны с загружаемым документом. Если функция включена,
                      совпадающие по нумерации главы будут перезаписаны. В выключенном состоянии, будет создана
                      новая глава на основании всего текста загружаемого документа.
                    </>
                  ) : (
                    <>
                      Для вашего удобства используте функцию автоматического разделения текста на главы. Если не уверены, что текст в вашем документе соответствует правилам, можете отключить эту функцию или{' '}
                      <a 
                        href="/faq/authors/upload_docx" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        style={{ color: theme === 'dark' ? '#60A5FA' : '#2563eb' }}
                        className="hover:underline"
                      >
                        ознакомиться с инструкцией для загружаемого документа
                      </a>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Тумблер красной строки - всегда показываем для владельца */}
      {isOwner && !isFullWidth && (
        <div className="flex flex-col gap-4 mb-4 ml-1">
          <div className="flex items-center gap-3">
            <Switch
              checked={localAutoIndent}
              onChange={onSwitchChange}
              className={theme === 'dark' ? 'bg-[#374151]' : 'bg-gray-300'}
              style={{ minWidth: 44 }}
            />
            <span className="text-sm font-medium" style={{ color: theme === 'dark' ? '#fff' : '#222' }}>
              Автоматическая красная строка для абзацев
            </span>
          </div>
        </div>
      )}
      {editMode ? (
        <ChapterEditorV2
            key={editorKey}
            bookId={bookId}
            chapterId={editMode === 'new' || editMode === 'preface' ? null : editMode}
            chapterOrder={editMode === 'preface' ? 0 : (editMode === 'new' ? getNextChapterNumber() : safeChapters.find(ch => ch.id === editMode)?.order)}
            initialTitle={editMode === 'preface' ? 'Вступление' : (editMode === 'new' ? `Глава ${getNextChapterNumber()}:` : safeChapters.find(ch => ch.id === editMode)?.title)}
            initialContent={null}
            onSave={handleSave}
            onCancel={() => {
                // Если редактор в полноэкранном режиме, выходим из него
                if (isFullWidth && onToggleFullWidth) {
                  onToggleFullWidth();
                }
                setEditMode(null);
                setIsPreface(false);
            }}
            isPreface={isPreface}
            getNextImgIndex={getNextImgIndex}
            chapterLoading={chapterLoading}
            autoIndent={localAutoIndent}
            username={username}
            isFullWidth={isFullWidth}
            onToggleFullWidth={onToggleFullWidth}
            maxChapterLength={MAX_CHAPTER_LENGTH}
            showCharacterCounter={true}
        />
      ) : (
        <div className="mt-8" style={{ display: isFullWidth && editMode ? 'none' : 'block' }}>
            <div className="flex justify-between items-center mb-6">
                <Title level={4} style={{ color: theme === 'dark' ? '#fff' : '#222', margin: 0 }}>Список глав</Title>
                <div className="flex items-center gap-4">
                    <Tooltip
                      title={localBook?.status === 'finished' ? isFinishedTooltipText : ''}
                      classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
                    >
                      <Button
                        onClick={() => handleAddChapter(true)}
                        disabled={localBook?.status === 'finished' || safeChapters.some(ch => ch.order === 0)}
                        style={{
                          background: theme === 'dark' ? '#23272f' : '#f3f4f6',
                          color: theme === 'dark' ? '#fff' : '#2563eb',
                          border: `1.5px solid ${theme === 'dark' ? '#374151' : '#2563eb'}`,
                        }}
                        onMouseOver={e => {
                          e.currentTarget.style.background = theme === 'dark' ? '#374151' : '#2563eb';
                          e.currentTarget.style.color = '#fff';
                        }}
                        onMouseOut={e => {
                          e.currentTarget.style.background = theme === 'dark' ? '#23272f' : '#f3f4f6';
                          e.currentTarget.style.color = theme === 'dark' ? '#fff' : '#2563eb';
                        }}
                      >
                        + Добавить вступление
                      </Button>
                    </Tooltip>
                    <Tooltip
                      title={localBook?.status === 'finished' ? isFinishedTooltipText : ''}
                      classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
                    >
                      <Button
                        type="primary"
                        onClick={() => handleAddChapter(false)}
                        disabled={localBook?.status === 'finished'}
                        style={{
                          background: theme === 'dark' ? '#2563eb' : '#2563eb',
                          color: '#fff',
                          border: 'none',
                          boxShadow: theme === 'dark' ? '0 2px 8px #1e293b33' : '0 2px 8px #2563eb33',
                        }}
                        onMouseOver={e => e.currentTarget.style.background = theme === 'dark' ? '#1d4ed8' : '#1d4ed8'}
                        onMouseOut={e => e.currentTarget.style.background = theme === 'dark' ? '#2563eb' : '#2563eb'}
                      >
                        + Добавить главу
                      </Button>
                    </Tooltip>
                </div>
            </div>
            {/* Верхнее меню "Выбрать всё" - показывается если глав больше 1 */}
            {safeChapters.length > 1 && !editMode && (
              <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 8, marginBottom: 16 }}>
                <Button
                  type="default"
                  icon={selectedChapters.length === safeChapters.length ? <MinusOutlined /> : <CheckOutlined />}
                  onClick={() => {
                    if (selectedChapters.length === safeChapters.length) {
                      // Снять выделение со всех
                      setSelectedChapters([]);
                    } else {
                      // Выбрать все главы
                      setSelectedChapters(safeChapters.map(ch => ch.id));
                    }
                  }}
                  disabled={isProcessing}
                  style={{
                    background: theme === 'dark' ? '#23272f' : '#f9fafb',
                    border: `1.5px solid ${selectedChapters.length === safeChapters.length ? '#ef4444' : '#22c55e'}`,
                    color: selectedChapters.length === safeChapters.length ? '#ef4444' : '#22c55e',
                    fontWeight: 500,
                  }}
                >
                  {selectedChapters.length === safeChapters.length ? 'Снять выделение' : 'Выбрать всё'}
                </Button>
              </div>
            )}

            {/* Панель массовых операций ВВЕРХУ - под кнопкой "Выбрать всё" перед списком глав */}
            {selectedChapters.length > 1 && (
              <BatchOperationsPanel style={{ marginTop: 16, marginBottom: 16 }} />
            )}

            <div style={{ display: 'grid', gridTemplateColumns: '56px 1fr', gap: 0 }}>
            {safeChapters.sort((a, b) => (a.order || 1) - (b.order || 1)).map((chapter, idx) => (
                <React.Fragment key={chapter.id}>
                    <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: 56,
                        minWidth: 36,
                        background: theme === 'dark' ? '#181c23' : '#e5e7eb',
                        borderRadius: '12px 0 0 12px',
                        marginBottom: 8,
                        border: theme === 'dark' ? '1.5px solid #23272f' : '1.5px solid #e5e7eb',
                        position: 'relative',
                        padding: 0,
                    }}>
                      {chapter.order > 0 && chapter.order !== 99999 ? (
                        <>
                          <span
                            style={{
                              minWidth: 20,
                              textAlign: 'center',
                              fontWeight: 700,
                              fontSize: 20,
                              color: theme === 'dark' ? '#f59e42' : '#b45309',
                              cursor: 'pointer',
                              userSelect: 'none',
                              display: 'inline-block',
                            }}
                            onClick={() => handleOrderEdit(chapter)}
                          >
                            {editOrderId === chapter.id ? (
                              <input
                                type="text"
                                value={editOrderValue}
                                onChange={handleOrderInputChange}
                                onBlur={() => handleOrderInputBlur(chapter)}
                                onKeyDown={e => { if (e.key === 'Enter') handleOrderInputBlur(chapter); }}
                                style={{
                                  width: 32,
                                  textAlign: 'center',
                                  fontWeight: 700,
                                  fontSize: 18,
                                  borderRadius: 4,
                                  border: '1px solid #d1d5db',
                                  background: theme === 'dark' ? '#23272f' : '#fff',
                                  color: theme === 'dark' ? '#fff' : '#222',
                                  MozAppearance: 'textfield',
                                  WebkitAppearance: 'none',
                                  appearance: 'textfield',
                                }}
                                min={1}
                                max={mainChaptersCount}
                                autoFocus
                                inputMode="numeric"
                                pattern="[0-9]*"
                              />
                            ) : (
                              chapter.order
                            )}
                          </span>
                          {editOrderId !== chapter.id && (
                            <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginTop: 0, paddingTop: 0, paddingBottom: 0 }}>
                              <Button
                                icon={<span style={{ color: chapter.order === mainChaptersCount ? '#d1d5db' : (theme === 'dark' ? '#f59e42' : '#b45309'), fontSize: 14, display: 'inline-block', lineHeight: 1 }}>▼</span>}
                                size="small"
                                style={{ background: 'transparent', border: 'none', margin: 0, padding: 0, minWidth: 0, height: 16, lineHeight: 1 }}
                                disabled={chapter.order === mainChaptersCount}
                                onClick={() => handleMoveDown(chapter)}
                              />
                              <Button
                                icon={<span style={{ color: chapter.order === 1 ? '#d1d5db' : (theme === 'dark' ? '#f59e42' : '#b45309'), fontSize: 14, display: 'inline-block', lineHeight: 1 }}>▲</span>}
                                size="small"
                                style={{ background: 'transparent', border: 'none', margin: 0, padding: 0, minWidth: 0, height: 16, lineHeight: 1 }}
                                disabled={chapter.order === 1}
                                onClick={() => handleMoveUp(chapter)}
                              />
                            </div>
                          )}
                        </>
                      ) : (
                        <span style={{
                          minWidth: 20,
                          textAlign: 'center',
                          fontWeight: 700,
                          fontSize: 20,
                          color: theme === 'dark' ? '#f59e42' : '#b45309',
                          userSelect: 'none',
                          display: 'inline-block',
                        }}>{chapter.order}</span>
                      )}
                    </div>
                    <div style={{
                        background: theme === 'dark' ? '#23272f' : '#f9fafb',
                        borderRadius: '0 12px 12px 0',
                        marginBottom: 8,
                        color: theme === 'dark' ? '#fff' : '#222',
                        display: 'flex',
                        alignItems: 'center',
                        minHeight: 56,
                        boxShadow: theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22',
                    }}>
                        <div style={{ paddingLeft: 16, flex: 1, textAlign: 'left' }}>
                          <span style={{ color: theme === 'dark' ? '#fff' : '#000' }}>
                            {chapter.title || (chapter.order === 0 ? 'Предисловие' : `Глава ${chapter.order}`)}
                          </span>
                        </div>
                        <div style={{ display: 'flex', gap: 8, marginRight: 12 }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            height: 40,
                            minWidth: 70,
                            textAlign: 'right',
                            justifyContent: 'center'
                          }}>
                            <Tooltip
                              title={isChapterOverLimit(chapter)
                                ? `Глава превышает лимит в ${MAX_CHAPTER_LENGTH.toLocaleString()} символов. Рекомендуется разделение.`
                                : `Лимит главы: ${MAX_CHAPTER_LENGTH.toLocaleString()} символов`
                              }
                              classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
                            >
                              <span style={{
                                color: isChapterOverLimit(chapter) ? '#ef4444' : (theme === 'dark' ? '#a1a1aa' : '#888'),
                                fontSize: 13,
                                fontWeight: isChapterOverLimit(chapter) ? 600 : 400,
                                cursor: 'help'
                              }}>
                                {getTextStats(chapter.content).formatted} зн.
                              </span>
                            </Tooltip>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', height: 40, gap: 8 }}>
                            <Tooltip title="Редактировать" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                              <Button 
                                icon={<EditOutlined />} 
                                onClick={() => handleEdit(chapter)} 
                                disabled={isProcessing}
                                className={theme === 'dark' ? 'dark-action-btn' : ''} 
                              />
                            </Tooltip>
                            <Tooltip title={chapter.is_published ? 'Снять главу с публикации' : 'Опубликовать главу'} classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                              <Button
                                icon={
                                  chapter.is_published
                                    ? <EyeOutlined style={{ color: '#22c55e', fontSize: 20 }} />
                                    : <EyeInvisibleOutlined style={{ color: '#f59e42', fontSize: 20 }} />
                                }
                                className={theme === 'dark' ? 'dark-action-btn' : ''}
                                disabled={isProcessing}
                                onClick={() => handleTogglePublishWithCheck(chapter)}
                              />
                            </Tooltip>
                            <Tooltip title="Запланировать публикацию" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                              <Button
                                icon={<HourglassOutlined style={{ color: '#60A5FA', fontSize: 20 }} />}
                                className={theme === 'dark' ? 'dark-action-btn' : ''}
                                style={{ display: chapter.is_published ? 'none' : undefined }}
                                onClick={() => openScheduleModal([chapter.id])}
                                disabled={isProcessing || chapter.is_published}
                              />
                            </Tooltip>
                            <Tooltip title="Удалить главу" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                              <Button
                                icon={<DeleteOutlined style={{ color: '#ef4444', fontSize: 20 }} />}
                                danger
                                className={theme === 'dark' ? 'dark-action-btn' : ''}
                                disabled={isProcessing}
                                onClick={() => handleDeleteAndNormalize(chapter)}
                              />
                            </Tooltip>
                            <AntdTooltip
                              title={isProcessing ? 'Операция выполняется...' : (isContiguous ? '' : 'Можно выбирать только смежные главы')}
                              color={isProcessing ? '#f59e42' : (isContiguous ? undefined : '#ef4444')}
                            >
                              <Checkbox
                                checked={selectedChapters.includes(chapter.id)}
                                onChange={() => handleSelectChapter(chapter)}
                                disabled={isProcessing}
                                style={{
                                  accentColor: selectedChapters.includes(chapter.id)
                                    ? (isContiguous ? '#22c55e' : '#ef4444')
                                    : undefined,
                                  borderColor: selectedChapters.includes(chapter.id)
                                    ? (isContiguous ? '#22c55e' : '#ef4444')
                                    : undefined,
                                  boxShadow: selectedChapters.includes(chapter.id) && !isContiguous ? '0 0 0 2px #ef444455' : undefined,
                                  cursor: isProcessing ? 'not-allowed' : 'pointer',
                                }}
                              />
                            </AntdTooltip>
                          </div>
                        </div>
                    </div>
                    {/* Вынесенный информирующий блок */}
                    {!chapter.is_published && chapter.scheduled_publish_at && (
                      <div style={{
                        margin: '2px 0 14px 56px',
                        fontSize: 13,
                        color: theme === 'dark' ? '#60A5FA' : '#2563eb',
                        gridColumn: '1 / span 2',
                        fontStyle: 'italic'
                      }}>
                        <HourglassOutlined style={{ color: theme === 'dark' ? '#60A5FA' : '#2563eb', fontSize: 15, marginRight: 6 }} />
                        {chapter.title || (chapter.order === 0 ? 'Вступление' : `Глава ${chapter.order}`)}, запланирована публикация на: {formatDateWithTimezone(chapter.scheduled_publish_at, timezone)}
                        <Button
                          type="text"
                          size="small"
                          icon={<CloseCircleFilled style={{ color: theme === 'dark' ? '#f87171' : '#ef4444', fontSize: 16 }} />}
                          disabled={isProcessing}
                          onClick={() => handleSmartCancelSchedule(chapter)}
                          style={{ marginLeft: 8, marginTop: -2, verticalAlign: 'middle', display: 'inline-block' }}
                          title="Снять отложенную публикацию"
                        />
                      </div>
                    )}
                </React.Fragment>
            ))}
            </div>
            {/* Нижнее меню "Выбрать всё" - показывается если глав больше 1 */}
            {safeChapters.length > 1 && !editMode && (
              <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 16 }}>
                <Button
                  type="default"
                  icon={selectedChapters.length === safeChapters.length ? <MinusOutlined /> : <CheckOutlined />}
                  onClick={() => {
                    if (selectedChapters.length === safeChapters.length) {
                      // Снять выделение со всех
                      setSelectedChapters([]);
                    } else {
                      // Выбрать все главы
                      setSelectedChapters(safeChapters.map(ch => ch.id));
                    }
                  }}
                  disabled={isProcessing}
                  style={{
                    background: theme === 'dark' ? '#23272f' : '#f9fafb',
                    border: `1.5px solid ${selectedChapters.length === safeChapters.length ? '#ef4444' : '#22c55e'}`,
                    color: selectedChapters.length === safeChapters.length ? '#ef4444' : '#22c55e',
                    fontWeight: 500,
                  }}
                >
                  {selectedChapters.length === safeChapters.length ? 'Снять выделение' : 'Выбрать всё'}
                </Button>
              </div>
            )}

            {/* Панель массовых операций ВНИЗУ - под кнопкой "Выбрать всё" внизу */}
            {selectedChapters.length > 1 && (
              <BatchOperationsPanel style={{ marginTop: 16 }} />
            )}



            {safeChapters.length > 0 && !editMode && (
              <div style={{ textAlign: 'left', marginTop: 20 }}>
                <Tooltip
                  title={localBook?.status === 'finished' ? isFinishedTooltipText : ''}
                  classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
                >
                  <Button
                    type="primary"
                    onClick={() => handleAddChapter(false)}
                    disabled={isProcessing || localBook?.status === 'finished'}
                    style={{
                      background: theme === 'dark' ? '#2563eb' : '#2563eb',
                      color: '#fff',
                      border: 'none',
                      boxShadow: theme === 'dark' ? '0 2px 8px #1e293b33' : '0 2px 8px #2563eb33',
                    }}
                    onMouseOver={e => e.currentTarget.style.background = theme === 'dark' ? '#1d4ed8' : '#1d4ed8'}
                    onMouseOut={e => e.currentTarget.style.background = theme === 'dark' ? '#2563eb' : '#2563eb'}
                  >
                    + Добавить главу
                  </Button>
                </Tooltip>
              </div>
            )}
        </div>
      )}
      {!editMode && (
        <div style={{ textAlign: 'left', marginTop: 20 }}>
          <Button
            onClick={onBack}
            disabled={isProcessing}
            style={{
              background: theme === 'dark' ? '#23272f' : '#fff',
              color: theme === 'dark' ? '#fff' : '#222',
              border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb'
            }}
          >
            Назад
          </Button>
        </div>
      )}
      {/* Модальное окно предупреждения при публикации */}
      <Modal
        open={showPublishWarning}
        onCancel={() => setShowPublishWarning(false)}
        footer={null}
        centered
        closable={false}
      >
        <div style={{ fontSize: 16, color: theme === 'dark' ? '#f87171' : '#ef4444', fontWeight: 600, marginBottom: 16, textAlign: 'center' }}>
          Вы публикуете главы, которые идут за неопубликованными. Будьте внимательны и соблюдайте целостность своего произведения.
        </div>
        <div style={{ marginBottom: 16, textAlign: 'center' }}>
          <Checkbox
            checked={publishConfirmChecked}
            onChange={e => setPublishConfirmChecked(e.target.checked)}
            style={{ marginRight: 8, color: theme === 'dark' ? '#fff' : '#222' }}
          >
            Подтверждаю, что вносимые изменения необходимы.
          </Checkbox>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between', gap: 16, marginTop: 24 }}>
          <Button onClick={() => setShowPublishWarning(false)} style={{ minWidth: 100, background: theme === 'dark' ? '#181c23' : '#f3f4f6', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', fontWeight: 500 }}>
            Отмена
          </Button>
          <div style={{ display: 'flex', gap: 16 }}>
            <Button
              type="primary"
              disabled={!publishConfirmChecked}
              style={{ minWidth: 100, background: publishConfirmChecked ? (theme === 'dark' ? '#22c55e' : '#22c55e') : (theme === 'dark' ? '#374151' : '#e5e7eb'), color: '#fff', border: 'none', fontWeight: 500 }}
              onClick={async () => {
                const chaptersToPublish = safeChapters.filter(ch => pendingPublishChapters.includes(ch.id) && !ch.is_published);

                // Проверяем лимиты перед массовой публикацией
                if (!checkPublishLimits(chaptersToPublish)) {
                  setShowPublishWarning(false);
                  return; // Блокируем публикацию при превышении лимитов
                }

                setShowPublishWarning(false);
                // Используем массовую публикацию вместо поштучной
                await handleBatchPublishConfirm(chaptersToPublish);
              }}
            >
              Опубликовать
            </Button>
          </div>
        </div>
      </Modal>
      {/* Модальное окно планирования публикации */}
      <Modal
        open={showScheduleModal}
        onCancel={() => {
          setShowScheduleModal(false);
          setSchedulingChapters([]);
          setSchedulingAsFinished(false);
          setScheduleDate(null);
          setMinScheduleTime(null);
        }}
        footer={null}
        centered
        closable={false}
        styles={{
          body: {
            overflow: 'visible' // Позволяем содержимому выходить за границы модального окна
          }
        }}
      >
        <div style={{ fontSize: 16, color: '#60A5FA', fontWeight: 600, marginBottom: 16, textAlign: 'center', display: 'flex', alignItems: 'center', gap: 8, justifyContent: 'center' }}>
          <HourglassOutlined style={{ color: '#60A5FA', fontSize: 20 }} />
          Запланировать публикацию
        </div>
        <div style={{ marginBottom: 16 }}>
          <ConfigProvider locale={ru_RU}>
            <DatePicker
              showTime
              value={scheduleDate}
              onChange={setScheduleDate}
              format="DD.MM.YYYY HH:mm"
              style={{
                width: '100%',
                // Убираем синий фокус
                '--ant-color-primary': 'transparent',
                '--ant-color-primary-hover': 'transparent'
              }}
              className="no-focus-outline"
              placeholder="Выберите дату и время"
              getPopupContainer={() => document.body} // Рендерим календарь в body, чтобы он не обрезался
              showNow={false}
              disabledDate={current => {
                if (!current || !minScheduleTime) return false;

                // Проверяем, есть ли в этой дате хотя бы одна доступная минута
                const currentDate = current.startOf('day');
                const minDate = minScheduleTime.startOf('day');

                // Если дата раньше сегодняшней - блокируем
                if (currentDate.isBefore(minDate)) return true;

                // Если дата позже сегодняшней - разрешаем
                if (currentDate.isAfter(minDate)) return false;

                // Если это сегодняшняя дата - проверяем, есть ли доступное время
                const isToday = currentDate.isSame(minDate);
                if (isToday) {
                  // Проверяем, есть ли доступные часы/минуты в этом дне
                  const currentHour = minScheduleTime.hour();
                  const currentMinute = minScheduleTime.minute();

                  // Если текущий час меньше 23, то есть доступное время
                  if (currentHour < 23) return false;

                  // Если текущий час 23, проверяем минуты
                  if (currentHour === 23 && currentMinute < 59) return false;

                  // Если 23:59 - блокируем сегодняшний день
                  return true;
                }

                return false;
              }}
              disabledTime={date => {
                if (!minScheduleTime || !date || date.isAfter(minScheduleTime, 'day')) return {};
                const isToday = date.isSame(minScheduleTime, 'day');
                return isToday ? {
                  disabledHours: () => Array.from({length: 24}, (_, i) => i).filter(h => h < minScheduleTime.hour()),
                  disabledMinutes: h => h === minScheduleTime.hour() ? Array.from({length: 60}, (_, i) => i).filter(m => m < minScheduleTime.minute()) : []
                } : {};
              }}
            />
          </ConfigProvider>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between', gap: 16, marginTop: 24 }}>
          <Button onClick={() => {
            setShowScheduleModal(false);
            setSchedulingChapters([]);
            setSchedulingAsFinished(false);
            setScheduleDate(null);
            setMinScheduleTime(null);
          }} style={{ minWidth: 100, background: theme === 'dark' ? '#181c23' : '#f3f4f6', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', fontWeight: 500 }}>
            Отмена
          </Button>
          <div style={{ display: 'flex', gap: 16 }}>
            <Button
              type="primary"
              disabled={!scheduleDate}
              loading={schedulingLoading}
              style={{ minWidth: 100, background: '#60A5FA', color: '#fff', border: 'none', fontWeight: 500 }}
              onClick={handleSchedulePublish}
            >
              Запланировать
            </Button>
          </div>
        </div>
      </Modal>
      {/* Модальное окно подтверждения удаления глав */}
      <Modal
        open={showBatchDeleteConfirm}
        onCancel={() => setShowBatchDeleteConfirm(false)}
        footer={null}
        centered
        closable={false}
      >
        <div style={{ fontSize: 16, color: theme === 'dark' ? '#f87171' : '#ef4444', fontWeight: 600, marginBottom: 20, whiteSpace: 'pre-line', textAlign: 'center' }}>
          Удаление глав в середине книги приведет к изменению порядка
          и названий последующих глав. Продолжить?
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between', gap: 16, marginTop: 24 }}>
          <Button
            onClick={() => setShowBatchDeleteConfirm(false)}
            style={{ minWidth: 100, background: theme === 'dark' ? '#181c23' : '#f3f4f6', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', fontWeight: 500 }}
          >
            Отмена
          </Button>
          <div style={{ display: 'flex', gap: 16 }}>
            <Button
              danger
              type="primary"
              loading={batchDeleteLoading}
              disabled={batchDeleteLoading}
              style={{ minWidth: 100, fontWeight: 500 }}
              onClick={handleBatchDeleteConfirmed}
            >
              {batchDeleteLoading ? 'Удаление...' : 'Удалить'}
            </Button>
          </div>
        </div>
      </Modal>
      
      {/* Модальное окно выбора статуса при публикации последней главы */}
      <Modal
        open={showPublishLastChapterModal}
        onCancel={() => {
          setShowPublishLastChapterModal(false);
          setPublishAction(null);
          setPublishAsFinished(false);
          setChapterToPublish(null);
        }}
        footer={null}
        centered
        closable={false}
        className={theme === 'dark' ? 'dark-modal' : ''}
        styles={{
          content: {
            background: theme === 'dark' ? '#23272f' : '#ffffff',
          },
        }}
      >
        <div style={{ fontSize: 16, color: '#60A5FA', fontWeight: 600, marginBottom: 20, textAlign: 'center' }}>
          {publishAction === 'scheduled' 
            ? 'Вы хотите запланировать публикацию последней главы и завершить произведение или публикуете частично?'
            : 'Вы хотите опубликовать последнюю главу и завершить произведение или публикуете частично?'
          }
        </div>
        
        <div style={{ marginBottom: 24 }}>
          <div style={{ fontSize: 14, fontWeight: 500, marginBottom: 12, color: theme === 'dark' ? '#fff' : '#222' }}>
            Укажите статус произведения после публикации последней главы:
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: 16 }}>
            <Switch
              checked={publishAsFinished}
              onChange={(checked) => {
                const finishCheck = canFinishBook();
                if (checked && !finishCheck.canFinish) {
                  Modal.error({
                    title: 'Невозможно завершить произведение',
                    content: (
                      <div>
                        <p>
                          При завершении произведения общий размер опубликованного контента составит <strong>{finishCheck.totalAfterFinish.toLocaleString()} символов</strong>,
                          что превышает допустимый лимит <strong>{MAX_NOVEL_LENGTH.toLocaleString()} символов</strong>.
                        </p>
                        <div style={{ marginTop: 12, fontSize: 13, color: '#6b7280' }}>
                          <div>• Опубликовано: {finishCheck.currentPublished.toLocaleString()} символов</div>
                          <div>• Запланировано: {finishCheck.scheduled.toLocaleString()} символов</div>
                          <div>• Неопубликованные главы: {finishCheck.unpublished.toLocaleString()} символов</div>
                        </div>
                        <p style={{ marginTop: 12 }}>
                          Рекомендуется сократить текст неопубликованных глав.
                        </p>
                      </div>
                    ),
                    okText: 'Понятно',
                  });
                  return;
                }
                setPublishAsFinished(checked);
              }}
              className={theme === 'dark' ? 'bg-[#374151]' : 'bg-gray-300'}
              style={{ minWidth: 44 }}
            />
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              {publishAsFinished ? (
                <>
                  <span style={{ color: '#22c55e', fontSize: 18 }}>✔</span>
                  <span style={{ color: '#22c55e', fontWeight: 600, fontSize: 14 }}>Завершено</span>
                </>
              ) : (
                <>
                  <EditOutlined style={{ color: '#60A5FA', fontSize: 16 }} />
                  <span style={{ color: '#60A5FA', fontWeight: 600, fontSize: 14 }}>В процессе публикации</span>
                </>
              )}
            </div>
          </div>
          
          {publishAsFinished && (
            <div style={{ 
              background: theme === 'dark' ? '#1f2937' : '#f3f4f6', 
              border: theme === 'dark' ? '1px solid #374151' : '1px solid #e5e7eb',
              borderRadius: 8,
              padding: 12,
              fontSize: 13,
              color: theme === 'dark' ? '#d1d5db' : '#6b7280',
              marginTop: 12
            }}>
              <strong>Внимание:</strong> При выборе "Завершено" все неопубликованные главы будут автоматически опубликованы, а произведение получит статус "Завершено".
            </div>
          )}
        </div>
        
        <div style={{ display: 'flex', justifyContent: 'space-between', gap: 16 }}>
          <Button 
            onClick={() => {
              setShowPublishLastChapterModal(false);
              setPublishAction(null);
              setPublishAsFinished(false);
              setChapterToPublish(null);
            }} 
            style={{ 
              minWidth: 100, 
              background: theme === 'dark' ? '#181c23' : '#f3f4f6', 
              color: theme === 'dark' ? '#fff' : '#222', 
              border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', 
              fontWeight: 500 
            }}
          >
            Отмена
          </Button>
          <Button
            type="primary"
            onClick={handleLastChapterPublishConfirm}
            style={{ 
              minWidth: 120, 
              background: '#60A5FA', 
              color: '#fff', 
              border: 'none', 
              fontWeight: 500 
            }}
          >
            {publishAction === 'scheduled' ? 'Запланировать публикацию' : 'Опубликовать'}
          </Button>
        </div>
      </Modal>
      
      {/* Модальное окно подтверждения удаления произведения */}
      <Modal
        open={isDeleteModalVisible}
        onCancel={handleDeleteCancel}
        footer={null}
        className={theme === 'dark' ? 'dark-modal' : ''}
        styles={{
          content: {
            background: theme === 'dark' ? '#23272f' : '#ffffff',
            borderRadius: '12px',
            padding: '24px',
          },
          mask: {
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        <div className="mb-6">
          <h3 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Подтверждение удаления
          </h3>
          <p className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>
            Вы точно уверены, что хотите удалить произведение: <strong className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>{localBook?.title}</strong>?
          </p>
          <p className="text-red-600 dark:text-red-400 mt-2">
            Операция необратима.
          </p>
        </div>
        <Form
          form={deleteForm}
          onFinish={handleDeleteConfirm}
          layout="vertical"
        >
          <Form.Item
            name="confirmation"
            rules={[
              { required: true, message: 'Пожалуйста, введите слово "удалить"' },
              {
                validator: (_, value) =>
                  value === 'удалить'
                    ? Promise.resolve()
                    : Promise.reject(new Error('Введите слово "удалить" для подтверждения')),
              },
            ]}
          >
            <Input
              placeholder="Введите слово 'удалить' для подтверждения"
              value={deleteConfirmation}
              onChange={(e) => setDeleteConfirmation(e.target.value)}
              className={theme === 'dark' ? 'bg-[#23272f] text-white border-[#4b5563] placeholder-[#9ca3af]' : 'bg-white text-gray-900 border-gray-300 placeholder-gray-400'}
              style={theme === 'dark' ? { borderColor: '#4b5563', color: '#fff', background: '#23272f' } : {}}
            />
          </Form.Item>
          <div className="flex justify-end space-x-4 mt-4">
            <Button 
              onClick={handleDeleteCancel}
              className={theme === 'dark' ? 'bg-[#23272f] text-white border-[#4b5563] hover:bg-[#374151]' : 'bg-white text-gray-900 border-gray-300 hover:bg-gray-100'}
              style={theme === 'dark' ? { borderColor: '#4b5563', color: '#fff', background: '#23272f' } : {}}
            >
              Отмена
            </Button>
            <Button
              type="primary"
              danger
              htmlType="submit"
              disabled={deleteConfirmation !== 'удалить'}
              className={theme === 'dark' ? 'bg-red-600 hover:bg-red-700 border-none text-white' : 'bg-red-600 hover:bg-red-700 border-none text-white'}
            >
              Подтверждаю удаление
            </Button>
          </div>
        </Form>
      </Modal>
      
      <style>
        {`
          .dark-action-btn {
            background: #23272f !important;
            color: #fff !important;
            border: 1.5px solid #374151 !important;
            transition: background 0.2s, color 0.2s;
          }
          .dark-action-btn:hover {
            background: #374151 !important;
            color: #60A5FA !important;
          }
          .dark-popconfirm .ant-popover-inner {
            background: #23272f !important;
            color: #fff !important;
          }
          .dark-popconfirm .ant-popover-message-title {
            color: #fff !important;
          }
          .dark-popconfirm .ant-popover-message {
            color: #fff !important;
          }
          .dark-popconfirm .ant-popover-buttons .ant-btn {
            background: #23272f !important;
            color: #fff !important;
            border: 1.5px solid #374151 !important;
          }
          .dark-popconfirm .ant-popover-buttons .ant-btn-dangerous {
            background: #ef4444 !important;
            color: #fff !important;
            border: none !important;
          }
          input[type='file']::-webkit-file-upload-button {
            background: ${theme === 'dark' ? '#23272f' : '#f3f4f6'};
            color: ${theme === 'dark' ? '#fff' : '#2563eb'};
            border: 1.5px solid ${theme === 'dark' ? '#374151' : '#2563eb'};
            border-radius: 6px;
            padding: 6px 16px;
            font-weight: 500;
            font-size: 15px;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
          }
          input[type='file']::-webkit-file-upload-button:hover {
            background: ${theme === 'dark' ? '#374151' : '#2563eb'};
            color: #fff;
          }
          input[type='file']::file-selector-button {
            background: ${theme === 'dark' ? '#23272f' : '#f3f4f6'};
            color: ${theme === 'dark' ? '#fff' : '#2563eb'};
            border: 1.5px solid ${theme === 'dark' ? '#374151' : '#2563eb'};
            border-radius: 6px;
            padding: 6px 16px;
            font-weight: 500;
            font-size: 15px;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
          }
          input[type='file']::file-selector-button:hover {
            background: ${theme === 'dark' ? '#374151' : '#2563eb'};
            color: #fff;
          }
          .dark-modal .ant-modal-content {
            background: #23272f !important;
            border-radius: 12px !important;
            border: none !important;
          }
          .dark-modal .ant-modal-close-x {
            color: #fff !important;
          }
          .ant-modal-close {
            top: 8px !important;
            right: 8px !important;
          }
          .ant-modal-close .ant-modal-close-x {
            font-size: 16px !important;
            line-height: 1 !important;
          }
          .ant-modal-content {
            border-radius: 12px !important;
            overflow: hidden;
          }
          .delete-book-button {
            background: ${theme === 'dark' ? '#374151' : '#f3f4f6'} !important;
            border-color: ${theme === 'dark' ? '#4b5563' : '#d1d5db'} !important;
            color: ${theme === 'dark' ? '#ef4444' : '#dc2626'} !important;
          }
          .delete-book-button:hover {
            background: ${theme === 'dark' ? '#ef4444' : '#fef2f2'} !important;
            border-color: ${theme === 'dark' ? '#ef4444' : '#fca5a5'} !important;
            color: ${theme === 'dark' ? '#fff' : '#dc2626'} !important;
          }
          .delete-book-button:focus {
            background: ${theme === 'dark' ? '#374151' : '#f3f4f6'} !important;
            border-color: ${theme === 'dark' ? '#4b5563' : '#d1d5db'} !important;
            color: ${theme === 'dark' ? '#ef4444' : '#dc2626'} !important;
          }
        `}
      </style>
    </div>
  );
};



export default NovelEditor;