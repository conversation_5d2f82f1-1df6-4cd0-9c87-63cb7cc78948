import { useState, useEffect, useContext, useRef } from 'react';
import Cropper from 'react-easy-crop';
import { AuthContext } from '../context/AuthContext';
import { useTheme } from '../theme/ThemeContext';

import { useNavigate } from 'react-router-dom';
import { getCSRFToken, csrfFetch } from '../utils/csrf';
import { message } from 'antd';
import { useAuth } from '../context/AuthContext';
import Tooltip from './Tooltip';
import { getCachedUserAvatar } from '../utils/avatarCache';
import { getRatingIcon, getRatingLevel, getReaderRatingIcon, getReaderRatingIconOffset, getAuthorRatingIcon, getAuthorRatingIconOffset, getFollowersIcon, getFollowersIconOffset, getFriendsIcon, getFriendsIconOffset, formatNumber } from '../utils/ratingIcons';

// Автоматический импорт статических пресетов и рамок через Vite
const presetModules = import.meta.glob('/header_presets/*.webp', { eager: true });
const frameModules = import.meta.glob('/header_frames/*.webp', { eager: true });
const headerPresetsList = Object.keys(presetModules).sort();
const headerFramesList = Object.keys(frameModules).sort();

function ProfileHeader({ 
  username, 
  userData, 
  setUserData, 
  refreshUserData, 
  imageVersion,
  stats: propStats,
  relation: propRelation,
  relationLoading: propRelationLoading,
  handleSubscribe: propHandleSubscribe,
  handleUnsubscribe: propHandleUnsubscribe,
  handleFriendRequest: propHandleFriendRequest,
  handleCancelFriendRequest: propHandleCancelFriendRequest,
  handleRemoveFriend: propHandleRemoveFriend,
  handleAuthRequired: propHandleAuthRequired
}) {
  const { user, refreshUser } = useAuth();
  const { theme } = useTheme ? useTheme() : { theme: 'light' };
  const navigate = useNavigate();
  const [editMotto, setEditMotto] = useState(false);
  const [mottoValue, setMottoValue] = useState('');
  const [mottoError, setMottoError] = useState('');
  const [mottoLoading, setMottoLoading] = useState(false);
  const mottoInputRef = useRef(null);
  const justAppliedUserHeaderRef = useRef(false);
  const [showHeaderSettings, setShowHeaderSettings] = useState(false);
  const [headerTab, setHeaderTab] = useState('preset'); // 'preset' | 'color' | 'upload' | 'frame'
  const [headerLoading, setHeaderLoading] = useState(false);
  const [headerPresetFilter, setHeaderPresetFilter] = useState('all');
  const [headerFrameFilter, setHeaderFrameFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(0);
  const [headerFrames, setHeaderFrames] = useState([]);
  const [headerPresets, setHeaderPresets] = useState([]);
  // Цвет/градиент
  const [color1, setColor1] = useState('#b4d8ff');
  const [color2, setColor2] = useState('#d1b4ff');
  const [gradient, setGradient] = useState(false);
  // Для загрузки изображения
  const [uploadImage, setUploadImage] = useState(null);
  const [uploadPreview, setUploadPreview] = useState(null);
  const [uploadError, setUploadError] = useState('');
  const [uploadLoading, setUploadLoading] = useState(false);
  // Для cropper'а
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [cropperOpen, setCropperOpen] = useState(false);
  const [cropperImage, setCropperImage] = useState(null);
  const [cropperAspectRatio, setCropperAspectRatio] = useState(1);
  const [cropperType, setCropperType] = useState(null);
  const [cropperLoading, setCropperLoading] = useState(false);
  const [cropperError, setCropperError] = useState('');
  const [relation, setRelation] = useState(propRelation || null);
  const [relationLoading, setRelationLoading] = useState(propRelationLoading || false);
  const [stats, setStats] = useState(propStats || { subscribers_count: 0, friends_count: 0 });
  const [lastUserHeader, setLastUserHeader] = useState(null);
  const backendUrl = 'http://localhost:8000';

  // Константы для пагинации
  const headersPerPage = 12;
  const framesPerPage = 12;

  // Фильтры для пресетов
  const headerPresetFilters = [
    { key: 'all', label: 'Все', keywords: [] },
    { key: 'light', label: 'Светлые', keywords: ['light'] },
    { key: 'dark', label: 'Темные', keywords: ['dark'] },
    { key: 'book', label: 'Книги', keywords: ['book'] },
    { key: 'nature', label: 'Природа', keywords: ['nature'] },
    { key: 'animals', label: 'Животные', keywords: ['animals'] },
	{ key: 'art', label: 'Арт', keywords: ['art'] },
    { key: 'cosmos', label: 'Космос', keywords: ['cosmos'] },
    { key: 'abstract', label: 'Абстрактные', keywords: ['abstract'] },
    { key: 'fantasy', label: 'Фэнтези', keywords: ['fantasy'] },
    { key: 'sport', label: 'Спорт', keywords: ['sport'] },
	{ key: 'lpon', label: 'ЛитПортал', keywords: ['lpon'] },
    { key: 'funny', label: 'Забавные', keywords: ['funny'] },
  ];

  // Фильтры для рамок
  const headerFrameFilters = [
    { key: 'all', label: 'Все', keywords: [] },
    { key: 'light', label: 'Светлые', keywords: ['light'] },
    { key: 'dark', label: 'Темные', keywords: ['dark'] },
    { key: 'color', label: 'Цветные', keywords: ['color'] },
    { key: 'flag', label: 'Флаги', keywords: ['flag'] },	
    { key: 'flowers', label: 'Цветы', keywords: ['flowers'] },
    { key: 'art', label: 'Арт', keywords: ['art'] },
    { key: 'abstract', label: 'Абстрактные', keywords: ['abstract'] },
    { key: 'fire', label: 'Огонь', keywords: ['fire'] },
    { key: 'smoke', label: 'Дым', keywords: ['smoke'] },
    { key: 'animals', label: 'Животные', keywords: ['animals'] },
	{ key: 'lpon', label: 'ЛитПортал', keywords: ['lpon'] },
	{ key: 'zodiac', label: 'Зодиак', keywords: ['zodiac'] },
    { key: 'funny', label: 'Забавные', keywords: ['funny'] }
  ];

  // Обновляем состояния при изменении пропсов
  useEffect(() => {
    if (propStats) {
      setStats(propStats);
    }
  }, [propStats]);

  useEffect(() => {
    if (propRelation !== undefined) {
      setRelation(propRelation);
    }
  }, [propRelation]);

  useEffect(() => {
    if (propRelationLoading !== undefined) {
      setRelationLoading(propRelationLoading);
    }
  }, [propRelationLoading]);

  // Загружаем статистику подписчиков и друзей только если не переданы пропсы
  useEffect(() => {
    if (!username || propStats) return;
    // console.log('Loading stats for username:', username); // Removed for performance
    fetch(`/api/auth/stats/${username}/`, { credentials: 'include' })
      .then(res => res.json())
      .then(data => {
        // console.log('Stats loaded:', data); // Removed for performance
        setStats(data);
      })
      .catch(err => {
        console.error('Error loading stats:', err);
        setStats({ subscribers_count: 0, friends_count: 0 });
      });
  }, [username, user, propStats]);

  // Загружаем статус отношений только если не переданы пропсы
  useEffect(() => {
    if (!user || !username || user.username === username || propRelation !== undefined) return;
    setRelationLoading(true);
    fetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' })
      .then(res => res.json())
      .then(data => setRelation(data))
      .finally(() => setRelationLoading(false));
  }, [user, username, propRelation]);

  // --- PATCH: инициализация lastUserHeader из localStorage при монтировании ---
  useEffect(() => {
    const stored = localStorage.getItem('lastUserHeader');
    if (stored) {
      setLastUserHeader(stored);
    }
  }, []);

  // После успешной загрузки пользовательского хедера сохраняем абсолютный путь (S3) в lastUserHeader и localStorage
  useEffect(() => {
    if (
      userData &&
      userData.header_bg_type === 'image' &&
      userData.header_bg_image_url &&
      userData.header_bg_image_url.startsWith('http')
    ) {
      setLastUserHeader(userData.header_bg_image_url);
      localStorage.setItem('lastUserHeader', userData.header_bg_image_url);
    }
  }, [userData && userData.header_bg_image_url]);

  // Фон хедера из API
  function getHeaderBgFromUserData() {
    // --- PATCH: логирование для диагностики ---
    // console.log('ProfileHeader userData:', userData); // Removed for performance
    if (!userData) {
      return { backgroundImage: 'linear-gradient(to right, #b4d8ff, #d1b4ff)' };
    }
    // solid + frame
    if (
      userData.header_bg_type === 'solid' &&
      userData.header_bg_color1 &&
      userData.header_frame
    ) {
      let frameUrl = userData.header_frame;
      if (frameUrl.startsWith('/header_frames/')) {
        // public static — используем как есть
      } else if (frameUrl.startsWith('http')) {
        // абсолютный путь (S3 или старый) — используем как есть
      } else if (frameUrl.includes('/static/header_frames/')) {
        frameUrl = frameUrl.replace('/static/header_frames/', '/header_frames/');
      } else {
        // fallback: возможно, старый относительный путь
      }
      return {
        backgroundImage: `url(${frameUrl}), linear-gradient(to right, ${userData.header_bg_color1}, ${userData.header_bg_color1})`,
        backgroundSize: 'cover, cover',
        backgroundPosition: 'center, center',
        backgroundRepeat: 'no-repeat, no-repeat',
      };
    }
    // gradient + frame
    if (
      userData.header_bg_type === 'gradient' &&
      userData.header_bg_color1 &&
      userData.header_bg_color2 &&
      userData.header_frame
    ) {
      let frameUrl = userData.header_frame;
      if (frameUrl.startsWith('/header_frames/')) {
        // public static — используем как есть
      } else if (frameUrl.startsWith('http')) {
        // абсолютный путь (S3 или старый) — используем как есть
      } else if (frameUrl.includes('/static/header_frames/')) {
        frameUrl = frameUrl.replace('/static/header_frames/', '/header_frames/');
      } else {
        // fallback
      }
      return {
        backgroundImage: `url(${frameUrl}), linear-gradient(to right, ${userData.header_bg_color1}, ${userData.header_bg_color2})`,
        backgroundSize: 'cover, cover',
        backgroundPosition: 'center, center',
        backgroundRepeat: 'no-repeat, no-repeat',
      };
    }
    // image + frame
    if (
      userData.header_bg_type === 'image' &&
      userData.header_frame
    ) {
      let url = userData.header_bg_image_url;
      // --- PATCH: если url абсолютный S3 для пресета, преобразуем к относительному ---
      const s3Prefix = 'https://storage.yandexcloud.net/lpo-test/header_presets/';
      if (url && url.startsWith(s3Prefix)) {
        url = url.replace(s3Prefix, '/header_presets/');
      }
      if (!url) {
        if (userData.header_bg_image && userData.header_bg_image.startsWith('/header_presets/')) {
          url = userData.header_bg_image;
        } else if (userData.header_bg_image && userData.header_bg_image.startsWith('http')) {
          url = userData.header_bg_image;
        }
      }
      if (url) url = `${url}?v=${imageVersion}`;
      let frameUrl = userData.header_frame;
      if (frameUrl.startsWith('/header_frames/')) {
        // public static — используем как есть
      } else if (frameUrl.startsWith('http')) {
        // абсолютный путь (S3 или старый) — используем как есть
      } else if (frameUrl.includes('/static/header_frames/')) {
        frameUrl = frameUrl.replace('/static/header_frames/', '/header_frames/');
      } else {
        // fallback
      }
      if (url) {
        return {
          backgroundImage: `url(${frameUrl}), url(${url})`,
          backgroundSize: 'cover, cover',
          backgroundPosition: 'center, center',
          backgroundRepeat: 'no-repeat, no-repeat',
        };
      } else {
        // fallback: только рамка
        return {
          backgroundImage: `url(${frameUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        };
      }
    }
    // solid без frame
    if (
      userData.header_bg_type === 'solid' &&
      userData.header_bg_color1 &&
      !userData.header_frame
    ) {
      return {
        backgroundImage: `linear-gradient(to right, ${userData.header_bg_color1}, ${userData.header_bg_color1})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      };
    }
    // Градиент
    if (userData.header_bg_type === 'gradient' && userData.header_bg_color1 && userData.header_bg_color2) {
      return {
        background: `linear-gradient(to right, ${userData.header_bg_color1}, ${userData.header_bg_color2})`,
      };
    }
    // Картинка (без frame)
    if (userData.header_bg_type === 'image') {
      let url = userData.header_bg_image_url;
      // --- PATCH: если url абсолютный S3 для пресета, преобразуем к относительному ---
      const s3Prefix = 'https://storage.yandexcloud.net/lpo-test/header_presets/';
      if (url && url.startsWith(s3Prefix)) {
        url = url.replace(s3Prefix, '/header_presets/');
      }
      if (!url) {
        if (userData.header_bg_image && userData.header_bg_image.startsWith('/header_presets/')) {
          url = userData.header_bg_image;
        } else if (userData.header_bg_image && userData.header_bg_image.startsWith('http')) {
          url = userData.header_bg_image;
        }
      }
      if (url) url = `${url}?v=${imageVersion}`;
      if (url) {
        return {
          backgroundImage: `url(${url})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        };
      }
    }
    // Фолбэк
    return { backgroundImage: 'linear-gradient(to right, #b4d8ff, #d1b4ff)' };
  }

  // Утилита для получения абсолютного публичного URL для пресета или пользовательского хедера
  function getAbsoluteHeaderImageUrl(path) {
    if (!path) return '';
    if (path.startsWith('http')) return path;
    // Для пользовательских хедеров и пресетов
    if (path.startsWith('/header_presets/')) {
      return `https://storage.yandexcloud.net/lpo-test${path}`;
    }
    // Для пользовательских хедеров (headers/...)
    if (path.startsWith('/headers/') || path.startsWith('headers/')) {
      return `https://storage.yandexcloud.net/lpo-test/media/public/${path.replace(/^\/+/, '')}`;
    }
    // fallback
    return path;
  }

  // Утилита для получения абсолютного публичного URL для рамки
  function getAbsoluteFrameUrl(path) {
    if (!path) return '';
    if (path.startsWith('http')) return path;
    if (path.startsWith('/header_frames/')) {
      return `https://storage.yandexcloud.net/lpo-test${path}`;
    }
    return path;
  }

  // Применить цвет/градиент
  async function applyColorBg() {
    if (!user) return;
    setHeaderLoading(true);
    try {
      const csrfToken = getCSRFToken();
      // Для цвета всегда убираем картинку
      const body = gradient
        ? {
            header_bg_type: 'gradient',
            header_bg_color1: color1,
            header_bg_color2: color2,
            header_frame: getAbsoluteFrameUrl(headerFrameLocal || ''),
            header_bg_image: '',
          }
        : {
            header_bg_type: 'solid',
            header_bg_color1: color1,
            header_bg_color2: '',
            header_frame: getAbsoluteFrameUrl(headerFrameLocal || ''),
            header_bg_image: '',
          };
      const res = await csrfFetch('/api/auth/profile/', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
        credentials: 'include',
      });
      if (!res.ok) throw new Error('Ошибка при сохранении');
      const data = await res.json();
      setUserData(prev => ({ ...prev, ...data }));
      if ('header_frame' in data) setHeaderFrameLocal(data.header_frame || '');
      setShowHeaderSettings(false);
    } catch (err) {
      alert('Ошибка при сохранении');
    } finally {
      setHeaderLoading(false);
    }
  }

  // Получить CSS для фона
  function getHeaderBgStyle() {
    if (!headerBg) return { background: 'linear-gradient(to right, #b4d8ff, #d1b4ff)' };
    if (headerBg.type === 'solid') return { background: headerBg.color1 };
    if (headerBg.type === 'gradient') return { background: `linear-gradient(to right, ${headerBg.color1}, ${headerBg.color2})` };
    if (headerBg.type === 'image') return { background: `url(${headerBg.url}) center/cover no-repeat` };
    return {};
  }

  // Обработка выбора файла
  function handleImageChange(e) {
    setUploadError('');
    const file = e.target.files[0];
    if (!file) return;
    const img = new window.Image();
    const reader = new FileReader();
    reader.onload = ev => {
      img.onload = () => {
        if (img.width < 1180 || img.height < 250) {
          setUploadError('Пожалуйста, загрузите изображение размером не менее 1180х250 px');
          setUploadImage(null);
          setUploadPreview(null);
        } else {
          setUploadImage(file);
          setUploadPreview(ev.target.result);
        }
      };
      img.onerror = () => {
        setUploadError('Не удалось прочитать изображение. Попробуйте другой файл.');
        setUploadImage(null);
        setUploadPreview(null);
      };
      img.src = ev.target.result;
    };
    reader.onerror = () => {
      setUploadError('Ошибка чтения файла. Попробуйте другой файл.');
      setUploadImage(null);
      setUploadPreview(null);
    };
    reader.readAsDataURL(file);
  }

  // При завершении cropper'а
  const onCropComplete = (croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  };

  // Получить обрезанный image blob
  async function getCroppedImg(imageSrc, cropPixels) {
    const image = new window.Image();
    image.src = imageSrc;
    await new Promise(resolve => { image.onload = resolve; });
    const canvas = document.createElement('canvas');
    canvas.width = 1180;
    canvas.height = 250;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(
      image,
      cropPixels.x, cropPixels.y, cropPixels.width, cropPixels.height,
      0, 0, 1180, 250
    );
    return new Promise(resolve => {
      canvas.toBlob(blob => {
        resolve(blob);
      }, 'image/webp', 0.9);
    });
  }

  // --- ДОБАВЛЯЮ ФУНКЦИЮ ДЛЯ СОЗДАНИЯ ЗАПИСИ В UserHeaderImage ---
  function toRelativeHeaderPath(path) {
    if (!path) return '';
    try { path = decodeURIComponent(path); } catch {}
    // Если уже относительный путь
    if (path.startsWith('headers/')) return path;
    // Если абсолютный URL S3
    const s3Prefix = 'https://storage.yandexcloud.net/lpo-test/media/public/';
    if (path.startsWith(s3Prefix)) {
      return path.replace(s3Prefix, '');
    }
    // Если начинается с /headers/
    if (path.startsWith('/headers/')) return path.slice(1);
    return path;
  }

  async function addUserHeaderImage(path) {
    try {
      const csrfToken = getCSRFToken();
      // Преобразуем к относительному пути для записи в БД
      const relPath = toRelativeHeaderPath(path);
      // Функция для повторных попыток
      const fetchWithRetry = async (retries = 3, delay = 1000) => {
        try {
          const res = await csrfFetch('/api/auth/user-header-images/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ path: relPath }),
            credentials: 'include',
          });
          if (!res.ok) {
            console.error('Ошибка при добавлении хедера:', res.status, res.statusText);
            if (retries > 0 && (res.status === 404 || res.status === 401)) {
              console.log(`Повторная попытка через ${delay}мс, осталось попыток: ${retries}`);
              await new Promise(resolve => setTimeout(resolve, delay));
              return fetchWithRetry(retries - 1, delay * 1.5);
            }
            throw new Error(`Ошибка ${res.status}: ${res.statusText}`);
          }
          return res.json();
        } catch (error) {
          if (retries > 0) {
            console.log(`Повторная попытка через ${delay}мс, осталось попыток: ${retries}`);
            await new Promise(resolve => setTimeout(resolve, delay));
            return fetchWithRetry(retries - 1, delay * 1.5);
          }
          throw error;
        }
      };
      const data = await fetchWithRetry();
      setUserHeaderImages(imgs => [data, ...imgs].slice(0, 4));
      return true;
    } catch (err) {
      console.error('Ошибка при добавлении хедера:', err);
      setUserHeaderImagesError('Ошибка при добавлении хедера');
      return false;
    }
  }

  // --- ДОРАБАТЫВАЮ handleUploadSave ---
  async function handleUploadSave() {
    if (!uploadImage || !croppedAreaPixels) return;
    setUploadLoading(true);
    setUploadError('');
    try {
      // Получаем обрезанный blob
      const croppedBlob = await getCroppedImg(uploadPreview, croppedAreaPixels);
      const formData = new FormData();
      formData.append('file', croppedBlob, 'header.webp');
      const uploadRes = await csrfFetch('/api/auth/upload-header/', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });
      if (!uploadRes.ok) throw new Error('Ошибка загрузки файла');
      const uploadData = await uploadRes.json();
      const path = uploadData.header_bg_image || uploadData.path || uploadData.url || uploadData.relative_path;
      console.log('[DEBUG] uploadData:', uploadData, 'path:', path);
      if (!path) throw new Error('Не получен путь к файлу');
      console.log('[DEBUG] addUserHeaderImage called with path:', path);
      // --- ВАЖНО: создаём запись в UserHeaderImage ---
      const ok = await addUserHeaderImage(path);
      if (!ok) throw new Error('Ошибка при добавлении миниатюры');
      setUploadImage(null);
      setUploadPreview(null);
      setShowHeaderSettings(true); // чтобы обновить список миниатюр
    } catch (err) {
      console.error('[DEBUG] handleUploadSave error:', err);
      setUploadError(err.message || 'Ошибка загрузки');
    } finally {
      setUploadLoading(false);
    }
  }

  // Применить пресет
  async function applyPresetBg(url) {
    if (!user) return;
    setHeaderLoading(true);
    try {
      const csrfToken = getCSRFToken();
      const absUrl = getAbsoluteHeaderImageUrl(url);
      const body = {
        header_bg_type: 'image',
        header_bg_image: absUrl,
        header_bg_color1: '',
        header_bg_color2: '',
        header_frame: getAbsoluteFrameUrl(headerFrameLocal || '')
      };
      const res = await csrfFetch('/api/auth/profile/', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
        credentials: 'include',
      });
      if (!res.ok) throw new Error('Ошибка при сохранении');
      const data = await res.json();
      setUserData(prev => ({ ...prev, ...data }));
      if ('header_frame' in data) setHeaderFrameLocal(data.header_frame || '');
      setShowHeaderSettings(false);
    } catch (err) {
      console.error('Error applying preset:', err);
      alert('Ошибка при сохранении фона');
    } finally {
      setHeaderLoading(false);
    }
  }

  // Применить рамку
  async function applyHeaderFrame(url) {
    if (!user) return;
    setHeaderLoading(true);
    try {
      const csrfToken = getCSRFToken();
      // Если url === null, убираем рамку
      let absFrameUrl = getAbsoluteFrameUrl(url);
      if (url === null) absFrameUrl = '';
      // Для header_bg_image всегда используем абсолютный URL
      let absImageUrl = userData.header_bg_image_url || userData.header_bg_image || '';
      absImageUrl = getAbsoluteHeaderImageUrl(absImageUrl);
      const body = {
        header_frame: absFrameUrl,
        header_bg_type: userData.header_bg_type,
        header_bg_color1: userData.header_bg_color1 || '',
        header_bg_color2: userData.header_bg_color2 || '',
        header_bg_image: absImageUrl,
      };
      const res = await csrfFetch('/api/auth/profile/', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
        credentials: 'include',
      });
      if (!res.ok) throw new Error('Ошибка при сохранении');
      const data = await res.json();
      setUserData(prev => ({ ...prev, ...data }));
      setHeaderFrameLocal(data.header_frame || ''); // Обновляем локальное состояние рамки из ответа
      await refreshUserData();
      setShowHeaderSettings(false);
    } catch (err) {
      console.error('Error applying header frame:', err);
      alert('Ошибка при применении рамки');
    } finally {
      setHeaderLoading(false);
    }
  }

  // Используем переданные обработчики или создаем локальные
  const handleSubscribe = propHandleSubscribe || (async () => {
    setRelationLoading(true);
    await csrfFetch('/api/auth/subscribe/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({ username })
    });
    // Обновить статус и статистику
    await Promise.all([
      csrfFetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setRelation),
      csrfFetch(`/api/auth/stats/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setStats)
    ]);
    setRelationLoading(false);
  });

  const handleUnsubscribe = propHandleUnsubscribe || (async () => {
    setRelationLoading(true);
    await csrfFetch('/api/auth/subscribe/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({ username })
    });
    // Обновить статус и статистику
    await Promise.all([
      csrfFetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setRelation),
      csrfFetch(`/api/auth/stats/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setStats)
    ]);
    setRelationLoading(false);
  });

  const handleFriendRequest = propHandleFriendRequest || (async () => {
    setRelationLoading(true);
    try {
      const res = await csrfFetch('/api/auth/friend-request/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ username })
      });
      if (!res.ok) {
        const data = await res.json();
        if (data.error && data.error.includes('существует')) {
          await csrfFetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' })
            .then(res => res.json())
            .then(setRelation);
          alert('Заявка уже отправлена!');
        }
        throw new Error(data.error || 'Ошибка');
      }
      await csrfFetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setRelation);
    } finally {
      setRelationLoading(false);
    }
  });

  const handleCancelFriendRequest = propHandleCancelFriendRequest || (async () => {
    setRelationLoading(true);
    try {
      const response = await csrfFetch('/api/auth/friend-request/', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ username })
      });
      if (!response.ok) throw new Error('Failed to cancel friend request');
      const relationResponse = await csrfFetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' });
      if (!relationResponse.ok) throw new Error('Failed to update relation status');
      const relationData = await relationResponse.json();
      setRelation(relationData);
    } catch (error) {
      console.error('Error canceling friend request:', error);
    } finally {
      setRelationLoading(false);
    }
  });

  const handleRemoveFriend = propHandleRemoveFriend || (async () => {
    setRelationLoading(true);
    await csrfFetch('/api/auth/remove-friend/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({ username })
    });
    await Promise.all([
      csrfFetch(`/api/auth/relation-status/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setRelation),
      csrfFetch(`/api/auth/stats/${username}/`, { credentials: 'include' }).then(res => res.json()).then(setStats)
    ]);
    setRelationLoading(false);
  });

  const handleAuthRequired = propHandleAuthRequired || (() => {
    navigate('/register');
  });

  // Добавить ниже хуков состояния:
  useEffect(() => {
    if (uploadPreview && uploadImage) {
      setCropperOpen(true);
      setCropperImage(uploadPreview);
      setCropperType('header');
      setCropperAspectRatio(4.72);
    }
  }, [uploadPreview, uploadImage]);

  // Для предпросмотра пользовательского хедера формируем путь по шаблону
  const userHeaderPath = `/media/users/${username[0]}/${username.slice(0,2)}/${username}/pics/header/header_${username}.jpg`;
  const [showUserHeader, setShowUserHeader] = useState(true);
  const [userHeaderLoading, setUserHeaderLoading] = useState(false);
  const [userHeaderError, setUserHeaderError] = useState('');

  // Функция для загрузки изображения по url и преобразования в base64
  async function fetchImageAsDataURL(url) {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        console.error('Ошибка загрузки изображения:', response.status, response.statusText);
        throw new Error('Ошибка загрузки изображения');
      }
      const blob = await response.blob();
      return await new Promise(resolve => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.readAsDataURL(blob);
      });
    } catch (err) {
      console.error('Ошибка в fetchImageAsDataURL:', err);
      throw err;
    }
  }

  // --- ДОБАВЛЯЕМ ФУНКЦИЮ ДЛЯ ПРИМЕНЕНИЯ РАНЕЕ ЗАГРУЖЕННОГО ХЕДЕРА ---
  async function handleApplyUserHeader(path) {
    console.log('[DEBUG] handleApplyUserHeader path:', path);
    setUserHeaderLoading(true);
    setUserHeaderError('');
    try {
      justAppliedUserHeaderRef.current = true;
      const csrfToken = getCSRFToken();
      const absUrl = getAbsoluteHeaderImageUrl(path);
      const body = {
        header_bg_type: 'image',
        header_bg_image: absUrl,
        header_frame: getAbsoluteFrameUrl(headerFrameLocal || ''),
        header_bg_color1: '',
        header_bg_color2: ''
      };
      const res = await csrfFetch('/api/auth/profile/', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
        credentials: 'include',
      });
      if (!res.ok) throw new Error('Ошибка при сохранении');
      const data = await res.json();
      setUserData(prev => ({ ...prev, ...data }));
      if ('header_frame' in data) setHeaderFrameLocal(data.header_frame || '');
      setShowHeaderSettings(false);
      message.success('Изображение успешно применено');
      await refreshUserData();
      setTimeout(() => { justAppliedUserHeaderRef.current = false; }, 1000);
    } catch (err) {
      setUserHeaderError('Ошибка при применении изображения');
      message.error('Ошибка при применении изображения');
      console.error('[DEBUG] PATCH error:', err);
    } finally {
      setUserHeaderLoading(false);
    }
  }

  useEffect(() => {
    fetch('/header_presets/index.json')
      .then(res => res.json())
      .then(setHeaderPresets);
    fetch('/header_frames/index.json')
      .then(res => res.json())
      .then(setHeaderFrames);
  }, []);

  // --- НОВАЯ ФУНКЦИЯ ДЛЯ ГРУППЫ ПО 500 ---
  function getGroupFolder(userId) {
    const group = Math.floor(userId / 500);
    const letterIndex = Math.floor(group / 1000);
    const letter = String.fromCharCode('a'.charCodeAt(0) + letterIndex);
    const number = (group % 1000).toString().padStart(3, '0');
    return `${letter}${number}`;
  }

  // --- ДОБАВЛЯЕМ СОСТОЯНИЯ ДЛЯ ПОЛЬЗОВАТЕЛЬСКИХ ХЕДЕРОВ ---
  const [userHeaderImages, setUserHeaderImages] = useState([]);
  const [userHeaderImagesLoading, setUserHeaderImagesLoading] = useState(false);
  const [userHeaderImagesError, setUserHeaderImagesError] = useState('');

  // --- ЗАГРУЗКА ПОЛЬЗОВАТЕЛЬСКИХ ХЕДЕРОВ ---
  useEffect(() => {
    if (!user) return;
    setUserHeaderImagesLoading(true);
    
    // Добавляем таймаут для повторных попыток
    const fetchWithRetry = (retries = 3, delay = 1000) => {
      fetch('/api/auth/user-header-images/', { 
        credentials: 'include',
        headers: {
          'X-CSRFToken': getCSRFToken()
        }
      })
        .then(res => {
          if (!res.ok) {
            console.error('Ошибка загрузки хедеров:', res.status, res.statusText);
            if (retries > 0 && (res.status === 404 || res.status === 401)) {
              // Повторяем запрос при ошибках аутентификации
              console.log(`Повторная попытка через ${delay}мс, осталось попыток: ${retries}`);
              setTimeout(() => fetchWithRetry(retries - 1, delay * 1.5), delay);
              return null;
            }
            throw new Error(`Ошибка ${res.status}: ${res.statusText}`);
          }
          return res.json();
        })
        .then(data => {
          if (data) {
            setUserHeaderImages(Array.isArray(data) ? data : []);
            setUserHeaderImagesLoading(false);
          }
        })
        .catch((err) => {
          console.error('Ошибка при загрузке хедеров:', err);
          setUserHeaderImages([]);
          setUserHeaderImagesLoading(false);
        });
    };
    
    fetchWithRetry();
  }, [showHeaderSettings, user]);

  // --- УДАЛЕНИЕ ПОЛЬЗОВАТЕЛЬСКОГО ХЕДЕРА ---
  async function handleDeleteUserHeader(id) {
    if (!window.confirm('Удалить этот хедер?')) return;
    setUserHeaderImagesLoading(true);
    const fetchWithRetry = async (retries = 3, delay = 1000) => {
      try {
        const csrfToken = getCSRFToken();
        const res = await csrfFetch(`/api/auth/user-header-images/${id}/`, {
          method: 'DELETE',
          headers: { 'X-CSRFToken': csrfToken },
          credentials: 'include',
        });
        if (!res.ok) {
          if (retries > 0 && (res.status === 404 || res.status === 401)) {
            await new Promise(resolve => setTimeout(resolve, delay));
            return fetchWithRetry(retries - 1, delay * 1.5);
          }
          throw new Error(`Ошибка ${res.status}: ${res.statusText}`);
        }
        return true;
      } catch (error) {
        if (retries > 0) {
          await new Promise(resolve => setTimeout(resolve, delay));
          return fetchWithRetry(retries - 1, delay * 1.5);
        }
        throw error;
      }
    };
    try {
      await fetchWithRetry();
      // После удаления обновляем список с сервера
      await fetchUserHeaderImages();
      message.success('Изображение успешно удалено');
      // Если удалённый был активным — сбрасываем фон через PATCH
      const activePath = userData.header_bg_image || userData.header_bg_image_url;
      const deletedImg = userHeaderImages.find(img => img.id === id);
      if (deletedImg && (deletedImg.path === activePath)) {
        await resetProfileHeaderBg();
      } else {
        await refreshUserData();
      }
    } catch (err) {
      setUserHeaderImagesError('Ошибка удаления');
      message.error('Ошибка при удалении изображения');
    } finally {
      setUserHeaderImagesLoading(false);
    }
  }

  // --- ПРИМЕНИТЬ ПОЛЬЗОВАТЕЛЬСКИЙ ХЕДЕР ---
  async function handleApplyUserHeader(path) {
    console.log('[DEBUG] handleApplyUserHeader path:', path);
    setUserHeaderLoading(true);
    setUserHeaderError('');
    try {
      justAppliedUserHeaderRef.current = true;
      const csrfToken = getCSRFToken();
      const res = await csrfFetch('/api/auth/profile/', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          header_bg_type: 'image',
          header_bg_image: path,
          header_frame: headerFrameLocal || '',
          header_bg_color1: '',
          header_bg_color2: ''
        }),
        credentials: 'include',
      });
      if (!res.ok) throw new Error('Ошибка при сохранении');
      const data = await res.json();
      setUserData(prev => ({ ...prev, ...data }));
      if ('header_frame' in data) setHeaderFrameLocal(data.header_frame || '');
      setShowHeaderSettings(false);
      message.success('Изображение успешно применено');
      await refreshUserData();
      setTimeout(() => { justAppliedUserHeaderRef.current = false; }, 1000);
    } catch (err) {
      setUserHeaderError('Ошибка при применении изображения');
      message.error('Ошибка при применении изображения');
      console.error('[DEBUG] PATCH error:', err);
    } finally {
      setUserHeaderLoading(false);
    }
  }

  // --- БЛОКИРУЮ ЗАГРУЗКУ ЕСЛИ УЖЕ 4 ---
  const canUploadMore = userHeaderImages.length < 4;

  // --- УТИЛИТА ДЛЯ ПОЛУЧЕНИЯ ПОЛНОГО URL ХЕДЕРА ---
  function getUserHeaderUrl(path) {
    if (!path) return '';
    if (path.startsWith('http')) return path;
    // S3 public bucket
    return `https://storage.yandexcloud.net/lpo-test/media/public/${path.replace(/^\/+/, '')}`;
  }

  // --- ФУНКЦИЯ ДЛЯ СБРОСА ФОНА ПРОФИЛЯ НА ДЕФОЛТНЫЙ ---
  async function resetProfileHeaderBg() {
    try {
      await csrfFetch('/api/auth/profile/', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          header_bg_type: 'solid',
          header_bg_image: '',
          header_bg_color1: '#b4d8ff',
          header_bg_color2: '',
          header_frame: ''
        }),
        credentials: 'include',
      });
      setUserData(prev => ({ ...prev, header_bg_type: 'solid', header_bg_image: '', header_bg_image_url: '', header_bg_color1: '#b4d8ff', header_bg_color2: '', header_frame: '' }));
      message.info('Фон профиля сброшен на стандартный.');
      await refreshUserData();
    } catch (err) {
      message.error('Ошибка при сбросе фона профиля');
    }
  }

  // --- ДОБАВЛЯЮ useEffect для проверки валидности активного хедера при загрузке профиля или userHeaderImages ---
  useEffect(() => {
    if (!userData || !userHeaderImages) return;
    const activePath = userData.header_bg_image || userData.header_bg_image_url;

    // Проверяем, что это именно пользовательский путь (например, начинается с /media/ или headers/)
    const isUserImagePath = activePath && (
      activePath.startsWith('/media/') ||
      activePath.startsWith('media/') ||
      activePath.startsWith('headers/') ||
      activePath.includes('/users/')
    );

    const isUserImage = userData.header_bg_type === 'image'
      && isUserImagePath
      && userHeaderImages.length > 0
      && !userHeaderImages.some(img => img.path === activePath);

    if (
      isUserImage &&
      !justAppliedUserHeaderRef.current &&
      !showHeaderSettings
    ) {
      resetProfileHeaderBg();
    }
  }, [userData, userHeaderImages, showHeaderSettings]);

  // --- ДОБАВЛЯЮ функцию для обновления миниатюр с сервера ---
  async function fetchUserHeaderImages() {
    setUserHeaderImagesLoading(true);
    try {
      const res = await csrfFetch('/api/auth/user-header-images/', {
        credentials: 'include',
        headers: { 'X-CSRFToken': getCSRFToken() },
      });
      if (!res.ok) throw new Error('Ошибка загрузки миниатюр');
      const data = await res.json();
      setUserHeaderImages(Array.isArray(data) ? data : []);
    } catch (err) {
      setUserHeaderImages([]);
      setUserHeaderImagesError('Ошибка загрузки миниатюр');
    } finally {
      setUserHeaderImagesLoading(false);
    }
  }

  const [headerFrameLocal, setHeaderFrameLocal] = useState(userData?.header_frame || '');

  // Синхронизируем локальное состояние рамки с userData при изменении профиля
  useEffect(() => {
    setHeaderFrameLocal(userData?.header_frame || '');
  }, [userData?.header_frame]);

  return (
    <div className="relative">
      <div 
        className="w-full h-[250px] rounded-lg shadow flex items-center" 
        style={getHeaderBgFromUserData()}
      >
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: 150, marginLeft: 20 }}>
          <div className="w-[150px] h-[150px] rounded-full border-2 border-white shadow-lg flex-shrink-0 overflow-hidden">
            <div className="w-full h-full rounded-full overflow-hidden border-2 border-white shadow-lg">
                                          {getCachedUserAvatar(userData, 'full', backendUrl, imageVersion) ? (
                              <img src={getCachedUserAvatar(userData, 'full', backendUrl, imageVersion)} alt="avatar" className="w-full h-full object-cover" />
              ) : (
                <span className="text-6xl text-gray-400"></span>
              )}
            </div>
          </div>
          {/* Индикатор активности по центру под аватаром */}
          {userData && (
            <div className="flex items-center gap-2 mt-2" style={{ justifyContent: 'center', width: '100%' }}>
              <div className={`w-3 h-3 rounded-full ${userData.is_online ? 'bg-green-500' : 'bg-red-500'}`}
                style={{
                  boxShadow: '0 2px 8px rgba(0,0,0,0.18)',
                  textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)'
                }}
              ></div>
              <span className="text-sm text-gray-700 dark:text-gray-200"
                style={{
                  color: '#fff',
                  textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)',
                  padding: '0 6px 2px 0',
                  display: 'inline-block'
                }}
              >{userData.status}</span>
            </div>
          )}
        </div>
        <div className="ml-[40px] flex flex-col justify-center h-full">
          <h1 className="text-2xl md:text-3xl font-bold text-white mb-1"
            style={{
              textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)',
              display: 'inline-block',
              padding: '0 6px 2px 0'
            }}
          >{userData.display_name}</h1>
          {(!editMotto && (userData.motto || (user && user.username === username))) && (
            <div className="italic text-white text-base mb-1 items-center group lg:block hidden"
              style={{
                maxWidth: 700,
                wordBreak: 'break-word',
                whiteSpace: 'pre-line',
                cursor: user && user.username === username ? 'pointer' : 'default',
                textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)',
                display: 'inline-block',
                padding: '0 6px 2px 0'
              }}
              onClick={() => { if (user && user.username === username) setEditMotto(true); }}
              title={user && user.username === username ? 'Редактировать девиз' : ''}
            >
              <span style={{ flex: 1, opacity: userData.motto ? 1 : 0.6, textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)', whiteSpace: 'pre-wrap', wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                {userData.motto
                  ? (userData.motto.length > 70
                      ? userData.motto.replace(/(.{70})/g, '$1\n')
                      : userData.motto)
                  : (user && user.username === username ? 'Здесь может быть ваш девиз!' : '')}
              </span>
              {user && user.username === username && (
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-white ml-2 opacity-60 group-hover:opacity-100 transition-opacity pointer-events-none drop-shadow" style={{ filter: 'drop-shadow(1px 1px 0 #181818)' }}>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-1.414.586H7v-3a2 2 0 01.586-1.414z" />
                </svg>
              )}
            </div>
          )}
          {editMotto && (
            <div style={{maxWidth: 700, minWidth: 300}} className="shadow">
              <textarea
                ref={mottoInputRef}
                className="italic text-white text-base mb-1 bg-transparent border-none shadow-none outline-none p-0 m-0 resize-none"
                maxLength={100}
                rows={2}
                value={mottoValue}
                onChange={e => {
                  let value = e.target.value;
                  if (value === mottoValue) return;
                  value = value.replace(/https?:\/\/\S+|www\.[^\s]+|\.[a-z]{2,6}(\/|\b)/gi, '');
                  if (value.length > 100) {
                    value = value.slice(0, 100);
                  }
                  let lines = value.split('\n');
                  let firstLine = lines[0] || '';
                  let secondLine = lines[1] || '';
                  if (firstLine.length > 50) {
                    let lastSpace = firstLine.slice(0, 50).lastIndexOf(' ');
                    if (lastSpace === -1) {
                      secondLine = firstLine.slice(50) + (secondLine ? ' ' + secondLine : '');
                      firstLine = firstLine.slice(0, 50);
                    } else {
                      secondLine = firstLine.slice(lastSpace + 1) + (secondLine ? ' ' + secondLine : '');
                      firstLine = firstLine.slice(0, lastSpace);
                    }
                  }
                  if (secondLine.length > 100 - firstLine.length) {
                    secondLine = secondLine.slice(0, 100 - firstLine.length);
                  }
                  let result = firstLine;
                  if (secondLine.length > 0) {
                    result += '\n' + secondLine;
                  }
                  setMottoValue(result);
                }}
                style={{wordBreak: 'break-word', fontStyle: 'italic', width: '52ch', textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)'}}
                autoFocus
                onKeyDown={e => {
                  if (e.key === 'Enter') {
                    const lines = mottoValue.split('\n');
                    if (lines.length >= 2) {
                      e.preventDefault();
                    }
                  }
                  if (e.key === 'Escape') {
                    setEditMotto(false);
                    setMottoValue(userData.motto || '');
                  }
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    mottoInputRef.current.blur();
                  }
                }}
                onBlur={async () => {
                  if (mottoValue !== userData.motto) {
                    setMottoLoading(true);
                    setMottoError('');
                    try {
                      const csrfToken = getCSRFToken();
                      const res = await csrfFetch('/api/auth/profile/', {
                        method: 'PATCH',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ motto: mottoValue }),
                        credentials: 'include',
                      });
                      if (!res.ok) throw new Error('Ошибка при сохранении');
                      setUserData(prev => ({ ...prev, motto: mottoValue }));
                      setEditMotto(false);
                    } catch (err) {
                      setMottoError('Ошибка при сохранении');
                      setEditMotto(false);
                    } finally {
                      setMottoLoading(false);
                    }
                  } else {
                    setEditMotto(false);
                  }
                }}
              />
              <div className="flex items-center gap-2 mt-1">
                <span className={mottoValue.length === 100 ? 'text-xs text-red-400' : 'text-xs text-gray-200'}>
                  {mottoValue.length}/100
                </span>
              </div>
            </div>
          )}
          <div style={{height: '2px', width: '100px', background: 'rgba(255,255,255,0.7)', margin: '12px 0', boxShadow: '0 2px 8px rgba(0,0,0,0.18)', borderRight: '1px solid #181818', borderBottom: '1px solid #181818'}} />
          {/* Общий рейтинг под чертой с девизом */}
          <div className="flex items-center gap-2 mb-1" style={{marginTop: '5px'}}>
            <Tooltip text={`Общий рейтинг (${getRatingLevel(userData.total_rating)} уровень)`} position="bottom">
              <img
                src={getRatingIcon(userData.total_rating)}
                alt="Rating icon"
                className="w-8 h-8 md:w-11 md:h-10 align-middle select-none drop-shadow"
                style={{
                  filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(0,0,0,0.18))',
                  padding: '0 6px 2px 0',
                  display: 'inline-block'
                }}
              />
            </Tooltip>
            <span className="text-3xl md:text-4xl font-extrabold text-white align-middle drop-shadow"
              style={{
                textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)',
                padding: '0 6px 2px 0',
                display: 'inline-block'
              }}
            >{formatNumber(userData.total_rating)}</span>

            {/* Иконка статистики - показываем только владельцу профиля */}
            {user && user.username === userData.username && (
              <Tooltip text="Статистика рейтингов" position="bottom">
                <button
                  onClick={() => navigate(`/lpu/${userData.username}/stat`)}
                  className="ml-1 md:ml-2 p-1.5 md:p-2 rounded-full transition-all duration-200 hover:scale-110 active:scale-95"
                  style={{
                    filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(0,0,0,0.18))',
                    background: 'transparent'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = 'rgba(128, 128, 128, 0.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = 'transparent';
                  }}
                >
                  <svg
                    className="w-4 h-4 md:w-5 md:h-5 text-white transition-transform duration-200"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M3 3v18h18"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </Tooltip>
            )}
          </div>
          {/* Рейтинги и кнопки: общий flex-контейнер */}
          <div className="relative flex flex-col md:flex-row md:items-center gap-4 mt-0 text-lg" style={{marginTop: '5px'}}>
            {/* Рейтинги */}
            <div className="flex gap-4 md:gap-8 justify-start text-base md:text-lg lg:text-xl xl:text-2xl">
              <div className="flex items-center gap-1 text-white"
                style={{textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)', padding: '0 6px 2px 0', display: 'inline-block'}}
              >
                <Tooltip text="Рейтинг читателя" position="bottom">
                  <img 
                    src={getReaderRatingIcon()}
                    alt="Reader rating icon"
                    className="w-6 h-6 md:w-7 md:h-7 align-middle select-none drop-shadow"
                    style={{
                      filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(0,0,0,0.18))',
                      // 🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ: измените значения для смещения иконки
                      // offsetX: смещение по горизонтали (+ вправо, - влево)
                      // offsetY: смещение по вертикали (+ вниз, - вверх)
                      ...getReaderRatingIconOffset(-2, +4), // ← ЗДЕСЬ МЕНЯЙТЕ ПИКСЕЛИ
                    }}
                  />
                </Tooltip>
                <span className="drop-shadow" style={{textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)'}}>{formatNumber(userData.reader_rating)}</span>
              </div>
              <div className="flex items-center gap-1 text-white"
                style={{textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)', padding: '0 6px 2px 0', display: 'inline-block'}}
              >
                <Tooltip text="Авторский рейтинг" position="bottom">
                  <img 
                    src={getAuthorRatingIcon()}
                    alt="Author rating icon"
                    className="w-6 h-6 md:w-7 md:h-7 align-middle select-none drop-shadow"
                    style={{
                      filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(0,0,0,0.18))',
                      // 🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ: измените значения для смещения иконки
                      // offsetX: смещение по горизонтали (+ вправо, - влево)
                      // offsetY: смещение по вертикали (+ вниз, - вверх)
                      ...getAuthorRatingIconOffset(-3, +4), // ← ЗДЕСЬ МЕНЯЙТЕ ПИКСЕЛИ
                    }}
                  />
                </Tooltip>
                <span className="drop-shadow" style={{textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)'}}>{formatNumber(userData.author_rating)}</span>
              </div>
              <div className="flex items-center gap-1 text-white"
                style={{textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)', padding: '0 6px 2px 0', display: 'inline-block'}}
              >
                <Tooltip text="Подписчики" position="bottom">
                  <img 
                    src={getFollowersIcon()}
                    alt="Followers icon"
                    className="w-6 h-6 md:w-7 md:h-7 align-middle select-none drop-shadow"
                    style={{
                      filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(0,0,0,0.18))',
                      // 🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ: измените значения для смещения иконки
                      // offsetX: смещение по горизонтали (+ вправо, - влево)
                      // offsetY: смещение по вертикали (+ вниз, - вверх)
                      ...getFollowersIconOffset(-4, +5), // ← ЗДЕСЬ МЕНЯЙТЕ ПИКСЕЛИ
                    }}
                  />
                </Tooltip>
                <span className="drop-shadow" style={{textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)'}}>{formatNumber(stats.subscribers_count)}</span>
              </div>
              <div className="flex items-center gap-1 text-white"
                style={{textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(0,0,0,0.18)', padding: '0 6px 2px 0', display: 'inline-block'}}
              >
                <Tooltip text="Друзья" position="bottom">
                  <img 
                    src={getFriendsIcon()}
                    alt="Friends icon"
                    className="w-6 h-6 md:w-7 md:h-7 align-middle select-none drop-shadow"
                    style={{
                      filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(255,0,0,0.25))',
                      // 🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ: измените значения для смещения иконки
                      // offsetX: смещение по горизонтали (+ вправо, - влево)
                      // offsetY: смещение по вертикали (+ вниз, - вверх)
                      ...getFriendsIconOffset(-4, +5), // ← ЗДЕСЬ МЕНЯЙТЕ ПИКСЕЛИ
                    }}
                  />
                </Tooltip>
                <span className="drop-shadow" style={{textShadow: '1px 1px 0 #181818, 0 2px 8px rgba(255,0,0,0.18)'}}>{formatNumber(stats.friends_count)}</span>
              </div>
            </div>
            {/* Кнопки подписки и дружбы */}
            {user?.username !== username && (
              <>
                {/* Десктоп: справа на одной линии с рейтингами */}
                <div className="hidden lg:flex flex-row ml-auto mr-[30px] gap-3 items-center z-10">
                  {/* Подписка */}
                  {user ? (
                    relation && relation.is_friend ? null : relation && relation.is_subscribed ? (
                      <div className="flex items-center gap-1">
                        <span className="text-xs text-white md:block hidden whitespace-nowrap" style={{textShadow: '0 0 2px rgba(0, 255, 145, 0.9)'}}>Вы подписаны</span>
                        <Tooltip text="Отписаться" position="bottom">
                          <button
                            className="ml-1 text-red-500 hover:text-red-700 text-lg cursor-pointer bg-transparent border-none p-0 shadow-none transition-all duration-200 hover:scale-110"
                            disabled={relationLoading}
                            onClick={handleUnsubscribe}
                            style={{ border: 'none', background: 'none' }}
                          >
                            ❌
                          </button>
                        </Tooltip>
                      </div>
                    ) : (
                      <Tooltip text="Подписаться" position="bottom">
                        <button
                          className="flex items-center gap-1 px-2 py-1 rounded bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-100 font-medium shadow text-sm transition-all duration-200 hover:scale-105"
                          disabled={relationLoading}
                          onClick={handleSubscribe}
                        >
                          <span className="text-purple-700 text-xl">👤</span>
                          <span className="text-green-600 text-xl">+</span>
                        </button>
                      </Tooltip>
                    )
                  ) : (
                    <Tooltip text="Подписаться" position="bottom">
                      <button
                        className="flex items-center gap-1 px-2 py-1 rounded bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-100 font-medium shadow text-sm transition-all duration-200 hover:scale-105"
                        onClick={() => handleAuthRequired('subscribe')}
                      >
                        <span className="text-purple-700 text-xl">👤</span>
                        <span className="text-green-600 text-xl">+</span>
                      </button>
                    </Tooltip>
                  )}
                  {/* Дружба */}
                  {user ? (
                    relation && relation.is_friend ? (
                      <div className="flex items-center gap-1">
                        <span className="text-xs text-white md:block hidden whitespace-nowrap" style={{textShadow: '0 0 2px rgba(255, 0, 0, 0.9)'}}>В друзьях</span>
                        <Tooltip text="Удалить из друзей" position="bottom">
                          <button
                            className="ml-1 text-red-500 hover:text-red-700 text-lg cursor-pointer bg-transparent border-none p-0 shadow-none transition-all duration-200 hover:scale-110"
                            disabled={relationLoading}
                            onClick={handleRemoveFriend}
                            style={{ border: 'none', background: 'none' }}
                          >
                            ❌
                          </button>
                        </Tooltip>
                      </div>
                    ) : relation && relation.outgoing_friend_request ? (
                      <div className="flex items-center gap-2">
                        <span className="text-gray-500 text-base">Заявка отправлена</span>
                        <Tooltip text="Отменить заявку" position="bottom">
                          <button
                            className="text-red-500 hover:text-red-700 text-lg cursor-pointer bg-transparent border-none p-0 shadow-none transition-all duration-200 hover:scale-110"
                            disabled={relationLoading}
                            onClick={handleCancelFriendRequest}
                            style={{ border: 'none', background: 'none' }}
                          >
                            ❌
                          </button>
                        </Tooltip>
                      </div>
                    ) : (
                      <Tooltip text="Запрос в друзья" position="bottom">
                        <button
                          className="flex items-center gap-1 px-2 py-1 rounded bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-100 font-medium shadow text-sm transition-all duration-200 hover:scale-105"
                          disabled={relationLoading}
                          onClick={handleFriendRequest}
                        >
                          <span className="text-pink-500 text-xl">❤️</span>
                          <span className="text-green-600 text-xl">+</span>
                        </button>
                      </Tooltip>
                    )
                  ) : (
                    <Tooltip text="Запрос в друзья" position="bottom">
                      <button
                        className="flex items-center gap-1 px-2 py-1 rounded bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-100 font-medium shadow text-sm transition-all duration-200 hover:scale-105"
                        onClick={() => handleAuthRequired('friend')}
                      >
                        <span className="text-pink-500 text-xl">❤️</span>
                        <span className="text-green-600 text-xl">+</span>
                      </button>
                    </Tooltip>
                  )}
                </div>
                {/* Мобильные: под рейтингами по центру, компактные */}
                <div className="flex lg:hidden justify-center gap-3 mt-0">
                  {/* Подписка */}
                  {user ? (
                    relation && relation.is_friend ? null : relation && relation.is_subscribed ? (
                      <div className="flex items-center gap-1">
                        <span className="text-green-500 text-lg">👤</span>
                        <span className="text-green-600 text-lg">+</span>
                        <Tooltip text="Отписаться" position="top">
                          <button
                            className="ml-1 text-red-500 hover:text-red-700 text-lg cursor-pointer bg-transparent border-none p-0 shadow-none transition-all duration-200 hover:scale-110"
                            disabled={relationLoading}
                            onClick={handleUnsubscribe}
                            style={{ border: 'none', background: 'none' }}
                          >
                            ❌
                          </button>
                        </Tooltip>
                      </div>
                    ) : (
                      <Tooltip text="Подписаться" position="top">
                        <button
                          className="flex items-center gap-1 px-1 py-1 rounded bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-100 font-medium shadow text-xs transition-all duration-200 hover:scale-105"
                          disabled={relationLoading}
                          onClick={handleSubscribe}
                        >
                          <span className="text-purple-700 text-lg">👤</span>
                          <span className="text-green-600 text-lg">+</span>
                        </button>
                      </Tooltip>
                    )
                  ) : (
                    <Tooltip text="Подписаться" position="top">
                      <button
                        className="flex items-center gap-1 px-1 py-1 rounded bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-100 font-medium shadow text-xs transition-all duration-200 hover:scale-105"
                        onClick={() => handleAuthRequired('subscribe')}
                      >
                        <span className="text-purple-700 text-lg">👤</span>
                        <span className="text-green-600 text-lg">+</span>
                      </button>
                    </Tooltip>
                  )}
                  {/* Дружба */}
                  {user ? (
                    relation && relation.is_friend ? (
                      <div className="flex items-center gap-1">
                        <span className="text-pink-500 text-lg">❤️</span>
                        <span className="text-green-600 text-lg">+</span>
                      </div>
                    ) : relation && relation.outgoing_friend_request ? (
                      <div className="flex items-center gap-2">
                        <span className="text-gray-500 text-xs">Заявка отправлена</span>
                        <Tooltip text="Отменить заявку" position="top">
                          <button
                            className="text-red-500 hover:text-red-700 text-lg cursor-pointer bg-transparent border-none p-0 shadow-none transition-all duration-200 hover:scale-110"
                            disabled={relationLoading}
                            onClick={handleCancelFriendRequest}
                            style={{ border: 'none', background: 'none' }}
                          >
                            ❌
                          </button>
                        </Tooltip>
                      </div>
                    ) : (
                      <Tooltip text="Запрос в друзья" position="top">
                        <button
                          className="flex items-center gap-1 px-1 py-1 rounded bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-100 font-medium shadow text-xs transition-all duration-200 hover:scale-105"
                          disabled={relationLoading}
                          onClick={handleFriendRequest}
                        >
                          <span className="text-pink-500 text-lg">❤️</span>
                          <span className="text-green-600 text-lg">+</span>
                        </button>
                      </Tooltip>
                    )
                  ) : (
                    <Tooltip text="Запрос в друзья" position="top">
                      <button
                        className="flex items-center gap-1 px-1 py-1 rounded bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-100 font-medium shadow text-xs transition-all duration-200 hover:scale-105"
                        onClick={() => handleAuthRequired('friend')}
                      >
                        <span className="text-pink-500 text-lg">❤️</span>
                        <span className="text-green-600 text-lg">+</span>
                      </button>
                    </Tooltip>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
        {/* Кнопка настроек хедера — только для владельца профиля */}
        {user && user.username === username && (
          <button
            className="absolute bottom-3 right-3 bg-white dark:bg-transparent bg-opacity-80 rounded-full p-2 shadow-lg hover:bg-opacity-100 transition z-20"
            title="Настроить фон хедера"
            onClick={() => setShowHeaderSettings(true)}
            style={{lineHeight: 0}}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-gray-700 dark:text-white" style={{ textShadow: '1px 1px 0 #181818' }}>
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0a1.724 1.724 0 002.573.982c.797-.46 1.8.237 1.623 1.118a1.724 1.724 0 001.516 2.01c.89.177 1.578 1.08 1.118 1.877a1.724 1.724 0 00.982 2.573c.921.3.921 1.603 0 1.902a1.724 1.724 0 00-.982 2.573c.46.797-.237 1.8-1.118 1.623a1.724 1.724 0 00-2.01 1.516c-.177.89-1.08 1.578-1.877 1.118a1.724 1.724 0 00-2.573.982c-.3.921-1.603.921-1.902 0a1.724 1.724 0 00-2.573-.982c-.797.46-1.8-.237-1.623-1.118a1.724 1.724 0 00-1.516-2.01c-.89-.177-1.578-1.08-1.118-1.877a1.724 1.724 0 00-.982-2.573c-.921-.3-.921-1.603 0-1.902a1.724 1.724 0 00.982-2.573c-.46-.797.237-1.8 1.118-1.623a1.724 1.724 0 002.01-1.516c.177-.89 1.08-1.578 1.877-1.118.797.46 1.8-.237 1.623-1.118z" />
              <circle cx="12" cy="12" r="3" />
            </svg>
          </button>
        )}
      </div>
      {/* Модальное окно настроек хедера */}
      {showHeaderSettings && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 min-w-[340px] max-w-[1000px] w-[95vw] relative">
            {/* Вкладки */}
            <div className="flex gap-2 mb-4 border-b dark:border-gray-700">
              {['preset', 'color', 'upload', 'frame'].map(tab => (
                <button
                  key={tab}
                  className={`px-4 py-2 rounded-t-lg transition-colors font-medium border-b-2 ${
                    headerTab === tab
                      ? theme === 'dark'
                        ? 'bg-gray-800 text-blue-400 border-blue-400'
                        : 'bg-white text-blue-600 border-blue-600'
                      : theme === 'dark'
                        ? 'bg-gray-900 text-gray-400 hover:text-gray-200 border-transparent'
                        : 'bg-gray-100 text-gray-600 hover:text-gray-800 border-transparent'
                  }`}
                  onClick={() => setHeaderTab(tab)}
                >
                  {tab === 'preset' && 'Пресеты'}
                  {tab === 'color' && 'Свои цвета'}
                  {tab === 'upload' && 'Загрузить'}
                  {tab === 'frame' && 'Рамка'}
                </button>
              ))}
            </div>

            {/* Кнопка закрытия */}
            <button
              className={`absolute top-2 right-2 text-2xl transition-colors shadow-lg z-10 ${
                theme === 'dark'
                  ? 'bg-gray-900 text-gray-300 hover:bg-gray-700 hover:text-white'
                  : 'bg-white text-gray-500 hover:bg-gray-200 hover:text-gray-800'
              } rounded-full w-10 h-10 flex items-center justify-center`}
              onClick={() => setShowHeaderSettings(false)}
              title="Закрыть"
              style={{lineHeight: 0, border: 'none'}}
            >
              ×
            </button>

            {/* Контент вкладок */}
            <div className="mt-4">
              {/* Вкладка пресетов */}
              {headerTab === 'preset' && (
                <div>
                  <div className="flex flex-wrap gap-2 mb-4 pb-2">
                    {headerPresetFilters.map(filter => (
                      <button
                        key={filter.key}
                        className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-colors ${
                          headerPresetFilter === filter.key
                            ? theme === 'dark'
                              ? 'bg-blue-600 text-white'
                              : 'bg-blue-500 text-white'
                            : theme === 'dark'
                              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                        onClick={() => {
                          setHeaderPresetFilter(filter.key);
                          setCurrentPage(0);
                        }}
                      >
                        {filter.label}
                      </button>
                    ))}
                  </div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                    {headerPresets
                      .filter(preset =>
                        headerPresetFilter === 'all' ||
                        headerPresetFilters.find(f => f.key === headerPresetFilter)?.keywords.some(k => preset.split('/').pop().toLowerCase().includes(k))
                      )
                      .slice(currentPage * headersPerPage, (currentPage + 1) * headersPerPage)
                      .map((preset, index) => (
                        <div
                          key={index}
                          className="relative aspect-[4.72/1] rounded-lg overflow-hidden cursor-pointer hover:opacity-90 transition-opacity"
                          onClick={() => applyPresetBg(preset)}
                        >
                          <img src={preset} alt={`Preset ${index + 1}`} className="w-full h-full object-cover" />
                        </div>
                      ))}
                  </div>
                  {/* Пагинация */}
                  {(() => {
                    const filtered = headerPresets.filter(preset => 
                      headerPresetFilter === 'all' || 
                      headerPresetFilters.find(f => f.key === headerPresetFilter)?.keywords.some(k => preset.toLowerCase().includes(k))
                    );
                    const totalPages = Math.ceil(filtered.length / headersPerPage);
                    if (totalPages <= 1) return null;
                    return (
                      <div className="flex justify-center gap-2 mt-4 items-center">
                        <button
                          className={`px-3 py-1 rounded transition-colors ${
                            theme === 'dark'
                              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600 disabled:opacity-50'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 disabled:opacity-50'
                          }`}
                          onClick={() => setCurrentPage(p => Math.max(0, p - 1))}
                          disabled={currentPage === 0}
                        >
                          ←
                        </button>
                        <span className={`mx-2 text-base font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}`}>{currentPage + 1} / {totalPages}</span>
                        <button
                          className={`px-3 py-1 rounded transition-colors ${
                            theme === 'dark'
                              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600 disabled:opacity-50'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 disabled:opacity-50'
                          }`}
                          onClick={() => setCurrentPage(p => p + 1)}
                          disabled={currentPage + 1 >= totalPages}
                        >
                          →
                        </button>
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* Вкладка цвет/градиент */}
              {headerTab === 'color' && (
                <div className="space-y-4">
                  <div className="flex items-center gap-4 mb-4">
                    <button
                      className={`px-4 py-2 rounded transition-colors font-medium ${
                        !gradient
                          ? theme === 'dark'
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'bg-blue-500 text-white hover:bg-blue-600'
                          : theme === 'dark'
                            ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                      onClick={() => setGradient(false)}
                    >
                      Цвет
                    </button>
                    <button
                      className={`px-4 py-2 rounded transition-colors font-medium ${
                        gradient
                          ? theme === 'dark'
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'bg-blue-500 text-white hover:bg-blue-600'
                          : theme === 'dark'
                            ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                      onClick={() => setGradient(true)}
                    >
                      Градиент
                    </button>
                  </div>
                  <div className="flex items-center gap-6 mb-4">
                    <div className="flex flex-col items-center">
                      <label className={`block text-sm mb-2 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}`}>Первый цвет</label>
                      <div className="w-14 h-14 rounded-full flex items-center justify-center shadow relative" style={{background: color1, border: '2px solid #ccc'}}>
                        <input
                          type="color"
                          value={color1}
                          onChange={e => setColor1(e.target.value)}
                          className="absolute top-0 left-0 w-full h-full rounded-full border-0 p-0 cursor-pointer opacity-0"
                          style={{background: 'transparent'}}
                          tabIndex={-1}
                        />
                      </div>
                    </div>
                    {gradient && (
                      <div className="flex flex-col items-center">
                        <label className={`block text-sm mb-2 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}`}>Второй цвет</label>
                        <div className="w-14 h-14 rounded-full flex items-center justify-center shadow relative" style={{background: color2, border: '2px solid #ccc'}}>
                          <input
                            type="color"
                            value={color2}
                            onChange={e => setColor2(e.target.value)}
                            className="absolute top-0 left-0 w-full h-full rounded-full border-0 p-0 cursor-pointer opacity-0"
                            style={{background: 'transparent'}}
                            tabIndex={-1}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="mt-4">
                    <div 
                      className="w-full h-16 rounded-lg"
                      style={{
                        background: gradient 
                          ? `linear-gradient(to right, ${color1}, ${color2})`
                          : color1
                      }}
                    />
                  </div>
                </div>
              )}

              {/* Вкладка загрузки */}
              {headerTab === 'upload' && (
                <div>
                  {/* Миниатюры пользовательских хедеров */}
                  {userHeaderImagesLoading ? (
                    <div className="mb-4 text-sm text-gray-500 dark:text-gray-300">Загрузка...</div>
                  ) : userHeaderImages.length > 0 && (
                    <div className="mb-4">
                      <div className="text-xs mb-1 text-gray-500 dark:text-gray-300">Ваши хедеры (до 4):</div>
                      <div className="flex gap-3 flex-wrap">
                        {userHeaderImages.map(img => (
                          <div key={img.id} className="relative group">
                            <img
                              src={getUserHeaderUrl(img.path)}
                              alt="user header"
                              className={`w-[120px] h-8 object-cover rounded border cursor-pointer ${userData.header_bg_image_url === img.path || userData.header_bg_image === img.path ? 'ring-2 ring-blue-500' : ''}`}
                              onClick={() => handleApplyUserHeader(img.path)}
                              style={{ opacity: userHeaderLoading ? 0.5 : 1 }}
                            />
                            <button
                              className="absolute top-0 right-0 bg-black bg-opacity-60 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity text-xs"
                              style={{lineHeight: 0, border: 'none'}}
                              title="Удалить"
                              onClick={e => { e.stopPropagation(); handleDeleteUserHeader(img.id); }}
                              disabled={userHeaderLoading}
                            >×</button>
                          </div>
                        ))}
                      </div>
                      {userHeaderImagesError && <div className="text-red-500 text-xs mt-1">{userHeaderImagesError}</div>}
                    </div>
                  )}
                  <div className="mb-4">
                    <label className={`block text-sm mb-2 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}`}>Выберите изображение (минимум 1180×250 пикселей)</label>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      disabled={!canUploadMore || uploadLoading}
                      className={`block w-full text-sm ${
                        theme === 'dark'
                          ? 'text-gray-400 file:bg-blue-900 file:text-blue-300 hover:file:bg-blue-800'
                          : 'text-gray-500 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
                      } file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold`}
                    />
                    {!canUploadMore && (
                      <div className="text-xs text-gray-500 mt-1">Максимум 4 пользовательских хедера. Удалите один, чтобы загрузить новый.</div>
                    )}
                  </div>
                  {/* Предпросмотр пользовательского хедера по шаблону */}
                  {showUserHeader && (
                    <div className="mb-4">
                      <div className="text-xs mb-1 text-gray-500 dark:text-gray-300">Пользовательский хедер:</div>
                      <img
                        src={userHeaderImages[0]?.path ? getUserHeaderUrl(userHeaderImages[0].path) : ''}
                        alt="Пользовательский хедер"
                        className="w-full max-w-[500px] h-24 object-cover rounded shadow border"
                        style={{marginBottom: 8}}
                        onError={e => { e.target.style.display = 'none'; }}
                      />
                      <button
                        className={`px-4 py-2 rounded transition-colors font-medium mt-2 ${
                          theme === 'dark'
                            ? 'bg-gray-700 text-gray-200 hover:bg-gray-600'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                        onClick={() => handleApplyUserHeader(userHeaderImages[0]?.path)}
                        disabled={userHeaderLoading}
                      >
                        {userHeaderLoading ? 'Загрузка...' : 'Использовать это изображение'}
                      </button>
                      {userHeaderError && <div className="text-red-500 text-sm mt-2">{userHeaderError}</div>}
                    </div>
                  )}
                  {/* Кнопка 'Обрезать и применить' только для нового изображения */}
                  {uploadPreview && uploadPreview !== backendUrl + userHeaderPath && (
                    <button
                      className={`px-4 py-2 rounded transition-colors font-medium mt-4 ${
                        theme === 'dark'
                          ? 'bg-blue-600 text-white hover:bg-blue-700'
                          : 'bg-blue-500 text-white hover:bg-blue-600'
                      } disabled:opacity-50`}
                      onClick={() => {
                        setCropperOpen(true);
                        setCropperImage(uploadPreview);
                        setCropperType('header');
                        setCropperAspectRatio(4.72);
                      }}
                      disabled={uploadLoading}
                    >
                      Обрезать и применить
                    </button>
                  )}
                  {uploadError && (
                    <div className="text-red-500 text-sm mb-4">{uploadError}</div>
                  )}
                </div>
              )}

              {/* Вкладка рамки */}
              {headerTab === 'frame' && (
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2 mb-4 pb-2">
                    <button
                      className={`p-2 rounded transition-colors ${
                        theme === 'dark'
                          ? 'bg-gray-700 text-gray-300 hover:bg-gray-600 border border-gray-600'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
                      }`}
                      onClick={() => {
                        applyHeaderFrame(null);
                      }}
                    >
                      Убрать рамку
                    </button>
                    {headerFrameFilters.map(filter => (
                      <button
                        key={filter.key}
                        className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-colors ${
                          headerFrameFilter === filter.key
                            ? theme === 'dark'
                              ? 'bg-blue-600 text-white'
                              : 'bg-blue-500 text-white'
                            : theme === 'dark'
                              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                        onClick={() => {
                          setHeaderFrameFilter(filter.key);
                          setCurrentPage(0);
                        }}
                      >
                        {filter.label}
                      </button>
                    ))}
                  </div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                    {headerFrames
                      .filter(frame =>
                        headerFrameFilter === 'all' ||
                        headerFrameFilters.find(f => f.key === headerFrameFilter)?.keywords.some(k => frame.split('/').pop().toLowerCase().includes(k))
                      )
                      .slice(currentPage * framesPerPage, (currentPage + 1) * framesPerPage)
                      .map((frame, index) => (
                        <div
                          key={index}
                          className="relative aspect-[4.72/1] rounded-lg overflow-hidden cursor-pointer hover:opacity-90 transition-opacity border border-blue-400"
                          onClick={() => applyHeaderFrame(frame)}
                        >
                          <img src={frame} alt={`Frame ${index + 1}`} className="w-full h-full object-cover" />
                        </div>
                      ))}
                  </div>
                  {/* Пагинация для рамок */}
                  {(() => {
                    const filtered = headerFrames.filter(frame => 
                      headerFrameFilter === 'all' || 
                      headerFrameFilters.find(f => f.key === headerFrameFilter)?.keywords.some(k => frame.toLowerCase().includes(k))
                    );
                    const totalPages = Math.ceil(filtered.length / framesPerPage);
                    if (totalPages <= 1) return null;
                    return (
                      <div className="flex justify-center gap-2 mt-4 items-center">
                        <button
                          className={`px-3 py-1 rounded transition-colors ${
                            theme === 'dark'
                              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600 disabled:opacity-50'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 disabled:opacity-50'
                          }`}
                          onClick={() => setCurrentPage(p => Math.max(0, p - 1))}
                          disabled={currentPage === 0}
                        >
                          ←
                        </button>
                        <span className={`mx-2 text-base font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}`}>{currentPage + 1} / {totalPages}</span>
                        <button
                          className={`px-3 py-1 rounded transition-colors ${
                            theme === 'dark'
                              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600 disabled:opacity-50'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 disabled:opacity-50'
                          }`}
                          onClick={() => setCurrentPage(p => p + 1)}
                          disabled={currentPage + 1 >= totalPages}
                        >
                          →
                        </button>
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* Кнопки управления */}
              <div className="flex justify-between items-center mt-4">
                <button
                  className={`px-4 py-2 rounded transition-colors ${
                    theme === 'dark'
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                  onClick={() => setShowHeaderSettings(false)}
                >
                  Отмена
                </button>
                {headerTab === 'color' && (
                  <button
                    className={`px-4 py-2 rounded transition-colors ${
                      theme === 'dark'
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-blue-500 text-white hover:bg-blue-600'
                    } disabled:opacity-50`}
                    onClick={applyColorBg}
                    disabled={headerLoading}
                  >
                    {headerLoading ? 'Сохранение...' : 'Сохранить'}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Модальное окно кроппера */}
      {cropperOpen && cropperImage && typeof cropperImage === 'string' && cropperImage.startsWith('data:image') ? (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-[95vw] max-w-[700px] relative`}>
            <button
              className={`absolute top-2 right-2 ${
                theme === 'dark' 
                  ? 'bg-gray-900 text-gray-300 hover:bg-gray-700 hover:text-white'
                  : 'bg-white text-gray-500 hover:bg-gray-200 hover:text-gray-800'
              } rounded-full w-10 h-10 flex items-center justify-center shadow-lg z-10`}
              onClick={() => setCropperOpen(false)}
              title="Закрыть"
              style={{lineHeight: 0, border: 'none'}}
            >
              ×
            </button>
            <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-gray-100' : 'text-gray-800'}`}>Обрезка изображения</h2>
            <div className="relative mb-4" style={{height: 280}}>
              <Cropper
                image={cropperImage}
                crop={crop}
                zoom={zoom}
                aspect={cropperAspectRatio}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={onCropComplete}
                restrictPosition={true}
                minZoom={1}
                maxZoom={3}
                showGrid={true}
                objectFit="contain"
                style={{
                  containerStyle: {
                    backgroundColor: theme === 'dark' ? '#1f2937' : '#f3f4f6',
                    borderRadius: '8px',
                    height: 280
                  }
                }}
              />
            </div>
            <div className="flex items-center gap-4 mb-4">
              <input
                type="range"
                min={1}
                max={3}
                step={0.1}
                value={zoom}
                onChange={e => setZoom(parseFloat(e.target.value))}
                className={`w-full ${
                  theme === 'dark' 
                    ? 'bg-gray-700' 
                    : 'bg-gray-200'
                } rounded-lg appearance-none cursor-pointer`}
              />
            </div>
            <div className="flex justify-end gap-4">
              <button
                className={`px-4 py-2 rounded font-medium transition-colors ${
                  theme === 'dark'
                    ? 'bg-gray-700 text-gray-200 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
                onClick={() => setCropperOpen(false)}
              >
                Отмена
              </button>
              <button
                className={`px-4 py-2 rounded font-medium transition-colors ${
                  theme === 'dark'
                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                    : 'bg-blue-500 hover:bg-blue-600 text-white'
                }`}
                onClick={async () => {
                  await handleUploadSave();
                  await refreshUserData();
                  setCropperOpen(false);
                  setShowHeaderSettings(false);
                }}
                disabled={uploadLoading}
              >
                {uploadLoading ? 'Сохранение...' : 'Применить'}
              </button>
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
}

export default ProfileHeader; 