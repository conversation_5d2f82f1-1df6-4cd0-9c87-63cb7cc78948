# Generated by Django 5.0.2 on 2025-05-09 18:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0016_feedevent_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='show_removed_from_friends_in_feed',
            field=models.BooleanField(default=False, help_text='Показывать уведомления об удалении из друзей в ленте'),
        ),
        migrations.AddField(
            model_name='user',
            name='show_unsubscribes_in_feed',
            field=models.BooleanField(default=False, help_text='Показывать уведомления об отписках в ленте'),
        ),
        migrations.AlterField(
            model_name='feedevent',
            name='event_type',
            field=models.CharField(choices=[('friend_request', 'Запрос в друзья'), ('friend_accepted', 'Дружба принята'), ('new_post', 'Новый пост'), ('new_comment', 'Новый комментарий'), ('new_book', 'Новая книга'), ('new_review', 'Новый отзыв'), ('subscribed', 'Подписка'), ('unsubscribed', 'Отписка'), ('removed_from_friends', 'Удаление из друзей')], max_length=20),
        ),
    ]
