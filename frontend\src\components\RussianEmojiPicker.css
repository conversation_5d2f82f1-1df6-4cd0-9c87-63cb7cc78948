/* Русифицированный эмодзи пикер - кастомные стили v2 */

.russian-emoji-picker {
  /* Основные переменные для кастомизации */
  --epr-emoji-size: 32px;
  --epr-emoji-gap: 4px;
  --epr-category-navigation-button-size: 30px; /* Делаем кнопки квадратными 30x30 */
  --epr-header-padding: 8px 12px;
  --epr-horizontal-padding: 12px;
  --epr-picker-border-radius: 8px;
  --epr-search-border-radius: 6px;
  --epr-category-label-padding: 8px 12px;
}

/* ГЛОБАЛЬНЫЕ правила - МАКСИМАЛЬНАЯ специфичность */
.EmojiPickerReact.epr_mw4zr1 .epr-header.epr_iogimd .epr-category-nav.epr_q53mwh button.epr-cat-btn.epr_iy6j2p {
  width: 30px !important;
  height: 30px !important;
  min-width: 30px !important;
  max-width: 30px !important;
  flex: 0 0 30px !important;
  box-sizing: border-box !important;
}

/* Альтернативный селектор с еще большей специфичностью */
aside.EmojiPickerReact div.epr-header div.epr-category-nav button.epr-cat-btn {
  width: 30px !important;
  height: 30px !important;
  min-width: 30px !important;
  max-width: 30px !important;
  flex: 0 0 30px !important;
}

/* ГЛОБАЛЬНЫЕ правила темной темы */
.EmojiPickerReact.epr-dark-theme,
aside.EmojiPickerReact.epr-dark-theme {
  --epr-bg-color: #2d3748 !important;
  --epr-text-color: #e2e8f0 !important;
  --epr-border-color: #4a5568 !important;
  --epr-hover-bg-color: rgba(255, 255, 255, 0.1) !important;
  --epr-highlight-color: #4299e1 !important;
  --epr-category-label-bg-color: #374151 !important;
  --epr-category-label-text-color: #d1d5db !important;
  --epr-preview-bg-color: #374151 !important;
  --epr-category-icon-active-color: #4299e1 !important;
  --epr-search-input-bg-color: #374151 !important;
  --epr-search-input-text-color: #e2e8f0 !important;
  --epr-search-input-placeholder-color: #9ca3af !important;
}

/* Убираем встроенный овальный фокус - МАКСИМАЛЬНАЯ специфичность */
.EmojiPickerReact.epr_mw4zr1 .epr-header.epr_iogimd .epr-category-nav.epr_q53mwh button.epr-cat-btn.epr_iy6j2p:focus:before,
aside.EmojiPickerReact div.epr-header div.epr-category-nav button.epr-cat-btn:focus:before {
  display: none !important;
  content: none !important;
  visibility: hidden !important;
}

/* Наш прямоугольный фокус - МАКСИМАЛЬНАЯ специфичность */
.EmojiPickerReact.epr_mw4zr1 .epr-header.epr_iogimd .epr-category-nav.epr_q53mwh button.epr-cat-btn.epr_iy6j2p:focus,
aside.EmojiPickerReact div.epr-header div.epr-category-nav button.epr-cat-btn:focus {
  outline: 2px solid var(--epr-category-icon-active-color, #007aff) !important;
  outline-offset: 1px !important;
}

.EmojiPickerReact .epr-header nav button:focus-visible {
  outline: none !important;
  box-shadow: none !important;
  border: 1px solid transparent !important;
}



/* Специальные правила для Firefox */
@-moz-document url-prefix() {
  .EmojiPickerReact .epr-header nav button {
    outline: none !important;
    -moz-outline: none !important;
    box-shadow: none !important;
  }

  .EmojiPickerReact .epr-header nav button:focus {
    outline: none !important;
    -moz-outline: none !important;
    box-shadow: none !important;
  }

  .EmojiPickerReact .epr-header nav button::-moz-focus-inner {
    border: 0 !important;
    outline: none !important;
  }
}

/* Плотное расположение кнопок категорий */
.russian-emoji-picker .EmojiPickerReact .epr-header .epr-category-nav {
  gap: 2px !important; /* Уменьшаем отступы между кнопками категорий */
  padding: 4px 8px !important;
  /* Убираем ограничения размеров - пусть панель растягивается по содержимому */
}

/* Удалено - дублирует правила ниже */

/* Локальные правила с максимальной специфичностью */
.russian-emoji-picker.EmojiPickerReact.epr_mw4zr1 .epr-header.epr_iogimd .epr-category-nav.epr_q53mwh button.epr-cat-btn.epr_iy6j2p,
.russian-emoji-picker aside.EmojiPickerReact div.epr-header div.epr-category-nav button.epr-cat-btn {
  width: 30px !important;
  height: 30px !important;
  min-width: 30px !important;
  max-width: 30px !important;
  flex: 0 0 30px !important;
  padding: 4px !important;
  margin: 0 1px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  box-sizing: border-box !important;
}

/* Локальные правила фокуса с максимальной специфичностью */
.russian-emoji-picker.EmojiPickerReact.epr_mw4zr1 .epr-header.epr_iogimd .epr-category-nav.epr_q53mwh button.epr-cat-btn.epr_iy6j2p:focus:before,
.russian-emoji-picker aside.EmojiPickerReact div.epr-header div.epr-category-nav button.epr-cat-btn:focus:before {
  display: none !important;
  content: none !important;
  visibility: hidden !important;
}

.russian-emoji-picker.EmojiPickerReact.epr_mw4zr1 .epr-header.epr_iogimd .epr-category-nav.epr_q53mwh button.epr-cat-btn.epr_iy6j2p:focus,
.russian-emoji-picker aside.EmojiPickerReact div.epr-header div.epr-category-nav button.epr-cat-btn:focus {
  outline: 2px solid var(--epr-category-icon-active-color, #007aff) !important;
  outline-offset: 1px !important;
}

.russian-emoji-picker .EmojiPickerReact .epr-header nav button:hover {
  /* Минимальные изменения при hover */
  background-color: var(--epr-hover-bg-color, rgba(0, 0, 0, 0.05)) !important;
}

/* Компактный заголовок */
.russian-emoji-picker .EmojiPickerReact .epr-header {
  padding: 6px 12px !important;
  border-bottom: 1px solid var(--epr-border-color, #e1e5e9) !important;
}

/* Улучшенное поле поиска */
.russian-emoji-picker .EmojiPickerReact .epr-search-container {
  padding: 8px 12px !important;
}

.russian-emoji-picker .EmojiPickerReact .epr-search {
  border-radius: 6px !important;
  border: 1px solid var(--epr-border-color, #e1e5e9) !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  transition: border-color 0.2s ease !important;
}

.russian-emoji-picker .EmojiPickerReact .epr-search:focus {
  border-color: var(--epr-highlight-color, #007bff) !important;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25) !important;
  outline: none !important;
}

/* Компактные категории с отключением capitalize */
.russian-emoji-picker .EmojiPickerReact .epr-body .epr-emoji-category-label {
  padding: 6px 12px !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  color: var(--epr-category-label-text-color, #666) !important;
  background-color: var(--epr-category-label-bg-color, #f8f9fa) !important;
  border-radius: 4px !important;
  margin: 4px 12px 8px 12px !important;
  text-transform: none !important; /* Отключаем встроенный capitalize */
}

/* Плотное расположение эмодзи */
.russian-emoji-picker .EmojiPickerReact .epr-body {
  padding: 0 8px !important;
}

.russian-emoji-picker .EmojiPickerReact .epr-emoji-category {
  margin-bottom: 12px !important;
}

.russian-emoji-picker .EmojiPickerReact .epr-emoji-list {
  gap: 3px !important; /* Уменьшаем отступы между эмодзи */
  padding: 0 4px !important;
}

/* Улучшенные эмодзи - убираем transform, оставляем встроенные эффекты */
.russian-emoji-picker .EmojiPickerReact .epr-emoji-list button {
  border-radius: 6px !important;
  transition: all 0.15s ease !important;
  padding: 4px !important;
}

.russian-emoji-picker .EmojiPickerReact .epr-emoji-list button:hover {
  /* Убираем transform, оставляем только цвет фона */
  background-color: var(--epr-hover-bg-color, rgba(0, 0, 0, 0.05)) !important;
}

/* Улучшенный превью */
.russian-emoji-picker .EmojiPickerReact .epr-preview {
  padding: 8px 12px !important;
  border-top: 1px solid var(--epr-border-color, #e1e5e9) !important;
  background-color: var(--epr-preview-bg-color, #f8f9fa) !important;
}

.russian-emoji-picker .EmojiPickerReact .epr-preview .epr-preview-emoji {
  font-size: 24px !important;
  margin-right: 8px !important;
}

.russian-emoji-picker .EmojiPickerReact .epr-preview .epr-preview-name {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: var(--epr-text-color, #333) !important;

}

/* Поисковое поле - сдвигаем лупу вправо на 10px */
.russian-emoji-picker .EmojiPickerReact .epr-search-container input {
  padding-left: 45px !important; /* Увеличиваем отступ слева для лупы */
}

.russian-emoji-picker .EmojiPickerReact .epr-search-container .epr-icn-search {
  left: 20px !important; /* Сдвигаем лупу на 10px вправо (было 10px по умолчанию) */
}

/* Отступ между поиском и выбором цвета кожи */
.russian-emoji-picker .EmojiPickerReact .epr-skin-tones {
  margin-left: 10px !important;
}

/* Темная тема - с максимальной специфичностью */
.russian-emoji-picker .EmojiPickerReact.epr-dark-theme,
.russian-emoji-picker.EmojiPickerReact.epr-dark-theme.epr_mw4zr1,
aside.EmojiPickerReact.epr-dark-theme {
  --epr-bg-color: #2d3748 !important;
  --epr-text-color: #e2e8f0 !important;
  --epr-border-color: #4a5568 !important;
  --epr-hover-bg-color: rgba(255, 255, 255, 0.1) !important;
  --epr-highlight-color: #4299e1 !important;
  --epr-category-label-bg-color: #374151 !important;
  --epr-category-label-text-color: #d1d5db !important;
  --epr-preview-bg-color: #374151 !important;
  --epr-category-icon-active-color: #4299e1 !important;
  --epr-search-input-bg-color: #374151 !important;
  --epr-search-input-text-color: #e2e8f0 !important;
  --epr-search-input-placeholder-color: #9ca3af !important;
}

/* Hover эффекты для темной темы - с максимальной специфичностью */
.russian-emoji-picker .EmojiPickerReact.epr-dark-theme .epr-header nav button:hover,
.russian-emoji-picker.EmojiPickerReact.epr-dark-theme.epr_mw4zr1 .epr-header.epr_iogimd .epr-category-nav.epr_q53mwh button.epr-cat-btn.epr_iy6j2p:hover,
aside.EmojiPickerReact.epr-dark-theme div.epr-header div.epr-category-nav button.epr-cat-btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.russian-emoji-picker .EmojiPickerReact.epr-dark-theme .epr-emoji-list button:hover,
.russian-emoji-picker.EmojiPickerReact.epr-dark-theme.epr_mw4zr1 .epr-body button:hover,
aside.EmojiPickerReact.epr-dark-theme .epr-body button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 480px) {
  .russian-emoji-picker .EmojiPickerReact .epr-header nav button {
    width: 24px !important;
    height: 24px !important;
    min-width: 24px !important;
    padding: 2px !important;
  }
  
  .russian-emoji-picker .EmojiPickerReact .epr-emoji-size {
    --epr-emoji-size: 28px;
  }
  
  .russian-emoji-picker .EmojiPickerReact .epr-horizontal-padding {
    --epr-horizontal-padding: 8px;
  }
}

/* Анимации */
.russian-emoji-picker .EmojiPickerReact {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Улучшенная прокрутка */
.russian-emoji-picker .EmojiPickerReact .epr-body::-webkit-scrollbar {
  width: 6px;
}

.russian-emoji-picker .EmojiPickerReact .epr-body::-webkit-scrollbar-track {
  background: var(--epr-scrollbar-track-color, #f1f1f1);
  border-radius: 3px;
}

.russian-emoji-picker .EmojiPickerReact .epr-body::-webkit-scrollbar-thumb {
  background: var(--epr-scrollbar-thumb-color, #c1c1c1);
  border-radius: 3px;
}

.russian-emoji-picker .EmojiPickerReact .epr-body::-webkit-scrollbar-thumb:hover {
  background: var(--epr-scrollbar-thumb-hover-color, #a8a8a8);
}
