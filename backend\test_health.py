#!/usr/bin/env python3
import urllib.request
import json

try:
    response = urllib.request.urlopen('http://localhost:8000/health/')
    data = response.read().decode()
    print("✅ Health check успешен!")
    print("Ответ:", data)
    
    # Попробуем распарсить JSON
    try:
        json_data = json.loads(data)
        print("📊 Статус:", json_data.get('status'))
        print("🗄 База данных:", json_data.get('database'))
        print("🔄 Redis:", json_data.get('redis'))
    except:
        print("Ответ не JSON:", data)
        
except Exception as e:
    print("❌ Health check не работает:", e) 