# Решение проблемы с ResizableImageNodeView

## Проблема
У нас был общий компонент `ResizableImageNodeView`, который использовался в разных редакторах с разной логикой загрузки изображений:

1. **ChapterEditor/ChapterEditorV2** - работают с приватными папками, привязанными к главам
2. **BioEditorX** - работает с публичными папками, не привязанными к главам

Проблема заключалась в том, что функция `window.uploadImageFunction` устанавливалась по-разному в каждом редакторе, но `ResizableImageNodeView` использовал одну и ту же глобальную функцию, что приводило к конфликтам.

## Решение
Мы реализовали передачу функции загрузки через props в `ResizableImageNodeView`:

### 1. Изменения в ResizableImageNodeView.jsx
```javascript
// Получаем функцию загрузки из props или глобального объекта (fallback)
const uploadFunction = props.uploadImageFunction || window.uploadImageFunction;
```

### 2. Изменения в ChapterEditor.jsx
```javascript
const ThemedResizableImageNodeView = (props) => {
  // Передаем функцию загрузки для глав
  const chapterUploadFunction = async (file) => {
    const formData = new FormData();
    formData.append('image', file);
    formData.append('chapter_order', chapterOrder);
    formData.append('img_index', imgCounter.current);
    formData.append('username', username);
    if (chapterId) {
      formData.append('chapter_id', chapterId);
    }
    
    const csrfToken = getCookie('csrftoken');
    
    const res = await fetch(`/api/books/${bookId}/chapters/upload_image/`, {
      method: 'POST',
      headers: {
        'X-CSRFToken': csrfToken,
      },
      body: formData,
      credentials: 'include',
    });
    
    if (!res.ok) throw new Error('Ошибка загрузки');
    
    const data = await res.json();
    imgCounter.current += 1;
    
    return data;
  };
  
  return <ResizableImageNodeView {...props} uploadImageFunction={chapterUploadFunction} />;
};
```

### 3. Изменения в BioEditorX.jsx
```javascript
const ThemedResizableImageNodeView = (props) => {
  // Передаем функцию загрузки для био
  const bioUploadFunction = async (file) => {
    if (!username) {
      throw new Error('Ошибка: не хватает данных для загрузки');
    }
    
    if (!file) {
      throw new Error('Ошибка: файл не выбран');
    }
    
    const formData = new FormData();
    formData.append('image', file);
    formData.append('width', 400);
    formData.append('height', 300);
    
    const csrfToken = getCookie('csrftoken');
    
    const res = await fetch('/api/auth/bio/upload-image/', {
      method: 'POST',
      headers: {
        'X-CSRFToken': csrfToken,
      },
      body: formData,
      credentials: 'include',
    });
    
    if (!res.ok) throw new Error('Ошибка загрузки');
    
    const data = await res.json();
    
    return data;
  };
  
  return <ResizableImageNodeView {...props} theme={theme} uploadImageFunction={bioUploadFunction} />;
};
```

## Преимущества решения

1. **Изоляция логики**: Каждый редактор теперь имеет свою собственную функцию загрузки
2. **Отсутствие конфликтов**: Нет больше проблем с перезаписью глобальной функции
3. **Обратная совместимость**: Сохранена поддержка глобальной функции как fallback
4. **Чистый код**: Логика загрузки инкапсулирована в соответствующих редакторах

## Как добавить новый редактор

Если вам нужно создать новый редактор с `ResizableImageNodeView`:

1. Создайте свою функцию загрузки изображений
2. Передайте её в `ResizableImageNodeView` через prop `uploadImageFunction`
3. Установите глобальную функцию для обратной совместимости (опционально)

Пример:
```javascript
const MyCustomEditor = () => {
  const customUploadFunction = async (file) => {
    // Ваша логика загрузки
    return { url: 'uploaded-image-url' };
  };

  const ThemedResizableImageNodeView = (props) => (
    <ResizableImageNodeView {...props} uploadImageFunction={customUploadFunction} />
  );

  // Остальной код редактора...
};
```

## Тестирование

Убедитесь, что:
1. Изображения корректно загружаются в ChapterEditor
2. Изображения корректно загружаются в BioEditorX  
3. Функция замены изображений работает в обоих редакторах
4. Нет конфликтов при переключении между редакторами