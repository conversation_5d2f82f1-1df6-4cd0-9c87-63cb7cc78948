from django.core.management.base import BaseCommand
from users.models import User
from django.conf import settings
import os

class Command(BaseCommand):
    help = 'Исправляет поля avatar и avatar_thumbnail для всех пользователей, если файлы существуют, либо сбрасывает на системный аватар.'

    def handle(self, *args, **options):
        fixed = 0
        reset = 0
        for user in User.objects.all():
            base_path = f"users/{user.username[0]}/{user.username[:2]}/{user.username}/avatar"
            avatar_path = f"{base_path}/ava_{user.username}.jpg"
            avatar_mini_path = f"{base_path}/ava_{user.username}_m.jpg"
            full_avatar = os.path.join(settings.MEDIA_ROOT, avatar_path)
            mini_avatar = os.path.join(settings.MEDIA_ROOT, avatar_mini_path)
            changed = False

            # Проверяем наличие файлов
            avatar_exists = os.path.isfile(full_avatar)
            mini_exists = os.path.isfile(mini_avatar)

            # Если есть хотя бы один файл, заполняем поля и avatar_type=2
            if avatar_exists or mini_exists:
                if avatar_exists and (not user.avatar or str(user.avatar) != avatar_path):
                    user.avatar = avatar_path
                    changed = True
                if mini_exists and (not user.avatar_thumbnail or str(user.avatar_thumbnail) != avatar_mini_path):
                    user.avatar_thumbnail = avatar_mini_path
                    changed = True
                # Всегда выставляем avatar_type=2 если есть кастомные файлы
                if user.avatar_type != 2:
                    user.avatar_type = 2
                    changed = True
                if changed:
                    user.save(update_fields=['avatar', 'avatar_thumbnail', 'avatar_type'])
                    self.stdout.write(self.style.SUCCESS(f"[OK] {user.username}: avatar set to {user.avatar}, thumbnail set to {user.avatar_thumbnail}, type=2"))
                    fixed += 1
            else:
                # Если файлов нет, сбрасываем на системный аватар и avatar_type=1
                if user.avatar or user.avatar_thumbnail or user.avatar_type != 1:
                    user.reset_to_system_avatar()
                    user.avatar_type = 1
                    user.save(update_fields=['avatar', 'avatar_thumbnail', 'avatar_preset', 'avatar_preset_mini', 'avatar_type'])
                    self.stdout.write(self.style.WARNING(f"[RESET] {user.username}: no avatar files, reset to system avatar, type=1"))
                    reset += 1
            # ДОПОЛНИТЕЛЬНО: если avatar пустой, а avatar_thumbnail есть — очищаем миниатюру у всех пользователей
            if not user.avatar and user.avatar_thumbnail:
                user.avatar_thumbnail = None
                user.save(update_fields=['avatar_thumbnail'])
                self.stdout.write(self.style.WARNING(f"[CLEAN] {user.username}: removed orphaned avatar_thumbnail"))
        self.stdout.write(self.style.SUCCESS(f"Готово! Исправлено: {fixed}, сброшено на системный: {reset}")) 