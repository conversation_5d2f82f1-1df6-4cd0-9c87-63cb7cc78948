# Generated by Django 5.0.2 on 2025-05-09 11:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('users', '0014_user_auto_accept_friends_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='FeedEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('friend_request', 'Запрос в друзья'), ('friend_accepted', 'Дружба принята'), ('new_post', 'Новый пост'), ('new_comment', 'Новый комментарий'), ('new_book', 'Новая книга'), ('new_review', 'Новый отзыв')], max_length=20)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_read', models.BooleanField(default=False)),
                ('actor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='caused_events', to=settings.AUTH_USER_MODEL)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feed_events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', '-created_at'], name='users_feede_user_id_83dd43_idx'), models.Index(fields=['user', 'is_read'], name='users_feede_user_id_9c5527_idx')],
            },
        ),
    ]
