import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from books.models import Book
from datetime import timedelta

class Command(BaseCommand):
    help = 'Удаляет незавершённые книги, которые были созданы более 6 часов назад и пользователь не был на странице создания более часа.'

    def handle(self, *args, **kwargs):
        now = timezone.now()
        six_hours_ago = now - timedelta(hours=6)
        one_hour_ago = now - timedelta(hours=1)
        books = Book.objects.filter(
            creation_status='creating',
            created_at__lte=six_hours_ago
        ).filter(
            abandon_at__lte=one_hour_ago
        ) | Book.objects.filter(
            creation_status='creating',
            created_at__lte=six_hours_ago,
            abandon_at__isnull=True
        )
        # --- Путь к логу, настройте под свой проект ---
        log_path = '/var/log/abandoned_books.log'  # или другой путь, например, './abandoned_books.log'
        logger = logging.getLogger('abandoned_books')
        handler = logging.FileHandler(log_path, encoding='utf-8')
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        for book in books.distinct():
            msg = f"{now}: Удаляю книгу {book.id} ({book.title}) автора {book.author.username}"
            self.stdout.write(msg)
            logger.info(msg)
            book.delete()
        handler.close() 