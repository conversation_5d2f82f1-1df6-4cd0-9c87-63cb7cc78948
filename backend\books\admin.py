from django.contrib import admin
from .models import Book, BookChapter, Like, Genre

@admin.register(Book)
class BookAdmin(admin.ModelAdmin):
    list_display = [field.name for field in Book._meta.fields]
    search_fields = ['title', 'id', 'author__username']
    list_filter = ['status', 'is_published', 'is_finished', 'type', 'cover_type', 'creation_status', 'age_rating', 'is_adult', 'has_profanity']
    ordering = ['id']
    fields = (
        'author', 'title', 'description', 'cover', 'cover_mini', 'cover_editor', 'status', 'is_published', 'is_finished',
        'type', 'cover_type', 'genres', 'is_adult', 'age_rating', 'has_profanity', 'hashtags', 'auto_indent',
        'position_finished', 'position_in_progress', 'position_draft',
        'creation_status', 'abandon_at', 'created_at', 'updated_at'
    )
    readonly_fields = ('created_at', 'updated_at')

@admin.register(BookChapter)
class BookChapterAdmin(admin.ModelAdmin):
    list_display = [field.name for field in BookChapter._meta.fields]
    search_fields = ['title', 'id', 'book__title']
    list_filter = ['is_published', 'book']
    ordering = ['id']

@admin.register(Like)
class LikeAdmin(admin.ModelAdmin):
    list_display = ('book', 'created_at')

@admin.register(Genre)
class GenreAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)
