from django.urls import path, include
from rest_framework.routers import <PERSON>fa<PERSON><PERSON><PERSON><PERSON>
from rest_framework_nested.routers import NestedSimpleRouter
from . import views
from .views import hashtag_autocomplete

router = DefaultRouter()
router.register(r'books', views.BookViewSet, basename='books')
router.register(r'chapters', views.BookChapterViewSet, basename='chapters')

books_router = NestedSimpleRouter(router, r'books', lookup='book')
books_router.register(r'chapters', views.BookChapterViewSet, basename='book-chapters')

urlpatterns = [
    path('', include(router.urls)),
    path('', include(books_router.urls)),
    path('books-list/', views.book_list, name='books-list'),  # Отдельный endpoint для списка всех книг
    path('genres/', views.genre_list, name='genre-list'),
    path('hashtags/', hashtag_autocomplete, name='hashtag-autocomplete'),
    path('users/<str:username>/books/', views.user_books, name='user-books-list'),
    path('users/<str:username>/books-with-user-simple/', views.user_books_with_user_data_simple, name='user-books-with-user-simple'),
    path('users/<str:username>/books/reorder/', views.BookViewSet.as_view({'patch': 'reorder'}), name='user-books-reorder'),
    path('books/<int:book_id>/upload_docx/', views.upload_docx, name='upload-docx'),
]
