{"include": ["src", "types"], "compilerOptions": {"module": "esnext", "lib": ["dom", "esnext"], "importHelpers": true, "declaration": true, "sourceMap": true, "rootDir": "./src", "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "resolveJsonModule": true, "jsx": "react", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true}}