import { useEffect, useRef, useCallback } from 'react';

export const useScrollToLoad = (loadMore, hasMore, loading) => {
    const containerRef = useRef(null);
    const loadingRef = useRef(false);
    const paginationUsedRef = useRef(false); // Флаг что пагинация уже использовалась

    const handleScroll = useCallback(() => {
        if (!containerRef.current || loading || loadingRef.current || !hasMore) return;

        const container = containerRef.current;
        const { scrollTop } = container;
        
        // Загружаем больше сообщений если близко к верху
        if (scrollTop <= 100) {
            loadingRef.current = true;
            paginationUsedRef.current = true; // Отмечаем что пагинация использована
            
            // Сохраняем точную позицию скролла и высоту контейнера ДО загрузки
            const oldScrollHeight = container.scrollHeight;
            const oldScrollTop = container.scrollTop;
            
            // console.log('[ANCHOR] Before loading - scrollTop:', oldScrollTop, 'scrollHeight:', oldScrollHeight); // Removed for performance
            
            // Запускаем загрузку асинхронно
            loadMore()
                .then((success) => {
                    // Если загрузка успешна, восстанавливаем позицию скролла
                    if (success) {
                        // Ждем рендера новых элементов
                        requestAnimationFrame(() => {
                            requestAnimationFrame(() => {
                                if (containerRef.current) {
                                    const newScrollHeight = containerRef.current.scrollHeight;
                                    const heightDifference = newScrollHeight - oldScrollHeight;
                                    
                                    // Устанавливаем новую позицию скролла с учетом добавленного контента
                                    const newScrollTop = oldScrollTop + heightDifference;
                                    containerRef.current.scrollTop = newScrollTop;
                                    
                                    // console.log('[ANCHOR] After loading - oldScrollHeight:', oldScrollHeight, 
                                    //           'newScrollHeight:', newScrollHeight, 
                                    //           'heightDifference:', heightDifference,
                                    //           'newScrollTop:', newScrollTop); // Removed for performance
                                }
                            });
                        });
                    }
                })
                .catch((error) => {
                    console.error('Error loading more messages:', error);
                })
                .finally(() => {
                    // Всегда сбрасываем состояние после завершения
                    loadingRef.current = false;
                });
        }
    }, [loadMore, hasMore, loading]);

    // Логика восстановления позиции скролла теперь встроена в handleScroll

    // Прокрутка к низу контейнера
    const scrollToBottom = useCallback(() => {
        if (!containerRef.current) return;
        const container = containerRef.current;
        
        // Самый простой и надежный способ - прокрутить к максимуму
        container.scrollTop = container.scrollHeight;
    }, []);

    // Фокус на последнем сообщении (только если пагинация еще не использовалась)
    const focusLastMessage = useCallback(() => {
        if (!containerRef.current || paginationUsedRef.current) return;
        
        const container = containerRef.current;
        const messages = container.querySelectorAll('[data-message-id]');
        if (messages.length > 0) {
            const lastMessage = messages[messages.length - 1];
            lastMessage.scrollIntoView({ behavior: 'auto', block: 'end' });
        }
    }, []);

    // Прокрутка к конкретному элементу
    const scrollToElement = useCallback((elementId) => {
        if (!containerRef.current) return;
        const element = containerRef.current.querySelector(`[data-message-id="${elementId}"]`);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }, []);

    // Функция для сброса флага пагинации (для новых диалогов)
    const resetPaginationFlag = useCallback(() => {
        paginationUsedRef.current = false;
    }, []);

    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        container.addEventListener('scroll', handleScroll);
        return () => container.removeEventListener('scroll', handleScroll);
    }, [handleScroll]);

    return {
        containerRef,
        scrollToBottom,
        scrollToElement,
        focusLastMessage,
        resetPaginationFlag
    };
}; 