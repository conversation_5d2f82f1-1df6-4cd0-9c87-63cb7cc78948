#!/bin/bash

# Скрипт для остановки Celery worker и beat

echo "Stopping Celery services..."

# Останавливаем worker
if [ -f celery_worker.pid ]; then
    echo "Stopping Celery worker..."
    kill -TERM $(cat celery_worker.pid)
    rm celery_worker.pid
    echo "✓ Celery worker stopped"
else
    echo "Celery worker PID file not found"
fi

# Останавливаем beat
if [ -f celery_beat.pid ]; then
    echo "Stopping Celery beat..."
    kill -TERM $(cat celery_beat.pid)
    rm celery_beat.pid
    echo "✓ Celery beat stopped"
else
    echo "Celery beat PID file not found"
fi

# Удаляем schedule файл beat
if [ -f celerybeat-schedule ]; then
    rm celerybeat-schedule
    echo "✓ Celery beat schedule file removed"
fi

echo "Celery services stopped!"
