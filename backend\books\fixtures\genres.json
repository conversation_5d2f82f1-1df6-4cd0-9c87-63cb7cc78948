[{"model": "books.genre", "pk": 1, "fields": {"name": "Фантастика", "parent": null}}, {"model": "books.genre", "pk": 2, "fields": {"name": "Альтернативная история", "parent": 1}}, {"model": "books.genre", "pk": 3, "fields": {"name": "Антиутопия / утопия", "parent": 1}}, {"model": "books.genre", "pk": 4, "fields": {"name": "Биопанк", "parent": 1}}, {"model": "books.genre", "pk": 5, "fields": {"name": "Боевая фантастика", "parent": 1}}, {"model": "books.genre", "pk": 6, "fields": {"name": "Героическая фантастика", "parent": 1}}, {"model": "books.genre", "pk": 7, "fields": {"name": "К<PERSON>б<PERSON>р<PERSON><PERSON>нк", "parent": 1}}, {"model": "books.genre", "pk": 8, "fields": {"name": "Космическая фантастика", "parent": 1}}, {"model": "books.genre", "pk": 9, "fields": {"name": "Научная фантастика", "parent": 1}}, {"model": "books.genre", "pk": 11, "fields": {"name": "Постапокалипсис", "parent": 1}}, {"model": "books.genre", "pk": 12, "fields": {"name": "Социальная фантастика", "parent": 1}}, {"model": "books.genre", "pk": 13, "fields": {"name": "Стимпанк", "parent": 1}}, {"model": "books.genre", "pk": 14, "fields": {"name": "Экофантастика", "parent": 1}}, {"model": "books.genre", "pk": 15, "fields": {"name": "Юмористическая фантастика", "parent": 1}}, {"model": "books.genre", "pk": 16, "fields": {"name": "Фэнтези", "parent": null}}, {"model": "books.genre", "pk": 17, "fields": {"name": "Боевое фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 18, "fields": {"name": "Борэ-Аниме", "parent": 16}}, {"model": "books.genre", "pk": 19, "fields": {"name": "Бытовое фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 20, "fields": {"name": "Героическое фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 21, "fields": {"name": "Городское фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 22, "fields": {"name": "Историческое фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 23, "fields": {"name": "Классическое фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 24, "fields": {"name": "ЛитРПГ-фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 25, "fields": {"name": "Магическая академия", "parent": 16}}, {"model": "books.genre", "pk": 26, "fields": {"name": "Романтическое фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 27, "fields": {"name": "Темное фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 28, "fields": {"name": "Уся (китайское боевое фэнтези)", "parent": 16}}, {"model": "books.genre", "pk": 29, "fields": {"name": "Эльфийское фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 30, "fields": {"name": "Эпическое фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 31, "fields": {"name": "Юмористическое фэнтези", "parent": 16}}, {"model": "books.genre", "pk": 32, "fields": {"name": "Попаданцы и ЛитРПГ", "parent": null}}, {"model": "books.genre", "pk": 33, "fields": {"name": "Попаданцы", "parent": 32}}, {"model": "books.genre", "pk": 34, "fields": {"name": "Попаданцы в космос", "parent": 32}}, {"model": "books.genre", "pk": 35, "fields": {"name": "Попаданцы в магические миры", "parent": 32}}, {"model": "books.genre", "pk": 36, "fields": {"name": "Попаданцы во времени", "parent": 32}}, {"model": "books.genre", "pk": 37, "fields": {"name": "Назад в СССР", "parent": 32}}, {"model": "books.genre", "pk": 38, "fields": {"name": "ЛитРПГ", "parent": 32}}, {"model": "books.genre", "pk": 39, "fields": {"name": "РеалРПГ", "parent": 32}}, {"model": "books.genre", "pk": 40, "fields": {"name": "Детективы", "parent": null}}, {"model": "books.genre", "pk": 41, "fields": {"name": "Классический детектив", "parent": 40}}, {"model": "books.genre", "pk": 42, "fields": {"name": "Детектив с животными", "parent": 40}}, {"model": "books.genre", "pk": 43, "fields": {"name": "Исторический детектив", "parent": 40}}, {"model": "books.genre", "pk": 44, "fields": {"name": "Мистический детектив", "parent": 40}}, {"model": "books.genre", "pk": 45, "fields": {"name": "Политический детектив", "parent": 40}}, {"model": "books.genre", "pk": 46, "fields": {"name": "Полицейский детектив", "parent": 40}}, {"model": "books.genre", "pk": 47, "fields": {"name": "Психологический детектив", "parent": 40}}, {"model": "books.genre", "pk": 48, "fields": {"name": "Триллер-детектив", "parent": 40}}, {"model": "books.genre", "pk": 49, "fields": {"name": "Уютный детектив", "parent": 40}}, {"model": "books.genre", "pk": 50, "fields": {"name": "Фантастический детектив", "parent": 40}}, {"model": "books.genre", "pk": 51, "fields": {"name": "Шпионский детектив", "parent": 40}}, {"model": "books.genre", "pk": 52, "fields": {"name": "Юмористический детектив", "parent": 40}}, {"model": "books.genre", "pk": 53, "fields": {"name": "Триллеры", "parent": null}}, {"model": "books.genre", "pk": 54, "fields": {"name": "Криминальный триллер", "parent": 53}}, {"model": "books.genre", "pk": 55, "fields": {"name": "Медицинский триллер", "parent": 53}}, {"model": "books.genre", "pk": 56, "fields": {"name": "Политический триллер", "parent": 53}}, {"model": "books.genre", "pk": 57, "fields": {"name": "Психологический триллер", "parent": 53}}, {"model": "books.genre", "pk": 58, "fields": {"name": "Технотриллер", "parent": 53}}, {"model": "books.genre", "pk": 59, "fields": {"name": "Юридический триллер", "parent": 53}}, {"model": "books.genre", "pk": 60, "fields": {"name": "Ужасы и мистика", "parent": null}}, {"model": "books.genre", "pk": 61, "fields": {"name": "Ужасы", "parent": 60}}, {"model": "books.genre", "pk": 62, "fields": {"name": "Мистика", "parent": 60}}, {"model": "books.genre", "pk": 63, "fields": {"name": "Готический роман", "parent": 60}}, {"model": "books.genre", "pk": 64, "fields": {"name": "Лавкрафтовский ужас", "parent": 60}}, {"model": "books.genre", "pk": 65, "fields": {"name": "Паранормальные ужасы", "parent": 60}}, {"model": "books.genre", "pk": 66, "fields": {"name": "Слэшер", "parent": 60}}, {"model": "books.genre", "pk": 67, "fields": {"name": "Хо<PERSON>р<PERSON>р-Маньяки", "parent": 60}}, {"model": "books.genre", "pk": 68, "fields": {"name": "Эко-хоррор", "parent": 60}}, {"model": "books.genre", "pk": 69, "fields": {"name": "Приключения", "parent": null}}, {"model": "books.genre", "pk": 70, "fields": {"name": "Морские приключения", "parent": 69}}, {"model": "books.genre", "pk": 71, "fields": {"name": "Исторические приключения", "parent": 69}}, {"model": "books.genre", "pk": 72, "fields": {"name": "Географические приключения", "parent": 69}}, {"model": "books.genre", "pk": 73, "fields": {"name": "Робинзонада", "parent": 69}}, {"model": "books.genre", "pk": 74, "fields": {"name": "Любовные романы", "parent": null}}, {"model": "books.genre", "pk": 75, "fields": {"name": "Современный любовный роман", "parent": 74}}, {"model": "books.genre", "pk": 76, "fields": {"name": "Исторический любовный роман", "parent": 74}}, {"model": "books.genre", "pk": 78, "fields": {"name": "Молодежный роман", "parent": 74}}, {"model": "books.genre", "pk": 79, "fields": {"name": "Драма о любви", "parent": 74}}, {"model": "books.genre", "pk": 80, "fields": {"name": "Комедийный роман о любви", "parent": 74}}, {"model": "books.genre", "pk": 81, "fields": {"name": "Короткий любовный роман", "parent": 74}}, {"model": "books.genre", "pk": 82, "fields": {"name": "Юмористический любовный роман", "parent": 74}}, {"model": "books.genre", "pk": 83, "fields": {"name": "Современная проза", "parent": null}}, {"model": "books.genre", "pk": 84, "fields": {"name": "Документальная проза", "parent": 83}}, {"model": "books.genre", "pk": 85, "fields": {"name": "Драма", "parent": 83}}, {"model": "books.genre", "pk": 86, "fields": {"name": "Историческая проза", "parent": 83}}, {"model": "books.genre", "pk": 87, "fields": {"name": "Комический роман", "parent": 83}}, {"model": "books.genre", "pk": 88, "fields": {"name": "Подростковая проза", "parent": 83}}, {"model": "books.genre", "pk": 89, "fields": {"name": "Психологическая проза", "parent": 83}}, {"model": "books.genre", "pk": 90, "fields": {"name": "РусРеал", "parent": 83}}, {"model": "books.genre", "pk": 91, "fields": {"name": "Сатирический роман", "parent": 83}}, {"model": "books.genre", "pk": 92, "fields": {"name": "Семейная сага", "parent": 83}}, {"model": "books.genre", "pk": 93, "fields": {"name": "Социальная проза", "parent": 83}}, {"model": "books.genre", "pk": 94, "fields": {"name": "Философская проза", "parent": 83}}, {"model": "books.genre", "pk": 95, "fields": {"name": "Юмористическая литература", "parent": null}}, {"model": "books.genre", "pk": 96, "fields": {"name": "Ироническая проза", "parent": 95}}, {"model": "books.genre", "pk": 97, "fields": {"name": "Комедия", "parent": 95}}, {"model": "books.genre", "pk": 98, "fields": {"name": "Сатира", "parent": 95}}, {"model": "books.genre", "pk": 99, "fields": {"name": "Ю<PERSON><PERSON><PERSON>", "parent": 95}}, {"model": "books.genre", "pk": 100, "fields": {"name": "Фанфикшн", "parent": null}}, {"model": "books.genre", "pk": 101, "fields": {"name": "Фанфик", "parent": 100}}, {"model": "books.genre", "pk": 102, "fields": {"name": "Фанфик по книге", "parent": 100}}, {"model": "books.genre", "pk": 103, "fields": {"name": "Фанфик по фильму и сериалу", "parent": 100}}, {"model": "books.genre", "pk": 104, "fields": {"name": "Фанфик по игре", "parent": 100}}, {"model": "books.genre", "pk": 105, "fields": {"name": "Альтернативные вселенные (AU)", "parent": 100}}, {"model": "books.genre", "pk": 106, "fields": {"name": "Юмористический фанфик", "parent": 100}}, {"model": "books.genre", "pk": 107, "fields": {"name": "Литература для детей и подростков", "parent": null}}, {"model": "books.genre", "pk": 108, "fields": {"name": "Детская литература", "parent": 107}}, {"model": "books.genre", "pk": 109, "fields": {"name": "Сказки", "parent": 107}}, {"model": "books.genre", "pk": 110, "fields": {"name": "Подростковая литература", "parent": 107}}, {"model": "books.genre", "pk": 111, "fields": {"name": "Поэзия", "parent": null}}, {"model": "books.genre", "pk": 112, "fields": {"name": "<PERSON>бор<PERSON>и<PERSON> стихов", "parent": 111}}, {"model": "books.genre", "pk": 113, "fields": {"name": "Прикладная и нон-фикшн литература", "parent": null}}, {"model": "books.genre", "pk": 114, "fields": {"name": "Развитие личности", "parent": 113}}, {"model": "books.genre", "pk": 115, "fields": {"name": "Публицистика", "parent": 113}}, {"model": "books.genre", "pk": 116, "fields": {"name": "Бизнес-литература", "parent": 113}}, {"model": "books.genre", "pk": 117, "fields": {"name": "Прочее", "parent": null}}, {"model": "books.genre", "pk": 118, "fields": {"name": "Боевик", "parent": 117}}, {"model": "books.genre", "pk": 119, "fields": {"name": "Дорама", "parent": 117}}, {"model": "books.genre", "pk": 120, "fields": {"name": "Разное", "parent": 117}}, {"model": "books.genre", "pk": 121, "fields": {"name": "Категория 18+", "parent": null}}, {"model": "books.genre", "pk": 122, "fields": {"name": "Эротика 18+", "parent": 121}}, {"model": "books.genre", "pk": 123, "fields": {"name": "Романтическая эротика 18+", "parent": 121}}, {"model": "books.genre", "pk": 124, "fields": {"name": "Эротическое фэнтези 18+", "parent": 121}}, {"model": "books.genre", "pk": 125, "fields": {"name": "Эротическая фантастика 18+", "parent": 121}}, {"model": "books.genre", "pk": 126, "fields": {"name": "Эротический фанфик 18+", "parent": 121}}]