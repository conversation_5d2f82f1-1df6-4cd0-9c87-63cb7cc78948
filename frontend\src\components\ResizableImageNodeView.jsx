import { NodeViewWrapper } from '@tiptap/react';
import React, { useRef, useState, useEffect, useMemo } from 'react';
import { Dropdown, Input, Upload, message } from 'antd';
import { 
  Pic<PERSON>enterOutlined, 
  PicLeftOutlined, 
  Pic<PERSON>ightOutlined,
  BorderOutlined,
  BlockOutlined,
  EditOutlined,
  SwapOutlined,
  DeleteOutlined,
  SettingOutlined
} from '@ant-design/icons';
import './ImageEditor.css';
import { useTheme } from '../theme/ThemeContext';

// Кастомный NodeView для image с плавающим меню управления
export default function ResizableImageNodeView(props) {
  const { node, updateAttributes, selected, deleteNode, editor, getPos, view } = props;
  const { 
    src, 
    width: rawWidth = 50, 
    alt = '', 
    title = '', 
    align = 'center',
    textWrap = 'break', // 'break' или 'wrap'
    caption = '', // подпись под изображением
    class: cssClass = 'align-center' // CSS класс для выравнивания/обтекания
  } = node.attrs;
  
  const width = typeof rawWidth === 'number' ? rawWidth : (parseInt(rawWidth, 10) || 50);
  const [showCaptionInput, setShowCaptionInput] = useState(false);
  const [captionText, setCaptionText] = useState(caption);

  // Синхронизация локального состояния с атрибутами изображения
  useEffect(() => {
    setCaptionText(caption);
  }, [caption]);

  // Функция-хелпер для выполнения действий с сохранением выделения изображения
  const withImageSelection = (callback) => {
    return (...args) => {
      if (editor && typeof getPos === 'function') {
        const pos = getPos();
        callback(...args);
        // Восстанавливаем выделение изображения после действия
        setTimeout(() => {
          if (editor.view && editor.view.state) {
            editor.commands.setNodeSelection(pos);
          }
        }, 10);
      }
    };
  };
  const [menuPosition, setMenuPosition] = useState('bottom');
  const [forcedMenuPosition, setForcedMenuPosition] = useState(null); // 'top', 'bottom' или null (авто)
  const [isResizing, setIsResizing] = useState(false);
  const [resizeData, setResizeData] = useState(null);
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
  const imageRef = useRef(null);
  const menuRef = useRef(null);
  const containerRef = useRef(null);
  
  // Используем ThemeContext как все остальные компоненты
  const { theme } = useTheme();

  // Определяем размер меню в зависимости от ширины изображения
  const getMenuSizeClass = useMemo(() => {
    if (width <= 35) return 'mini';    // Очень маленькие изображения (≤35%)
    if (width <= 50) return 'compact'; // Средние изображения (35-50%)
    return '';                         // Обычные изображения (>50%)
  }, [width]);

  // Определяем позицию меню и обновляем размеры
  const updateMenuPosition = (newForcedPosition = forcedMenuPosition) => {
    if (selected && imageRef.current) {
      // Если установлена принудительная позиция, используем её
      if (newForcedPosition) {
        setMenuPosition(newForcedPosition);
      } else {
        // Автоматическое позиционирование
        const rect = imageRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - rect.bottom;
        const spaceAbove = rect.top;
        
        // Проверяем, находится ли редактор в видимой области
        const editorElement = containerRef.current?.closest('.ProseMirror');
        let editorTop = 0;
        let editorBottom = viewportHeight;
        
        if (editorElement) {
          const editorRect = editorElement.getBoundingClientRect();
          editorTop = editorRect.top;
          editorBottom = editorRect.bottom;
        }
        
        // Если меню сверху уходит за верхний край редактора, переключаем вниз
        // Если снизу мало места (меньше 80px) и сверху достаточно, показываем сверху
        const menuWouldBeAboveEditor = rect.top - 60 < editorTop; // 60px - примерная высота меню
        const shouldShowBelow = spaceBelow < 80 && spaceAbove > 80 && !menuWouldBeAboveEditor;
        
        const autoPosition = shouldShowBelow ? 'top' : 'bottom';
        setMenuPosition(autoPosition);
      }
      
      // Обновляем размеры при показе/скрытии ручек ресайза
      setTimeout(() => {
        if (imageRef.current && containerRef.current) {
          const imageRect = imageRef.current.getBoundingClientRect();
          const containerRect = containerRef.current.getBoundingClientRect();
          
          const offsetLeft = imageRect.left - containerRect.left;
          const offsetTop = imageRect.top - containerRect.top;
          
          setImageDimensions({
            width: imageRect.width,
            height: imageRect.height,
            offsetLeft,
            offsetTop
          });
        }
      }, 50);
    }
  };

  useEffect(() => {
    updateMenuPosition();
  }, [selected]);

  // Отслеживание скролла для обновления позиции меню
  useEffect(() => {
    if (!selected) return;

    const handleScroll = () => {
      updateMenuPosition();
    };

    // Добавляем обработчик скролла к окну и родительским элементам
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Также отслеживаем скролл в редакторе, если он есть
    const editorElement = containerRef.current?.closest('.ProseMirror');
    const scrollableParent = editorElement?.closest('[class*="scroll"], .overflow-auto, .overflow-y-auto');
    
    if (scrollableParent) {
      scrollableParent.addEventListener('scroll', handleScroll, { passive: true });
    }

    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollableParent) {
        scrollableParent.removeEventListener('scroll', handleScroll);
      }
    };
  }, [selected]);

  // Очистка событий при размонтировании
  useEffect(() => {
    return () => {
      if (isResizing) {
        document.body.style.userSelect = '';
        document.body.style.cursor = '';
      }
    };
  }, [isResizing]);

  // Отслеживание размеров изображения
  useEffect(() => {
    const updateImageDimensions = () => {
      if (imageRef.current && containerRef.current) {
        const imageRect = imageRef.current.getBoundingClientRect();
        const containerRect = containerRef.current.getBoundingClientRect();
        
        // Вычисляем смещение изображения относительно контейнера
        const offsetLeft = imageRect.left - containerRect.left;
        const offsetTop = imageRect.top - containerRect.top;
        
        setImageDimensions({
          width: imageRect.width,
          height: imageRect.height,
          offsetLeft,
          offsetTop
        });
      }
    };

    // Обновляем размеры при загрузке изображения
    if (imageRef.current) {
      if (imageRef.current.complete) {
        updateImageDimensions();
      } else {
        imageRef.current.onload = updateImageDimensions;
      }
    }

    // Обновляем размеры при изменении размера окна
    const handleResize = () => {
      setTimeout(updateImageDimensions, 100); // Небольшая задержка для корректного пересчета
    };

    window.addEventListener('resize', handleResize);
    
    // Добавляем ResizeObserver для отслеживания изменений размера контейнера
    let resizeObserver;
    if (containerRef.current && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        setTimeout(updateImageDimensions, 50);
      });
      resizeObserver.observe(containerRef.current);
    }

    // Добавляем MutationObserver для отслеживания изменений в DOM
    let mutationObserver;
    if (containerRef.current) {
      mutationObserver = new MutationObserver(() => {
        setTimeout(updateImageDimensions, 50);
      });
      
      // Наблюдаем за изменениями атрибутов родительских элементов
      const editorElement = containerRef.current.closest('.ProseMirror') || containerRef.current.closest('[class*="editor"]');
      if (editorElement) {
        mutationObserver.observe(editorElement, {
          attributes: true,
          attributeFilter: ['style', 'class'],
          subtree: true
        });
      }
    }

    // Добавляем IntersectionObserver для отслеживания видимости
    let intersectionObserver;
    if (containerRef.current && window.IntersectionObserver) {
      intersectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setTimeout(updateImageDimensions, 100);
          }
        });
      }, { threshold: 0.1 });
      
      intersectionObserver.observe(containerRef.current);
    }

    // Дополнительное обновление через requestAnimationFrame
    const scheduleUpdate = () => {
      requestAnimationFrame(() => {
        updateImageDimensions();
        // Повторяем еще раз через небольшую задержку для стабильности
        setTimeout(updateImageDimensions, 200);
      });
    };

    // Планируем обновление при монтировании
    scheduleUpdate();
    
    // Обновляем размеры при изменении ширины изображения
    updateImageDimensions();

    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
      if (mutationObserver) {
        mutationObserver.disconnect();
      }
      if (intersectionObserver) {
        intersectionObserver.disconnect();
      }
    };
  }, [width, src]);

  // Управление выравниванием
  const handleAlign = (newAlign) => {
    const newAttributes = { align: newAlign };
    
    // Всегда обновляем CSS класс в соответствии с выравниванием
    if (newAlign === 'left') {
      newAttributes.class = textWrap === 'wrap' ? 'float-left' : 'align-left';
    } else if (newAlign === 'right') {
      newAttributes.class = textWrap === 'wrap' ? 'float-right' : 'align-right';
    } else {
      // Если выравнивание center, всегда отключаем обтекание
      newAttributes.textWrap = 'break';
      newAttributes.class = 'align-center';
    }
    
    updateAttributes(newAttributes);
    
    // Обновляем размеры после изменения выравнивания
    setTimeout(() => {
      if (imageRef.current && containerRef.current) {
        const imageRect = imageRef.current.getBoundingClientRect();
        const containerRect = containerRef.current.getBoundingClientRect();
        
        const offsetLeft = imageRect.left - containerRect.left;
        const offsetTop = imageRect.top - containerRect.top;
        
        setImageDimensions({
          width: imageRect.width,
          height: imageRect.height,
          offsetLeft,
          offsetTop
        });
      }
      
      // Обновляем классы параграфов для правильного отображения значков
      if (window.updateParagraphClasses) {
        window.updateParagraphClasses();
      }
    }, 100);
  };

  // Управление обтеканием текста
  const handleTextWrap = (wrapType) => {
    const newAttributes = { textWrap: wrapType };
    
    // Устанавливаем соответствующий CSS класс
    if (wrapType === 'wrap') {
      if (align === 'left') {
        newAttributes.class = 'float-left';
      } else if (align === 'right') {
        newAttributes.class = 'float-right';
      } else {
        // Если align center, переключаем на break
        newAttributes.textWrap = 'break';
        newAttributes.class = 'align-center';
      }
    } else {
      // Режим break - используем align классы
      if (align === 'left') {
        newAttributes.class = 'align-left';
      } else if (align === 'right') {
        newAttributes.class = 'align-right';
      } else {
        newAttributes.class = 'align-center';
      }
    }
    
    updateAttributes(newAttributes);
    
    // Обновляем размеры после изменения обтекания
    setTimeout(() => {
      if (imageRef.current && containerRef.current) {
        const imageRect = imageRef.current.getBoundingClientRect();
        const containerRect = containerRef.current.getBoundingClientRect();
        
        const offsetLeft = imageRect.left - containerRect.left;
        const offsetTop = imageRect.top - containerRect.top;
        
        setImageDimensions({
          width: imageRect.width,
          height: imageRect.height,
          offsetLeft,
          offsetTop
        });
      }
      
      // Обновляем классы параграфов для правильного отображения значков
      if (window.updateParagraphClasses) {
        window.updateParagraphClasses();
      }
    }, 100);
  };

  // Управление позиционированием меню
  const handleMenuPositionChange = (position) => {
    const newForcedPosition = position === 'auto' ? null : position;
    
    // Обновляем состояние
    setForcedMenuPosition(newForcedPosition);
    
    // Немедленно применяем новую позицию, передавая её напрямую
    updateMenuPosition(newForcedPosition);
  };

  // Начало ресайза
  const handleResizeStart = (e, direction) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!containerRef.current) {
      return;
    }
    
    const containerRect = containerRef.current.getBoundingClientRect();
    const editorElement = containerRef.current.closest('.ProseMirror');
    const editorRect = editorElement ? editorElement.getBoundingClientRect() : containerRect;
    
    const newResizeData = {
      direction,
      startX: e.clientX,
      startY: e.clientY,
      startWidth: width,
      containerWidth: containerRect.width,
      editorWidth: editorRect.width,
      aspectRatio: containerRect.height / containerRect.width
    };
    
    setIsResizing(true);
    setResizeData(newResizeData);
    
         // Создаем функции с замыканием для правильного контекста
     const handleMove = (moveEvent) => {
       if (!newResizeData) return;
       
       const { direction, startX, startY, startWidth, editorWidth } = newResizeData;
       const deltaX = moveEvent.clientX - startX;
       const deltaY = moveEvent.clientY - startY;
       
       let newWidth = startWidth;
       

       
       // Вычисляем новую ширину в зависимости от направления
       if (direction.includes('right')) {
         const widthChange = (deltaX / editorWidth) * 100;
         newWidth = startWidth + widthChange;
       } else if (direction.includes('left')) {
         const widthChange = (deltaX / editorWidth) * 100;
         newWidth = startWidth - widthChange;
       } else if (direction === 'top' || direction === 'bottom') {
         // Для вертикального ресайза сохраняем пропорции
         const heightChange = direction === 'bottom' ? deltaY : -deltaY;
         const widthChange = heightChange / newResizeData.aspectRatio;
         const widthChangePercent = (widthChange / editorWidth) * 100;
         newWidth = startWidth + widthChangePercent;
       }
       
       // Округляем до целых процентов и ограничиваем размер от 25% до 100%
       const clampedWidth = Math.max(25, Math.min(100, Math.round(newWidth)));
       
       // Обновляем только если значение изменилось (избегаем лишних обновлений)
       if (clampedWidth !== width) {
         // Автоматически переключаем на 'break' если в режиме обтекания ширина становится > 70%
         const newAttributes = { width: clampedWidth };
         if (textWrap === 'wrap' && clampedWidth > 70) {
           newAttributes.textWrap = 'break';
           // Переключаем на соответствующий align класс
           if (align === 'left') {
             newAttributes.class = 'align-left';
           } else if (align === 'right') {
             newAttributes.class = 'align-right';
           } else {
             newAttributes.class = 'align-center';
           }
         }
         updateAttributes(newAttributes);
       }
     };
    
         const handleEnd = () => {
       setIsResizing(false);
       setResizeData(null);
       
       document.removeEventListener('mousemove', handleMove);
       document.removeEventListener('mouseup', handleEnd);
       document.body.style.userSelect = '';
       document.body.style.cursor = '';
       
       // Обновляем классы параграфов после изменения размера
       setTimeout(() => {
         if (window.updateParagraphClasses) {
           window.updateParagraphClasses();
         }
       }, 100);
     };
    
    document.addEventListener('mousemove', handleMove);
    document.addEventListener('mouseup', handleEnd);
    document.body.style.userSelect = 'none';
    document.body.style.cursor = getResizeCursor(direction);
  };



  // Получить курсор для направления ресайза
  const getResizeCursor = (direction) => {
    const cursors = {
      'top-left': 'nw-resize',
      'top-right': 'ne-resize',
      'bottom-left': 'sw-resize',
      'bottom-right': 'se-resize',
      'top': 'n-resize',
      'bottom': 's-resize',
      'left': 'w-resize',
      'right': 'e-resize'
    };
    return cursors[direction] || 'default';
  };

  // Сохранение подписи
  const handleCaptionSave = () => {
    const trimmedCaption = captionText.trim();
    updateAttributes({ caption: trimmedCaption });
    setShowCaptionInput(false);
    // Обновляем локальное состояние
    setCaptionText(trimmedCaption);
    
    // Небольшая задержка перед показом панели, чтобы избежать конфликтов
    setTimeout(() => {
      // Панель автоматически появится, так как showCaptionInput стал false
    }, 100);
  };

  // Замена изображения
  const handleImageReplace = async (file) => {
    try {
      const uploadFunction = props.uploadImageFunction || window.uploadImageFunction;
      if (!uploadFunction) {
        message.error('Функция загрузки недоступна');
        return;
      }

      message.loading({ content: 'Замена изображения...', key: 'imageReplace' });
      
      const result = await uploadFunction(file);
      
      if (result && result.url) {
        // Обновляем src, сохраняя настройки (старое изображение удалится при сохранении)
        updateAttributes({ 
          src: result.url,
          width,
          align,
          textWrap,
          caption,
          alt,
          title
        });
        
        message.success({ content: 'Изображение заменено', key: 'imageReplace' });
      } else {
        throw new Error('Не получен URL изображения');
      }
    } catch (error) {
      message.error({ content: 'Ошибка замены изображения', key: 'imageReplace' });
    }
  };

  // Удаление изображения из текста (файл удаляется при сохранении)
  const handleDelete = () => {
    if (typeof deleteNode === 'function') {
      deleteNode();
    } else if (editor) {
      editor.chain().focus().deleteSelection().run();
    }
    
    message.info({
      content: 'Изображение удалено. Файл будет удален при сохранении.',
      duration: 3
    });
  };



  // Меню выравнивания
  const alignmentMenu = {
    items: [
      {
        key: 'left',
        label: 'Слева',
        icon: <PicLeftOutlined />,
        onClick: withImageSelection(() => handleAlign('left'))
      },
      {
        key: 'center',
        label: 'По центру',
        icon: <PicCenterOutlined />,
        onClick: withImageSelection(() => handleAlign('center'))
      },
      {
        key: 'right',
        label: 'Справа',
        icon: <PicRightOutlined />,
        onClick: withImageSelection(() => handleAlign('right'))
      }
    ]
  };

  // Меню обтекания текста
  const textWrapMenu = {
    items: [
      {
        key: 'break',
        label: 'Разрыв текста',
        icon: <BorderOutlined />,
        onClick: withImageSelection(() => handleTextWrap('break'))
      },
      {
        key: 'wrap',
        label: align === 'center' ? 'Обтекание (только для лево/право)' : width > 70 ? 'Обтекание (только для ширины ≤70%)' : 'Обтекание',
        icon: <BlockOutlined />,
        onClick: withImageSelection(() => handleTextWrap('wrap')),
        disabled: align === 'center' || width > 70 // Обтекание работает только для left/right и при ширине <= 70%
      }
    ]
  };

  // Определяем текущую позицию меню для отображения галочки
  const getCurrentMenuPosition = () => {
    return forcedMenuPosition === 'top' ? 'top' : 
           forcedMenuPosition === 'bottom' ? 'bottom' : 'auto';
  };

  // Меню позиционирования
  const positionMenu = {
    items: [
      {
        key: 'header',
        label: 'Позиция меню:',
        type: 'group',
        children: [
          {
            key: 'auto',
            label: (
              <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                Автоматически
                {getCurrentMenuPosition() === 'auto' && (
                  <span style={{ marginLeft: 8, color: '#2563eb', fontWeight: 'bold' }}>✓</span>
                )}
              </span>
            ),
            onClick: withImageSelection(() => handleMenuPositionChange('auto'))
          },
          {
            key: 'top',
            label: (
              <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                Сверху
                {getCurrentMenuPosition() === 'top' && (
                  <span style={{ marginLeft: 8, color: '#2563eb', fontWeight: 'bold' }}>✓</span>
                )}
              </span>
            ),
            onClick: withImageSelection(() => handleMenuPositionChange('top'))
          },
          {
            key: 'bottom',
            label: (
              <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                Снизу
                {getCurrentMenuPosition() === 'bottom' && (
                  <span style={{ marginLeft: 8, color: '#2563eb', fontWeight: 'bold' }}>✓</span>
                )}
              </span>
            ),
            onClick: withImageSelection(() => handleMenuPositionChange('bottom'))
          }
        ]
      }
    ]
  };



  return (
    <NodeViewWrapper 
      className={`custom-resizable-image ${cssClass}${selected ? ' ProseMirror-selectednode' : ''} ${theme === 'dark' ? 'dark-theme' : 'light-theme'}`}
      style={{ 
        position: 'relative',
        width: `${Math.max(25, Math.min(100, width))}%`,
        maxWidth: '100%'
      }}
      tabIndex={0}
      onClick={e => {
        if (editor && typeof getPos === 'function') {
          editor.commands.setNodeSelection(getPos());
        }
      }}
    >
      <div 
        ref={containerRef}
        className={`
          ${selected ? 'image-container-resizable image-container-selected' : ''} 
          ${isResizing ? 'image-container-resizing' : ''}
        `}
        style={{ 
          position: 'relative', 
          width: '100%'
        }}
      >
          <img
            ref={imageRef}
            src={src}
            alt={alt}
            title={title}
            style={{
              width: '100%',
              maxWidth: '100%',
              display: 'block',
              borderRadius: 8,
              boxShadow: selected ? '0 0 0 2px #2563eb44' : 'none',
              transition: isResizing ? 'none' : 'box-shadow 0.2s',
              pointerEvents: cssClass.includes('float-') ? 'auto' : 'none',
              cursor: cssClass.includes('float-') ? 'pointer' : 'default',
            }}
            draggable={false}
          />
          
          {/* Ресайзеры */}
          {selected && !showCaptionInput && (
            <div 
              className="image-resizers-container"
              style={{
                width: imageDimensions.width || '100%',
                height: imageDimensions.height || 'auto',
                left: imageDimensions.offsetLeft || 0,
                top: imageDimensions.offsetTop || 0
              }}
            >
              {/* Угловые ресайзеры */}
              <div 
                className="image-resizer corner top-left"
                onMouseDown={(e) => handleResizeStart(e, 'top-left')}
              />
              <div 
                className="image-resizer corner top-right"
                onMouseDown={(e) => handleResizeStart(e, 'top-right')}
              />
              <div 
                className="image-resizer corner bottom-left"
                onMouseDown={(e) => handleResizeStart(e, 'bottom-left')}
              />
              <div 
                className="image-resizer corner bottom-right"
                onMouseDown={(e) => handleResizeStart(e, 'bottom-right')}
              />
              
              {/* Боковые ресайзеры */}
              <div 
                className="image-resizer side top"
                onMouseDown={(e) => handleResizeStart(e, 'top')}
              />
              <div 
                className="image-resizer side bottom"
                onMouseDown={(e) => handleResizeStart(e, 'bottom')}
              />
              <div 
                className="image-resizer side left"
                onMouseDown={(e) => handleResizeStart(e, 'left')}
              />
              <div 
                className="image-resizer side right"
                onMouseDown={(e) => handleResizeStart(e, 'right')}
              />
            </div>
          )}
          
          {/* Плавающее меню управления - скрываем когда редактируем подпись */}
          {selected && !showCaptionInput && (
            <div 
              ref={menuRef}
              className={`floating-image-menu ${menuPosition === 'top' ? 'menu-top' : 'menu-bottom'} ${theme === 'light' ? 'light-theme' : 'dark-theme'} ${getMenuSizeClass}`}
              style={{
                left: imageDimensions.width ? (imageDimensions.offsetLeft || 0) + (imageDimensions.width / 2) : '50%',
                transform: 'translateX(-50%)'
              }}
              onMouseDown={(e) => e.preventDefault()} // Предотвращаем потерю фокуса для всего меню
            >
              {/* 0. Управление позицией меню */}
              <Dropdown 
                menu={positionMenu} 
                trigger={['click']} 
                placement="bottom"
                destroyPopupOnHide={false}
                getPopupContainer={() => document.body}
                overlayClassName={`position-dropdown ${theme === 'dark' ? 'dark-theme' : 'light-theme'}`}
              >
                <button
                  className={`floating-menu-button position-toggle ${forcedMenuPosition ? 'active' : ''}`}
                  title="Настройки позиции меню"
                  onMouseDown={(e) => e.preventDefault()} // Предотвращаем потерю фокуса
                >
                  <SettingOutlined />
                </button>
              </Dropdown>

              {/* Разделитель */}
              <div 
                className="menu-divider"
                style={{ 
                  width: '1px', 
                  height: '24px', 
                  background: theme === 'light' ? 'rgba(0,0,0,0.1)' : 'rgba(255,255,255,0.2)', 
                  margin: '0 4px' 
                }} 
              />

              {/* 1. Выравнивание */}
              <Dropdown 
                menu={alignmentMenu} 
                trigger={['click']} 
                placement="bottom"
                destroyPopupOnHide={false}
                getPopupContainer={() => document.body}
                overlayClassName={`position-dropdown ${theme === 'dark' ? 'dark-theme' : 'light-theme'}`}
              >
                <button
                  className="floating-menu-button"
                  title="Выравнивание"
                  onMouseDown={(e) => e.preventDefault()} // Предотвращаем потерю фокуса
                >
                  {align === 'left' ? <PicLeftOutlined /> : 
                   align === 'right' ? <PicRightOutlined /> : 
                   <PicCenterOutlined />}
                </button>
              </Dropdown>

              {/* 2. Обтекание текста */}
              <Dropdown 
                menu={textWrapMenu} 
                trigger={['click']} 
                placement="bottom"
                destroyPopupOnHide={false}
                getPopupContainer={() => document.body}
                overlayClassName={`position-dropdown ${theme === 'dark' ? 'dark-theme' : 'light-theme'}`}
              >
                <button
                  className={`floating-menu-button ${textWrap === 'wrap' ? 'active' : ''}`}
                  title="Обтекание текста"
                  onMouseDown={(e) => e.preventDefault()} // Предотвращаем потерю фокуса
                >
                  {textWrap === 'wrap' ? <BlockOutlined /> : <BorderOutlined />}
                </button>
              </Dropdown>

              {/* 3. Подпись */}
              <button
                onClick={() => {
                  setShowCaptionInput(true);
                  // Небольшая задержка для корректного фокуса
                  setTimeout(() => {
                    const captionInput = document.querySelector('.caption-input-direct');
                    if (captionInput) {
                      captionInput.focus();
                      captionInput.select(); // Выделяем существующий текст для удобства редактирования
                    }
                  }, 100);
                }}
                onMouseDown={(e) => e.preventDefault()} // Предотвращаем потерю фокуса
                className={`floating-menu-button ${caption ? 'caption-active' : ''}`}
                title={caption ? 'Изменить подпись' : 'Добавить подпись'}
              >
                <EditOutlined />
              </button>

              {/* 4. Заменить изображение */}
              <Upload
                accept="image/*"
                showUploadList={false}
                beforeUpload={(file) => {
                  handleImageReplace(file);
                  return false; // Предотвращаем автоматическую загрузку
                }}
              >
                <button
                  className="floating-menu-button"
                  title="Заменить изображение"
                  onMouseDown={(e) => e.preventDefault()} // Предотвращаем потерю фокуса
                >
                  <SwapOutlined />
                </button>
              </Upload>

              {/* 5. Удалить */}
              <button
                onClick={handleDelete}
                onMouseDown={(e) => e.preventDefault()} // Предотвращаем потерю фокуса
                className="floating-menu-button delete-button"
                title="Удалить изображение"
              >
                <DeleteOutlined />
              </button>
            </div>
          )}

          {/* Поле ввода подписи - отображается прямо под изображением */}
          {showCaptionInput && (
            <div style={{ marginTop: '8px', width: '100%' }}>
              <input
                className="caption-input-direct"
                type="text"
                placeholder="Введите подпись к изображению..."
                value={captionText}
                maxLength={150}
                onChange={(e) => setCaptionText(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleCaptionSave();
                  } else if (e.key === 'Escape') {
                    setShowCaptionInput(false);
                    setCaptionText(caption); // Восстанавливаем исходное значение
                  }
                }}
                onBlur={handleCaptionSave} // Автосохранение при потере фокуса
                autoFocus
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: theme === 'dark' ? '1px solid #4b5563' : '1px solid #e5e7eb',
                  borderRadius: '6px',
                  background: theme === 'dark' ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.95)',
                  color: theme === 'dark' ? '#9ca3af' : '#6b7280',
                  fontSize: '0.875rem',
                  fontStyle: 'italic',
                  textAlign: 'center',
                  outline: 'none',
                  fontFamily: 'inherit',
                  transition: 'border-color 0.2s ease, color 0.2s ease'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#2563eb';
                  e.target.style.boxShadow = '0 0 0 3px rgba(37, 99, 235, 0.1)';
                }}
                onBlurCapture={(e) => {
                  e.target.style.borderColor = theme === 'dark' ? '#4b5563' : '#e5e7eb';
                  e.target.style.boxShadow = 'none';
                }}
              />
              
              {/* Счетчик символов и подсказка - компактная версия */}
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginTop: '4px',
                fontSize: '11px',
                color: theme === 'dark' ? '#6b7280' : '#9ca3af',
                opacity: 0.7
              }}>
                <span style={{ fontSize: '10px' }}>
                  Enter - сохранить, Esc - отмена
                </span>
                <span>
                  {captionText.length}/150
                </span>
              </div>
            </div>
          )}

          {/* Подпись под изображением */}
          {caption && (
            <div className="image-caption">
              {caption}
            </div>
          )}
        </div>



    </NodeViewWrapper>
  );
}