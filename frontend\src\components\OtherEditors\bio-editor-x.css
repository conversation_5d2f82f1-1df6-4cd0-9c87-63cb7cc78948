.tiptap-editor {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  /* background: transparent !important; */
  min-height: 100px;
  padding-bottom: 48px;
}
.tiptap-editor .ProseMirror {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  /* background: transparent !important; */
  line-height: 1.5;
  padding-bottom: 48px;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}
.tiptap-editor .ProseMirror p {
  margin-top: 0;
  margin-bottom: 0.6em;
}
.tiptap-indent p {
  text-indent: 1.5em;
}
.tiptap-fontsize-option {
  padding-left: 8px;
}
.color-dropdown-menu.dark .ant-dropdown-menu {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
  border-radius: 8px !important;
}
.color-dropdown-menu.dark .ant-dropdown-menu-item {
  color: #fff !important;
  font-size: 15px;
  border-radius: 6px;
}
.color-dropdown-menu.dark .ant-dropdown-menu-item-selected,
.color-dropdown-menu.dark .ant-dropdown-menu-item-active {
  background: #374151 !important;
  color: #60A5FA !important;
}
.color-dropdown-menu.light .ant-dropdown-menu {
  background: #fff !important;
  color: #222 !important;
  border: 1.5px solid #d1d5db !important;
  border-radius: 8px !important;
}
.color-dropdown-menu.light .ant-dropdown-menu-item {
  color: #222 !important;
  font-size: 15px;
  border-radius: 6px;
}
.color-dropdown-menu.light .ant-dropdown-menu-item-selected,
.color-dropdown-menu.light .ant-dropdown-menu-item-active {
  background: #e5e7eb !important;
  color: #2563eb !important;
}
.dark-link-popover .ant-popover-inner {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
  border-radius: 10px !important;
}
.dark-link-popover .ant-popover-title {
  color: #fff !important;
}
.dark-link-popover .ant-input {
  background: #181c23 !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
}
.dark-link-popover .ant-input::placeholder {
  color: #888 !important;
}
.dark-link-popover .ant-checkbox-inner {
  background: #23272f !important;
  border-color: #374151 !important;
}
.dark-link-popover .ant-checkbox-checked .ant-checkbox-inner {
  background: #2563eb !important;
  border-color: #2563eb !important;
}
.dark-link-popover .ant-btn {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
}
.dark-link-popover .ant-btn-primary {
  background: #2563eb !important;
  border-color: #2563eb !important;
  color: #fff !important;
}
.dark-link-popover .ant-btn-dangerous {
  background: #ef4444 !important;
  border: none !important;
  color: #fff !important;
}
.dark-link-modal .ant-modal-content {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
  border-radius: 12px !important;
}
.dark-link-modal .ant-modal-title {
  background: #23272f !important;
  color: #fff !important;
  border-bottom: 1.5px solid #23272f !important;
}
.dark-link-modal .ant-input {
  background: #181c23 !important;
  color: #fff !important;
  border: 1.5px solid #181c23 !important;
}
.dark-link-modal .ant-input::placeholder {
  color: #bfc9d1 !important;
  opacity: 1 !important;
}
.dark-link-modal .ant-input-affix-wrapper {
  background: #181c23 !important;
  color: #fff !important;
  border: 1.5px solid #181c23 !important;
}
.dark-link-modal .ant-input-affix-wrapper input {
  background: #181c23 !important;
  color: #fff !important;
}
.dark-link-modal .ant-input-affix-wrapper .ant-input-clear-icon {
  color: #fff !important;
  opacity: 0.8 !important;
}
.dark-link-modal .ant-checkbox-inner {
  background: #23272f !important;
  border-color: #374151 !important;
}
.dark-link-modal .ant-checkbox-checked .ant-checkbox-inner {
  background: #2563eb !important;
  border-color: #2563eb !important;
}
.dark-link-modal .ant-checkbox + span, .dark-link-modal .ant-checkbox-wrapper span {
  color: #fff !important;
}
.dark-link-modal .ant-btn {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
}
.dark-link-modal .ant-btn-primary {
  background: #2563eb !important;
  border-color: #2563eb !important;
  color: #fff !important;
}
.dark-link-modal .ant-btn-dangerous {
  background: #ef4444 !important;
  border: none !important;
  color: #fff !important;
}
.dark-link-modal .ant-modal-close {
  color: #fff !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}
.dark-link-modal .ant-modal-close-x {
  color: #fff !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Стили для обтекания текста в редакторе */
.tiptap-editor .float-left {
  float: left !important;
  margin: 8px 16px 8px 0 !important;
  clear: left;
}

.tiptap-editor .float-right {
  float: right !important;
  margin: 8px 0 8px 16px !important;
  clear: right;
}

.tiptap-editor .align-left {
  display: block;
  margin-left: 0 !important;
  margin-right: auto !important;
  margin-top: 8px;
  margin-bottom: 8px;
}

.tiptap-editor .align-center {
  display: block;
  margin-left: auto !important;
  margin-right: auto !important;
  margin-top: 8px;
  margin-bottom: 8px;
}

.tiptap-editor .align-right {
  display: block;
  margin-left: auto !important;
  margin-right: 0 !important;
  margin-top: 8px;
  margin-bottom: 8px;
}

/* Стили для подписей к изображениям в редакторе и профиле */
.tiptap-editor .image-caption,
.bio-content .image-caption {
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
  font-style: italic;
  margin-top: 8px;
  margin-bottom: 12px;
  line-height: 1.4;
}

.dark .tiptap-editor .image-caption,
.dark .bio-content .image-caption {
  color: #9ca3af;
}