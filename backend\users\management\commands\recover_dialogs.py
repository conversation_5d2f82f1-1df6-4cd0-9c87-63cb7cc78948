from django.core.management.base import BaseCommand
from users.models import Message, Dialog

class Command(BaseCommand):
    help = 'Восстанавливает диалоги для всех сообщений без диалога'

    def handle(self, *args, **options):
        pairs = set()
        for msg in Message.objects.filter(dialog__isnull=True):
            pair = tuple(sorted([msg.sender_id, msg.recipient_id]))
            pairs.add(pair)

        created = 0
        updated = 0

        for sender_id, recipient_id in pairs:
            dialog = Dialog.objects.filter(participants__id=sender_id).filter(participants__id=recipient_id).first()
            if not dialog:
                dialog = Dialog.objects.create()
                dialog.participants.add(sender_id, recipient_id)
                created += 1
            # Проставляем dialog для всех сообщений между этими пользователями
            msgs = Message.objects.filter(
                sender_id__in=[sender_id, recipient_id],
                recipient_id__in=[sender_id, recipient_id],
                dialog__isnull=True
            )
            count = msgs.update(dialog=dialog)
            updated += count

        self.stdout.write(self.style.SUCCESS(
            f'Создано диалогов: {created}, обновлено сообщений: {updated}'
        )) 