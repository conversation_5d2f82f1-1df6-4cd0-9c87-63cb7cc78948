import React, { useState, useRef, useEffect } from 'react';
import './custom-hashtag-input.css';
import { useTheme } from '../theme/ThemeContext';
import { isWordForbidden } from '../utils/wordFilter';
import { includesTag, tagsForDisplay, validateHashtag, cleanHashtag } from '../utils/hashtagUtils';

const MAX_TAGS = 10;

export default function CustomHashtagInput({ value = [], onChange }) {
  const { theme } = useTheme();
  const [input, setInput] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [dropdown, setDropdown] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const inputRef = useRef();
  const wrapperRef = useRef();
  // Обеспечиваем, что internalValue всегда является массивом
  const [internalValue, setInternalValue] = useState(Array.isArray(value) ? value : []);

  // Синхронизируем внутреннее состояние с внешним значением
  useEffect(() => {
    // Убеждаемся, что value является массивом перед установкой
    setInternalValue(Array.isArray(value) ? value : []);
  }, [value]);

  useEffect(() => {
    function handleClickOutside(e) {
      if (wrapperRef.current && !wrapperRef.current.contains(e.target)) {
        setDropdown(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (input.trim()) {
      setLoading(true);
      fetch(`/api/hashtags/?q=${encodeURIComponent(input.trim())}`)
        .then(res => res.json())
        .then(data => {
          const filteredSuggestions = data
            .map(tag => tagsForDisplay([tag.name])[0] || tag.name) // Преобразуем для отображения
            .filter(n => !includesTag(internalValue, n)); // Используем утилиту для фильтрации
          setSuggestions(filteredSuggestions);
        })
        .catch(err => {
          console.error('Error fetching hashtags:', err);
          setSuggestions([]);
        })
        .finally(() => setLoading(false));
    } else {
      setSuggestions([]);
    }
  }, [input, internalValue]);

  const addTag = tag => {
    if (!tag) return;

    // Очищаем от символов # в начале
    const withoutHash = tag.replace(/^#+/, '');

    // Нормализуем хештег: убираем пробелы по краям, множественные пробелы, недопустимые символы
    const normalized = withoutHash
      .toLowerCase()
      .replace(/[^а-яё0-9\s]/g, '') // Только русские буквы, цифры и пробелы
      .replace(/\s+/g, ' ') // Множественные пробелы → одинарные
      .trim(); // Убираем пробелы по краям

    if (!normalized) {
      setError('Хештег содержит только недопустимые символы');
      return;
    }

    // Проверяем длину
    if (normalized.length > 40) {
      setError('Хештег не может быть длиннее 40 символов');
      return;
    }

    // Проверяем, что есть хотя бы одна русская буква
    if (!/[а-яё]/i.test(normalized)) {
      setError('Хештег должен содержать хотя бы одну русскую букву');
      return;
    }

    const currentValue = Array.isArray(internalValue) ? internalValue : [];

    // Проверяем дубликаты (сравниваем с учетом преобразования пробелов в подчеркивания)
    if (includesTag(currentValue, normalized)) {
      setError('Такой хештег уже добавлен');
      return;
    }

    if (currentValue.length >= MAX_TAGS) {
      setError(`Максимум ${MAX_TAGS} хештегов`);
      return;
    }

    // Проверяем на запрещенные слова
    if (isWordForbidden(normalized)) {
      setError('Хештег содержит запрещенные слова');
      return;
    }

    // Все проверки пройдены, добавляем хештег (с пробелами для отображения)
    setError('');
    const newValue = [...currentValue, normalized];
    setInternalValue(newValue);
    onChange(newValue);
    setInput('');
    setDropdown(false);
  };

  const removeTag = idx => {
    const currentValue = Array.isArray(internalValue) ? internalValue : [];
    const newValue = currentValue.filter((_, i) => i !== idx);
    setInternalValue(newValue);
    onChange(newValue);
  };

  const handleInput = e => {
    let rawValue = e.target.value;

    // Заменяем подчеркивания на пробелы для визуального отображения
    rawValue = rawValue.replace(/_/g, ' ');

    // Предотвращаем ввод пробела в самом начале
    if (rawValue.startsWith(' ')) {
      rawValue = rawValue.trimStart(); // Убираем пробелы в начале
    }

    // Предотвращаем множественные пробелы в реальном времени
    if (rawValue.includes('  ')) {
      rawValue = rawValue.replace(/\s{2,}/g, ' '); // Заменяем множественные пробелы на одинарные
    }

    // Очищаем от недопустимых символов, но оставляем пробелы
    const cleaned = rawValue
      .toLowerCase() // Приводим к нижнему регистру
      .replace(/[^а-яё0-9\s]/g, '') // Убираем все кроме русских букв, цифр и пробелов
      .slice(0, 40); // Ограничиваем длину

    // Обновляем поле ввода
    setInput(cleaned);
    setDropdown(true);

    // Показываем предупреждение о длине
    if (cleaned.length > 35) {
      setError(`Осталось символов: ${40 - cleaned.length}`);
    } else {
      setError(''); // Очищаем ошибку при вводе
    }
  };

  const handleKeyDown = e => {
    // Предотвращаем ввод пробела в начале
    if (e.key === ' ' && input.length === 0) {
      e.preventDefault();
      return;
    }

    // Предотвращаем двойные пробелы
    if (e.key === ' ' && input.endsWith(' ')) {
      e.preventDefault();
      return;
    }

    // Завершение хештега: Enter, запятая, точка (но НЕ пробел)
    if ([',', '.', 'Enter'].includes(e.key)) {
      e.preventDefault();
      if (input.trim()) addTag(input);
    } else if (e.key === 'Backspace' && !input && Array.isArray(internalValue) && internalValue.length) {
      removeTag(internalValue.length - 1);
    }
  };

  const handleSuggestionClick = s => {
    addTag(s);
  };

  return (
    <div
      className={`custom-hashtag-input-wrapper ${theme}`}
      ref={wrapperRef}
      tabIndex={-1}
    >
      <div className={`custom-hashtag-input-field ${dropdown ? 'active' : ''} ${theme} relative`}
        onClick={() => inputRef.current && inputRef.current.focus()}
      >
        {Array.isArray(internalValue) && internalValue.map((tag, i) => (
          <span className="hashtag-chip" key={tag}>
            <span className="hashtag-chip-label">#{tag}</span>
            <span className="hashtag-chip-close" onClick={e => { e.stopPropagation(); removeTag(i); }}>×</span>
          </span>
        ))}
        <input
          ref={inputRef}
          value={input}
          onChange={handleInput}
          onKeyDown={handleKeyDown}
          placeholder={(Array.isArray(internalValue) ? internalValue.length : 0) >= MAX_TAGS ? '' : 'Введите хештег (русские буквы, цифры, одинарные пробелы)'}
          disabled={(Array.isArray(internalValue) ? internalValue.length : 0) >= MAX_TAGS}
          className="hashtag-input"
          onFocus={() => setDropdown(true)}
        />
        {input && (
          <div className="text-xs text-gray-400 absolute right-2 top-1/2 transform -translate-y-1/2">
            {input.length}/40
          </div>
        )}
      </div>
      {error && (
        <div className="hashtag-error">
          {error}
        </div>
      )}
      {dropdown && suggestions.length > 0 && (
        <div className={`hashtag-dropdown ${theme}`}>
          {loading ? (
            <div className="hashtag-dropdown-item">Загрузка...</div>
          ) : suggestions.map(s => (
            <div className="hashtag-dropdown-item" key={s} onMouseDown={() => handleSuggestionClick(s)}>
              #{s}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}