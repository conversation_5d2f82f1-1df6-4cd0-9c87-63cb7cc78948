import Container from '../components/Container';

function Home() {
  return (
    <Container className="space-y-8 bg-white dark:bg-gray-900 min-h-screen transition-colors duration-300">
      <section className="py-12">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4 text-center">
          Добро пожаловать!
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 text-center">
          ЛитПортал.онлайн (ЛПО) - Платформа для авторов и читателей. Публикуйте свои произведения, находите интересные книги, создавайте свои клубы по интересам и общайтесь с единомышленниками.
        </p>
      </section>
      <section className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Для авторов</h2>
          <p className="text-gray-600 dark:text-gray-300">
            Публикуйте свои произведения, получайте отзывы и находите свою аудиторию.
          </p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Для читателей</h2>
          <p className="text-gray-600 dark:text-gray-300">
            Открывайте для себя новые произведения, следите за любимыми авторами.
          </p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Сообщество</h2>
          <p className="text-gray-600 dark:text-gray-300">
            Общайтесь с другими авторами и читателями, делитесь мнениями.
          </p>
        </div>
      </section>
    </Container>
  );
}

export default Home; 