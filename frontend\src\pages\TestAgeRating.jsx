import React, { useState } from 'react';
import AgeRatingSelector from '../components/AgeRatingSelector';
import { useTheme } from '../theme/ThemeContext';

const TestAgeRating = () => {
  const { theme } = useTheme();
  const [ageRating, setAgeRating] = useState({ age_rating: '0+', has_profanity: false });

  const handleChange = (rating) => {
    console.log('Age rating changed:', rating);
    setAgeRating(rating);
  };

  return (
    <div className={`min-h-screen p-8 ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-100'}`}>
      <div className="max-w-2xl mx-auto">
        <h1 className={`text-3xl font-bold mb-8 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
          Тест компонента возрастных ограничений
        </h1>
        
        <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6`}>
          <h2 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Возрастные ограничения
          </h2>
          
          <AgeRatingSelector
            value={ageRating}
            onChange={handleChange}
          />
        </div>

        <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6`}>
          <h3 className={`text-lg font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Текущие значения:
          </h3>
          <pre className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
            {JSON.stringify(ageRating, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default TestAgeRating;
