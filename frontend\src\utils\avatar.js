/**
 * Получает URL аватара пользователя в зависимости от размера.
 * @param {Object} user - Объект пользователя
 * @param {string} size - Размер аватара ('mini' или 'full')
 * @param {string} backendUrl - URL бэкенда (теперь используется только для пользовательских аватаров)
 * @param {number} imageVersion - Версия изображения
 * @param {boolean} forceFull - Принудительно использовать полный аватар для системных аватаров
 * @returns {string} URL аватара
 */
export const getUserAvatar = (user, size = "mini", backendUrl = '', imageVersion, forceFull = false) => {
  if (!user) {
    return null;
  }

  // Проверяем, удален ли пользователь
  if (user.display_name === 'Аккаунт удален') {
    const base = import.meta.env.VITE_BASE_URL || '';
    if (size === "mini") {
      return `${base}ava_presets/ava_del_s.webp`;
    }
    return `${base}ava_presets/ava_del.webp`;
  }

  let version = imageVersion;
  if (version === undefined || version === null) {
    version = user.avatar_updated_at ? Date.parse(user.avatar_updated_at) : 0;
  }

  // Пользовательский аватар (S3)
  if (user.avatar_type === 2) {
    if (size === "mini" && (user.avatar_thumbnail_url || user.avatar_thumbnail)) {
      const url = user.avatar_thumbnail_url || user.avatar_thumbnail;
      return url ? url + (version ? `?v=${version}` : '') : null;
    }
    if (size === "full" && (user.avatar_url || user.avatar)) {
      const url = user.avatar_url || user.avatar;
      return url ? url + (version ? `?v=${version}` : '') : null;
    }
    // Если пользовательский аватар но нет URL - fallback на системный
  }

  // Системный аватар (S3 static) или если avatar_type не определен/равен 1
  if (user.avatar_type === 1 || user.avatar_type === undefined || user.avatar_type === null) {
    const base = import.meta.env.VITE_BASE_URL || '';
    const gender = (user.gender || 'U').toUpperCase();
    if (size === "mini")
      return `${base}ava_presets/ava_${gender}_s.webp${version ? `?v=${version}` : ''}`;
    return `${base}ava_presets/ava_${gender}.webp${version ? `?v=${version}` : ''}`;
  }

  // Fallback для неопределенных случаев - системный аватар
  const base = import.meta.env.VITE_BASE_URL || '';
  const gender = (user.gender || 'U').toUpperCase();
  if (size === "mini")
    return `${base}ava_presets/ava_${gender}_s.webp`;
  return `${base}ava_presets/ava_${gender}.webp`;
}; 