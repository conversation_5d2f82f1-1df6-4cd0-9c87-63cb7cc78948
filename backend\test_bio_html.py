#!/usr/bin/env python
"""
Тестовый скрипт для проверки сохранения HTML с data-caption в базе данных
"""
import os
import sys
import django

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Настраиваем Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from users.models import User

def test_bio_html_saving():
    """Тестируем сохранение HTML с data-caption"""
    
    # HTML с data-caption атрибутом
    test_html = '''
    <div class="custom-resizable-image align-center" style="position: relative; width: 50%; max-width: 100%;">
        <img src="/media/bio_images/test.jpg" data-caption="Тестовая подпись к изображению" data-width="50" data-align="center" style="width: 100%; max-width: 100%; display: block; border-radius: 8px; height: auto;">
        <div class="image-caption">Тестовая подпись к изображению</div>
    </div>
    <p>Обычный текст после изображения.</p>
    '''
    
    try:
        # Находим первого пользователя для теста
        user = User.objects.first()
        if not user:
            print("Нет пользователей в базе данных")
            return
        
        print(f"Тестируем с пользователем: {user.username}")
        print(f"Исходный HTML:")
        print(test_html)
        print("-" * 50)
        
        # Сохраняем HTML в bio
        user.bio = test_html
        user.save()
        
        # Перезагружаем пользователя из базы
        user.refresh_from_db()
        
        print(f"HTML после сохранения в базу:")
        print(user.bio)
        print("-" * 50)
        
        # Проверяем, сохранился ли data-caption
        if 'data-caption' in user.bio:
            print("✅ Атрибут data-caption сохранился!")
        else:
            print("❌ Атрибут data-caption был удален!")
        
        # Проверяем, сохранился ли div с классом image-caption
        if 'image-caption' in user.bio:
            print("✅ Элемент с классом image-caption сохранился!")
        else:
            print("❌ Элемент с классом image-caption был удален!")
            
        # Проверяем другие data-атрибуты
        if 'data-width' in user.bio:
            print("✅ Атрибут data-width сохранился!")
        else:
            print("❌ Атрибут data-width был удален!")
            
        if 'data-align' in user.bio:
            print("✅ Атрибут data-align сохранился!")
        else:
            print("❌ Атрибут data-align был удален!")
        
    except Exception as e:
        print(f"Ошибка при тестировании: {e}")

if __name__ == "__main__":
    test_bio_html_saving()