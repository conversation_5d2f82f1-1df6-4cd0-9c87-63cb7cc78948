/* Полная поддержка стилей биографии как в BookReader */
.bio-content {
  line-height: 1.5;
  color: inherit;
  white-space: pre-wrap !important;
  word-break: break-word;
  hyphens: auto;
  font-family: inherit;
  overflow: visible;
  width: 100% !important;
  max-width: none !important;
}

.bio-content p {
  margin-top: 0;
  margin-bottom: 0.6em;
  line-height: inherit;
  overflow: visible;
  white-space: pre-wrap !important;
  position: relative;
}

.bio-content p:empty {
  min-height: 1.2em;
  margin-bottom: 0.6em;
}

/* Поддержка красной строки (отступов для абзацев) */
.bio-content.tiptap-indent p {
  text-indent: 1.5em;
}

/* Поддержка всех базовых стилей текста */
.bio-content strong {
  font-weight: bold;
}

.bio-content em {
  font-style: italic;
}

.bio-content u {
  text-decoration: underline;
}

.bio-content s {
  text-decoration: line-through;
}

/* Поддержка выравнивания текста */
.bio-content p[style*="text-align: left"] {
  text-align: left !important;
}

.bio-content p[style*="text-align: center"] {
  text-align: center !important;
}

.bio-content p[style*="text-align: right"] {
  text-align: right !important;
}

.bio-content p[style*="text-align: justify"] {
  text-align: justify !important;
}

/* Поддержка ссылок */
.bio-content a {
  color: #2563eb;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.bio-content a:hover {
  color: #1d4ed8;
}

.dark .bio-content a {
  color: #60a5fa;
}

.dark .bio-content a:hover {
  color: #93c5fd;
}

/* Полная поддержка изображений как в редакторе */
.bio-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1rem auto;
  border-radius: 8px;
}

/* Поддержка выравнивания изображений */
.bio-content img[data-align="left"],
.bio-content .float-left img,
.bio-content .align-left img {
  float: left;
  margin: 0 1rem 1rem 0;
  max-width: 50%;
}

.bio-content img[data-align="right"],
.bio-content .float-right img,
.bio-content .align-right img {
  float: right;
  margin: 0 0 1rem 1rem;
  max-width: 50%;
}

.bio-content img[data-align="center"],
.bio-content .align-center img {
  display: block;
  margin: 1rem auto;
  float: none;
}

/* Поддержка обтекания текстом - КРИТИЧЕСКИ ВАЖНО */
.bio-content img[data-text-wrap="wrap"],
.bio-content img[data-text-wrap="true"],
.bio-content .custom-resizable-image.float-left img,
.bio-content .custom-resizable-image.float-right img,
.bio-content img.float-left,
.bio-content img.float-right {
  shape-outside: margin-box !important;
  -webkit-shape-outside: margin-box !important;
}

/* Поддержка прямых классов на изображениях (как в вашем HTML) */
.bio-content img.float-left {
  float: left !important;
  margin: 0 16px 16px 0 !important;
  clear: none !important;
  max-width: 50%;
}

.bio-content img.float-right {
  float: right !important;
  margin: 0 0 16px 16px !important;
  clear: none !important;
  max-width: 50%;
}

.bio-content img.align-left {
  display: block;
  margin: 1rem 0 1rem 0;
  float: none;
}

.bio-content img.align-right {
  display: block;
  margin: 1rem auto 1rem auto;
  float: none;
}

.bio-content img.align-center {
  display: block;
  margin: 1rem auto;
  float: none;
}

/* Классы выравнивания изображений - точное соответствие редактору */
.bio-content .custom-resizable-image {
  display: block;
  position: relative;
  margin: 16px auto;
  clear: both;
}

/* CSS классы для обтекания (float) - ТОЧНО как в редакторе */
.bio-content .custom-resizable-image.float-left {
  float: left !important;
  margin: 0 16px 16px 0 !important;
  clear: none !important;
  z-index: 10;
  position: relative;
  display: block !important;
}

.bio-content .custom-resizable-image.float-right {
  float: right !important;
  margin: 0 0 16px 16px !important;
  clear: none !important;
  z-index: 10;
  position: relative;
  display: block !important;
}

/* CSS классы для выравнивания без обтекания */
.bio-content .custom-resizable-image.align-left {
  float: none;
  margin: 16px 0 16px 0;
  text-align: left;
  clear: both;
}

.bio-content .custom-resizable-image.align-right {
  float: none;
  margin: 16px 0 16px auto;
  text-align: right;
  clear: both;
}

.bio-content .custom-resizable-image.align-center {
  float: none;
  margin: 16px auto;
  text-align: center;
  clear: both;
}

/* Обеспечиваем правильное обтекание для параграфов после floating изображений */
.bio-content .custom-resizable-image.float-left + p,
.bio-content .custom-resizable-image.float-right + p {
  margin-top: 0 !important;
  clear: none !important;
}

/* Стили для обычного выравнивания (без обтекания) */
.bio-content .custom-resizable-image.align-left + p,
.bio-content .custom-resizable-image.align-right + p,
.bio-content .custom-resizable-image.align-center + p {
  margin-top: 1em !important;
  clear: both !important;
}

/* Очистка float после последнего параграфа */
.bio-content::after {
  content: '';
  display: table;
  clear: both;
}

/* Подписи к изображениям - точно как в редакторе */
.bio-content figcaption,
.bio-content .image-caption {
  font-size: 0.875rem;
  font-style: italic;
  text-align: center;
  color: #6b7280;
  margin-top: 8px;
  margin-bottom: 12px;
  padding: 0;
  line-height: 0.4;
  width: 100%;
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  font-weight: 400;
}



.dark .bio-content figcaption,
.dark .bio-content .image-caption {
  color: #9ca3af;
}

/* Поддержка цветов и размеров шрифтов из редактора */
.bio-content span[style*="color:"] {
  /* Цвет будет применен через inline стили */
}

.bio-content span[style*="background-color:"] {
  /* Цвет фона будет применен через inline стили */
}

.bio-content span[style*="font-size:"] {
  /* Размер шрифта будет применен через inline стили */
}

/* Поддержка темной темы */
.dark .bio-content {
  color: #e5e7eb;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
  .bio-content .custom-resizable-image.float-left,
  .bio-content .custom-resizable-image.float-right,
  .bio-content .custom-resizable-image.align-left,
  .bio-content .custom-resizable-image.align-right {
    float: none !important;
    display: block !important;
    margin: 16px auto !important;
    text-align: center !important;
    max-width: 100% !important;
  }
  
  .bio-content.tiptap-indent p {
    text-indent: 1em;
  }
}

@media (max-width: 480px) {
  .bio-content .custom-resizable-image {
    display: block !important;
    float: none !important;
    margin: 16px auto !important;
    max-width: 100% !important;
  }
}