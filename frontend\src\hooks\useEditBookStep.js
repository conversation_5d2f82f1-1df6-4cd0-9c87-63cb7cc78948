import { useState, useEffect } from 'react';

/**
 * Кастомный хук для управления шагом редактирования книги.
 * @param {object} book - объект книги
 * @param {array} chapters - массив глав (опциональный, для обратной совместимости)
 * @returns {[number, function]} [step, setStep]
 */
export function useEditBookStep(book, chapters) {
  const [step, setStep] = useState(1);
  const [autoStepSet, setAutoStepSet] = useState(false);

  useEffect(() => {
    if (!autoStepSet && book) {
      // ОПТИМИЗИРОВАНО: Используем chapters_count из API книги для мгновенного определения этапа
      if (typeof book.chapters_count === 'number') {
        // Новая логика: используем chapters_count из API
        if (book.chapters_count > 0) {
          setStep(2);
        } else {
          setStep(1);
        }
        setAutoStepSet(true);
      } else if (Array.isArray(chapters)) {
        // Fallback: старая логика для обратной совместимости
        if (chapters.length > 0) {
          setStep(2);
        } else {
          setStep(1);
        }
        setAutoStepSet(true);
      }
    }
  }, [book, chapters, autoStepSet]);

  useEffect(() => {
    setAutoStepSet(false);
  }, [book?.id]);

  return [step, setStep];
}