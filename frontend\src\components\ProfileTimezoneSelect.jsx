import { Select } from 'antd';
import timezones from '../utils/timezones';
import '../theme/timezone-select.css';

const ProfileTimezoneSelect = ({ value, onChange, savedValue }) => {
  // value — это tz (например, 'Europe/Moscow')
  // savedValue — это сохраненное значение value из базы данных для правильного отображения
  // Для Select нужно найти правильный элемент
  let selected;
  if (savedValue) {
    // Если есть savedValue, ищем по нему (это value из базы)
    selected = timezones.find(tz => tz.value === savedValue);
  } else {
    // Иначе ищем по tz, но берем последний найденный (для случая Москва/СПб)
    const matchingTimezones = timezones.filter(tz => tz.tz === value);
    selected = matchingTimezones.length > 0 ? matchingTimezones[matchingTimezones.length - 1] : null;
  }

  return (
    <>
      <label className="text-lg font-semibold text-gray-900 dark:text-gray-100">Часовой пояс:</label>
      <Select
        showSearch
        value={selected ? selected.value : undefined}
        onChange={val => {
          const tzObj = timezones.find(tz => tz.value === val);
          if (tzObj) onChange(tzObj.tz, val); // Передаем и tz, и value
        }}
        options={timezones.map(tz => ({ value: tz.value, label: tz.label }))}
        placeholder="Выберите часовой пояс"
        style={{ maxWidth: 300 }}
        className="profile-timezone-select"
        filterOption={(input, option) =>
          option.label.toLowerCase().includes(input.toLowerCase())
        }
      />
      <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">(Часовой пояс используется для отображения времени на сайте)</span>
    </>
  );
};

export default ProfileTimezoneSelect; 