# Быстрый запуск OAuth авторизации

## 🚀 Краткая инструкция

### 1. Настройте переменные окружения

**Backend** (создайте `.env` в папке `backend/`):
```bash
# Скопируйте из backend/env_example.txt и заполните ваши ключи
VK_CLIENT_ID=ваш_vk_app_id
VK_SECRET_KEY=ваш_vk_protected_key
```

**Frontend** (создайте `.env` в папке `frontend/`):
```bash
# Скопируйте из frontend/env_example.txt и заполните ваши ключи  
VITE_VK_CLIENT_ID=ваш_vk_app_id
```

### 2. В настройках VK ID приложения добавьте:

**Redirect URI:**
- `http://localhost:5173/auth/callback/`

**Разрешенные домены:**
- `localhost:5173`
- `localhost:8000`

### 3. Запустите приложение

```bash
# Backend (Django)
cd backend
python manage.py runserver

# Frontend (React) - в новом терминале
cd frontend  
npm run dev
```

### 4. Протестируйте

1. Откройте http://localhost:5173/login
2. Нажмите кнопку "VK ID"
3. Авторизуйтесь через VK
4. Popup закроется и произойдет вход

## 📋 Что уже готово

✅ **Backend настроен:**
- Django allauth с VK провайдером
- API endpoints для OAuth
- Обработка callback

✅ **Frontend готов:**
- React компонент с OAuth кнопками
- Popup авторизация
- Callback обработка

✅ **Интеграция работает:**
- Автоматическое создание пользователя
- Вход в систему после OAuth
- Сохранение сессии

## 🔧 Возможные проблемы

**"Invalid redirect_uri"** → Проверьте точный URL в настройках VK ID

**"Invalid client_id"** → Проверьте переменные окружения

**Popup блокируется браузером** → Разрешите popup для localhost

## 📚 Подробная документация

- [Настройка VK ID](VK_ID_SETUP.md) - детальная инструкция
- [OAuth провайдеры](oauth_settings.md) - настройка других соц.сетей

## 🎯 Следующие шаги

1. **Для production**: обновите redirect URI на ваш домен
2. **Больше провайдеров**: добавьте Yandex, Google, OK по аналогии  
3. **Кастомизация**: настройте дополнительные поля пользователя 