#!/usr/bin/env python3
"""
Скрипт для смены пароля пользователя.
Может использоваться администратором для сброса/смены паролей.

Использование:
python change_user_password.py <username_or_email> [new_password]

Если новый пароль не указан, будет сгенерирован автоматически.
"""

import os
import sys
import django
from pathlib import Path

# Добавляем путь к проекту Django
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# Настраиваем Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
import secrets
import string

User = get_user_model()

def generate_password(length=12):
    """Генерирует случайный пароль"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for i in range(length))
    return password

def find_user(identifier):
    """Находит пользователя по username или email"""
    try:
        # Сначала пробуем найти по username
        user = User.objects.get(username=identifier)
        return user, 'username'
    except User.DoesNotExist:
        try:
            # Если не найден по username, пробуем по email
            user = User.objects.get(email=identifier)
            return user, 'email'
        except User.DoesNotExist:
            return None, None

def change_user_password(identifier, new_password=None):
    """
    Меняет пароль пользователя
    """
    # Находим пользователя
    user, found_by = find_user(identifier)
    
    if not user:
        print(f"❌ Пользователь с логином/email '{identifier}' не найден!")
        return False
    
    print(f"👤 Найден пользователь:")
    print(f"   ID: {user.id}")
    print(f"   Логин: {user.username}")
    print(f"   Email: {user.email}")
    print(f"   Отображаемое имя: {user.display_name}")
    print(f"   Найден по: {found_by}")
    print()
    
    # Генерируем пароль если не указан
    if not new_password:
        new_password = generate_password()
        print(f"🔑 Сгенерирован новый пароль: {new_password}")
    else:
        print(f"🔑 Установлен указанный пароль: {new_password}")
    
    # Подтверждение
    confirm = input("Продолжить смену пароля? (yes/no): ").lower().strip()
    if confirm not in ['yes', 'y', 'да', 'д']:
        print("Отменено.")
        return False
    
    try:
        # Меняем пароль
        user.set_password(new_password)
        user.save()
        
        print(f"✅ Пароль успешно изменен!")
        print(f"   Пользователь: {user.username}")
        print(f"   Email для входа: {user.email}")
        print(f"   Новый пароль: {new_password}")
        print()
        print("🔓 Пользователь может войти используя:")
        print(f"   Email: {user.email}")
        print(f"   Пароль: {new_password}")
        
        return True
        
    except ValidationError as e:
        print(f"❌ Ошибка валидации: {e}")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False

def list_users():
    """Показывает список всех пользователей"""
    users = User.objects.all().order_by('id')
    
    print("📋 Список всех пользователей:")
    print("=" * 80)
    print(f"{'ID':<5} | {'Логин':<20} | {'Email':<30} | {'Отображаемое имя'}")
    print("=" * 80)
    
    for user in users:
        print(f"{user.id:<5} | {user.username:<20} | {user.email:<30} | {user.display_name}")
    
    print("=" * 80)
    print(f"Всего пользователей: {users.count()}")

def main():
    if len(sys.argv) == 1:
        print("Использование: python change_user_password.py <username_or_email> [new_password]")
        print("Примеры:")
        print("  python change_user_password.py litportalx")
        print("  python change_user_password.py <EMAIL> newpassword123")
        print("  python change_user_password.py --list  # показать всех пользователей")
        return
    
    if sys.argv[1] == '--list':
        list_users()
        return
    
    identifier = sys.argv[1]
    new_password = sys.argv[2] if len(sys.argv) > 2 else None
    
    print("🔐 Смена пароля пользователя...")
    print(f"   Поиск: {identifier}")
    if new_password:
        print(f"   Новый пароль: {new_password}")
    else:
        print("   Новый пароль: будет сгенерирован автоматически")
    print()
    
    success = change_user_password(identifier, new_password)
    
    if not success:
        print("💥 Не удалось сменить пароль.")
        sys.exit(1)

if __name__ == "__main__":
    main() 