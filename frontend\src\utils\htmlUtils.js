/**
 * Утилиты для работы с HTML контентом
 */

/**
 * Создает безопасный HTML для отображения описания книги
 * @param {string} htmlContent - HTML контент
 * @param {number} maxLength - максимальная длина текста (по умолчанию 400)
 * @returns {object} - объект с HTML и флагом обрезки
 */
export function createSafeBookDescription(htmlContent, maxLength = 400) {
  if (!htmlContent) {
    return { html: '', isTruncated: false };
  }

  // Проверяем, содержит ли контент HTML теги
  const hasHtmlTags = /<[^>]+>/.test(htmlContent);

  // Если нет HTML тегов, обрабатываем как обычный текст с сохранением переносов строк
  if (!hasHtmlTags) {
    // Преобразуем переносы строк в <br> теги
    const textWithBreaks = htmlContent
      .replace(/\n\n+/g, '</p><p>') // Двойные переносы как новые параграфы
      .replace(/\n/g, '<br>');      // Одиночные переносы как <br>

    const wrappedText = `<p>${textWithBreaks}</p>`;

    // Обрезаем если нужно
    if (htmlContent.length <= maxLength) {
      return { html: wrappedText, isTruncated: false };
    } else {
      const truncated = htmlContent.substring(0, maxLength);
      const lastSpaceIndex = truncated.lastIndexOf(' ');
      const finalText = lastSpaceIndex > 0 ? truncated.substring(0, lastSpaceIndex) : truncated;

      // Преобразуем переносы строк в <br> теги
      const truncatedWithBreaks = finalText
        .replace(/\n\n+/g, '</p><p>') // Двойные переносы как новые параграфы
        .replace(/\n/g, '<br>');      // Одиночные переносы как <br>

      return {
        html: `<p>${truncatedWithBreaks}…</p>`,
        isTruncated: true
      };
    }
  }

  // Создаем временный элемент для безопасной работы с HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;
  
  // Получаем текстовое содержимое для проверки длины
  const textContent = tempDiv.textContent || tempDiv.innerText || '';
  
  // Если текст короткий, возвращаем как есть
  if (textContent.length <= maxLength) {
    return { 
      html: htmlContent, 
      isTruncated: false 
    };
  }
  
  // Если текст длинный, нужно обрезать с сохранением HTML структуры
  let truncatedHtml = '';
  let currentLength = 0;
  let isTruncated = false;
  
  // Рекурсивная функция для обхода узлов
  function processNode(node) {
    if (currentLength >= maxLength) {
      isTruncated = true;
      return false; // Прекращаем обработку
    }
    
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent;
      const remainingLength = maxLength - currentLength;
      
      if (text.length <= remainingLength) {
        currentLength += text.length;
        return text;
      } else {
        // Обрезаем текст по словам
        const truncatedText = text.substring(0, remainingLength);
        const lastSpaceIndex = truncatedText.lastIndexOf(' ');
        const finalText = lastSpaceIndex > 0 ? truncatedText.substring(0, lastSpaceIndex) : truncatedText;
        currentLength += finalText.length;
        isTruncated = true;
        return finalText;
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const tagName = node.tagName.toLowerCase();
      let result = `<${tagName}`;
      
      // Копируем атрибуты (только безопасные)
      const safeAttributes = ['style', 'class'];
      for (const attr of node.attributes) {
        if (safeAttributes.includes(attr.name.toLowerCase())) {
          // Экранируем значения атрибутов для безопасности
          const escapedValue = attr.value.replace(/"/g, '&quot;');
          result += ` ${attr.name}="${escapedValue}"`;
        }
      }
      result += '>';
      
      // Обрабатываем дочерние узлы
      for (const child of node.childNodes) {
        const childResult = processNode(child);
        if (childResult === false) break; // Прекращаем если достигли лимита
        if (typeof childResult === 'string') {
          result += childResult;
        }
      }
      
      result += `</${tagName}>`;
      return result;
    }
    
    return '';
  }
  
  // Обрабатываем все дочерние узлы
  for (const child of tempDiv.childNodes) {
    const childResult = processNode(child);
    if (childResult === false) break;
    if (typeof childResult === 'string') {
      truncatedHtml += childResult;
    }
  }
  
  // Добавляем многоточие если текст был обрезан
  if (isTruncated) {
    truncatedHtml += '…';
  }
  
  return { 
    html: truncatedHtml, 
    isTruncated 
  };
}

/**
 * Создает безопасный HTML для краткого описания (для карточек книг)
 * @param {string} htmlContent - HTML контент
 * @param {number} maxLength - максимальная длина текста
 * @returns {string} - безопасный HTML
 */
export function createShortDescription(htmlContent, maxLength = 120) {
  const { html } = createSafeBookDescription(htmlContent, maxLength);
  return html;
}

/**
 * Удаляет HTML теги и возвращает чистый текст
 * @param {string} htmlContent - HTML контент
 * @returns {string} - чистый текст
 */
export function stripHtmlTags(htmlContent) {
  if (!htmlContent) return '';
  
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;
  return tempDiv.textContent || tempDiv.innerText || '';
}

/**
 * Проверяет, содержит ли строка HTML теги
 * @param {string} content - контент для проверки
 * @returns {boolean} - true если содержит HTML теги
 */
export function containsHtmlTags(content) {
  if (!content) return false;
  return /<[^>]+>/.test(content);
}
