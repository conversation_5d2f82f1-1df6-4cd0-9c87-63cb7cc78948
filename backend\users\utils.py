from django.contrib.contenttypes.models import ContentType
from .models import FeedEvent
from django.conf import settings
from PIL import Image
import os
from django.core.files.base import ContentFile
import io

def create_feed_event(user, event_type, actor, content_object=None, status='new'):
    """
    Создает событие в ленте пользователя
    
    Args:
        user: Пользователь, для которого создается событие
        event_type: Тип события (из FeedEvent.EVENT_TYPES)
        actor: Пользователь, который вызвал событие
        content_object: Связанный объект (опционально)
        status: Статус события (по умолчанию 'new')
    """
    content_type = None
    object_id = None
    
    if content_object:
        content_type = ContentType.objects.get_for_model(content_object)
        object_id = content_object.id
    
    return FeedEvent.objects.create(
        user=user,
        event_type=event_type,
        actor=actor,
        content_type=content_type,
        object_id=object_id,
        status=status
    )

def create_avatar_thumbnail(user):
    """Create a thumbnail for user's avatar"""
    if not user.avatar:
        return
    
    # Открываем оригинальное изображение
    img = Image.open(user.avatar)
    
    # Определяем размеры для миниатюры
    thumbnail_size = (100, 100)
    
    # Создаем миниатюру
    img.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)
    
    # Сохраняем миниатюру во временный буфер
    buffer = io.BytesIO()
    img.save(buffer, format='WEBP', quality=90)
    
    # Формируем только имя файла для миниатюры
    filename = f"thumbnail_{user.username}.webp"
    
    # Удаляем старую миниатюру, если она существует (независимо от имени)
    if user.avatar_thumbnail:
        user.avatar_thumbnail.delete(save=False)
        user.avatar_thumbnail = None  # Сбросить поле, чтобы storage не думал, что файл ещё есть
    
    # Сохраняем новую миниатюру (перезаписывает старую, если имя совпадает)
    user.avatar_thumbnail.save(filename, ContentFile(buffer.getvalue()), save=False) 