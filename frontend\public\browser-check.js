// Расширенная детекция старых браузеров
(function() {
  'use strict';
  
  // Проверяем, не находимся ли мы уже на странице badbrowser
  if (window.location.pathname.includes('badbrowser.html')) {
    return;
  }
  
  // Проверяем поддержку WebP
  function supportsWebP() {
    try {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    } catch(err) {
      return false;
    }
  }
  
  // Проверяем поддержку современных CSS функций
  function supportsModernCSS() {
    try {
      const testEl = document.createElement('div');
      testEl.style.display = 'grid';
      testEl.style.display = 'flex';
      
      // Проверяем поддержку CSS Grid и Flexbox
      return testEl.style.display === 'flex' && 
             CSS.supports && 
             CSS.supports('display', 'grid');
    } catch(err) {
      return false;
    }
  }
  
  // Проверяем поддержку современных JS функций
  function supportsModernJS() {
    try {
      // Проверяем поддержку ES6+ функций
      return typeof Symbol !== 'undefined' && 
             typeof Promise !== 'undefined' && 
             typeof Map !== 'undefined' && 
             typeof Set !== 'undefined' &&
             typeof Array.prototype.includes !== 'undefined' &&
             typeof Object.assign !== 'undefined' &&
             typeof fetch !== 'undefined' &&
             typeof URLSearchParams !== 'undefined';
    } catch(err) {
      return false;
    }
  }
  
  // Детальная проверка версий браузеров
  function getBrowserInfo() {
    const ua = navigator.userAgent;
    
    // Internet Explorer
    if (ua.indexOf('MSIE') !== -1) {
      const version = ua.match(/MSIE (\d+)/);
      return { name: 'IE', version: version ? parseInt(version[1]) : 0, isOld: true };
    }
    
    if (ua.indexOf('Trident/') !== -1) {
      const version = ua.match(/rv:(\d+)/);
      return { name: 'IE', version: version ? parseInt(version[1]) : 11, isOld: true };
    }
    
    // Edge Legacy
    if (ua.indexOf('Edge/') !== -1) {
      const version = ua.match(/Edge\/(\d+)/);
      const ver = version ? parseInt(version[1]) : 0;
      return { name: 'Edge Legacy', version: ver, isOld: ver < 79 };
    }
    
    // Chrome
    if (ua.indexOf('Chrome/') !== -1 && ua.indexOf('Edg/') === -1) {
      const version = ua.match(/Chrome\/(\d+)/);
      const ver = version ? parseInt(version[1]) : 0;
      return { name: 'Chrome', version: ver, isOld: ver < 60 };
    }
    
    // Firefox
    if (ua.indexOf('Firefox/') !== -1) {
      const version = ua.match(/Firefox\/(\d+)/);
      const ver = version ? parseInt(version[1]) : 0;
      return { name: 'Firefox', version: ver, isOld: ver < 55 };
    }
    
    // Safari
    if (ua.indexOf('Safari/') !== -1 && ua.indexOf('Chrome/') === -1) {
      const version = ua.match(/Version\/(\d+)/);
      const ver = version ? parseInt(version[1]) : 0;
      return { name: 'Safari', version: ver, isOld: ver < 11 };
    }
    
    // Opera
    if (ua.indexOf('OPR/') !== -1 || ua.indexOf('Opera/') !== -1) {
      const version = ua.match(/(?:OPR|Opera)\/(\d+)/);
      const ver = version ? parseInt(version[1]) : 0;
      return { name: 'Opera', version: ver, isOld: ver < 47 };
    }
    
    // Yandex Browser
    if (ua.indexOf('YaBrowser/') !== -1) {
      const version = ua.match(/YaBrowser\/(\d+)/);
      const ver = version ? parseInt(version[1]) : 0;
      return { name: 'Yandex', version: ver, isOld: ver < 18 };
    }
    
    // Неизвестный браузер - считаем старым
    return { name: 'Unknown', version: 0, isOld: true };
  }
  
  // Проверяем мобильные браузеры
  function isMobileBrowser() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }
  
  // Основная функция проверки
  function checkBrowser() {
    const browserInfo = getBrowserInfo();
    const webpSupport = supportsWebP();
    const modernJSSupport = supportsModernJS();
    const modernCSSSupport = supportsModernCSS();
    
    // Логируем информацию для отладки (только в dev режиме)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      console.log('Browser Check:', {
        browser: browserInfo,
        webp: webpSupport,
        modernJS: modernJSSupport,
        modernCSS: modernCSSSupport,
        mobile: isMobileBrowser()
      });
    }
    
    // Определяем, нужно ли редиректить
    const shouldRedirect = browserInfo.isOld || 
                          !webpSupport || 
                          !modernJSSupport || 
                          !modernCSSSupport;
    
    if (shouldRedirect) {
      // Сохраняем информацию о попытке редиректа
      try {
        sessionStorage.setItem('browserCheckRedirect', JSON.stringify({
          browser: browserInfo,
          timestamp: Date.now(),
          reason: {
            oldBrowser: browserInfo.isOld,
            noWebP: !webpSupport,
            noModernJS: !modernJSSupport,
            noModernCSS: !modernCSSSupport
          }
        }));
      } catch(e) {
        // Игнорируем ошибки с sessionStorage
      }
      
      // Редиректим на страницу для старых браузеров
      window.location.href = '/badbrowser.html';
    }
  }
  
  // Запускаем проверку
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', checkBrowser);
  } else {
    checkBrowser();
  }
})();
