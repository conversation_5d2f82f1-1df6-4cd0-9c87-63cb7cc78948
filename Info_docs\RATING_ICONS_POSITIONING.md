# 🎯 Настройка позиции иконок рейтингов и счетчиков

## 📍 Где менять значения для экспериментов

### Файл: `frontend/src/components/ProfileHeader.jsx`

#### 📖 Читательский рейтинг
**Строка ~1218** (ищите комментарий "🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ"):

```jsx
style={{
  filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(0,0,0,0.18))',
  // 🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ: измените значения для смещения иконки
  // offsetX: смещение по горизонтали (+ вправо, - влево)
  // offsetY: смещение по вертикали (+ вниз, - вверх)
  ...getReaderRatingIconOffset(-2, +4), // ← ЗДЕСЬ МЕНЯЙТЕ ПИКСЕЛИ
}}
```

#### ✍️ Авторский рейтинг
**Строка ~1235** (ищите комментарий "🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ"):

```jsx
style={{
  filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(0,0,0,0.18))',
  // 🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ: измените значения для смещения иконки
  // offsetX: смещение по горизонтали (+ вправо, - влево)
  // offsetY: смещение по вертикали (+ вниз, - вверх)
  ...getAuthorRatingIconOffset(-3, +4), // ← ЗДЕСЬ МЕНЯЙТЕ ПИКСЕЛИ
}}
```

#### 👥 Подписчики
**Строка ~1257** (ищите комментарий "🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ"):

```jsx
style={{
  filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(0,0,0,0.18))',
  // 🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ: измените значения для смещения иконки
  // offsetX: смещение по горизонтали (+ вправо, - влево)
  // offsetY: смещение по вертикали (+ вниз, - вверх)
  ...getFollowersIconOffset(-4, +5), // ← ЗДЕСЬ МЕНЯЙТЕ ПИКСЕЛИ
}}
```

#### ❤️ Друзья
**Строка ~1277** (ищите комментарий "🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ"):

```jsx
style={{
  filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(255,0,0,0.25))',
  // 🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ: измените значения для смещения иконки
  // offsetX: смещение по горизонтали (+ вправо, - влево)
  // offsetY: смещение по вертикали (+ вниз, - вверх)
  ...getFriendsIconOffset(0, -2), // ← ЗДЕСЬ МЕНЯЙТЕ ПИКСЕЛИ
}}
```

## ⚙️ Как настраивать смещение

### Параметры функций смещения:

**Для читательского рейтинга:** `getReaderRatingIconOffset(offsetX, offsetY)`  
**Для авторского рейтинга:** `getAuthorRatingIconOffset(offsetX, offsetY)`  
**Для подписчиков:** `getFollowersIconOffset(offsetX, offsetY)`  
**Для друзей:** `getFriendsIconOffset(offsetX, offsetY)`

- **offsetX** - смещение по горизонтали:
  - `+` положительные значения = сдвиг вправо
  - `-` отрицательные значения = сдвиг влево
  
- **offsetY** - смещение по вертикали:
  - `+` положительные значения = сдвиг вниз
  - `-` отрицательные значения = сдвиг вверх

## 📋 Примеры настроек

| Код | Результат |
|-----|-----------|
| `getFunctionOffset(0, 0)` | Без смещения (по умолчанию) |
| `getFunctionOffset(2, 0)` | Сдвиг на 2px вправо |
| `getFunctionOffset(-3, 0)` | Сдвиг на 3px влево |
| `getFunctionOffset(0, -2)` | Сдвиг на 2px вверх |
| `getFunctionOffset(0, 1)` | Сдвиг на 1px вниз |
| `getFunctionOffset(1, -1)` | Сдвиг на 1px вправо и 1px вверх |
| `getFunctionOffset(-2, 2)` | Сдвиг на 2px влево и 2px вниз |

*Где `getFunctionOffset` заменяется на соответствующую функцию: `getReaderRatingIconOffset`, `getAuthorRatingIconOffset` или `getFollowersIconOffset`*

## 🔧 Рекомендуемые значения для тестирования

1. **Точное выравнивание с текстом**: `(0, -1)` или `(0, -2)`
2. **Немного правее**: `(1, -1)` или `(2, -1)`
3. **Немного левее**: `(-1, -1)` или `(-2, -1)`
4. **Поднять выше**: `(0, -3)` или `(0, -4)`
5. **Опустить ниже**: `(0, 1)` или `(0, 2)`

## 💡 Советы по настройке

- Начните с малых значений (±1, ±2 пикселя)
- Протестируйте на разных размерах экрана (мобильные и десктоп)
- Учитывайте, что иконки имеют разные размеры на мобильных и десктопах
- После изменений обновите страницу в браузере для проверки

## 🎨 Текущие настройки

### 📖 Читательский рейтинг
По умолчанию установлено: `getReaderRatingIconOffset(-2, +4)`
- Смещение на 2 пикселя влево
- Смещение на 4 пикселя вниз

### ✍️ Авторский рейтинг
По умолчанию установлено: `getAuthorRatingIconOffset(-3, +4)`
- Смещение на 3 пикселя влево
- Смещение на 4 пикселя вниз

### 👥 Подписчики
По умолчанию установлено: `getFollowersIconOffset(-4, +5)`
- Смещение на 4 пикселя влево
- Смещение на 5 пикселей вниз

### ❤️ Друзья
По умолчанию установлено: `getFriendsIconOffset(0, -2)`
- Без горизонтального смещения
- Смещение на 2 пикселя вверх для лучшего выравнивания с текстом

## 🖼️ Используемые иконки

- **Общий рейтинг**: Динамически изменяется в зависимости от уровня
  - `rating_green.webp` (0-4999 баллов)
  - `rating_blue.webp` (5000-49999 баллов)
  - `rating_red.webp` (50000+ баллов)
- **Читательский рейтинг**: `rating_readers.webp`
- **Авторский рейтинг**: `rating_writers.webp`
- **Подписчики**: `followers.webp`
- **Друзья**: `friends.webp`

Все иконки загружаются с S3: `https://storage.yandexcloud.net/lpo-test/dist/icons/` 