from django.core.management.base import BaseCommand
from books.models import Book
from books.tasks import get_book_cover_folder, get_current_cover_files
from users.models import get_group_folder


class Command(BaseCommand):
    help = 'Debug cover paths for a specific book'

    def add_arguments(self, parser):
        parser.add_argument(
            'book_id',
            type=int,
            help='Book ID to debug',
        )

    def handle(self, *args, **options):
        book_id = options['book_id']
        
        try:
            book = Book.objects.get(id=book_id)
        except Book.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Book {book_id} not found")
            )
            return
        
        self.stdout.write(f"=== Book {book_id} Cover Paths Debug ===")
        
        # Показываем группу и папку
        group_folder = get_group_folder(book_id)
        cover_folder = get_book_cover_folder(book_id)
        
        self.stdout.write(f"Group folder: {group_folder}")
        self.stdout.write(f"Cover folder: {cover_folder}")
        
        # Показываем текущие файлы
        self.stdout.write("\n=== Current Cover Files ===")
        if book.cover:
            self.stdout.write(f"cover: {book.cover.name}")
        else:
            self.stdout.write("cover: None")
            
        if book.cover_mini:
            self.stdout.write(f"cover_mini: {book.cover_mini.name}")
        else:
            self.stdout.write("cover_mini: None")
            
        if book.cover_editor:
            self.stdout.write(f"cover_editor: {book.cover_editor.name}")
        else:
            self.stdout.write("cover_editor: None")
        
        # Показываем что вернет функция get_current_cover_files
        current_files = get_current_cover_files(book)
        self.stdout.write(f"\n=== get_current_cover_files result (with media/public prefix) ===")
        for file_path in current_files:
            self.stdout.write(f"- {file_path}")

        self.stdout.write(f"\nTotal current files: {len(current_files)}")

        # Проверяем совпадения с S3
        self.stdout.write(f"\n=== File Matching Check ===")
        try:
            from books.tasks import get_s3_client, list_s3_objects
            from django.conf import settings

            s3_client = get_s3_client()
            bucket = settings.AWS_STORAGE_BUCKET_NAME
            all_files = list_s3_objects(s3_client, bucket, cover_folder)

            s3_files = {file_obj['Key'] for file_obj in all_files}

            for current_file in current_files:
                if current_file in s3_files:
                    self.stdout.write(f"✓ MATCH: {current_file}")
                else:
                    self.stdout.write(f"✗ NO MATCH: {current_file}")

            unused_files = s3_files - current_files
            if unused_files:
                self.stdout.write(f"\n=== Unused files (would be deleted) ===")
                for unused_file in unused_files:
                    self.stdout.write(f"- {unused_file}")
            else:
                self.stdout.write(f"\nNo unused files found")

        except Exception as e:
            self.stdout.write(f"File matching check failed: {e}")

        # Проверяем S3 напрямую
        self.stdout.write(f"\n=== S3 Check ===")
        try:
            from books.tasks import get_s3_client, list_s3_objects
            from django.conf import settings

            s3_client = get_s3_client()
            bucket = settings.AWS_STORAGE_BUCKET_NAME

            # Проверяем точную папку
            all_files = list_s3_objects(s3_client, bucket, cover_folder)
            self.stdout.write(f"Files in {cover_folder}: {len(all_files)}")
            for file_obj in all_files:
                self.stdout.write(f"  - {file_obj['Key']} (size: {file_obj['Size']})")

            # Проверяем более широкий поиск
            broader_prefix = f"media/public/book_covers/{group_folder}/"
            broader_files = list_s3_objects(s3_client, bucket, broader_prefix)
            self.stdout.write(f"\nFiles in {broader_prefix}: {len(broader_files)}")
            for file_obj in broader_files:
                self.stdout.write(f"  - {file_obj['Key']} (size: {file_obj['Size']})")

            # Также проверяем без media/public префикса
            old_prefix = f"book_covers/{group_folder}/"
            old_files = list_s3_objects(s3_client, bucket, old_prefix)
            self.stdout.write(f"\nFiles in {old_prefix} (old format): {len(old_files)}")
            for file_obj in old_files:
                self.stdout.write(f"  - {file_obj['Key']} (size: {file_obj['Size']})")

        except Exception as e:
            self.stdout.write(f"S3 check failed: {e}")
