from django.core.management.base import BaseCommand
from users.models import User

class Command(BaseCommand):
    help = 'Updates system avatars for existing users based on their gender'

    def handle(self, *args, **options):
        users = User.objects.all()
        updated = 0
        
        for user in users:
            # Если у пользователя нет системного аватара
            if not user.avatar_preset:
                # Если есть пользовательский аватар, очищаем системные поля
                if user.avatar:
                    user.use_custom_avatar()
                # Если нет пользовательского аватара, устанавливаем системный
                else:
                    user.update_system_avatar()
                user.save()
                updated += 1
                self.stdout.write(f'Updated avatar for user {user.username}')
        
        self.stdout.write(self.style.SUCCESS(f'Successfully updated {updated} users')) 