import React from 'react';
import { getRatingIcon, getRatingLevel, formatNumber } from '../utils/ratingIcons';

const RatingIcon = ({ rating, size = 'sm' }) => {
  // Определяем размеры в зависимости от параметра size
  const sizeClasses = {
    xs: 'w-4 h-4',
    sm: 'w-5 h-5', 
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const iconSize = sizeClasses[size] || sizeClasses.sm;
  const ratingValue = rating || 0;

  // Не показываем значок если рейтинг 0
  if (ratingValue === 0) {
    return null;
  }

  // Получаем уровень рейтинга для определения цвета текста
  const level = getRatingLevel(ratingValue);
  
  // Определяем цвет текста в зависимости от уровня рейтинга
  const getTextColor = (levelName) => {
    switch (levelName) {
      case 'Профессиональный':
        return 'text-red-600 dark:text-red-400';     // Красный для профессионального (50000+)
      case 'Повышенный':
        return 'text-blue-600 dark:text-blue-400';   // Синий для повышенного (5000-49999)
      case 'Начальный':
        return 'text-green-600 dark:text-green-400'; // Зеленый для начального (0-4999)
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <div className="flex items-center space-x-1">
      <img
        src={getRatingIcon(ratingValue)}
        alt={`${level} уровень`}
        className={`${iconSize} object-contain`}
        title={`${level} уровень`}
      />
      <span className={`text-sm font-medium ${getTextColor(level)}`}>
        {formatNumber(ratingValue)}
      </span>
    </div>
  );
};

export default RatingIcon; 