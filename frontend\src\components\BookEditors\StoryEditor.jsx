import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON>, Typo<PERSON>, Popconfirm, message, Tooltip, Dropdown, Menu, Switch, Modal, Checkbox, Spin, DatePicker, ConfigProvider, message as antdMessage, Form, Input } from 'antd';
import { EyeInvisibleOutlined, EyeOutlined, EditOutlined, DeleteOutlined, PlusOutlined, FileAddOutlined, ExclamationCircleOutlined, ClockCircleOutlined, MoreOutlined, CloseCircleFilled, HourglassOutlined, FormOutlined, CheckOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import ChapterEditor from '../ChapterEditor';
import { useTheme } from '../../theme/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import { useUserSettings } from '../../context/UserSettingsContext';
import { formatDateWithTimezone } from '../../utils/formatDate';
import { formatPublishedDate, formatUpdatedDate } from '../../utils/dateHelpers';
import dayjs from 'dayjs';
import ru_RU from 'antd/locale/ru_RU';
import 'dayjs/locale/ru';
import mammoth from 'mammoth';
import { useCustomMessage } from '../../hooks/useCustomMessage';

dayjs.locale('ru');

// Стили для убирания синего фокуса в DatePicker
const datePickerStyles = `
  .no-focus-outline .ant-picker-input > input:focus {
    box-shadow: none !important;
    border-color: #d9d9d9 !important;
    outline: none !important;
  }
  .no-focus-outline .ant-picker:focus,
  .no-focus-outline .ant-picker-focused {
    box-shadow: none !important;
    border-color: #d9d9d9 !important;
    outline: none !important;
  }
`;

// Добавляем стили в head (только если еще не добавлены)
if (typeof document !== 'undefined' && !document.querySelector('#story-editor-datepicker-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'story-editor-datepicker-styles';
  styleElement.textContent = datePickerStyles;
  document.head.appendChild(styleElement);
}

const { Title } = Typography;

// Максимальный размер рассказа в символах
const MAX_STORY_LENGTH = 50000;

  const StoryEditor = ({
  bookId,
  book,
  chapters,
  handleSaveChapter,
  chapterModal,
  setChapterModal,
  chapterLoading,
  setChapters,
  onBack,
  fetchBook,
  fetchChapters,
  setChapterLoading,
  isFullWidth = false,
  onToggleFullWidth
}) => {
  // Локальное состояние для книги для мгновенного обновления UI
  const [localBook, setLocalBook] = useState(book);

  // Синхронизируем локальное состояние с пропсом
  useEffect(() => {
    setLocalBook(book);
    setLocalAutoIndent(book?.auto_indent || false);
  }, [book]);

  const [editMode, setEditMode] = useState(null);
  const [isPreface, setIsPreface] = useState(false);
  const [editorKey, setEditorKey] = useState(0);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [scheduleDate, setScheduleDate] = useState(null);
  const [schedulingLoading, setSchedulingLoading] = useState(false);
  const [minScheduleTime, setMinScheduleTime] = useState(null);
  const [showPublishStatusModal, setShowPublishStatusModal] = useState(false);
  const [publishAsFinished, setPublishAsFinished] = useState(true);
  const [publishAction, setPublishAction] = useState(null); // 'immediate' или 'scheduled'
  const [localAutoIndent, setLocalAutoIndent] = useState(book?.auto_indent || false);
  const [addingChapter, setAddingChapter] = useState(false);
  const [editorContent, setEditorContent] = useState('');
  const [buttonStates, setButtonStates] = useState({
    finish: { hover: false, focus: false },
    partial: { hover: false, focus: false },
    draft: { hover: false, focus: false }
  });
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState('');
  const [deleteForm] = Form.useForm();
  const { theme } = useTheme();
  const { timezone } = useUserSettings();
  const navigate = useNavigate();
  const prevChaptersCount = useRef(Array.isArray(chapters) ? chapters.length : 0);
  const editorRef = useRef(null);
  const { user } = useAuth();
  const isOwner = user && localBook?.author?.username === user.username;
  const { showSuccessWithClose, showErrorWithClose, showInfoWithClose } = useCustomMessage();

  const username = localBook?.author?.username || '';
    const coverUrl = 
    localBook?.cover_type === 'custom'
      ? (localBook?.cover_mini_url || '/static/cover_placeholder.jpg')
      : (localBook?.cover_temp_url || '/static/cover_placeholder.jpg');

  const safeChapters = Array.isArray(chapters) ? chapters : [];
  const canAddChapter = true;
  const nextChapterNumber = safeChapters.length === 0 || (isPreface && !safeChapters.some(ch => ch.order === 0)) ? 1 : Math.max(...safeChapters.map(ch => ch.order || 1)) + 1;

  // Функция для подсчета общего количества символов в рассказе
  const getTotalCharacterCount = () => {
    return safeChapters.reduce((total, chapter) => {
      let content = chapter.content || '';
      
      // Если эта глава сейчас редактируется, используем контент из редактора
      if (editMode && chapter.id === editMode && editorContent) {
        content = editorContent;
      }
      
      // Убираем HTML теги для точного подсчета символов
      const plainText = content.replace(/<[^>]*>/g, '');
      return total + plainText.length;
    }, 0);
  };

  // Проверка превышения лимита символов для рассказа
  const totalCharacters = getTotalCharacterCount();
  const isStoryOverLimit = localBook?.type === 'story' && totalCharacters > MAX_STORY_LENGTH;

  const getNextImgIndex = () => {
    let maxIdx = 0;
    safeChapters.forEach(ch => {
      const matches = (ch.content || '').match(/\/media\/users\/[^"']+\.(jpg|jpeg|png|webp)/g);
      if (matches) {
        matches.forEach(url => {
          const m = url.match(/chapter\/(\d+)_\d+_\d+\.(jpg|jpeg|png|webp)/);
          if (m && m[1]) {
            const idx = parseInt(m[1], 10);
            if (idx > maxIdx) maxIdx = idx;
          }
        });
      }
    });
    return maxIdx + 1;
  };

  const handleAddChapter = async (preface = false) => {
    // Защита от двойного клика
    if (addingChapter) return;
    
    setIsPreface(preface);
    const isStory = localBook?.type === 'story';
    let order = 1;
    let chapter = null;
    if (isStory) {
      // Для рассказа ищем главу с order=1 (id может быть любым)
      chapter = safeChapters.find(ch => ch.order === 1);
    } else {
      order = getNextChapterNumber();
      chapter = safeChapters.find(ch => ch.order === order);
    }
    if (chapter) {
      setEditMode(chapter.id);
      setEditorKey(prev => prev + 1);
      setEditorContent(chapter.content || '');
      return;
    }
    
    // Устанавливаем флаг загрузки
    setAddingChapter(true);
    
    try {
      // Если нет главы, создаём новую с order=1 (id будет автоинкремент)
      const csrfToken = getCookie('csrftoken');
      const res = await fetch(`/api/books/${bookId}/chapters/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({
          title: preface ? 'Предисловие' : (localBook?.title || 'Текст'),
          content: '',
          order: preface ? 0 : order,
          is_published: false,
        }),
      });
      
      if (res.ok) {
        const data = await res.json();
        if (typeof setChapters === 'function') {
          setChapters(prev => [...prev, data]);
        }
        setTimeout(() => {
          setEditMode(data.id);
          setEditorKey(prev => prev + 1);
          setEditorContent('');
        }, 100);
      } else {
        message.error('Ошибка создания главы');
      }
    } catch (error) {
      console.error('Error creating chapter:', error);
      message.error('Ошибка создания главы');
    } finally {
      // Снимаем флаг загрузки
      setAddingChapter(false);
    }
  };

  useEffect(() => {
    if (Array.isArray(chapters) && chapters.length < prevChaptersCount.current) {
      setEditMode(null);
      setIsPreface(false);
    }
    prevChaptersCount.current = Array.isArray(chapters) ? chapters.length : 0;
  }, [chapters]);

  // WebSocket подключение для обновления в реальном времени
  useEffect(() => {
    if (!bookId) return;
    
    let wsProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
    let wsHost = window.location.hostname;
    let wsPort = 8000; // порт Daphne
    let wsUrl = `${wsProtocol}://${wsHost}:${wsPort}/ws/books/${bookId}/`;
    let socket = null;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 3;
    let reconnectTimeout = null;

    const connectWebSocket = () => {
      // Задержка перед первой попыткой подключения
      const delay = reconnectAttempts === 0 ? 1000 : 2000 * reconnectAttempts;
      
      reconnectTimeout = setTimeout(() => {
        try {
          socket = new window.WebSocket(wsUrl);

          // Устанавливаем обработчики сразу, до подключения
          socket.onerror = () => {
            // Полностью подавляем ошибки - они нормальны при разработке
          };

          socket.onopen = () => {
            // console.log('WebSocket connected successfully');
            reconnectAttempts = 0; // Сбрасываем счетчик при успешном подключении
          };

          socket.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);
              if (data.type === 'chapters_batch_published') {
                // Обработка массовой публикации (отложенная публикация рассказа)
                const publishedChapterIds = data.chapters.map(ch => ch.id);

                setChapters(prevChapters => prevChapters.map(ch =>
                  publishedChapterIds.includes(ch.id) ? {
                    ...ch,
                    is_published: true,
                    scheduled_publish_at: null,
                    celery_task_id: null,
                    publish_as_finished: null,
                    published_at: new Date().toISOString()
                  } : ch
                ));

                // Обновляем статус книги
                const bookIsFinished = data.book_is_finished || false;
                setLocalBook(prev => ({
                  ...prev,
                  is_published: true,
                  is_finished: bookIsFinished,
                  status: data.book_status || (bookIsFinished ? 'finished' : 'in_progress'),
                  published_at: prev.published_at || new Date().toISOString(),
                  updated_at: new Date().toISOString()
                }));

                // Показываем уведомление о запланированной публикации
                if (data.was_scheduled) {
                  const message = bookIsFinished ? 'Рассказ завершен! Запланированный к публикации рассказ успешно опубликован' : 'Запланированный к публикации рассказ успешно опубликован';
                  antdMessage.success(message);
                }

              } else if (data.type === 'chapters_batch_unpublished') {
                // Обработка массового снятия с публикации (для рассказов)
                const unpublishedChapterIds = data.chapters.map(ch => ch.id);

                setChapters(prevChapters => prevChapters.map(ch =>
                  unpublishedChapterIds.includes(ch.id) ? {
                    ...ch,
                    is_published: false,
                    scheduled_publish_at: null,
                    celery_task_id: null,
                    publish_as_finished: null,
                    published_at: null
                  } : ch
                ));

                // Обновляем статус книги
                setLocalBook(prev => ({
                  ...prev,
                  is_published: data.book_status !== 'draft',
                  is_finished: data.book_is_finished || false,
                  status: data.book_status,
                  updated_at: new Date().toISOString()
                }));

                // Показываем уведомление
                if (data.was_batch_operation) {
                  const message = data.book_status === 'draft' ? 'Рассказ переведен в черновики' : 'Рассказ снят с публикации';
                  antdMessage.success(message);
                }

              } else if (data.type === 'chapter_published') {
                // Обработка одиночной публикации
                setChapters(prevChapters => prevChapters.map(ch =>
                  ch.id === data.chapter_id ? {
                    ...ch,
                    is_published: true,
                    scheduled_publish_at: null,
                    celery_task_id: null,
                    publish_as_finished: null,
                    published_at: new Date().toISOString()
                  } : ch
                ));

                // Мгновенно обновляем локальное состояние книги
                const newStatus = data.book_is_finished ? 'finished' : 'in_progress';
                setLocalBook(prev => ({
                  ...prev,
                  is_published: true,
                  is_finished: data.book_is_finished || false,
                  status: newStatus,
                  published_at: prev.published_at || new Date().toISOString(),
                  updated_at: new Date().toISOString()
                }));

                // Показываем уведомление только если это НЕ часть массовой операции
                if (!data.is_batch_operation) {
                  const message = data.book_is_finished ? 'Рассказ опубликован и завершен' : 'Рассказ переведен в процесс публикации';
                  antdMessage.success(message);
                }
              } else if (data.type === 'chapter_unpublished') {
                setChapters(prevChapters => prevChapters.map(ch =>
                  ch.id === data.chapter_id ? { ...ch, is_published: false, scheduled_publish_at: null } : ch
                ));
                
                // Мгновенно обновляем локальное состояние книги
                setLocalBook(prev => ({
                  ...prev,
                  is_published: false,
                  is_finished: false,
                  status: 'draft'
                }));
                
                // fetchBook больше не нужен - состояние обновляется мгновенно
                
                // Показываем уведомление только если это НЕ часть массовой операции
                if (!data.is_batch_operation) {
                  antdMessage.success('Рассказ снят с публикации');
                }
              }
            } catch (e) {
              // ignore invalid JSON
            }
          };

          socket.onclose = (event) => {
            // Автоматическое переподключение только при необходимости
            if (reconnectAttempts < maxReconnectAttempts && !event.wasClean) {
              reconnectAttempts++;
              // console.log(`WebSocket disconnected, attempting to reconnect... (${reconnectAttempts}/${maxReconnectAttempts})`);
              connectWebSocket(); // Рекурсивный вызов с задержкой внутри
            }
          };

        } catch (error) {
          // Полностью подавляем ошибки создания WebSocket
          if (reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            connectWebSocket();
          }
        }
      }, delay);
    };

    connectWebSocket();

    return () => {
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }
      if (socket) {
        socket.close();
      }
    };
  }, [bookId, fetchBook]);

  const handleSave = async (data) => {
    // Подсчитываем символы с учетом нового контента
    const newContent = data.content || '';
    const plainNewContent = newContent.replace(/<[^>]*>/g, '');

    // Считаем общее количество символов с учетом изменений
    let totalWithNewContent = 0;
    if (editingChapter) {
      // Заменяем контент редактируемой главы
      totalWithNewContent = safeChapters.reduce((total, chapter) => {
        const content = chapter.id === editingChapter.id ? newContent : (chapter.content || '');
        const plainText = content.replace(/<[^>]*>/g, '');
        return total + plainText.length;
      }, 0);
    } else {
      // Добавляем новую главу
      totalWithNewContent = totalCharacters + plainNewContent.length;
    }

    // Проверяем превышение лимита символов для всех рассказов
    if (totalWithNewContent > MAX_STORY_LENGTH) {
      const isPublished = localBook?.is_published || false;
      Modal.error({
        title: `Объем рассказа превышает допустимый`,
        content: (
          <div>
            <p>
              Размер рассказа <strong>{totalWithNewContent.toLocaleString()} символов</strong> превышает
              допустимый лимит <strong>{MAX_STORY_LENGTH.toLocaleString()} символов</strong>.
            </p>
            <p>
              Сохранение невозможно. Рекомендуется сократить текст рассказа{isPublished ? ' или снять его с публикации' : ''}.
            </p>
            <p style={{ color: '#666', fontSize: '14px', marginTop: '12px' }}>
              Превышение: <strong>{(totalWithNewContent - MAX_STORY_LENGTH).toLocaleString()} символов</strong>
            </p>
          </div>
        ),
        okText: 'Понятно',
        onOk: () => {
          // Пользователь остается в редакторе для внесения изменений
        }
      });
      return; // Блокируем сохранение
    }

    // Если размер рассказа в пределах нормы, сохраняем без подтверждения
    await performStorySave(data);
  };

  // Вынесенная функция сохранения для рассказов
  const performStorySave = async (data) => {
    const ok = await handleSaveChapter({
      ...data,
      order: editingChapter ? editingChapter.order : (isPreface ? 0 : nextChapterNumber),
      chapterId: editingChapter ? editingChapter.id : undefined,
      isEdit: !!editingChapter,
    });
    if (ok) {
      // Если редактор в полноэкранном режиме, выходим из него
      if (isFullWidth && onToggleFullWidth) {
        onToggleFullWidth();
      }
      setEditMode(null);
      setIsPreface(false);
      setEditorContent('');
    }
  };

  // Функция для снятия рассказа с публикации
  const updateBookPublishStatus = async (book, shouldPublish) => {
    if (!shouldPublish) {
      // Снимаем с публикации
      try {
        const csrfToken = getCookie('csrftoken');

        const bookResponse = await fetch(`/api/books/${bookId}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({ status: 'draft' }),
        });

        if (!bookResponse.ok) {
          message.error('Ошибка при изменении статуса книги');
          return false;
        }

        // Обновляем локальное состояние
        setChapters(prev => prev.map(ch => ({ ...ch, is_published: false })));
        setLocalBook(prev => ({ ...prev, status: 'draft', is_published: false }));

        message.success('Рассказ снят с публикации');
        return true;
      } catch (error) {
        console.error('Ошибка при снятии с публикации:', error);
        message.error('Ошибка при снятии с публикации');
        return false;
      }
    }
    return true;
  };

  const handleEdit = (chapter) => {
    setIsPreface(chapter.order === 0);
    setEditMode(chapter.id);
    setEditorKey(prev => prev + 1);
    // Инициализируем editorContent текущим содержимым главы
    setEditorContent(chapter.content || '');
  };

  const handleTogglePublish = async (chapter) => {
    if (!chapter.is_published) {
      // Проверяем превышение лимита символов для рассказа
      if (isStoryOverLimit) {
        message.error('Функция недоступна, превышен максимальный размер рассказа');
        return;
      }
      // Если публикуем - показываем модальное окно выбора статуса
      setPublishAction('immediate');
      setPublishAsFinished(true);
      setShowPublishStatusModal(true);
    } else {
      // Если снимаем с публикации - сразу выполняем
      try {
        const csrfToken = getCookie('csrftoken');

        // ОПТИМИЗИРОВАНО: Используем прямое изменение статуса книги на черновик
        // Бэкенд автоматически снимет главу с публикации при изменении статуса на 'draft'
        const bookResponse = await fetch(`/api/books/${bookId}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({ status: 'draft' }),
        });

        if (!bookResponse.ok) {
          message.error('Ошибка при изменении статуса книги');
          return;
        }

        // Мгновенно обновляем локальное состояние до получения WebSocket события
        setChapters(prev => prev.map(ch => 
          ch.id === chapter.id 
            ? { ...ch, is_published: false }
            : ch
        ));
        setLocalBook(prev => ({
          ...prev,
          is_published: false,
          is_finished: false,
          status: 'draft'
        }));
        
        // Уведомление будет показано через WebSocket обработчик
      } catch (error) {
        console.error('Error toggling publish status:', error);
        message.error('Ошибка при изменении статуса публикации');
      }
    }
  };

  // Функции для планирования публикации
  const openScheduleModal = (chapter) => {
    // Проверяем превышение лимита символов для рассказа
    if (isStoryOverLimit) {
      message.error('Функция недоступна, превышен максимальный размер рассказа');
      return;
    }
    setPublishAction('scheduled');
    setPublishAsFinished(true);
    setShowPublishStatusModal(true);
  };

  const handleSchedulePublish = async () => {
    if (!scheduleDate || !safeChapters.length) return;

    // Проверяем превышение лимита символов для рассказа
    if (isStoryOverLimit) {
      message.error('Планирование публикации невозможно: превышен максимальный размер рассказа');
      setShowScheduleModal(false);
      return;
    }

    // Проверяем, не прошло ли уже выбранное время
    const now = dayjs();
    if (scheduleDate.isBefore(now)) {
      message.error('Выбранное время уже прошло. Пожалуйста, выберите корректное время, для публикации.');
      return;
    }
    
    setSchedulingLoading(true);
    
    try {
      const mainChapter = safeChapters[0];
      const csrfToken = getCookie('csrftoken');
      
      // Планируем публикацию главы
      const chapterRes = await fetch(`/api/books/${bookId}/chapters/${mainChapter.id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({ 
          scheduled_publish_at: scheduleDate.second(0).millisecond(0).toISOString(),
          publish_as_finished: publishAsFinished
        }),
      });
      
      if (!chapterRes.ok) {
        throw new Error('Ошибка при планировании публикации главы');
      }
      
      // Для отложенной публикации НЕ меняем статус книги сразу
      // Статус будет изменен автоматически при фактической публикации
      
      // Обновляем локальное состояние
      setChapters(prev => prev.map(ch => 
        ch.id === mainChapter.id 
          ? { ...ch, scheduled_publish_at: scheduleDate.second(0).millisecond(0).toISOString() }
          : ch
      ));
      
      setShowScheduleModal(false);
      setScheduleDate(null);
      setMinScheduleTime(null);
      
      // WebSocket обновит статус книги автоматически при публикации
      
      const statusText = publishAsFinished ? 'как завершенный' : 'как в процессе';
      message.success(`Публикация рассказа запланирована ${statusText}.`);
    } catch (error) {
      console.error('Error scheduling chapter:', error);
      message.error('Ошибка при планировании публикации');
    } finally {
      setSchedulingLoading(false);
    }
  };

  const handleCancelSchedule = async (chapter) => {
    try {
      const csrfToken = getCookie('csrftoken');
      const res = await fetch(`/api/books/${bookId}/chapters/${chapter.id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({ 
          scheduled_publish_at: null,
          publish_as_finished: null
        })
      });
      
      if (res.ok) {
        // Обновляем локальное состояние
        setChapters(prev => prev.map(ch => 
          ch.id === chapter.id 
            ? { ...ch, scheduled_publish_at: null }
            : ch
        ));
        message.success('Отложенная публикация отменена');
      } else {
        message.error('Ошибка при отмене планирования');
      }
    } catch (error) {
      console.error('Error canceling schedule:', error);
      message.error('Ошибка при отмене планирования');
    }
  };

  const handleDelete = async (chapter) => {
    try {
      const csrfToken = getCookie('csrftoken');
      const res = await fetch(`/api/books/${bookId}/chapters/${chapter.id}/`, {
        method: 'DELETE',
        headers: { 'X-CSRFToken': csrfToken },
        credentials: 'include',
      });
      if (res.ok) {
        message.success('Текст удален');
        if (typeof setChapters === 'function') {
          setChapters(prev => prev.filter(ch => ch.id !== chapter.id));
        }
        
        // Если книга была завершена, переводим её в статус "в процессе" при удалении любой главы
        if (localBook?.is_finished) {
          setLocalBook(prev => ({
            ...prev,
            is_finished: false,
            status: 'in_progress'
          }));
          
          // Также обновляем статус на сервере
          try {
            await fetch(`/api/books/${bookId}/`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
              },
              credentials: 'include',
              body: JSON.stringify({ status: 'in_progress' }),
            });
          } catch (err) {
            console.error('Error updating book status:', err);
          }
        }
      } else {
        message.error('Ошибка при удалении текста');
      }
    } catch {
      message.error('Ошибка при удалении текста');
    }
  };

  const getNextChapterNumber = () => {
    const nums = safeChapters.filter(ch => ch.order !== 0).map(ch => ch.order || 1);
    return nums.length === 0 ? 1 : Math.max(...nums) + 1;
  };

  const editingChapter =
    editMode === 'preface'
      ? null
      : editMode === 'new'
      ? null
      : safeChapters.find(ch => ch.id === editMode);

  const BOOK_TYPE_LABELS = {
    story: 'Рассказ',
  };

  // --- Публикация ---
  const isFullyPublished = localBook?.status === 'finished';

    const handlePublish = async () => {
    // Проверяем превышение лимита символов для рассказа
    if (isStoryOverLimit) {
      message.error('Функция недоступна, превышен максимальный размер рассказа');
      return;
    }
    
    // Если есть хотя бы одна глава, показываем модальное окно с выбором статуса
    if (safeChapters.length > 0) {
      setPublishAction('immediate');
      setPublishAsFinished(true);
      setShowPublishStatusModal(true);
    } else {
      // Если глав нет, сначала нужно создать главу
      message.info('Сначала добавьте текст рассказа');
      handleAddChapter(false);
    }
  };

  // Функция для подтверждения публикации с выбранным статусом
  const handleConfirmPublish = async () => {
    if (publishAction === 'immediate') {
      await executeImmediatePublish();
    } else if (publishAction === 'scheduled') {
      // Переходим к выбору времени
      setShowPublishStatusModal(false);
      const minTime = dayjs().add(5, 'minute').second(0).millisecond(0);
      setMinScheduleTime(minTime);
      setScheduleDate(dayjs().add(30, 'minute').second(0).millisecond(0));
      setShowScheduleModal(true);
    }
  };

  // Выполнение немедленной публикации
  const executeImmediatePublish = async () => {
    if (!bookId || !safeChapters.length) return;
    const csrfToken = getCookie('csrftoken');
    
    try {
      // Публикуем главу
      const mainChapter = safeChapters[0];
      const chapterRes = await fetch(`/api/books/${bookId}/chapters/${mainChapter.id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        body: JSON.stringify({ 
          is_published: true,
          publish_as_finished: publishAsFinished
        }),
        credentials: 'include',
      });

      if (!chapterRes.ok) {
        message.error('Ошибка при публикации главы');
        return;
      }

      // Определяем новый статус книги
      const newStatus = publishAsFinished ? 'finished' : 'in_progress';
      
      // Обновляем статус книги для корректного учета рейтинга
      const bookResponse = await fetch(`/api/books/${bookId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({ status: newStatus }),
      });

      if (!bookResponse.ok) {
        message.error('Ошибка при обновлении статуса книги');
        return;
      }

      // Мгновенно обновляем локальное состояние до получения WebSocket события
      setChapters(prev => prev.map(ch => ({ ...ch, is_published: true })));
      setLocalBook(prev => ({
        ...prev,
        is_published: true,
        is_finished: publishAsFinished,
        status: newStatus
      }));
      
      setShowPublishStatusModal(false);
      // Уведомление будет показано через WebSocket обработчик
    } catch (error) {
      console.error('Error publishing story:', error);
      message.error('Ошибка публикации');
    }
  };

  const handleToDraft = async () => {
    if (!bookId || !safeChapters.length) return;
    const csrfToken = getCookie('csrftoken');
    
    try {
      // ОПТИМИЗИРОВАНО: Используем прямое изменение статуса книги на черновик
      // Бэкенд автоматически снимет главу с публикации при изменении статуса на 'draft'
      const bookResponse = await fetch(`/api/books/${bookId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({ status: 'draft' }),
      });

      if (!bookResponse.ok) {
        message.error('Ошибка при изменении статуса книги');
        return;
      }

      // Мгновенно обновляем локальное состояние до получения WebSocket события
      setChapters(prev => prev.map(ch => ({ ...ch, is_published: false })));
      setLocalBook(prev => ({
        ...prev,
        is_published: false,
        is_finished: false,
        status: 'draft'
      }));
      
      // Уведомление будет показано через WebSocket обработчик
    } catch (error) {
      console.error('Error unpublishing story:', error);
      message.error('Ошибка');
    }
  };

  const handleChangeToInProgress = async () => {
    if (!bookId || !safeChapters.length) return;
    const csrfToken = getCookie('csrftoken');
    
    try {
      // Если рассказ не опубликован (черновик), публикуем главу как "в процессе"
      if (!localBook?.is_published) {
        console.log('Публикация рассказа из черновика как "в процессе"');
        const mainChapter = safeChapters[0];
        console.log('Глава для публикации:', mainChapter);
        
        const chapterRes = await fetch(`/api/books/${bookId}/chapters/${mainChapter.id}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          body: JSON.stringify({ 
            is_published: true,
            publish_as_finished: false
          }),
          credentials: 'include',
        });

        if (!chapterRes.ok) {
          const errorData = await chapterRes.json().catch(() => ({}));
          console.error('Ошибка при публикации главы:', chapterRes.status, errorData);
          message.error('Ошибка при публикации главы');
          return;
        }

        console.log('Рассказ успешно опубликован как "в процессе"');
        
        // Мгновенно обновляем локальное состояние
        setChapters(prev => prev.map(ch => ({ ...ch, is_published: true })));
        setLocalBook(prev => ({
          ...prev,
          is_published: true,
          is_finished: false,
          status: 'in_progress'
        }));
        
        // Уведомление будет показано через WebSocket обработчик
      } else {
        // Если уже опубликован и завершен, просто меняем статус на "в процессе"
        console.log('Изменение статуса рассказа на "в процессе"');
        
        // Важно: используем status вместо is_finished чтобы правильно обрабатывался переход в rating_service
        const response = await fetch(`/api/books/${bookId}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({ status: 'in_progress' }),
        });
        
        if (response.ok) {
          console.log('Статус рассказа успешно изменен на "в процессе"');
          setLocalBook(prev => ({
            ...prev,
            is_finished: false,
            status: 'in_progress'
          }));
          message.success('Рассказ переведен в процесс публикации');
        } else {
          const errorData = await response.json().catch(() => ({}));
          console.error('Ошибка при изменении статуса:', response.status, errorData);
          message.error('Ошибка при изменении статуса');
        }
      }
    } catch (error) {
      console.error('Error changing status:', error);
      message.error('Ошибка при изменении статуса');
    }
  };

  const handleFinishStory = async () => {
    if (!bookId || !safeChapters.length) return;
    
    // Проверяем превышение лимита символов для рассказа
    if (isStoryOverLimit) {
      message.error('Функция недоступна, превышен максимальный размер рассказа');
      return;
    }
    
    const csrfToken = getCookie('csrftoken');
    
    try {
      // Если рассказ не опубликован (черновик), публикуем главу как завершенную
      if (!localBook?.is_published) {
        console.log('Публикация рассказа из черновика как завершенного');
        const mainChapter = safeChapters[0];
        console.log('Глава для публикации:', mainChapter);
        
        const chapterRes = await fetch(`/api/books/${bookId}/chapters/${mainChapter.id}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          body: JSON.stringify({ 
            is_published: true,
            publish_as_finished: true
          }),
          credentials: 'include',
        });

        if (!chapterRes.ok) {
          const errorData = await chapterRes.json().catch(() => ({}));
          console.error('Ошибка при публикации рассказа:', chapterRes.status, errorData);
          message.error('Ошибка при публикации рассказа');
          return;
        }

        console.log('Рассказ успешно опубликован как завершенный');
        
        // Мгновенно обновляем локальное состояние
        setChapters(prev => prev.map(ch => ({ ...ch, is_published: true })));
        setLocalBook(prev => ({
          ...prev,
          is_published: true,
          is_finished: true,
          status: 'finished'
        }));
        
        // Уведомление будет показано через WebSocket обработчик
      } else {
        // Если уже опубликован в процессе, просто меняем статус на завершенный
        console.log('Изменение статуса рассказа на завершенный');
        
        // Важно: используем status вместо is_finished чтобы правильно обрабатывался переход в rating_service
        const response = await fetch(`/api/books/${bookId}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({ status: 'finished' }),
        });
        
        if (response.ok) {
          console.log('Статус рассказа успешно изменен на завершенный');
          setLocalBook(prev => ({
            ...prev,
            is_finished: true,
            status: 'finished'
          }));
          message.success('Рассказ завершен');
        } else {
          const errorData = await response.json().catch(() => ({}));
          console.error('Ошибка при завершении рассказа:', response.status, errorData);
          message.error('Ошибка при завершении рассказа');
        }
      }
    } catch (error) {
      console.error('Error finishing story:', error);
      message.error('Ошибка при завершении рассказа');
    }
  };

  const handleDeleteClick = () => {
    setIsDeleteModalVisible(true);
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalVisible(false);
    setDeleteConfirmation('');
    deleteForm.resetFields();
  };

  const handleDeleteConfirm = async () => {
    if (!localBook) return;

    try {
      const csrfToken = getCookie('csrftoken');
      if (!csrfToken) {
        throw new Error('CSRF token not found');
      }

      const response = await fetch(`/api/books/${localBook.id}/`, {
        method: 'DELETE',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete book');
      }

      message.success('Произведение успешно удалено');
      handleDeleteCancel();
      // Переходим к странице со списком книг пользователя
      navigate(`/lpu/${username}/books`);
    } catch (error) {
      message.error('Ошибка при удалении произведения');
    }
  };

  const handleButtonState = (buttonKey, stateType, value) => {
    setButtonStates(prev => ({
      ...prev,
      [buttonKey]: {
        ...prev[buttonKey],
        [stateType]: value
      }
    }));
  };

  const renderStatusActions = () => {
    if (!isOwner) return null;
    
    const btnStyle = { 
      fontSize: 13, 
      fontWeight: 500, 
      padding: '4px 8px', 
      display: 'flex', 
      alignItems: 'center', 
      height: 32, 
      justifyContent: 'flex-start',
      border: `1px solid ${theme === 'dark' ? '#374151' : '#d1d5db'}`,
      borderRadius: 6
    };

    const getButtonStyle = (buttonKey, baseColor, isDisabled = false) => {
      const state = buttonStates[buttonKey];
      const isActive = state.hover || state.focus;
      
      return {
        ...btnStyle,
        color: isDisabled ? '#9ca3af' : baseColor,
        cursor: isDisabled ? 'not-allowed' : 'pointer',
        borderColor: isActive && !isDisabled ? baseColor : (isDisabled ? '#e5e7eb' : (theme === 'dark' ? '#374151' : '#d1d5db')),
        borderWidth: isActive && !isDisabled ? '2px' : '1px',
        transition: 'all 0.2s ease'
      };
    };
    
    const buttons = [];
    
    // Кнопка "Завершить рассказ" - для черновиков и в процессе публикации (только если есть главы)
    if (safeChapters.length > 0 && (localBook?.status === 'draft' || localBook?.status === 'in_progress')) {
      buttons.push(
        <Tooltip
          key="finish-story-tooltip"
          title={isStoryOverLimit ? "Превышен максимальный размер рассказа (50,000 символов)" : ""}
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <Button
            key="finish-story"
            type="link"
            style={getButtonStyle('finish', '#22c55e', isStoryOverLimit)}
            icon={<CheckOutlined style={{ color: isStoryOverLimit ? '#9ca3af' : '#22c55e', fontSize: 18, marginRight: 8 }} />}
            onClick={handleFinishStory}
            disabled={isStoryOverLimit}
            onMouseEnter={() => handleButtonState('finish', 'hover', true)}
            onMouseLeave={() => handleButtonState('finish', 'hover', false)}
            onFocus={() => handleButtonState('finish', 'focus', true)}
            onBlur={() => handleButtonState('finish', 'focus', false)}
          >
            Завершить рассказ
          </Button>
        </Tooltip>
      );
    }
    
    // Кнопка "Опубликовать частично" или "В процесс публикации" - для черновиков и полностью опубликованных (только если есть главы)
    if (safeChapters.length > 0 && (localBook?.status === 'draft' || localBook?.status === 'finished')) {
      const buttonText = localBook?.status === 'finished' ? 
        "В процесс публикации" : "Опубликовать частично";
        
      buttons.push(
        <Tooltip
          key="change-to-progress-tooltip"
          title={isStoryOverLimit ? "Превышен максимальный размер рассказа (50,000 символов)" : ""}
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <Button
            key="change-to-progress"
            type="link"
            style={getButtonStyle('partial', '#60A5FA', isStoryOverLimit)}
            icon={<FormOutlined style={{ color: isStoryOverLimit ? '#9ca3af' : '#60A5FA', fontSize: 18, marginRight: 8 }} />}
            onClick={handleChangeToInProgress}
            disabled={isStoryOverLimit}
            onMouseEnter={() => handleButtonState('partial', 'hover', true)}
            onMouseLeave={() => handleButtonState('partial', 'hover', false)}
            onFocus={() => handleButtonState('partial', 'focus', true)}
            onBlur={() => handleButtonState('partial', 'focus', false)}
          >
            {buttonText}
          </Button>
        </Tooltip>
      );
    }
    
    // Кнопка "Убрать в черновики" - для в процессе публикации и полностью опубликованных
    if (localBook?.status === 'in_progress' || localBook?.status === 'finished') {
      buttons.push(
        <Button
          key="to-draft"
          type="link"
          style={getButtonStyle('draft', '#ef4444')}
          icon={<CloseCircleFilled style={{ color: '#ef4444', fontSize: 18, marginRight: 8 }} />}
          onClick={handleToDraft}
          onMouseEnter={() => handleButtonState('draft', 'hover', true)}
          onMouseLeave={() => handleButtonState('draft', 'hover', false)}
          onFocus={() => handleButtonState('draft', 'focus', true)}
          onBlur={() => handleButtonState('draft', 'focus', false)}
        >
          Убрать в черновики
        </Button>
      );
    }
    
    if (buttons.length === 0) return null;
    
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: 6, marginTop: 8 }}>
        {buttons}
      </div>
    );
  };

  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  // --- Подсчет знаков и АЛ ---
  function getTextStats(text) {
    if (!text) return { count: 0, formatted: '0', al: '0.00' };
    // Удаляем html-теги
    const plain = text.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ');
    const count = plain.length;
    const formatted = count.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
    const al = (count / 40000).toFixed(2);
    return { count, formatted, al };
  }
  const mainChapter = safeChapters[0];
  const stats = getTextStats(mainChapter?.content || '');

  // --- DOCX загрузка ---
  // Удаляем старую логику и состояния docx
  // Новый способ: отправка docx на backend
  const handleBackendDocxUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    if (!/\.docx$/.test(file.name.toLowerCase())) {
      message.error('Поддерживаются только файлы .docx');
      return;
    }

    // Жесткая проверка размера файла для рассказов
    const fileSizeInMB = file.size / (1024 * 1024);
    if (fileSizeInMB > 10) { // Жесткий лимит 10 МБ для рассказов
      Modal.error({
        title: 'Файл слишком большой',
        content: (
          <div>
            <p>Размер файла <strong>{fileSizeInMB.toFixed(1)} МБ</strong> превышает максимально допустимый размер <strong>10 МБ</strong> для рассказов.</p>
            <p>Рекомендуется:</p>
            <ul style={{ marginTop: 8, marginBottom: 8 }}>
              <li>Удалить лишние изображения из документа</li>
              <li>Сжать изображения в документе</li>
              <li>Упростить форматирование документа</li>
              <li>Разделить большой текст на несколько рассказов</li>
            </ul>
          </div>
        ),
      });
      // Очищаем input для возможности выбора другого файла
      e.target.value = '';
      return;
    }

    message.loading({ content: 'Загрузка и обработка файла...', key: 'docx' });
    try {
      const formData = new FormData();
      formData.append('file', file);
      const csrfToken = getCookie('csrftoken');
      const res = await fetch(`/api/books/${bookId}/upload_docx/`, {
        method: 'POST',
        headers: { 'X-CSRFToken': csrfToken },
        body: formData,
        credentials: 'include',
      });
      let data = {};
      try {
        data = await res.json();
      } catch {}
      if (!res.ok) {
        const errorMsg = data.error || data.detail || data.message || 'Ошибка загрузки или обработки файла';
        showErrorWithClose(errorMsg, { key: 'docx', duration: 0 });
        return;
      }
      console.log('Backend response:', data); // Отладочная информация
      if (!data.html) {
        console.error('No html in response:', data); // Отладочная информация
        showErrorWithClose('Ошибка: backend не вернул текст', { key: 'docx', duration: 0 });
        return;
      }
      
      // Если книга была завершена, переводим её в статус "в процессе" при загрузке нового файла
      if (localBook?.status === 'finished') {
        try {
          await fetch(`/api/books/${bookId}/`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': csrfToken,
            },
            credentials: 'include',
            body: JSON.stringify({ status: 'in_progress' }),
          });
          
          // Обновляем локальное состояние
          setLocalBook(prev => ({
            ...prev,
            is_finished: false,
            status: 'in_progress'
          }));
        } catch (err) {
          console.error('Error updating book status:', err);
        }
      }
      
      // Для рассказа: если глава есть - перезаписываем содержимое, если нет - создаём новую
      const mainChapter = safeChapters.find(ch => ch.order === 1);
      if (mainChapter) {
        // Перезаписываем содержимое существующей главы
        const res2 = await fetch(`/api/books/${bookId}/chapters/${mainChapter.id}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({ content: data.html }),
        });
        if (res2.ok) {
          showSuccessWithClose('Текст успешно заменен', { key: 'docx', duration: 0 });
          // Обновляем локальное состояние
          const updatedChapter = await res2.json();
          setChapters(prev => prev.map(ch => ch.id === mainChapter.id ? updatedChapter : ch));
          if (typeof fetchChapters === 'function') await fetchChapters();
        } else {
          showErrorWithClose('Ошибка обновления главы', { key: 'docx', duration: 0 });
        }
      } else {
        // Создаём новую главу
        const res2 = await fetch(`/api/books/${bookId}/chapters/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({
            title: localBook?.title || 'Текст',
            content: data.html,
            order: 1,
            is_published: false,
          }),
        });
        if (res2.ok) {
          const data2 = await res2.json();
          if (typeof setChapters === 'function') {
            setChapters(prev => [...prev, data2]);
          }
          if (typeof fetchChapters === 'function') {
            await fetchChapters();
          }
          showSuccessWithClose('Рассказ успешно создан', { key: 'docx', duration: 0 });
        } else {
          showErrorWithClose('Ошибка создания рассказа', { key: 'docx', duration: 0 });
        }
      }
    } catch (err) {
      showErrorWithClose('Ошибка: ' + (err?.message || err), { key: 'docx', duration: 0 });
    }
  };

  const onSwitchChange = async (checked) => {
    if (!bookId) return;
    const csrfToken = getCookie('csrftoken');
    const newValue = checked;
    
    try {
      const response = await fetch(`/api/books/${bookId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({ auto_indent: newValue }),
      });
      
      if (response.ok) {
        setLocalAutoIndent(newValue);
        setLocalBook(prev => ({
          ...prev,
          auto_indent: newValue
        }));
      } else {
        message.error('Ошибка при изменении настройки автоматической красной строки');
      }
    } catch (error) {
      console.error('Error updating auto_indent:', error);
      message.error('Ошибка при изменении настройки автоматической красной строки');
    }
  };



  // Утилита для приведения HTML к относительным путям
  const toRelative = html =>
    html ? html.replace(/https?:\/\/[^\"]+(\/media\/private\/book_pics\/[\w\/-]+\/chapics\/(?:[\w\/-]*\/)?[\w\d_]+\.(jpg|jpeg|png|gif|webp))/g, '$1') : '';

  return (
    <div className={theme === 'dark' ? 'bg-gray-900 text-gray-100' : 'bg-gray-100 text-gray-900'} style={{ borderRadius: isFullWidth ? 0 : 16, padding: isFullWidth ? 0 : 24, position: 'relative' }}>
      {/* Заголовок страницы */}
      <div className="mb-4 text-center" style={{ display: isFullWidth ? 'none' : 'block' }}>
        <Title level={3} style={{ color: theme === 'dark' ? '#fff' : '#222', marginBottom: 16 }}>2. Работа с текстом рассказа</Title>
      </div>
      
      {/* Кнопка удаления в правом верхнем углу */}
      {!isFullWidth && (
        <div style={{ position: 'absolute', top: 16, right: 16, zIndex: 10 }}>
          <Tooltip key="delete-book-tooltip" title="Удалить произведение" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleDeleteClick}
              className={theme === 'dark' ? 'dark-action-btn' : ''}
            />
          </Tooltip>
        </div>
      )}
      {!isFullWidth && (
        <div className="flex flex-row items-start mb-6">
        <div className="relative group cursor-pointer mr-4" style={{ width: 160, height: 240 }}>
          {/* Основная обложка книги */}
          <div className="relative h-full bg-gray-200 dark:bg-gray-700 overflow-hidden shadow-lg transform transition-transform duration-300 group-hover:scale-105" 
               style={{
                 borderRadius: '0 8px 8px 0'
               }}>
            <img
              src={coverUrl}
              alt="cover"
              className="w-full h-full object-cover"
            />
            
            {/* Корешок книги - левая полоска */}
            <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-r from-black/30 via-black/10 to-transparent"></div>
            
            {/* Дополнительная тень для глубины */}
            <div className="absolute left-1 top-0 bottom-0 w-1 bg-gradient-to-r from-black/20 to-transparent"></div>
            
            {/* Блик на обложке */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
          
          {/* Тень под книгой */}
          <div className="absolute -bottom-1 left-1 right-2 h-1 bg-black/20 rounded-full blur-sm transform transition-transform duration-300 group-hover:scale-110"></div>
        </div>
        <div>
          <div className="text-xl font-bold mb-1" style={{ color: theme === 'dark' ? '#fff' : '#222' }}>{localBook?.title}</div>
          <div className="text-sm flex items-center gap-2" style={{ color: theme === 'dark' ? '#a1a1aa' : '#888', fontStyle: 'italic' }}>
            {BOOK_TYPE_LABELS[localBook?.type] || ''}
            {/* Разделитель и знаки/АЛ */}
            {mainChapter && stats.count > 0 && (
              <>
                <span className="mx-2 text-gray-400">|</span>
                <span style={{ color: theme === 'dark' ? '#a1a1aa' : '#888', fontStyle: 'italic' }}>{stats.formatted} зн.</span>
                <Tooltip key="author-sheet-tooltip" title="Авторский лист = 40 000 знаков" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                  <span style={{ color: '#38bdf8', fontStyle: 'italic', marginLeft: 8 }}>
                    АЛ: {stats.al}
                    <InfoCircleOutlined style={{ color: theme === 'dark' ? '#60a5fa' : '#2563eb', marginLeft: 6, fontSize: 14 }} />
                  </span>
                </Tooltip>
              </>
            )}
          </div>
          <div className="text-xs mt-1" style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
              {!localBook?.is_published ? (
                <>
                  <ClockCircleOutlined style={{ color: '#f59e42', fontSize: 15, marginRight: 4 }} />
                  <span style={{ color: '#f59e42', fontWeight: 600 }}>Черновик</span>
                </>
              ) : !localBook?.is_finished ? (
                <>
                  <EditOutlined style={{ color: '#60A5FA', fontSize: 16, marginRight: 4 }} />
                  <span style={{ color: '#60A5FA', fontWeight: 600 }}>В процессе публикации</span>
                </>
              ) : (
                <>
                  <span style={{ color: '#22c55e', fontSize: 18, marginRight: 4 }}>✔</span>
                  <span style={{ color: '#22c55e', fontWeight: 600 }}>Завершено</span>
                </>
              )}
            </div>
            {localBook?.status === 'in_progress' && localBook?.updated_at && (
              <div className="text-xs" style={{ color: theme === 'dark' ? '#9ca3af' : '#6b7280' }}>
                {formatUpdatedDate(localBook.updated_at, timezone)}
              </div>
            )}
            {localBook?.status === 'finished' && localBook?.published_at && (
              <div className="text-xs" style={{ color: theme === 'dark' ? '#9ca3af' : '#6b7280' }}>
                {formatPublishedDate(localBook.published_at, timezone)}
              </div>
            )}
          </div>
          {renderStatusActions()}
        </div>
      </div>
      )}
      <div className="mb-4 flex items-center gap-2 ml-1" style={{ display: isFullWidth ? 'none' : 'flex' }}>
        <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Загрузить свой файл (docx):</span>
        <Tooltip
          key="upload-docx-tooltip"
          title={localBook?.status === 'finished' ?
            'Для загрузки и замены текста, переведите произведение в статус черновика и загрузите новый файл. Внимание: Весь текст рассказа будет заменен.' : 
            safeChapters.length > 0 ? 'Внимание! Весь текст рассказа будет заменен.' : 'Загрузите DOCX файл для создания текста рассказа.'}
          placement="top"
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <input
            type="file"
            accept=".docx"
            onChange={handleBackendDocxUpload}
            disabled={localBook?.status === 'finished'}
            className="block text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:opacity-60"
            style={{ cursor: (localBook?.status === 'finished') ? 'not-allowed' : undefined }}
          />
        </Tooltip>
      </div>
      {isOwner && (
        <div className="flex items-center gap-3 mb-4 ml-1" style={{ display: isFullWidth ? 'none' : 'flex' }}>
          <Switch
            checked={localAutoIndent}
            onChange={onSwitchChange}
            className={theme === 'dark' ? 'bg-[#374151]' : 'bg-gray-300'}
            style={{ minWidth: 44 }}
          />
          <span className="text-sm font-medium" style={{ color: theme === 'dark' ? '#fff' : '#222' }}>
            Автоматическая красная строка для абзацев
          </span>
        </div>
      )}
      <div className="mt-8" style={{ display: isFullWidth ? 'none' : 'block' }}>
        <div className="flex items-center justify-between mb-4">
          <Title level={4} style={{ color: theme === 'dark' ? '#fff' : '#222', marginBottom: 0 }}>Текст книги</Title>
          {!isFullWidth && safeChapters.length === 0 && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleAddChapter(false)}
              loading={addingChapter}
            >
              Добавить текст
            </Button>
          )}
        </div>
        <div style={{ display: 'grid', gridTemplateColumns: '56px 1fr', gap: 0 }}>
          {safeChapters.sort((a, b) => (a.order || 1) - (b.order || 1)).map((ch, idx) => (
            <React.Fragment key={ch.id}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: 56,
                background: theme === 'dark' ? '#181c23' : '#e5e7eb',
                color: theme === 'dark' ? '#f59e42' : '#b45309',
                fontWeight: 700,
                fontSize: 20,
                borderRadius: '12px 0 0 12px',
                marginBottom: 8,
                border: theme === 'dark' ? '1.5px solid #23272f' : '1.5px solid #e5e7eb',
              }}>★</div>
              <div style={{
                background: theme === 'dark' ? '#23272f' : '#f9fafb',
                borderRadius: '0 12px 12px 0',
                marginBottom: 8,
                color: theme === 'dark' ? '#fff' : '#222',
                display: 'flex',
                alignItems: 'center',
                minHeight: 56,
                boxShadow: theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22',
              }}>
                <span style={{ paddingLeft: 16, flex: 1, textAlign: 'left' }}><b>{ch.title}</b></span>
                <div style={{ display: 'flex', gap: 8, marginRight: 12 }}>
                  {mainChapter && stats.count > 0 && (
                    <span style={{ color: theme === 'dark' ? '#a1a1aa' : '#888', fontSize: 13, marginRight: 8, minWidth: 70, textAlign: 'right', display: 'flex', alignItems: 'center', height: 40 }}>{stats.formatted} зн.</span>
                  )}
                  <div style={{ display: 'flex', alignItems: 'center', height: 40, gap: 8 }}>
                    <Tooltip key={`edit-tooltip-${ch.id}`} title="Редактировать" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                      <Button icon={<EditOutlined />} onClick={() => handleEdit(ch)} className={theme === 'dark' ? 'dark-action-btn' : ''} />
                    </Tooltip>
                    <Tooltip 
                      key={`publish-tooltip-${ch.id}`}
                      title={
                        ch.is_published 
                          ? 'Снять с публикации' 
                          : (isStoryOverLimit ? 'Функция недоступна, превышен максимальный размер рассказа' : 'Опубликовать рассказ')
                      } 
                      classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
                    >
                      <Button
                        icon={
                          ch.is_published
                            ? <EyeOutlined style={{ color: '#22c55e', fontSize: 20 }} />
                            : <EyeInvisibleOutlined style={{ color: isStoryOverLimit ? '#6b7280' : '#f59e42', fontSize: 20 }} />
                        }
                        onClick={() => handleTogglePublish(ch)}
                        className={theme === 'dark' ? 'dark-action-btn' : ''}
                        style={{ marginRight: 0 }}
                        disabled={!ch.is_published && isStoryOverLimit}
                      />
                    </Tooltip>
                    <Tooltip 
                      key={`schedule-tooltip-${ch.id}`}
                      title={
                        isStoryOverLimit 
                          ? 'Функция недоступна, превышен максимальный размер рассказа' 
                          : 'Запланировать публикацию'
                      } 
                      classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
                    >
                      <Button
                        icon={<HourglassOutlined style={{ color: isStoryOverLimit ? '#6b7280' : '#60A5FA', fontSize: 20 }} />}
                        className={theme === 'dark' ? 'dark-action-btn' : ''}
                        style={{ display: ch.is_published ? 'none' : undefined }}
                        onClick={() => openScheduleModal(ch)}
                        disabled={ch.is_published || isStoryOverLimit}
                      />
                    </Tooltip>
                    <Popconfirm
                      key={`delete-popconfirm-${ch.id}`}
                      icon={null}
                      title={
                        <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                          <ExclamationCircleOutlined style={{ color: '#f59e42', fontSize: 18 }} />
                          <span style={{ color: theme === 'dark' ? '#fff' : '#222', fontWeight: 600 }}>Удалить текст?</span>
                        </span>
                      }
                      description={<span style={{ color: '#f59e42', fontSize: 13 }}>Текст и загруженные к нему изображения будут удалены.</span>}
                      onConfirm={() => handleDelete(ch)}
                      okText="Удалить"
                      okButtonProps={{ danger: true, style: { background: theme === 'dark' ? '#ef4444' : '#ef4444', color: '#fff', border: 'none' } }}
                      cancelText="Отмена"
                      cancelButtonProps={{ style: { background: theme === 'dark' ? '#23272f' : '#fff', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb' } }}
                      classNames={{ root: theme === 'dark' ? 'dark-popconfirm' : '' }}
                    >
                      <Tooltip key={`delete-tooltip-${ch.id}`} title="Удалить текст" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                        <Button icon={<DeleteOutlined />} danger className={theme === 'dark' ? 'dark-action-btn' : ''} />
                      </Tooltip>
                    </Popconfirm>
                  </div>
                </div>
              </div>
              {/* Блок с информацией о запланированной публикации */}
              {!ch.is_published && ch.scheduled_publish_at && (
                <div style={{
                  margin: '2px 0 14px 56px',
                  fontSize: 13,
                  color: theme === 'dark' ? '#60A5FA' : '#2563eb',
                  gridColumn: '1 / span 2',
                  fontStyle: 'italic'
                }}>
                  <HourglassOutlined style={{ color: theme === 'dark' ? '#60A5FA' : '#2563eb', fontSize: 15, marginRight: 6 }} />
                  {ch.title || 'Рассказ'}, запланирована публикация на: {formatDateWithTimezone(ch.scheduled_publish_at, timezone)}
                  {/* TODO: Показать статус завершения после добавления поддержки в бэкенд */}
                  <Button
                    type="text"
                    size="small"
                    icon={<CloseCircleFilled style={{ color: theme === 'dark' ? '#f87171' : '#ef4444', fontSize: 16 }} />}
                    onClick={() => handleCancelSchedule(ch)}
                    style={{ marginLeft: 8, marginTop: -2, verticalAlign: 'middle', display: 'inline-block' }}
                    title="Снять отложенную публикацию"
                  />
                </div>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
      {(editMode && safeChapters.find(ch => ch.id === editMode)) && (
        <ChapterEditor
          key={`${editMode}-${editorKey}`}
          initialTitle={editingChapter?.title || ''}
          initialContent={editingChapter?.content || ''}
          onSave={handleSave}
          onCancel={() => {
            // Если редактор в полноэкранном режиме, выходим из него
            if (isFullWidth && onToggleFullWidth) {
              onToggleFullWidth();
            }
            setEditMode(null);
            setEditorContent('');
          }}
          onContentChange={setEditorContent}
          loading={chapterLoading}
          bookId={bookId}
          chapterOrder={editingChapter?.order || 1}
          chapterId={editingChapter?.id}
          username={username}
          imgIndex={getNextImgIndex()}
          autoIndent={localBook?.auto_indent || false}
          disableSave={isStoryOverLimit}
          disableSaveTooltip={isStoryOverLimit ? "Превышен максимальный размер рассказа (50,000 символов)" : ""}
          isFullWidth={isFullWidth}
          onToggleFullWidth={onToggleFullWidth}
        />
      )}
      {/* Счетчик символов для рассказа */}
      {localBook?.type === 'story' && safeChapters.length > 0 && (
        <div style={{
          marginTop: 12,
          marginBottom: isFullWidth ? 16 : 8,
          textAlign: 'center',
          fontSize: 14,
          fontWeight: 500,
          // Ручная настройка позиции счетчика по вертикали
          position: 'relative',
          top: isFullWidth ? '-7px' : '7px'  // полноэкранный: -25px (вверх), обычный: 0px
        }}>
          <span style={{ 
            color: isStoryOverLimit ? '#ef4444' : (theme === 'dark' ? '#a1a1aa' : '#666'),
            fontWeight: isStoryOverLimit ? 600 : 500
          }}>
            {totalCharacters.toLocaleString()}/{MAX_STORY_LENGTH.toLocaleString()} символов
            {isStoryOverLimit && (
              <span style={{ color: '#ef4444', marginLeft: 8, fontWeight: 600 }}>
                (Превышен размер рассказа)
              </span>
            )}
          </span>
        </div>
      )}
      
      {/* Скрываем кнопки "К основным настройкам" и "Опубликовать" во время редактирования */}
      {!editMode && (
        <div style={{ marginTop: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button
            onClick={onBack}
            style={{
              background: theme === 'dark' ? '#23272f' : '#fff',
              color: theme === 'dark' ? '#fff' : '#222',
              border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb',
              // Добавляем смещение для кнопки "К основным настройкам"
              position: 'relative',
              left: isFullWidth ? 'var(--back-btn-fullscreen-offset-x, 0px)' : 'var(--back-btn-normal-offset-x, 0px)',
              top: isFullWidth ? 'var(--back-btn-fullscreen-offset-y, 0px)' : 'var(--back-btn-normal-offset-y, 0px)'
            }}
          >
            К основным настройкам
          </Button>
          {safeChapters.length > 0 && (
            <>
              {isFullyPublished ? (
                <Tooltip key="draft-info-tooltip" title="Рассказ будет снят с публикации и станет недоступен для чтения." classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                  <Button
                    danger
                    onClick={handleToDraft}
                    style={{
                      background: theme === 'dark' ? '#dc2626' : '#ef4444',
                      color: '#fff',
                      border: 'none',
                      // Добавляем смещение для кнопки "Снять с публикации"
                      position: 'relative',
                      right: isFullWidth ? 'var(--publish-btn-fullscreen-offset-x, 0px)' : 'var(--publish-btn-normal-offset-x, 0px)',
                      top: isFullWidth ? 'var(--publish-btn-fullscreen-offset-y, 0px)' : 'var(--publish-btn-normal-offset-y, 0px)'
                    }}
                  >
                    Снять с публикации
                  </Button>
                </Tooltip>
              ) : (
                <Tooltip
                  title={
                    isStoryOverLimit
                      ? 'Функция недоступна, превышен максимальный размер рассказа'
                      : 'Рассказ будет опубликован и станет доступен для чтения.'
                  }
                  classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
                >
                  <Button
                    type="primary"
                    onClick={handlePublish}
                    disabled={isStoryOverLimit}
                    style={{
                      // Добавляем смещение для кнопки "Опубликовать"
                      position: 'relative',
                      right: isFullWidth ? 'var(--publish-btn-fullscreen-offset-x, 0px)' : 'var(--publish-btn-normal-offset-x, 0px)',
                      top: isFullWidth ? 'var(--publish-btn-fullscreen-offset-y, 0px)' : 'var(--publish-btn-normal-offset-y, 0px)'
                    }}
                  >
                    Опубликовать
                  </Button>
                </Tooltip>
              )}
            </>
          )}
        </div>
      )}
      <style>
        {`
:root {
  /* Настройки смещения для кнопки "К основным настройкам" в обычном режиме */
  --back-btn-normal-offset-x: 0px;
  --back-btn-normal-offset-y: 0px;
  
  /* Настройки смещения для кнопки "К основным настройкам" в полноэкранном режиме */
  --back-btn-fullscreen-offset-x: 28px;
  --back-btn-fullscreen-offset-y: -20px;
  
  /* Настройки смещения для кнопки "Опубликовать/Снять с публикации" в обычном режиме */
  --publish-btn-normal-offset-x: 0px;
  --publish-btn-normal-offset-y: 0px;
  
  /* Настройки смещения для кнопки "Опубликовать/Снять с публикации" в полноэкранном режиме */
  --publish-btn-fullscreen-offset-x: 28px;
  --publish-btn-fullscreen-offset-y: -20px;
}

.dark-action-btn {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
  transition: background 0.2s, color 0.2s;
}
.dark-action-btn:hover {
  background: #374151 !important;
  color: #60A5FA !important;
}
.dark-popconfirm .ant-popover-inner {
  background: #23272f !important;
  color: #fff !important;
}
.dark-popconfirm .ant-popover-message-title {
  color: #fff !important;
}
.dark-popconfirm .ant-popover-message {
  color: #fff !important;
}
.dark-popconfirm .ant-popover-buttons .ant-btn {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
}
.dark-popconfirm .ant-popover-buttons .ant-btn-dangerous {
  background: #ef4444 !important;
  color: #fff !important;
  border: none !important;
}
.custom-disabled-btn[disabled] {
  color: #a1a1aa !important;
  background: #23272f !important;
  border-color: #23272f !important;
  opacity: 1 !important;
}
.dark .custom-disabled-btn[disabled] {
  color: #a1a1aa !important;
  background: #23272f !important;
  border-color: #23272f !important;
  opacity: 1 !important;
}
.light .custom-disabled-btn[disabled] {
  color: #6b7280 !important;
  background: #f3f4f6 !important;
  border-color: #e5e7eb !important;
  opacity: 1 !important;
}
.dark .ant-dropdown-menu {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
}
.dark .ant-dropdown-menu-item {
  color: #fff !important;
}
.dark .ant-dropdown-menu-item-active {
  background: #374151 !important;
  color: #60A5FA !important;
}
.light .ant-dropdown-menu {
  background: #fff !important;
  color: #222 !important;
}
.light .ant-dropdown-menu-item-active {
  background: #e5e7eb !important;
  color: #2563eb !important;
}
.dark .dark-tooltip .ant-tooltip-inner {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
}
.dark .dark-tooltip .ant-tooltip-arrow {
  border-top-color: #23272f !important;
}
input[type='file']::-webkit-file-upload-button {
  background: ${theme === 'dark' ? '#23272f' : '#f3f4f6'};
  color: ${theme === 'dark' ? '#fff' : '#2563eb'};
  border: 1.5px solid ${theme === 'dark' ? '#374151' : '#2563eb'};
  border-radius: 6px;
  padding: 6px 16px;
  font-weight: 500;
  font-size: 15px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
input[type='file']::-webkit-file-upload-button:hover {
  background: ${theme === 'dark' ? '#374151' : '#2563eb'};
  color: #fff;
}
input[type='file']::file-selector-button {
  background: ${theme === 'dark' ? '#23272f' : '#f3f4f6'};
  color: ${theme === 'dark' ? '#fff' : '#2563eb'};
  border: 1.5px solid ${theme === 'dark' ? '#374151' : '#2563eb'};
  border-radius: 6px;
  padding: 6px 16px;
  font-weight: 500;
  font-size: 15px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
input[type='file']::file-selector-button:hover {
  background: ${theme === 'dark' ? '#374151' : '#2563eb'};
  color: #fff;
}
.dark-modal .ant-modal-content {
  background: #23272f !important;
  border-radius: 12px !important;
  border: none !important;
}
.dark-modal .ant-modal-close-x {
  color: #fff !important;
}
.ant-modal-close {
  top: 8px !important;
  right: 8px !important;
}
.ant-modal-close .ant-modal-close-x {
  font-size: 16px !important;
  line-height: 1 !important;
}
.ant-modal-content {
  border-radius: 12px !important;
  overflow: hidden;
}
.delete-book-button {
  background: ${theme === 'dark' ? '#374151' : '#f3f4f6'} !important;
  border-color: ${theme === 'dark' ? '#4b5563' : '#d1d5db'} !important;
  color: ${theme === 'dark' ? '#ef4444' : '#dc2626'} !important;
}
.delete-book-button:hover {
  background: ${theme === 'dark' ? '#ef4444' : '#fef2f2'} !important;
  border-color: ${theme === 'dark' ? '#ef4444' : '#fca5a5'} !important;
  color: ${theme === 'dark' ? '#fff' : '#dc2626'} !important;
}
.delete-book-button:focus {
  background: ${theme === 'dark' ? '#374151' : '#f3f4f6'} !important;
  border-color: ${theme === 'dark' ? '#4b5563' : '#d1d5db'} !important;
  color: ${theme === 'dark' ? '#ef4444' : '#dc2626'} !important;
}
        `}
      </style>
      {/* Модальное окно выбора статуса публикации */}
      <Modal
        open={showPublishStatusModal}
        onCancel={() => {
          setShowPublishStatusModal(false);
          setPublishAction(null);
          setPublishAsFinished(true);
        }}
        footer={null}
        centered
        closable={false}
      >
        <div style={{ fontSize: 16, color: '#60A5FA', fontWeight: 600, marginBottom: 20, textAlign: 'center' }}>
          {publishAction === 'scheduled' 
            ? 'Вы хотите запланировать публикацию и завершить рассказ или публикуете частично?'
            : 'Вы хотите опубликовать и завершить рассказ или публикуете частично?'
          }
        </div>
        
        <div style={{ marginBottom: 24 }}>
          <div style={{ fontSize: 14, fontWeight: 500, marginBottom: 12, color: theme === 'dark' ? '#fff' : '#222' }}>
            Укажите статус рассказа после публикации:
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: 16 }}>
            <Switch
              checked={publishAsFinished}
              onChange={setPublishAsFinished}
              className={theme === 'dark' ? 'bg-[#374151]' : 'bg-gray-300'}
              style={{ minWidth: 44 }}
            />
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              {publishAsFinished ? (
                <>
                  <span style={{ color: '#22c55e', fontSize: 18 }}>✔</span>
                  <span style={{ color: '#22c55e', fontWeight: 600, fontSize: 14 }}>Завершен</span>
                </>
              ) : (
                <>
                  <EditOutlined style={{ color: '#60A5FA', fontSize: 16 }} />
                  <span style={{ color: '#60A5FA', fontWeight: 600, fontSize: 14 }}>В процессе публикации</span>
                </>
              )}
            </div>
          </div>
        </div>
        
        <div style={{ display: 'flex', justifyContent: 'space-between', gap: 16 }}>
          <Button 
            onClick={() => {
              setShowPublishStatusModal(false);
              setPublishAction(null);
              setPublishAsFinished(true);
            }} 
            style={{ 
              minWidth: 100, 
              background: theme === 'dark' ? '#181c23' : '#f3f4f6', 
              color: theme === 'dark' ? '#fff' : '#222', 
              border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', 
              fontWeight: 500 
            }}
          >
            Отмена
          </Button>
          <Button
            type="primary"
            onClick={handleConfirmPublish}
            style={{ 
              minWidth: 120, 
              background: '#60A5FA', 
              color: '#fff', 
              border: 'none', 
              fontWeight: 500 
            }}
          >
            {publishAction === 'scheduled' ? 'Запланировать публикацию' : 'Опубликовать'}
          </Button>
        </div>
      </Modal>

      {/* Модальное окно планирования публикации */}
      <Modal
        open={showScheduleModal}
        onCancel={() => {
          setShowScheduleModal(false);
          setScheduleDate(null);
          setMinScheduleTime(null);
        }}
        footer={null}
        centered
        closable={false}
        styles={{
          body: {
            overflow: 'visible' // Позволяем содержимому выходить за границы модального окна
          }
        }}
      >
        <div style={{ fontSize: 16, color: '#60A5FA', fontWeight: 600, marginBottom: 16, textAlign: 'center', display: 'flex', alignItems: 'center', gap: 8, justifyContent: 'center' }}>
          <HourglassOutlined style={{ color: '#60A5FA', fontSize: 20 }} />
          Запланировать публикацию рассказа
        </div>
        <div style={{ marginBottom: 16 }}>
          <ConfigProvider locale={ru_RU}>
            <DatePicker
              showTime
              value={scheduleDate}
              onChange={setScheduleDate}
              format="DD.MM.YYYY HH:mm"
              style={{
                width: '100%',
                // Убираем синий фокус
                '--ant-color-primary': 'transparent',
                '--ant-color-primary-hover': 'transparent'
              }}
              className="no-focus-outline"
              placeholder="Выберите дату и время"
              getPopupContainer={() => document.body} // Рендерим календарь в body, чтобы он не обрезался
              showNow={false}
              disabledDate={current => {
                if (!current || !minScheduleTime) return false;

                // Проверяем, есть ли в этой дате хотя бы одна доступная минута
                const currentDate = current.startOf('day');
                const minDate = minScheduleTime.startOf('day');

                // Если дата раньше сегодняшней - блокируем
                if (currentDate.isBefore(minDate)) return true;

                // Если дата позже сегодняшней - разрешаем
                if (currentDate.isAfter(minDate)) return false;

                // Если это сегодняшняя дата - проверяем, есть ли доступное время
                const isToday = currentDate.isSame(minDate);
                if (isToday) {
                  // Проверяем, есть ли доступные часы/минуты в этом дне
                  const currentHour = minScheduleTime.hour();
                  const currentMinute = minScheduleTime.minute();

                  // Если текущий час меньше 23, то есть доступное время
                  if (currentHour < 23) return false;

                  // Если текущий час 23, проверяем минуты
                  if (currentHour === 23 && currentMinute < 59) return false;

                  // Если 23:59 - блокируем сегодняшний день
                  return true;
                }

                return false;
              }}
              disabledTime={date => {
                if (!minScheduleTime || !date || date.isAfter(minScheduleTime, 'day')) return {};
                const isToday = date.isSame(minScheduleTime, 'day');
                return isToday ? {
                  disabledHours: () => Array.from({length: 24}, (_, i) => i).filter(h => h < minScheduleTime.hour()),
                  disabledMinutes: h => h === minScheduleTime.hour() ? Array.from({length: 60}, (_, i) => i).filter(m => m < minScheduleTime.minute()) : []
                } : {};
              }}
            />
          </ConfigProvider>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between', gap: 16, marginTop: 24 }}>
          <Button onClick={() => {
            setShowScheduleModal(false);
            setScheduleDate(null);
            setMinScheduleTime(null);
          }} style={{ minWidth: 100, background: theme === 'dark' ? '#181c23' : '#f3f4f6', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', fontWeight: 500 }}>
            Отмена
          </Button>
          <div style={{ display: 'flex', gap: 16 }}>
            <Button
              type="primary"
              disabled={!scheduleDate}
              loading={schedulingLoading}
              style={{ minWidth: 100, background: '#60A5FA', color: '#fff', border: 'none', fontWeight: 500 }}
              onClick={handleSchedulePublish}
            >
              Запланировать
            </Button>
          </div>
        </div>
      </Modal>

      {/* Модальное окно подтверждения удаления произведения */}
      <Modal
        open={isDeleteModalVisible}
        onCancel={handleDeleteCancel}
        footer={null}
        className={theme === 'dark' ? 'dark-modal' : ''}
        styles={{
          content: {
            background: theme === 'dark' ? '#23272f' : '#ffffff',
            borderRadius: '12px',
            padding: '24px',
          },
          mask: {
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        <div className="mb-6">
          <h3 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Подтверждение удаления
          </h3>
          <p className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>
            Вы точно уверены, что хотите удалить произведение: <strong className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>{localBook?.title}</strong>?
          </p>
          <p className="text-red-600 dark:text-red-400 mt-2">
            Операция необратима.
          </p>
        </div>
        <Form
          form={deleteForm}
          onFinish={handleDeleteConfirm}
          layout="vertical"
        >
          <Form.Item
            name="confirmation"
            rules={[
              { required: true, message: 'Пожалуйста, введите слово "удалить"' },
              {
                validator: (_, value) =>
                  value === 'удалить'
                    ? Promise.resolve()
                    : Promise.reject(new Error('Введите слово "удалить" для подтверждения')),
              },
            ]}
          >
            <Input
              placeholder="Введите слово 'удалить' для подтверждения"
              value={deleteConfirmation}
              onChange={(e) => setDeleteConfirmation(e.target.value)}
              className={theme === 'dark' ? 'bg-[#23272f] text-white border-[#4b5563] placeholder-[#9ca3af]' : 'bg-white text-gray-900 border-gray-300 placeholder-gray-400'}
              style={theme === 'dark' ? { borderColor: '#4b5563', color: '#fff', background: '#23272f' } : {}}
            />
          </Form.Item>
          <div className="flex justify-end space-x-4 mt-4">
            <Button 
              onClick={handleDeleteCancel}
              className={theme === 'dark' ? 'bg-[#23272f] text-white border-[#4b5563] hover:bg-[#374151]' : 'bg-white text-gray-900 border-gray-300 hover:bg-gray-100'}
              style={theme === 'dark' ? { borderColor: '#4b5563', color: '#fff', background: '#23272f' } : {}}
            >
              Отмена
            </Button>
            <Button
              type="primary"
              danger
              onClick={handleDeleteConfirm}
              disabled={deleteConfirmation !== 'удалить'}
              className={theme === 'dark' ? 'bg-red-600 hover:bg-red-700 border-none text-white' : 'bg-red-600 hover:bg-red-700 border-none text-white'}
            >
              Подтверждаю удаление
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default StoryEditor;