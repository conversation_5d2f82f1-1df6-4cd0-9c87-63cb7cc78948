"""
Celery задачи для работы с рейтингами пользователей.
"""

from celery import shared_task
from django.utils import timezone
from django.db import transaction
from django.db.models import Q
import logging

from .models import User, RatingRecalculationTask
from .rating_service import RatingService

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def recalculate_user_rating(self, user_id: int, task_id: int = None):
    """
    Пересчитать рейтинг конкретного пользователя.
    """
    try:
        user = User.objects.get(id=user_id)
        
        # Обновляем статус задачи
        if task_id:
            task = RatingRecalculationTask.objects.get(id=task_id)
            task.status = 'processing'
            task.started_at = timezone.now()
            task.celery_task_id = self.request.id
            task.save()
        
        # Выполняем пересчет
        stats = RatingService.recalculate_user_stats(user)
        
        # Завершаем задачу
        if task_id:
            task.status = 'completed'
            task.completed_at = timezone.now()
            task.save()
        
        logger.info(f"Успешно пересчитан рейтинг пользователя {user.username}")
        return {
            'success': True,
            'user_id': user_id,
            'total_rating': stats.total_rating,
            'reader_rating': stats.reader_rating,
            'author_rating': stats.author_rating
        }
        
    except User.DoesNotExist:
        logger.error(f"Пользователь с ID {user_id} не найден")
        if task_id:
            task = RatingRecalculationTask.objects.get(id=task_id)
            task.status = 'failed'
            task.error_message = f"Пользователь с ID {user_id} не найден"
            task.save()
        return {'success': False, 'error': 'User not found'}
        
    except Exception as exc:
        logger.error(f"Ошибка при пересчете рейтинга пользователя {user_id}: {exc}")
        if task_id:
            task = RatingRecalculationTask.objects.get(id=task_id)
            task.status = 'failed'
            task.error_message = str(exc)
            task.save()
        
        # Повторяем задачу через некоторое время
        raise self.retry(exc=exc, countdown=60)


@shared_task
def process_rating_queue():
    """
    Обработать очередь задач пересчета рейтингов.
    """
    # Получаем задачи в порядке приоритета
    pending_tasks = RatingRecalculationTask.objects.filter(
        status='pending'
    ).order_by('priority', 'created_at')[:10]  # Обрабатываем по 10 задач за раз
    
    processed_count = 0
    
    for task in pending_tasks:
        try:
            if task.task_type == 'user_stats':
                # Запускаем задачу пересчета для пользователя
                recalculate_user_rating.delay(task.user.id, task.id)
                processed_count += 1
                
            elif task.task_type == 'bulk_recalculation':
                # Запускаем массовый пересчет
                bulk_recalculate_ratings.delay(task.id)
                processed_count += 1
                
        except Exception as exc:
            logger.error(f"Ошибка при обработке задачи {task.id}: {exc}")
            task.status = 'failed'
            task.error_message = str(exc)
            task.save()
    
    logger.info(f"Обработано {processed_count} задач пересчета рейтингов")
    return processed_count


@shared_task(bind=True)
def bulk_recalculate_ratings(self, task_id: int = None, limit: int = 100):
    """
    Массовый пересчет рейтингов пользователей.
    """
    try:
        # Обновляем статус задачи
        if task_id:
            task = RatingRecalculationTask.objects.get(id=task_id)
            task.status = 'processing'
            task.started_at = timezone.now()
            task.celery_task_id = self.request.id
            task.save()
        
        # Получаем пользователей, требующих пересчета
        users_to_recalculate = User.objects.filter(
            Q(stats__recalculation_scheduled=True) |
            Q(stats__isnull=True)
        ).select_related('stats')[:limit]
        
        processed_count = 0
        errors = []
        
        for user in users_to_recalculate:
            try:
                RatingService.recalculate_user_stats(user)
                processed_count += 1
                
                # Каждые 10 пользователей делаем небольшую паузу
                if processed_count % 10 == 0:
                    self.update_state(
                        state='PROGRESS',
                        meta={'processed': processed_count, 'total': len(users_to_recalculate)}
                    )
                    
            except Exception as exc:
                logger.error(f"Ошибка при пересчете рейтинга пользователя {user.id}: {exc}")
                errors.append(f"User {user.id}: {str(exc)}")
        
        # Завершаем задачу
        if task_id:
            task.status = 'completed'
            task.completed_at = timezone.now()
            if errors:
                task.error_message = "; ".join(errors[:5])  # Сохраняем первые 5 ошибок
            task.save()
        
        logger.info(f"Массовый пересчет завершен: обработано {processed_count} пользователей, ошибок: {len(errors)}")
        
        return {
            'success': True,
            'processed_count': processed_count,
            'errors_count': len(errors),
            'errors': errors[:10]  # Возвращаем первые 10 ошибок
        }
        
    except Exception as exc:
        logger.error(f"Критическая ошибка при массовом пересчете: {exc}")
        if task_id:
            task = RatingRecalculationTask.objects.get(id=task_id)
            task.status = 'failed'
            task.error_message = str(exc)
            task.save()
        raise


@shared_task
def daily_rating_update():
    """
    Ежедневное обновление рейтингов - выполняется по расписанию.
    """
    # Создаем задачу массового пересчета
    task = RatingRecalculationTask.objects.create(
        task_type='daily_update',
        priority=3,
        parameters={'batch_size': 500},
        created_by='cron'
    )
    
    # Запускаем массовый пересчет
    bulk_recalculate_ratings.delay(task.id, limit=500)
    
    logger.info(f"Запущено ежедневное обновление рейтингов (задача {task.id})")
    return task.id


@shared_task
def cleanup_old_rating_tasks():
    """
    Очистка старых задач пересчета рейтингов.
    """
    # Удаляем завершенные задачи старше 30 дней
    old_completed = RatingRecalculationTask.objects.filter(
        status__in=['completed', 'failed', 'cancelled'],
        created_at__lt=timezone.now() - timezone.timedelta(days=30)
    )
    
    deleted_count = old_completed.count()
    old_completed.delete()
    
    logger.info(f"Удалено {deleted_count} старых задач пересчета рейтингов")
    return deleted_count


@shared_task
def update_user_metric(user_id: int, action_type: str, metric_name: str, 
                      change_delta: int, related_object_type: str = None, 
                      related_object_id: int = None):
    """
    Асинхронное обновление метрики пользователя.
    Используется для неблокирующего обновления рейтингов.
    """
    try:
        user = User.objects.get(id=user_id)
        
        related_object = None
        if related_object_type and related_object_id is not None:
            # Динамически получаем модель
            from django.apps import apps
            try:
                model_class = apps.get_model('books', related_object_type)
                related_object = model_class.objects.get(id=related_object_id)
            except LookupError:
                logger.warning(f"Модель {related_object_type} не найдена")
            except Exception as e:
                # Ловим model_class.DoesNotExist и другие исключения
                logger.warning(f"Связанный объект {related_object_type}:{related_object_id} не найден: {e}")
        
        RatingService.update_metric(
            user=user,
            action_type=action_type,
            metric_name=metric_name,
            change_delta=change_delta,
            related_object=related_object,
            created_by='async_task'
        )
        
        logger.info(f"Асинхронно обновлена метрика {metric_name} для пользователя {user.username}")
        return {'success': True}
        
    except User.DoesNotExist:
        logger.error(f"Пользователь с ID {user_id} не найден при обновлении метрики")
        return {'success': False, 'error': 'User not found'}
        
    except Exception as exc:
        logger.error(f"Ошибка при асинхронном обновлении метрики: {exc}")
        return {'success': False, 'error': str(exc)}


@shared_task
def recalculate_leaderboards():
    """
    Пересчет данных для лидербордов.
    Создает кэшированные версии рейтингов.
    """
    from django.core.cache import cache
    
    try:
        # Топ по общему рейтингу
        total_leaders = RatingService.get_leaderboard('total', 100)
        cache.set('leaderboard:total', list(total_leaders.values(
            'user__id', 'user__username', 'user__display_name', 
            'total_rating', 'reader_rating', 'author_rating'
        )), timeout=3600)  # Кэш на час
        
        # Топ по читательскому рейтингу
        reader_leaders = RatingService.get_leaderboard('reader', 100)
        cache.set('leaderboard:reader', list(reader_leaders.values(
            'user__id', 'user__username', 'user__display_name', 
            'reader_rating', 'comments_left_count', 'reviews_left_count'
        )), timeout=3600)
        
        # Топ по авторскому рейтингу
        author_leaders = RatingService.get_leaderboard('author', 100)
        cache.set('leaderboard:author', list(author_leaders.values(
            'user__id', 'user__username', 'user__display_name', 
            'author_rating', 'published_books_count', 'books_likes_count'
        )), timeout=3600)
        
        logger.info("Лидерборды обновлены и закэшированы")
        return {'success': True, 'cached_leaderboards': 3}
        
    except Exception as exc:
        logger.error(f"Ошибка при обновлении лидербордов: {exc}")
        return {'success': False, 'error': str(exc)}


@shared_task(bind=True, max_retries=3)
def process_like_added_async(self, like_id, content_type='comment'):
    """
    Асинхронная обработка добавления лайка/дизлайка.
    """
    try:
        from books.models import CommentLike

        # Получаем объект лайка
        like = CommentLike.objects.select_related('comment__book', 'comment__user', 'user').get(id=like_id)

        # Обрабатываем добавление лайка
        if content_type == 'comment':
            target_user = like.comment.user
        else:
            target_user = None

        if target_user:
            RatingService.process_like_added(
                like=like,
                content_type=content_type,
                target_user=target_user
            )

        logger.info(f"Асинхронно обработан лайк {like_id} для пользователя {target_user.username if target_user else 'None'}")
        return {'success': True, 'like_id': like_id}

    except CommentLike.DoesNotExist:
        logger.warning(f"CommentLike с ID {like_id} не найден")
        return {'success': False, 'error': 'Like not found'}
    except Exception as exc:
        logger.error(f"Ошибка при обработке лайка {like_id}: {exc}")
        # Повторяем задачу с экспоненциальной задержкой
        raise self.retry(exc=exc, countdown=60 * (2 ** self.request.retries))


@shared_task(bind=True, max_retries=3)
def process_like_removed_async(self, user_id, comment_id, reaction, content_type='comment'):
    """
    Асинхронная обработка удаления лайка/дизлайка.
    """
    try:
        from books.models import Comment
        from .rating_service import RatingEventHandlers

        # Получаем пользователя и комментарий
        user = User.objects.get(id=user_id)
        comment = Comment.objects.select_related('book', 'user').get(id=comment_id)

        # Создаем временный объект лайка для обработки
        temp_like = type('TempLike', (), {
            'user': user,
            'comment': comment,
            'reaction': reaction
        })()

        # Обрабатываем удаление лайка
        RatingEventHandlers.on_like_removed(temp_like, content_type)

        logger.info(f"Асинхронно обработано удаление лайка пользователя {user.username} к комментарию {comment_id}")
        return {'success': True, 'user_id': user_id, 'comment_id': comment_id}

    except (User.DoesNotExist, Comment.DoesNotExist) as e:
        logger.warning(f"Объект не найден при обработке удаления лайка: {e}")
        return {'success': False, 'error': str(e)}
    except Exception as exc:
        logger.error(f"Ошибка при обработке удаления лайка: {exc}")
        raise self.retry(exc=exc, countdown=60 * (2 ** self.request.retries))


@shared_task(bind=True, max_retries=3)
def process_comment_added_async(self, comment_id, comment_type='book'):
    """
    Асинхронная обработка добавления комментария.
    """
    try:
        from books.models import Comment

        # Получаем комментарий
        comment = Comment.objects.select_related('book__author', 'user').get(id=comment_id)

        # Определяем автора контента
        content_author = None
        if comment_type == 'book' and comment.book:
            content_author = comment.book.author

        # Обрабатываем добавление комментария
        RatingService.process_comment_added(
            comment=comment,
            comment_type=comment_type,
            content_author=content_author
        )

        logger.info(f"Асинхронно обработан комментарий {comment_id} пользователя {comment.user.username}")
        return {'success': True, 'comment_id': comment_id}

    except Comment.DoesNotExist:
        logger.warning(f"Comment с ID {comment_id} не найден")
        return {'success': False, 'error': 'Comment not found'}
    except Exception as exc:
        logger.error(f"Ошибка при обработке комментария {comment_id}: {exc}")
        raise self.retry(exc=exc, countdown=60 * (2 ** self.request.retries))


@shared_task
def batch_update_total_ratings():
    """
    Пакетное обновление общих рейтингов всех пользователей.
    Запускается по расписанию (например, каждые 5 минут).
    """
    try:
        from .models import UserStats
        from django.db import transaction

        # Получаем всех пользователей, у которых изменились рейтинги
        stats_to_update = UserStats.objects.select_related('user').all()

        updated_count = 0
        with transaction.atomic():
            for stats in stats_to_update:
                old_total = stats.total_rating
                new_total = stats.reader_rating + stats.author_rating

                if old_total != new_total:
                    stats.total_rating = new_total
                    stats.save(update_fields=['total_rating'])

                    # Обновляем также в основной таблице User для обратной совместимости
                    stats.user.total_rating = new_total
                    stats.user.save(update_fields=['total_rating'])

                    updated_count += 1

        logger.info(f"Пакетно обновлено {updated_count} общих рейтингов")
        return updated_count

    except Exception as exc:
        logger.error(f"Ошибка при пакетном обновлении рейтингов: {exc}")
        raise


@shared_task
def cleanup_old_rating_history():
    """
    Очистка старой истории рейтинга (старше 100 дней).
    Рейтинги пользователей НЕ изменяются - они уже применены к UserStats.
    Запускается ежедневно.
    """
    try:
        from .models import RatingHistory
        from django.utils import timezone
        from datetime import timedelta

        # Удаляем записи старше 100 дней
        cutoff_date = timezone.now() - timedelta(days=100)

        # Подсчитываем количество записей для удаления
        records_to_delete = RatingHistory.objects.filter(created_at__lt=cutoff_date)
        count_to_delete = records_to_delete.count()

        if count_to_delete > 0:
            # Удаляем пакетами для избежания блокировок
            batch_size = 1000
            deleted_total = 0

            while True:
                # Получаем ID записей для удаления
                ids_to_delete = list(
                    RatingHistory.objects.filter(created_at__lt=cutoff_date)
                    .values_list('id', flat=True)[:batch_size]
                )

                if not ids_to_delete:
                    break

                # Удаляем пакет
                deleted_count, _ = RatingHistory.objects.filter(id__in=ids_to_delete).delete()
                deleted_total += deleted_count

                logger.info(f"Удален пакет из {deleted_count} записей истории рейтинга")

                # Небольшая пауза между пакетами
                import time
                time.sleep(0.1)

            logger.info(f"Очистка завершена: удалено {deleted_total} записей истории рейтинга старше 100 дней")
            return deleted_total
        else:
            logger.info("Нет записей для удаления")
            return 0

    except Exception as exc:
        logger.error(f"Ошибка при очистке истории рейтинга: {exc}")
        raise


@shared_task
def monitor_rating_history_size():
    """
    Мониторинг размера таблицы истории рейтинга.
    Отправляет предупреждения если таблица растет слишком быстро.
    """
    try:
        from .models import RatingHistory
        from django.utils import timezone
        from datetime import timedelta

        # Подсчитываем записи за разные периоды
        now = timezone.now()

        total_count = RatingHistory.objects.count()
        last_day_count = RatingHistory.objects.filter(
            created_at__gte=now - timedelta(days=1)
        ).count()
        last_week_count = RatingHistory.objects.filter(
            created_at__gte=now - timedelta(days=7)
        ).count()
        last_month_count = RatingHistory.objects.filter(
            created_at__gte=now - timedelta(days=30)
        ).count()

        # Прогнозируем рост
        daily_rate = last_day_count
        weekly_rate = last_week_count / 7
        monthly_rate = last_month_count / 30

        # Логируем статистику
        logger.info(f"Статистика RatingHistory:")
        logger.info(f"  Всего записей: {total_count}")
        logger.info(f"  За последний день: {last_day_count}")
        logger.info(f"  За последнюю неделю: {last_week_count} (среднее: {weekly_rate:.1f}/день)")
        logger.info(f"  За последний месяц: {last_month_count} (среднее: {monthly_rate:.1f}/день)")

        # Предупреждения
        if total_count > 1000000:  # 1M записей
            logger.warning(f"Таблица RatingHistory содержит {total_count} записей - рассмотрите оптимизацию")

        if daily_rate > 10000:  # 10K записей в день
            logger.warning(f"Высокая скорость роста: {daily_rate} записей за день")

        return {
            'total_count': total_count,
            'daily_rate': daily_rate,
            'weekly_rate': weekly_rate,
            'monthly_rate': monthly_rate
        }

    except Exception as exc:
        logger.error(f"Ошибка при мониторинге размера истории рейтинга: {exc}")
        raise