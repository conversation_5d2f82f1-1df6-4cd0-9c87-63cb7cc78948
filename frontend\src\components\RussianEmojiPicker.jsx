import { useEffect } from 'react';
import EmojiPicker from 'emoji-picker-react';
import './RussianEmojiPicker.css?v=18';

/**
 * Русифицированный эмодзи пикер
 * Обертка над emoji-picker-react с русскими настройками по умолчанию
 * Включает полную локализацию и улучшенное визуальное оформление
 */
const RussianEmojiPicker = (props) => {
  // Принудительно добавляем CSS правила для убирания фокуса
  useEffect(() => {
    const styleId = 'russian-emoji-picker-fixes';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        .EmojiPickerReact .epr-body .epr-emoji-category-label {
          text-transform: none !important;
        }
        /* Максимальная специфичность для принудительного применения */
        .EmojiPickerReact.epr_mw4zr1 .epr-header.epr_iogimd .epr-category-nav.epr_q53mwh button.epr-cat-btn.epr_iy6j2p,
        aside.EmojiPickerReact div.epr-header div.epr-category-nav button.epr-cat-btn {
          width: 30px !important;
          height: 30px !important;
          min-width: 30px !important;
          max-width: 30px !important;
          flex: 0 0 30px !important;
          box-sizing: border-box !important;
        }

        /* Убираем овальный фокус */
        .EmojiPickerReact.epr_mw4zr1 .epr-header.epr_iogimd .epr-category-nav.epr_q53mwh button.epr-cat-btn.epr_iy6j2p:focus:before,
        aside.EmojiPickerReact div.epr-header div.epr-category-nav button.epr-cat-btn:focus:before {
          display: none !important;
          content: none !important;
          visibility: hidden !important;
        }

        /* Наш прямоугольный фокус */
        .EmojiPickerReact.epr_mw4zr1 .epr-header.epr_iogimd .epr-category-nav.epr_q53mwh button.epr-cat-btn.epr_iy6j2p:focus,
        aside.EmojiPickerReact div.epr-header div.epr-category-nav button.epr-cat-btn:focus {
          outline: 2px solid var(--epr-category-icon-active-color, #007aff) !important;
          outline-offset: 1px !important;
        }

        /* Темная тема - максимальная специфичность */
        .russian-emoji-picker[style*="--epr-bg-color"] .EmojiPickerReact,
        .russian-emoji-picker[style*="--epr-bg-color"] aside.EmojiPickerReact,
        .EmojiPickerReact.epr-dark-theme,
        aside.EmojiPickerReact.epr-dark-theme {
          --epr-bg-color: #2d3748 !important;
          --epr-text-color: #e2e8f0 !important;
          --epr-border-color: #4a5568 !important;
          --epr-hover-bg-color: rgba(255, 255, 255, 0.1) !important;
          --epr-highlight-color: #4299e1 !important;
          --epr-category-label-bg-color: #374151 !important;
          --epr-category-label-text-color: #d1d5db !important;
          --epr-preview-bg-color: #374151 !important;
          --epr-category-icon-active-color: #4299e1 !important;
          --epr-search-input-bg-color: #374151 !important;
          --epr-search-input-text-color: #e2e8f0 !important;
          --epr-search-input-placeholder-color: #9ca3af !important;
        }

        /* Темная тема - только основные элементы */
        .EmojiPickerReact.epr-dark-theme,
        aside.EmojiPickerReact.epr-dark-theme {
          --epr-bg-color: #2d3748 !important;
          --epr-text-color: #e2e8f0 !important;
          --epr-border-color: #4a5568 !important;
          --epr-hover-bg-color: rgba(255, 255, 255, 0.1) !important;
          --epr-highlight-color: #4299e1 !important;
          --epr-category-label-bg-color: #374151 !important;
          --epr-category-label-text-color: #d1d5db !important;
          --epr-preview-bg-color: #374151 !important;
          --epr-category-icon-active-color: #4299e1 !important;
          --epr-search-input-bg-color: #374151 !important;
          --epr-search-input-text-color: #e2e8f0 !important;
          --epr-search-input-placeholder-color: #9ca3af !important;
        }
        .EmojiPickerReact .epr-search-container input {
          padding-left: 45px !important;
        }
        .EmojiPickerReact .epr-search-container .epr-icn-search {
          left: 20px !important;
        }
        .EmojiPickerReact .epr-skin-tones {
          margin-left: 10px !important;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  const {
    searchPlaceholder = 'Поиск эмодзи...',
    previewConfig = {
      defaultCaption: 'Выберите эмодзи',
      showPreview: true
    },
    categories = [
      {
        category: 'suggested',
        name: 'Часто используемые'
      },
      {
        category: 'smileys_people',
        name: 'Смайлики и люди'
      },
      {
        category: 'animals_nature',
        name: 'Животные и природа'
      },
      {
        category: 'food_drink',
        name: 'Еда и напитки'
      },
      {
        category: 'travel_places',
        name: 'Путешествия и места'
      },
      {
        category: 'activities',
        name: 'Активности'
      },
      {
        category: 'objects',
        name: 'Объекты'
      },
      {
        category: 'symbols',
        name: 'Символы'
      },
      {
        category: 'flags',
        name: 'Флаги'
      }
    ],
    className = '',
    ...otherProps
  } = props;

  return (
    <div className={`russian-emoji-picker ${className}`}>
      <EmojiPicker
        searchPlaceholder={searchPlaceholder}
        previewConfig={previewConfig}
        categories={categories}
        {...otherProps}
      />
    </div>
  );
};

export default RussianEmojiPicker;
