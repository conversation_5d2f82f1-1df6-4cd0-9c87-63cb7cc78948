import React, { useEffect, useState, useContext } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { getCachedUserAvatar } from '../utils/avatarCache';
import { formatDistanceToNow } from 'date-fns';
import { ru } from 'date-fns/locale';
import { getRatingIcon, getRatingLevel, formatNumber } from '../utils/ratingIcons';

const backendUrl = 'http://localhost:8000';

function formatLastSeen(dateString, gender) {
  if (!dateString) return '';
  const date = new Date(dateString);
  const now = new Date();
  const diff = now - date;
  
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  let prefix = 'Был';
  if (gender === 'F') {
    prefix = 'Была';
  } else if (gender === 'U') {
    prefix = 'Был(а)';
  }

  if (minutes < 60) {
    return `${prefix} ${minutes} мин. назад`;
  } else if (hours < 24) {
    return `${prefix} ${hours} ч. назад`;
  } else {
    return `${prefix} ${days} д. назад`;
  }
}

function getMiniAvatarUrl(user, imageVersion = 0) {
  if (!user?.username) return '/lpo/ava.webp';
  const username = user.username;
  const miniPath = `/media/users/${username[0]}/${username.slice(0,2)}/${username}/avatar/ava_${username}_m.jpg`;
  return `${backendUrl}${miniPath}?v=${imageVersion}`;
}

const ProfileSidebar = ({ userData, username, isOwner, friendsList: propFriendsList, subscribersList: propSubscribersList, subscriptionsList: propSubscriptionsList }) => {
  const { user } = useAuth();
  const [profileData, setProfileData] = useState(userData || null);
  const [friendsList, setFriendsList] = useState(propFriendsList || []);
  const [subscriptionsList, setSubscriptionsList] = useState(propSubscriptionsList || []);
  const [subscribersList, setSubscribersList] = useState(propSubscribersList || []);
  const [stats, setStats] = useState({ subscribers_count: 0, friends_count: 0 });
  const [imageVersion, setImageVersion] = useState(0);

  // Спойлеры и пагинация
  const [openSection, setOpenSection] = useState(null); // 'friends' | 'subscriptions' | 'subscribers' | null
  const [friendsPage, setFriendsPage] = useState(0);
  const [subscriptionsPage, setSubscriptionsPage] = useState(0);
  const [subscribersPage, setSubscribersPage] = useState(0);
  const PAGE_SIZE = 5;

  // Определяем имя пользователя для запросов
  const targetUsername = username || (userData?.username);

  // Обновляем данные профиля при изменении userData
  useEffect(() => {
    if (userData) {
      setProfileData(userData);
    }
  }, [userData]);

  // Обновляем списки при изменении пропсов
  useEffect(() => {
    if (propFriendsList) {
      setFriendsList(propFriendsList);
    }
  }, [propFriendsList]);

  useEffect(() => {
    if (propSubscribersList) {
      setSubscribersList(propSubscribersList);
    }
  }, [propSubscribersList]);

  useEffect(() => {
    if (propSubscriptionsList) {
      setSubscriptionsList(propSubscriptionsList);
    }
  }, [propSubscriptionsList]);

  // Обновляем imageVersion при изменении данных пользователя
  useEffect(() => {
    if (userData?.avatar_updated_at) {
      setImageVersion(Date.parse(userData.avatar_updated_at));
    }
  }, [userData?.avatar_updated_at]);

  // Обновляем imageVersion при изменении данных профиля
  useEffect(() => {
    if (profileData?.avatar_updated_at) {
      setImageVersion(Date.parse(profileData.avatar_updated_at));
    }
  }, [profileData?.avatar_updated_at]);

  // Загружаем данные пользователя, если передан только username
  useEffect(() => {
    if (!targetUsername) return;
    
    // Если данные пользователя не переданы, загружаем их
    if (!userData && username) {
      fetch(`${backendUrl}/api/auth/${username}/`)
        .then(res => {
          if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
          }
          return res.json();
        })
        .then(data => {
          if (data && !data.error) {
            setProfileData(data);
          }
        })
        .catch(err => console.error('Error fetching user data:', err));
    }

    // Загружаем списки друзей, подписок и подписчиков только если они не переданы как пропсы
    if (!propFriendsList) {
      fetch(`${backendUrl}/api/auth/friends/${targetUsername}/`)
        .then(res => {
          if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
          }
          return res.json();
        })
        .then(data => setFriendsList(data.friends || []))
        .catch(err => console.error('Error fetching friends list:', err));
    }
    
    if (!propSubscriptionsList) {
      fetch(`${backendUrl}/api/auth/subscriptions/${targetUsername}/`)
        .then(res => {
          if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
          }
          return res.json();
        })
        .then(data => setSubscriptionsList(data.subscriptions || []))
        .catch(err => console.error('Error fetching subscriptions list:', err));
    }
    
    if (!propSubscribersList) {
      fetch(`${backendUrl}/api/auth/subscribers/${targetUsername}/`)
        .then(res => {
          if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
          }
          return res.json();
        })
        .then(data => setSubscribersList(data.subscribers || []))
        .catch(err => console.error('Error fetching subscribers list:', err));
    }
    
    fetch(`${backendUrl}/api/auth/stats/${targetUsername}/`)
      .then(res => {
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        return res.json();
      })
      .then(data => setStats(data))
      .catch(err => console.error('Error fetching stats:', err));
  }, [targetUsername, userData, username, propFriendsList, propSubscribersList, propSubscriptionsList]);

  // Вспомогательная функция для пагинации
  const getPage = (list, page) => list.slice(page * PAGE_SIZE, page * PAGE_SIZE + PAGE_SIZE);
  const getPageCount = (list) => Math.ceil(list.length / PAGE_SIZE);

  // Функция для старта диалога с пользователем
  const handleStartDialog = async (targetUser) => {
    // Переходим на вкладку сообщений (если есть роутинг)
    window.location.href = `/lpu/${user.username}/messages?to=${targetUser.username}`;
    // Альтернатива: если есть useNavigate, можно использовать navigate(`/lpu/${user.username}/messages?to=${targetUser.username}`)
  };

  // Если данные пользователя еще не загружены, показываем загрузку
  if (!profileData && !userData) {
    return (
      <div className="hidden sm:block w-[250px] flex-shrink-0">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6 flex flex-col items-center">
          <div className="animate-pulse flex flex-col items-center w-full">
            <div className="w-[90px] h-[90px] rounded-full bg-gray-300 dark:bg-gray-600 mb-4"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-4"></div>
            <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-full mb-2"></div>
            <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-full mb-2"></div>
            <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
          </div>
        </div>
      </div>
    );
  }

  // Используем данные из profileData или userData
  const displayData = profileData || userData;

  return (
    <div className="hidden sm:block w-[250px] flex-shrink-0">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6 flex flex-col items-center">
        {/* Крупный аватар, имя, рейтинг, статус */}
        <div className="flex flex-col items-center mb-4">
          <span className="font-semibold text-lg text-gray-800 dark:text-gray-100 mb-1">{displayData?.display_name}</span>
          <div className="w-[90px] h-[90px] rounded-full border-2 border-white shadow-lg overflow-hidden mb-2">
            {getCachedUserAvatar(displayData, 'full', backendUrl, imageVersion) ? (
              <img src={getCachedUserAvatar(displayData, 'full', backendUrl, imageVersion)} alt="avatar" className="w-full h-full object-cover" />
            ) : (
              <span className="text-5xl text-gray-400"></span>
            )}
          </div>
          <div className="flex items-center gap-2 mb-1">
            <span className="text-2xl font-bold text-gray-800 dark:text-gray-100">{formatNumber(displayData?.total_rating)}</span>
            <img 
              src={getRatingIcon(displayData?.total_rating)}
              alt="Rating icon"
              className="w-8 h-8"
              title={`${getRatingLevel(displayData?.total_rating)} уровень`}
            />
          </div>
          <div className="flex items-center gap-2">
            <span className={`w-3 h-3 rounded-full ${displayData?.is_online ? 'bg-green-500' : 'bg-red-500'}`}></span>
            <span className="text-sm text-gray-700 dark:text-gray-200">{displayData?.status}</span>
            {!isOwner && user && user.username !== displayData?.username && (
              <button
                className="ml-2 text-blue-500 hover:text-blue-700 text-xl p-1 bg-transparent border-none cursor-pointer"
                title="Написать сообщение"
                onClick={() => handleStartDialog(displayData)}
              >
                ✉️
              </button>
            )}
          </div>
        </div>
        {/* Спойлеры */}
        <div className="w-full flex flex-col gap-2">
          {/* Подписчики */}
          <div>
            <button
              className="w-full flex justify-between items-center px-2 py-2 rounded bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 font-semibold text-gray-800 dark:text-gray-100"
              onClick={() => {
                setOpenSection(openSection === 'subscribers' ? null : 'subscribers');
                setSubscribersPage(0);
              }}
            >
              <span>Подписчики</span>
              <span className="ml-2">{formatNumber(subscribersList.length)}</span>
              <span className={`ml-2 transition-transform ${openSection === 'subscribers' ? 'rotate-90' : ''}`}>▶</span>
            </button>
            {openSection === 'subscribers' && (
              <div className="mt-2">
                {getPage(subscribersList, subscribersPage).map(u => {
                  const isCurrentUser = user && u.username === user.username;
                  const displayUser = isCurrentUser ? user : u;

                  return (
                    <div key={u.username} className="flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                      <div className={`w-8 h-8 rounded-full overflow-hidden ${u.is_online ? 'ring-2 ring-green-500' : ''}`}>
                        <img src={getCachedUserAvatar(displayUser, 'mini', backendUrl, imageVersion)} alt="avatar" className="w-full h-full object-cover" />
                      </div>
                      <div className="flex-1 min-w-0">
                        {u.display_name === 'Аккаунт удален' ? (
                          <span className="text-gray-500 dark:text-gray-400 text-sm block truncate">
                            Аккаунт удален
                          </span>
                        ) : (
                          <Link to={`/lpu/${u.username}`} className="text-blue-600 dark:text-blue-400 hover:underline text-sm block truncate">
                            {u.display_name || u.username}
                          </Link>
                        )}
                        <div className="flex items-center gap-1">
                          <span className={`w-2 h-2 rounded-full ${u.is_online ? 'bg-green-500' : 'bg-red-500'}`}></span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {u.is_online ? 'В сети' : u.last_activity ? formatLastSeen(u.last_activity, u.gender) : ''}
                          </span>
                        </div>
                      </div>
                      {user && u.username !== user.username && (
                        <button
                          className="text-blue-500 hover:text-blue-700 text-xl p-1 bg-transparent border-none cursor-pointer"
                          title="Написать сообщение"
                          onClick={() => handleStartDialog(u)}
                        >
                          ✉️
                        </button>
                      )}
                    </div>
                  );
                })}
                {/* Пагинация */}
                {getPageCount(subscribersList) > 1 && (
                  <div className="flex justify-between mt-2">
                    <button
                      className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded disabled:opacity-50"
                      disabled={subscribersPage === 0}
                      onClick={() => setSubscribersPage(p => p - 1)}
                    >
                      ◀
                    </button>
                    <span className="text-sm">
                      {subscribersPage + 1} / {getPageCount(subscribersList)}
                    </span>
                    <button
                      className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded disabled:opacity-50"
                      disabled={subscribersPage >= getPageCount(subscribersList) - 1}
                      onClick={() => setSubscribersPage(p => p + 1)}
                    >
                      ▶
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Друзья */}
          <div>
            <button
              className="w-full flex justify-between items-center px-2 py-2 rounded bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 font-semibold text-gray-800 dark:text-gray-100"
              onClick={() => {
                setOpenSection(openSection === 'friends' ? null : 'friends');
                setFriendsPage(0);
              }}
            >
              <span>Друзья</span>
              <span className="ml-2">{formatNumber(friendsList.length)}</span>
              <span className={`ml-2 transition-transform ${openSection === 'friends' ? 'rotate-90' : ''}`}>▶</span>
            </button>
            {openSection === 'friends' && (
              <div className="mt-2">
                {getPage(friendsList, friendsPage).map(u => {
                  const isCurrentUser = user && u.username === user.username;
                  const displayUser = isCurrentUser ? user : u;

                  return (
                    <div key={u.username} className="flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                      <div className={`w-8 h-8 rounded-full overflow-hidden ${u.is_online ? 'ring-2 ring-green-500' : ''}`}>
                        <img src={getCachedUserAvatar(displayUser, 'mini', backendUrl, imageVersion)} alt="avatar" className="w-full h-full object-cover" />
                      </div>
                      <div className="flex-1 min-w-0">
                        {u.display_name === 'Аккаунт удален' ? (
                          <span className="text-gray-500 dark:text-gray-400 text-sm block truncate">
                            Аккаунт удален
                          </span>
                        ) : (
                          <Link to={`/lpu/${u.username}`} className="text-blue-600 dark:text-blue-400 hover:underline text-sm block truncate">
                            {u.display_name || u.username}
                          </Link>
                        )}
                        <div className="flex items-center gap-1">
                          <span className={`w-2 h-2 rounded-full ${u.is_online ? 'bg-green-500' : 'bg-red-500'}`}></span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {u.is_online ? 'В сети' : u.last_activity ? formatLastSeen(u.last_activity, u.gender) : ''}
                          </span>
                        </div>
                      </div>
                      {user && u.username !== user.username && (
                        <button
                          className="text-blue-500 hover:text-blue-700 text-xl p-1 bg-transparent border-none cursor-pointer"
                          title="Написать сообщение"
                          onClick={() => handleStartDialog(u)}
                        >
                          ✉️
                        </button>
                      )}
                    </div>
                  );
                })}
                {/* Пагинация */}
                {getPageCount(friendsList) > 1 && (
                  <div className="flex justify-between mt-2">
                    <button
                      className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded disabled:opacity-50"
                      disabled={friendsPage === 0}
                      onClick={() => setFriendsPage(p => p - 1)}
                    >
                      ◀
                    </button>
                    <span className="text-sm">
                      {friendsPage + 1} / {getPageCount(friendsList)}
                    </span>
                    <button
                      className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded disabled:opacity-50"
                      disabled={friendsPage >= getPageCount(friendsList) - 1}
                      onClick={() => setFriendsPage(p => p + 1)}
                    >
                      ▶
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Подписки */}
          <div>
            <button
              className="w-full flex justify-between items-center px-2 py-2 rounded bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 font-semibold text-gray-800 dark:text-gray-100"
              onClick={() => {
                setOpenSection(openSection === 'subscriptions' ? null : 'subscriptions');
                setSubscriptionsPage(0);
              }}
            >
              <span>Подписки</span>
              <span className="ml-2">{formatNumber(subscriptionsList.length)}</span>
              <span className={`ml-2 transition-transform ${openSection === 'subscriptions' ? 'rotate-90' : ''}`}>▶</span>
            </button>
            {openSection === 'subscriptions' && (
              <div className="mt-2">
                {getPage(subscriptionsList, subscriptionsPage).map(u => {
                  const isCurrentUser = user && u.username === user.username;
                  const displayUser = isCurrentUser ? user : u;

                  return (
                    <div key={u.username} className="flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                      <div className={`w-8 h-8 rounded-full overflow-hidden ${u.is_online ? 'ring-2 ring-green-500' : ''}`}>
                        <img src={getCachedUserAvatar(displayUser, 'mini', backendUrl, imageVersion)} alt="avatar" className="w-full h-full object-cover" />
                      </div>
                      <div className="flex-1 min-w-0">
                        {u.display_name === 'Аккаунт удален' ? (
                          <span className="text-gray-500 dark:text-gray-400 text-sm block truncate">
                            Аккаунт удален
                          </span>
                        ) : (
                          <Link to={`/lpu/${u.username}`} className="text-blue-600 dark:text-blue-400 hover:underline text-sm block truncate">
                            {u.display_name || u.username}
                          </Link>
                        )}
                        <div className="flex items-center gap-1">
                          <span className={`w-2 h-2 rounded-full ${u.is_online ? 'bg-green-500' : 'bg-red-500'}`}></span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {u.is_online ? 'В сети' : u.last_activity ? formatLastSeen(u.last_activity, u.gender) : ''}
                          </span>
                        </div>
                      </div>
                      {user && u.username !== user.username && (
                        <button
                          className="text-blue-500 hover:text-blue-700 text-xl p-1 bg-transparent border-none cursor-pointer"
                          title="Написать сообщение"
                          onClick={() => handleStartDialog(u)}
                        >
                          ✉️
                        </button>
                      )}
                    </div>
                  );
                })}
                {/* Пагинация */}
                {getPageCount(subscriptionsList) > 1 && (
                  <div className="flex justify-between mt-2">
                    <button
                      className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded disabled:opacity-50"
                      disabled={subscriptionsPage === 0}
                      onClick={() => setSubscriptionsPage(p => p - 1)}
                    >
                      ◀
                    </button>
                    <span className="text-sm">
                      {subscriptionsPage + 1} / {getPageCount(subscriptionsList)}
                    </span>
                    <button
                      className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded disabled:opacity-50"
                      disabled={subscriptionsPage >= getPageCount(subscriptionsList) - 1}
                      onClick={() => setSubscriptionsPage(p => p + 1)}
                    >
                      ▶
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSidebar;