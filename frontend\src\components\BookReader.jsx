import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Typography, Spin, message, But<PERSON>, Divider, Tooltip, Drawer } from 'antd';
import { ArrowLeftOutlined, MenuOutlined, SettingOutlined } from '@ant-design/icons';
import { useTheme } from '../theme/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { getBookCoverMiniUrl } from '../utils/bookCover';
import { csrfFetch } from '../utils/csrf';
import { useLibraryActivity } from '../hooks/useLibraryActivity';
import './BookReader.css';

const { Title } = Typography;
const backendUrl = import.meta.env.VITE_BACKEND_URL || '';

const BookReader = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [book, setBook] = useState(null);
  const [chapters, setChapters] = useState([]);
  const [chapter, setChapter] = useState(null);
  const [ageRestricted, setAgeRestricted] = useState(false);
  const [currentChapterIndex, setCurrentChapterIndex] = useState(0);
  const [showMenu, setShowMenu] = useState(false);
  const [fontSize, setFontSize] = useState(localStorage.getItem('reader_font_size') || '18px');
  const [lineHeight, setLineHeight] = useState(localStorage.getItem('reader_line_height') || '1.6');
  const [fontFamily, setFontFamily] = useState(localStorage.getItem('reader_font_family') || 'Georgia');
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [processedContent, setProcessedContent] = useState('');
  const contentRef = useRef(null);

  // Функция для проверки возраста пользователя
  const checkUserAge = useCallback((birthDate) => {
    if (!birthDate) return true; // Если дата рождения не указана, разрешаем доступ

    const today = new Date();
    const birth = new Date(birthDate);
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    // Проверяем, исполнилось ли уже 18 лет
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return age - 1 >= 18;
    }

    return age >= 18;
  }, []);

  // Функция для проверки доступа к книге 18+
  const checkBookAccess = useCallback((book, user) => {
    if (!book || book.age_rating !== '18+') {
      return true; // Доступ разрешен для всех книг кроме 18+
    }

    if (!user) {
      return false; // Неавторизованные пользователи не могут просматривать 18+ контент
    }

    return checkUserAge(user.birth_date);
  }, [checkUserAge]);
  
  // Обновляем активность в библиотеке при чтении
  useLibraryActivity(id);
  
  // Обработка контента главы при изменении главы
  useEffect(() => {
    if (chapter?.content) {
      processChapterContent(chapter.content).then(setProcessedContent);
    } else {
      setProcessedContent('');
    }
  }, [chapter, id]); // Добавляем id в зависимости для обновления при смене книги
  
  // Отслеживание времени чтения для автодобавления в библиотеку
  const sessionRef = useRef({
    sessionId: null,
    startTime: null,
    lastActivity: null,
    isActive: false
  });

  // Функции для работы с сессиями чтения
  const startReadingSession = async (bookId) => {
    if (!user || !bookId) return;
    
    try {
      const response = await csrfFetch('/api/reading-sessions/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          book_id: bookId,
          action: 'start_session'
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        sessionRef.current = {
          sessionId: data.session_id,
          startTime: Date.now(),
          lastActivity: Date.now(),
          isActive: true
        };
        console.log('Reading session started:', data.session_id);
      }
    } catch (error) {
      console.error('Error starting reading session:', error);
    }
  };

  const updateReadingActivity = async () => {
    if (!sessionRef.current.sessionId || !sessionRef.current.isActive) return;
    
    try {
      await csrfFetch(`/api/reading-sessions/${sessionRef.current.sessionId}/update_activity/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({})
      });
      
      sessionRef.current.lastActivity = Date.now();
      console.log('Reading activity updated');
    } catch (error) {
      console.error('Error updating reading activity:', error);
    }
  };

  const closeReadingSession = async () => {
    if (!sessionRef.current.sessionId) return;
    
    try {
      await csrfFetch(`/api/reading-sessions/${sessionRef.current.sessionId}/close_session/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({})
      });
      
      console.log('Reading session closed');
      sessionRef.current = {
        sessionId: null,
        startTime: null,
        lastActivity: null,
        isActive: false
      };
    } catch (error) {
      console.error('Error closing reading session:', error);
    }
  };

  // Эффект для управления сессией чтения
  useEffect(() => {
    if (book && user) {
      // Стартуем сессию чтения
      startReadingSession(book.id);
      
      // Обновляем активность каждые 30 секунд
      const activityInterval = setInterval(() => {
        updateReadingActivity();
      }, 30000);
      
      // Закрываем сессию при размонтировании компонента
      return () => {
        clearInterval(activityInterval);
        closeReadingSession();
      };
    }
  }, [book, user]);

  // Отслеживание активности пользователя (скролл, клики)
  useEffect(() => {
    const handleActivity = () => {
      if (sessionRef.current.isActive) {
        sessionRef.current.lastActivity = Date.now();
      }
    };

    // Отслеживаем скролл и клики как признаки активности
    window.addEventListener('scroll', handleActivity);
    window.addEventListener('click', handleActivity);
    window.addEventListener('keydown', handleActivity);

    return () => {
      window.removeEventListener('scroll', handleActivity);
      window.removeEventListener('click', handleActivity);
      window.removeEventListener('keydown', handleActivity);
    };
  }, []);

  // Загрузка книги и глав
  useEffect(() => {
    const fetchBook = async () => {
      try {
        setLoading(true);
        // Запрос данных книги
        const response = await fetch(`${backendUrl}/api/books/${id}/`);
        if (!response.ok) {
          throw new Error('Не удалось загрузить книгу');
        }
        
        const data = await response.json();

        // Проверяем доступ к книге 18+
        if (!checkBookAccess(data, user)) {
          setAgeRestricted(true);
          setLoading(false);
          return;
        }

        setBook(data);
        
        // Запрос глав книги
        const chaptersResponse = await fetch(`${backendUrl}/api/books/${id}/chapters/`);
        if (!chaptersResponse.ok) {
          throw new Error('Не удалось загрузить главы книги');
        }
        
        const chaptersData = await chaptersResponse.json();
        console.log('Chapters data:', chaptersData);
        
        let chaptersArray = [];
        
        // Проверяем, есть ли главы в results (для пагинированного API)
        if (chaptersData.results && chaptersData.results.length > 0) {
          chaptersArray = chaptersData.results;
        } else if (Array.isArray(chaptersData) && chaptersData.length > 0) {
          // Для обратной совместимости, если API возвращает просто массив
          chaptersArray = chaptersData;
        }
        
        if (chaptersArray.length > 0) {
          setChapters(chaptersArray);
          
          // Проверяем, есть ли hash в URL для перехода к конкретной главе
          const hash = window.location.hash;
          let targetChapterIndex = 0;
          let targetChapterId = chaptersArray[0].id;
          
          if (hash.startsWith('#chapter-')) {
            const chapterIdFromHash = parseInt(hash.replace('#chapter-', ''));
            const foundIndex = chaptersArray.findIndex(ch => ch.id === chapterIdFromHash);
            
            if (foundIndex !== -1) {
              targetChapterIndex = foundIndex;
              targetChapterId = chapterIdFromHash;
              console.log(`Переход к главе из hash: ${chapterIdFromHash}, индекс: ${foundIndex}`);
            } else {
              console.log(`Глава с ID ${chapterIdFromHash} не найдена, загружаем первую главу`);
            }
          }
          
          // Загружаем нужную главу
          await loadChapter(targetChapterId);
          setCurrentChapterIndex(targetChapterIndex);
          
          // Очищаем hash из URL после загрузки
          if (hash) {
            window.history.replaceState(null, null, window.location.pathname);
          }
        } else {
          console.log('У этой книги нет глав');
          setChapters([]);
          setChapter(null);
        }
      } catch (error) {
        console.error('Ошибка при загрузке книги:', error);
        message.error('Произошла ошибка при загрузке книги');
      } finally {
        setLoading(false);
      }
    };

    fetchBook();
  }, [id]);

  // Загрузка конкретной главы
  const loadChapter = async (chapterId) => {
    try {
      const response = await fetch(`${backendUrl}/api/chapters/${chapterId}/`);
      if (!response.ok) {
        throw new Error('Не удалось загрузить главу');
      }
      
      const data = await response.json();
      console.log('Chapter data:', data);
      setChapter(data);
      return data;
    } catch (error) {
      console.error('Ошибка при загрузке главы:', error);
      message.error('Произошла ошибка при загрузке главы');
      return null;
    }
  };

  // Навигация между главами
  const navigateToChapter = async (index) => {
    if (index >= 0 && index < chapters.length) {
      setLoading(true);
      try {
        await loadChapter(chapters[index].id);
        setCurrentChapterIndex(index);
        // Прокрутка в начало страницы при смене главы
        window.scrollTo(0, 0);
      } catch (error) {
        console.error('Ошибка при навигации между главами:', error);
        message.error('Произошла ошибка при загрузке главы');
      } finally {
        setLoading(false);
      }
    }
  };

  // Функция для извлечения путей к изображениям из HTML
  const extractImagePaths = (html) => {
    if (!html) return [];
    const div = document.createElement('div');
    div.innerHTML = html;
    
    // Получаем все пути из src атрибутов изображений
    const paths = Array.from(div.querySelectorAll('img'))
      .map(img => img.getAttribute('src'))
      .filter(src => src && !src.startsWith('data:'));
    
    // Фильтруем только медиа пути (не внешние URL)
    const mediaPaths = paths.filter(path => {
      // Проверяем различные форматы медиа путей
      return (path.includes('/media/') || 
             !path.startsWith('http') || 
             path.includes('/book_pics/'));
    });
    
    console.log('Extracted image paths:', mediaPaths);
    return mediaPaths;
  };

  // Функция для получения значения cookie по имени (универсальная)
  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  const getPresignedUrls = async (imagePaths) => {
    if (!imagePaths || imagePaths.length === 0) return {};
    
    console.log('Getting presigned URLs for paths:', imagePaths);
    const csrfToken = getCookie('csrftoken');
    
    try {
      const response = await fetch(`${backendUrl}/api/books/${id}/get_reader_image_links/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({ paths: imagePaths }),
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('Got presigned URLs:', Object.keys(result).length);
        return result;
      } else {
        console.error('Error getting presigned URLs:', response.status);
        return {};
      }
    } catch (error) {
      console.error('Failed to get presigned URLs:', error);
      return {};
    }
  };

  // Функция для полной обработки контента главы с поддержкой всех стилей редактора
  const processChapterContent = async (content) => {
    if (!content) return content;
    
    console.log('Processing chapter content...');
    
    // Получаем presigned URL для изображений
    const imagePaths = extractImagePaths(content);
    console.log(`Found ${imagePaths.length} image paths in chapter content`);
    
    // Проверка и получение временных URL
    const urlMap = await getPresignedUrls(imagePaths);
    console.log(`Received ${Object.keys(urlMap).length} presigned URLs`);
    
    // Создаем DOM для манипуляции с контентом
    const div = document.createElement('div');
    div.innerHTML = content;
    
    // Обрабатываем изображения - точно как в редакторе
    div.querySelectorAll('img').forEach(img => {
      const src = img.getAttribute('src');
      
      // Если URL уже абсолютный и не требует замены
      if (src && (src.startsWith('http') || src.startsWith('data:'))) {
        console.log('Skipping external image:', src);
        return;
      }
      
      // Если есть presigned URL для этого пути, заменяем src
      if (src && urlMap[src]) {
        console.log(`Replacing path ${src} with presigned URL`);
        img.setAttribute('src', urlMap[src]);
      } else {
        console.log(`No presigned URL for path: ${src}`);
      }
      
      // Проверяем, находится ли изображение в кастомном wrapper
      const isInWrapper = img.closest('.custom-resizable-image');
      
      // Если изображение уже в wrapper, просто обновляем его свойства
      if (isInWrapper) {
        return;
      }
      
      // Получаем атрибуты изображения
      const caption = img.getAttribute('data-caption');
      const dataWidth = img.getAttribute('data-width');
      const align = img.getAttribute('data-align') || 'center';
      const textWrap = img.getAttribute('data-text-wrap');
      
      // Определяем класс контейнера ТОЧНО как в редакторе
      // В редакторе: cssClass = textWrap === 'wrap' ? 'float-left' : 'align-left'
      let containerCssClass = '';
      if (align === 'left') {
        containerCssClass = textWrap === 'wrap' ? 'float-left' : 'align-left';
      } else if (align === 'right') {
        containerCssClass = textWrap === 'wrap' ? 'float-right' : 'align-right';
      } else {
        containerCssClass = 'align-center';
      }
      
      // Создаем NodeViewWrapper ТОЧНО как в редакторе
      const wrapper = document.createElement('div');
      wrapper.className = `custom-resizable-image ${containerCssClass}`;
      
      // Устанавливаем inline стили ТОЧНО как в NodeViewWrapper
      if (dataWidth) {
        const widthPercent = Math.max(25, Math.min(100, parseInt(dataWidth, 10) || 100));
        wrapper.style.position = 'relative';
        wrapper.style.width = widthPercent + '%';
        wrapper.style.maxWidth = '100%';
      } else {
        // Устанавливаем базовые стили для wrapper
        wrapper.style.position = 'relative';
        wrapper.style.maxWidth = '100%';
      }
      
      // Вставляем wrapper перед изображением
      img.parentNode.insertBefore(wrapper, img);
      
      // Перемещаем изображение в wrapper
      wrapper.appendChild(img);
      
      // Настраиваем стили изображения ТОЧНО как в редакторе
      img.style.width = '100%';
      img.style.maxWidth = '100%';
      img.style.display = 'block';
      img.style.borderRadius = '8px';
      img.style.height = 'auto';
      img.draggable = false;
      img.style.userSelect = 'none';
      img.style.WebkitUserSelect = 'none';
      img.style.pointerEvents = 'none';
      
      // Применяем обтекание текстом - КРИТИЧЕСКИ ВАЖНО для floating изображений
      if (textWrap === 'wrap' && (align === 'left' || align === 'right')) {
        // Применяем shape-outside к изображению
        img.style.shapeOutside = 'margin-box';
        img.style.webkitShapeOutside = 'margin-box';
        
        // Применяем float стили к wrapper-элементу для правильного обтекания
        if (align === 'left') {
          wrapper.style.float = 'left';
          wrapper.style.margin = '0 16px 16px 0';
          wrapper.style.clear = 'none';
        } else if (align === 'right') {
          wrapper.style.float = 'right';
          wrapper.style.margin = '0 0 16px 16px';
          wrapper.style.clear = 'none';
        }
        wrapper.style.zIndex = '10';
        wrapper.style.position = 'relative';
        wrapper.style.display = 'block';
      }
      
      // Добавляем подпись если есть
      if (caption && caption.trim()) {
        const captionDiv = document.createElement('div');
        captionDiv.className = 'image-caption';
        captionDiv.textContent = caption.trim();
        wrapper.appendChild(captionDiv);
      }
      
      // Сохраняем оригинальные атрибуты
      if (img.getAttribute('alt')) img.alt = img.getAttribute('alt');
      if (img.getAttribute('title')) img.title = img.getAttribute('title');
    });
    
    // Обрабатываем уже существующие wrapper-элементы (для обратной совместимости)
    div.querySelectorAll('.custom-resizable-image').forEach(wrapper => {
      const img = wrapper.querySelector('img');
      if (!img) return;
      
      // Подставляем presigned URL
      const src = img.getAttribute('src');
      if (src && urlMap[src]) { // Use urlMap here
        img.setAttribute('src', urlMap[src]);
      }
      
      // Убеждаемся, что стили применены правильно
      if (wrapper.classList.contains('float-left') || wrapper.classList.contains('float-right')) {
        img.style.shapeOutside = 'margin-box';
        img.style.webkitShapeOutside = 'margin-box';
      }
    });
    
    // Обрабатываем текстовые стили - убеждаемся что все inline стили сохранены
    div.querySelectorAll('span[style]').forEach(span => {
      // Стили уже применены через атрибут style, просто убеждаемся что они не перезаписаны
      const style = span.getAttribute('style');
      if (style) {
        span.setAttribute('style', style);
      }
    });
    
    // Обрабатываем выравнивание параграфов
    div.querySelectorAll('p[style*="text-align"]').forEach(p => {
      const style = p.getAttribute('style');
      if (style) {
        p.setAttribute('style', style);
      }
    });
    
    return div.innerHTML;
  };

  const saveReaderSettings = (setting, value) => {
    switch (setting) {
      case 'fontSize':
        setFontSize(value);
        localStorage.setItem('reader_font_size', value);
        break;
      case 'lineHeight':
        setLineHeight(value);
        localStorage.setItem('reader_line_height', value);
        break;
      case 'fontFamily':
        setFontFamily(value);
        localStorage.setItem('reader_font_family', value);
        break;
      default:
        break;
    }
  };

  // Проверка возрастных ограничений
  if (ageRestricted) {
    return (
      <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-100'} flex items-center justify-center`}>
        <div className="max-w-md mx-auto p-8">
          <div className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 rounded p-8 text-center">
            <div className="text-6xl mb-4">🔞</div>
            <h2 className="text-2xl font-bold mb-4">Возрастное ограничение 18+</h2>
            <p className="text-lg mb-4">
              Данное произведение содержит контент, предназначенный только для совершеннолетних читателей.
            </p>
            {!user ? (
              <div>
                <p className="mb-4">Для просмотра этого контента необходимо войти в систему и указать дату рождения в профиле.</p>
                <Button
                  type="primary"
                  size="large"
                  onClick={() => navigate('/login')}
                >
                  Войти в систему
                </Button>
              </div>
            ) : (
              <div>
                <p className="mb-4">
                  {user.birth_date
                    ? 'Вам еще не исполнилось 18 лет. Доступ к данному контенту ограничен.'
                    : 'Для просмотра контента 18+ необходимо указать дату рождения в настройках профиля.'
                  }
                </p>
                {!user.birth_date && (
                  <Button
                    type="primary"
                    size="large"
                    onClick={() => navigate('/profile/settings')}
                  >
                    Настройки профиля
                  </Button>
                )}
              </div>
            )}
            <div className="mt-4">
              <Button
                type="default"
                onClick={() => navigate(`/book/${id}`)}
              >
                Вернуться к книге
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-100'} flex flex-col`}>
      <div className="max-w-5xl mx-auto w-full flex-grow flex flex-col">
        {loading ? (
          <div className="flex-grow flex items-center justify-center">
            <Spin size="large" />
          </div>
        ) : (
          <div className="flex-grow flex flex-col">
            {/* Верхняя панель с заголовком и кнопками */}
            <div className="py-4 px-4 flex items-center justify-between bg-white dark:bg-gray-800 shadow-sm rounded-t-lg">
              <div className="flex items-center">
                <Button 
                  type="text" 
                  icon={<ArrowLeftOutlined />} 
                  onClick={() => navigate(`/book/${id}`)} 
                  className="mr-4"
                />
                <h1 className="text-xl font-semibold m-0 mr-2">{book?.title}</h1>
                {chapter && <span className="text-gray-500">— {chapter.title}</span>}
              </div>
              <div className="flex items-center">
                <Tooltip title="Содержание">
                  <Button 
                    type="text" 
                    icon={<MenuOutlined />} 
                    onClick={() => setShowMenu(!showMenu)} 
                    className="mr-2"
                  />
                </Tooltip>
                <Tooltip title="Настройки чтения">
                  <Button 
                    type="text" 
                    icon={<SettingOutlined />} 
                    onClick={() => setSettingsVisible(!settingsVisible)} 
                  />
                </Tooltip>
              </div>
            </div>
            
            {/* Панель настроек чтения */}
            {settingsVisible && (
              <div className="bg-white dark:bg-gray-800 p-4 mb-4 rounded-lg shadow-md">
                <div className="flex flex-wrap gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Размер шрифта</label>
                    <select 
                      value={fontSize} 
                      onChange={(e) => saveReaderSettings('fontSize', e.target.value)}
                      className="border rounded p-1"
                      style={{
                        background: theme === 'dark' ? '#374151' : '#ffffff',
                        borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',
                        color: theme === 'dark' ? '#ffffff' : '#000000'
                      }}
                    >
                      <option value="14px">Маленький</option>
                      <option value="18px">Средний</option>
                      <option value="22px">Большой</option>
                      <option value="26px">Очень большой</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Межстрочный интервал</label>
                    <select 
                      value={lineHeight} 
                      onChange={(e) => saveReaderSettings('lineHeight', e.target.value)}
                      className="border rounded p-1"
                      style={{
                        background: theme === 'dark' ? '#374151' : '#ffffff',
                        borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',
                        color: theme === 'dark' ? '#ffffff' : '#000000'
                      }}
                    >
                      <option value="1.4">Узкий</option>
                      <option value="1.6">Стандартный</option>
                      <option value="1.8">Широкий</option>
                      <option value="2">Очень широкий</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Шрифт</label>
                    <select 
                      value={fontFamily} 
                      onChange={(e) => saveReaderSettings('fontFamily', e.target.value)}
                      className="border rounded p-1"
                      style={{
                        background: theme === 'dark' ? '#374151' : '#ffffff',
                        borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',
                        color: theme === 'dark' ? '#ffffff' : '#000000'
                      }}
                    >
                      <option value="Georgia">Georgia</option>
                      <option value="Arial">Arial</option>
                      <option value="Verdana">Verdana</option>
                      <option value="Times New Roman">Times New Roman</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
            
            {/* Основной контент */}
            <div className="flex-grow flex">
              {/* Боковое меню с главами */}
              <Drawer 
                title="Содержание" 
                placement="left"
                onClose={() => setShowMenu(false)}
                open={showMenu}
                width={300}
              >
                {chapters.length > 0 ? (
                  <div className="flex flex-col">
                    {chapters.map((chapterItem, index) => (
                      <div 
                        key={chapterItem.id} 
                        className={`p-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${index === currentChapterIndex ? 'bg-blue-50 dark:bg-blue-900/30 font-medium' : ''}`}
                        onClick={() => {
                          navigateToChapter(index);
                          setShowMenu(false);
                        }}
                      >
                        <div className="flex items-center">
                          <span className="mr-2 text-gray-500">{index + 1}.</span>
                          <span>{chapterItem.title}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p>У этой книги пока нет глав.</p>
                  </div>
                )}
              </Drawer>

              {/* Контент главы */}
              <main className="flex-grow bg-white dark:bg-gray-800 p-6 rounded-b-lg shadow-md" style={{ width: '100%', maxWidth: 'none' }}>
                {chapter ? (
                  <div className="mb-6">
                    <h2 className="text-2xl font-bold mb-4">{chapter.title}</h2>
                    {processedContent ? (
                      <div 
                        ref={contentRef}
                        className={`prose dark:prose-invert max-w-none chapter-content ${book?.auto_indent ? 'tiptap-indent' : ''}`} 
                        style={{ 
                          fontSize, 
                          lineHeight,
                          fontFamily,
                          width: '100%',
                          maxWidth: 'none',
                          userSelect: 'none',
                          WebkitUserSelect: 'none',
                          MozUserSelect: 'none',
                          msUserSelect: 'none'
                        }}
                        dangerouslySetInnerHTML={{ __html: processedContent }}
                      />
                    ) : (
                      <div className="flex justify-center py-8">
                        <Spin size="large" />
                      </div>
                    )}
                    
                    <div className="mt-8 flex justify-between">
                      <Button 
                        disabled={currentChapterIndex === 0} 
                        onClick={() => navigateToChapter(currentChapterIndex - 1)}
                        className="dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600"
                      >
                        Предыдущая глава
                      </Button>
                      <Button 
                        disabled={currentChapterIndex === chapters.length - 1} 
                        type="primary"
                        onClick={() => navigateToChapter(currentChapterIndex + 1)}
                      >
                        Следующая глава
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-10">
                    <p className="text-lg">У этой книги пока нет глав.</p>
                    <Button 
                      type="primary" 
                      onClick={() => navigate(`/book/${id}`)}
                      className="mt-4"
                    >
                      Вернуться к книге
                    </Button>
                  </div>
                )}
              </main>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BookReader;