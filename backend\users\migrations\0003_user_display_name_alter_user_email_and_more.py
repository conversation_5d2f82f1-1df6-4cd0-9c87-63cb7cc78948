# Generated by Django 4.2.7 on 2025-05-04 19:41

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_remove_user_is_author_user_author_rating_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='display_name',
            field=models.CharField(default='Пользователь', help_text='Your name or pseudonym that will be displayed on the site.', max_length=150, verbose_name='display name'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='user',
            name='email',
            field=models.EmailField(max_length=254, unique=True, verbose_name='email address'),
        ),
        migrations.AlterField(
            model_name='user',
            name='username',
            field=models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters and digits only.', max_length=150, unique=True, validators=[django.core.validators.RegexValidator(code='invalid_username', message='Username must contain only lowercase letters and numbers.', regex='^[a-z0-9]+$')], verbose_name='username'),
        ),
    ]
