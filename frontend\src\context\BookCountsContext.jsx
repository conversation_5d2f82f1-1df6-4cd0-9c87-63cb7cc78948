import React, { createContext, useContext, useState, useCallback } from 'react';

const BookCountsContext = createContext();

export const useBookCounts = () => {
  const context = useContext(BookCountsContext);
  if (!context) {
    throw new Error('useBookCounts must be used within a BookCountsProvider');
  }
  return context;
};

export const BookCountsProvider = ({ children }) => {
  // Кеш счетчиков по username
  const [countsCache, setCountsCache] = useState(new Map());

  // Получить счетчики для пользователя
  const getCounts = useCallback((username) => {
    if (!username) return { finished: 0, inProgress: 0, drafts: 0, total: 0 };
    
    const cached = countsCache.get(username);
    if (cached) {
      return cached;
    }
    
    return { finished: 0, inProgress: 0, drafts: 0, total: 0 };
  }, [countsCache]);

  // Установить счетчики для пользователя
  const setCounts = useCallback((username, counts) => {
    if (!username) return;
    
    setCountsCache(prev => {
      const newCache = new Map(prev);
      newCache.set(username, counts);
      return newCache;
    });
  }, []);

  // Обновить счетчики на основе массива книг
  const updateCountsFromBooks = useCallback((username, books) => {
    if (!username || !Array.isArray(books)) return;

    const visibleBooks = books.filter(b => b.creation_status !== 'creating');
    const finished = visibleBooks.filter(b => b.status === 'finished').length;
    const inProgress = visibleBooks.filter(b => b.status === 'in_progress').length;
    const drafts = visibleBooks.filter(b => b.status === 'draft').length;
    const total = visibleBooks.length;

    const newCounts = { finished, inProgress, drafts, total };
    
    // Проверяем, изменились ли счетчики, чтобы избежать лишних обновлений
    const currentCounts = countsCache.get(username);
    if (currentCounts && 
        currentCounts.finished === newCounts.finished &&
        currentCounts.inProgress === newCounts.inProgress &&
        currentCounts.drafts === newCounts.drafts &&
        currentCounts.total === newCounts.total) {
      return currentCounts; // Возвращаем текущие, если они не изменились
    }
    
    setCounts(username, newCounts);
    
    return newCounts;
  }, [setCounts, countsCache]);

  // Удалить книгу из счетчиков
  const removeBookFromCounts = useCallback((username, bookStatus) => {
    if (!username) return;
    
    const currentCounts = getCounts(username);
    const newCounts = { ...currentCounts };
    
    if (bookStatus.status === 'finished') {
      newCounts.finished = Math.max(0, newCounts.finished - 1);
    } else if (bookStatus.status === 'in_progress') {
      newCounts.inProgress = Math.max(0, newCounts.inProgress - 1);
    } else if (bookStatus.status === 'draft') {
      newCounts.drafts = Math.max(0, newCounts.drafts - 1);
    }
    
    newCounts.total = Math.max(0, newCounts.total - 1);
    setCounts(username, newCounts);
    
    return newCounts;
  }, [getCounts, setCounts]);

  // Обновить статус книги в счетчиках
  const updateBookStatusInCounts = useCallback((username, oldStatus, newStatus) => {
    if (!username) return;
    
    const currentCounts = getCounts(username);
    const newCounts = { ...currentCounts };
    
    // Убираем из старой категории
    if (oldStatus.status === 'finished') {
      newCounts.finished = Math.max(0, newCounts.finished - 1);
    } else if (oldStatus.status === 'in_progress') {
      newCounts.inProgress = Math.max(0, newCounts.inProgress - 1);
    } else if (oldStatus.status === 'draft') {
      newCounts.drafts = Math.max(0, newCounts.drafts - 1);
    }
    
    // Добавляем в новую категорию
    if (newStatus.status === 'finished') {
      newCounts.finished += 1;
    } else if (newStatus.status === 'in_progress') {
      newCounts.inProgress += 1;
    } else if (newStatus.status === 'draft') {
      newCounts.drafts += 1;
    }
    
    setCounts(username, newCounts);
    
    return newCounts;
  }, [getCounts, setCounts]);

  // Очистить кеш для пользователя
  const clearCounts = useCallback((username) => {
    if (!username) return;
    
    setCountsCache(prev => {
      const newCache = new Map(prev);
      newCache.delete(username);
      return newCache;
    });
  }, []);

  const value = {
    getCounts,
    setCounts,
    updateCountsFromBooks,
    removeBookFromCounts,
    updateBookStatusInCounts,
    clearCounts,
  };

  return (
    <BookCountsContext.Provider value={value}>
      {children}
    </BookCountsContext.Provider>
  );
}; 