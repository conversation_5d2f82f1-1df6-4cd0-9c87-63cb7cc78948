from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.validators import RegexValidator
from .models import User, Dialog, Message, UserHeaderImage, UserAvatar, MessageAttachment, UserStats, UserMetricHistory, RatingCalculationRule, Subscription, FriendRequest, FeedEvent, UserNotification, UserLibrary, ReadingSession
import os
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import uuid
import re
from django.utils import timezone
from django.db import transaction, IntegrityError
from books.serializers import BookListSerializer

class UserSerializer(serializers.ModelSerializer):
    total_rating = serializers.SerializerMethodField()
    avatar_url = serializers.SerializerMethodField()
    avatar_thumbnail_url = serializers.SerializerMethodField()
    system_avatar_url = serializers.SerializerMethodField()
    system_avatar_thumbnail_url = serializers.SerializerMethodField()
    display_name_safe = serializers.SerializerMethodField()
    avatar_updated_at = serializers.DateTimeField(read_only=True)
    avatar_type = serializers.IntegerField(read_only=True)
    birth_date = serializers.DateField(read_only=True)
    show_birth_date = serializers.BooleanField(read_only=True)
    timezone = serializers.CharField(read_only=True)
    timezone_display_value = serializers.CharField(read_only=True)
    is_deleted = serializers.BooleanField(read_only=True)
    deleted_at = serializers.DateTimeField(read_only=True)
    header_bg_image_url = serializers.SerializerMethodField()
    books_blocks_order = serializers.ListField(child=serializers.CharField(), required=False)

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'bio', 'reader_rating', 'author_rating', 'avatar', 'avatar_thumbnail',
            'avatar_preset', 'avatar_preset_mini', 'display_name', 'display_name_safe', 'total_rating', 'motto',
            'header_bg_type', 'header_bg_color1', 'header_bg_color2', 'header_bg_image',
            'header_frame', 'hide_email', 'gender', 'last_activity',
            'auto_remove_on_unfriend', 'auto_accept_friends',
            'show_unsubscribes_in_feed', 'show_removed_from_friends_in_feed',
            'avatar_url', 'avatar_thumbnail_url', 'system_avatar_url', 'system_avatar_thumbnail_url',
            'avatar_updated_at', 'avatar_type', 'birth_date', 'show_birth_date', 'timezone', 'timezone_display_value',
            'header_bg_image_url', 'books_blocks_order', 'is_deleted', 'deleted_at',
        ]
        read_only_fields = [
            'reader_rating', 'author_rating', 'total_rating',
            'avatar_url', 'avatar_thumbnail_url', 'system_avatar_url', 'system_avatar_thumbnail_url'
        ]

    def get_total_rating(self, obj):
        return (obj.reader_rating or 0) + (obj.author_rating or 0)

    def get_display_name_safe(self, obj):
        """Возвращает безопасное отображаемое имя пользователя"""
        if getattr(obj, 'is_deleted', False):
            return 'Аккаунт удален'
        return obj.display_name

    def get_avatar_url(self, obj):
        # Используем кэшированный URL для всех компонентов, кроме настроек профиля
        request = self.context.get('request')
        if request and request.path.startswith('/api/users/profile/settings/'):
            # Старая логика для настроек профиля
            if getattr(obj, 'avatar_type', 1) == 1:
                return self.get_system_avatar_url(obj)
            if obj.avatar:
                return obj.avatar.url
            return None
        # Новая логика для всех остальных компонентов (уже учитывает is_deleted в модели)
        return obj.avatar_url_cached

    def get_avatar_thumbnail_url(self, obj):
        # Используем кэшированный URL для всех компонентов, кроме настроек профиля
        request = self.context.get('request')
        if request and request.path.startswith('/api/users/profile/settings/'):
            # Старая логика для настроек профиля
            if getattr(obj, 'avatar_type', 1) == 1:
                return self.get_system_avatar_thumbnail_url(obj)
            if obj.avatar_thumbnail:
                return obj.avatar_thumbnail.url
            return None
        # Новая логика для всех остальных компонентов (уже учитывает is_deleted в модели)
        return obj.avatar_thumbnail_url_cached

    def get_system_avatar_url(self, obj):
        preset = obj.get_system_avatar_path()
        if preset:
            return f'{settings.STATIC_URL}{preset}'
        return None

    def get_system_avatar_thumbnail_url(self, obj):
        # Для системного аватара всегда возвращаем полный аватар
        preset = obj.get_system_avatar_path()
        if preset:
            return f'{settings.STATIC_URL}{preset}'
        return None

    def get_header_bg_image_url(self, obj):
        url = obj.header_bg_image
        if not url:
            return None
        url_str = getattr(url, 'name', str(url))
        if url_str.startswith('http'):
            return url_str
        return f"https://storage.yandexcloud.net/lpo-test/media/public/{url_str}"

    def validate_header_bg_type(self, value):
        if value and value not in ['solid', 'gradient', 'image', '']:
            raise serializers.ValidationError("header_bg_type must be one of: solid, gradient, image")
        return value

    def validate_header_bg_color1(self, value):
        if value and not re.match(r'^#[0-9a-fA-F]{6}$', value):
            raise serializers.ValidationError("header_bg_color1 must be a valid hex color (e.g. #FF0000)")
        return value

    def validate_header_bg_color2(self, value):
        if value and not re.match(r'^#[0-9a-fA-F]{6}$', value):
            raise serializers.ValidationError("header_bg_color2 must be a valid hex color (e.g. #FF0000)")
        return value

    def validate(self, data):
        # Если тип фона - градиент, требуем оба цвета
        if data.get('header_bg_type') == 'gradient':
            if not data.get('header_bg_color1') or not data.get('header_bg_color2'):
                raise serializers.ValidationError("Both colors are required for gradient background")
        # Если тип фона - solid, требуем только первый цвет
        elif data.get('header_bg_type') == 'solid':
            if not data.get('header_bg_color1'):
                raise serializers.ValidationError("Color is required for solid background")
        # Если тип фона - image, проверяем наличие изображения
        elif data.get('header_bg_type') == 'image':
            if not data.get('header_bg_image'):
                raise serializers.ValidationError("Image is required for image background")
        return data

class UserCreateSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])
    password2 = serializers.CharField(write_only=True, required=True)
    username = serializers.CharField(
        required=True,
        validators=[
            RegexValidator(
                regex='^[a-z0-9]+$',
                message='Username must contain only lowercase letters and numbers.',
                code='invalid_username'
            ),
        ]
    )
    display_name = serializers.CharField(required=True, max_length=150)
    email = serializers.EmailField(required=True)
    birth_date = serializers.DateField(required=True)
    show_birth_date = serializers.BooleanField(required=True)
    timezone = serializers.CharField(required=False)
    timezone_display_value = serializers.CharField(required=False)

    class Meta:
        model = User
        fields = ('username', 'display_name', 'email', 'password', 'password2', 'birth_date', 'show_birth_date', 'timezone', 'timezone_display_value')

    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs

    def create(self, validated_data):
        validated_data.pop('password2')
        try:
            with transaction.atomic():
                user = User.objects.create_user(**validated_data)
                user.set_default_avatar()
                return user
        except IntegrityError:
            raise serializers.ValidationError({'username': 'Пользователь с таким именем уже существует.'})
        except Exception as e:
            raise serializers.ValidationError({'error': str(e)})

class CharOrFileField(serializers.Field):
    def to_internal_value(self, data):
        # Если файл
        if hasattr(data, 'read'):
            return data
        # Если строка
        if isinstance(data, str):
            return data
        raise serializers.ValidationError('Значение должно быть строкой или файлом.')

    def to_representation(self, value):
        return value

class NullableImageField(serializers.ImageField):
    def to_internal_value(self, data):
        print("DEBUG: NullableImageField.to_internal_value data =", data)
        if data in (None, '', 'null'):
            print("DEBUG: Returning None from NullableImageField")
            return None
        return super().to_internal_value(data)

    def to_representation(self, value):
        return value

class UserUpdateSerializer(serializers.ModelSerializer):
    header_bg_image = CharOrFileField(required=False, allow_null=True)
    avatar = CharOrFileField(required=False, allow_null=True)
    avatar_thumbnail = CharOrFileField(required=False, allow_null=True)
    avatar_preset = serializers.CharField(required=False, allow_blank=True)
    avatar_preset_mini = serializers.CharField(required=False, allow_blank=True)
    birth_date = serializers.DateField(required=False)
    show_birth_date = serializers.BooleanField(required=False)
    timezone = serializers.CharField(required=False)
    timezone_display_value = serializers.CharField(required=False)

    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'bio', 'avatar', 'avatar_thumbnail',
            'avatar_preset', 'avatar_preset_mini', 'motto', 'display_name',
            'header_bg_type', 'header_bg_color1', 'header_bg_color2', 'header_bg_image', 'header_frame',
            'gender',
            'hide_email', 'auto_remove_on_unfriend', 'auto_accept_friends',
            'show_unsubscribes_in_feed', 'show_removed_from_friends_in_feed',
            'birth_date', 'show_birth_date', 'timezone', 'timezone_display_value',
        ]

    def update(self, instance, validated_data):
        request = self.context.get('request')

        # Обработка сброса аватара (avatar: None)
        if 'avatar' in validated_data and validated_data['avatar'] is None:
            instance.reset_to_system_avatar()
            instance.refresh_from_db()
            validated_data.pop('avatar', None)
            # После сброса продолжаем обновление остальных полей
            return super().update(instance, validated_data)

        # --- ДОБАВЛЕНО: применение пользовательского аватара по относительному пути ---
        if 'avatar' in validated_data and isinstance(validated_data['avatar'], str):
            instance.avatar = validated_data['avatar']
            instance.avatar_type = 2
            instance.avatar_updated_at = timezone.now()
            validated_data.pop('avatar', None)
            
            # Если есть миниатюра, обновляем её тоже
            if 'avatar_thumbnail' in validated_data and isinstance(validated_data['avatar_thumbnail'], str):
                instance.avatar_thumbnail = validated_data['avatar_thumbnail']
                validated_data.pop('avatar_thumbnail', None)
            
            # Обновляем кэшированные URL
            instance.update_avatar_urls()
            
            # Сохраняем изменения
            instance.save(update_fields=[
                'avatar', 'avatar_thumbnail', 'avatar_type', 
                'avatar_updated_at', 'avatar_url_cached', 
                'avatar_thumbnail_url_cached'
            ])
            
            # Возвращаем обновленный экземпляр
            return instance

        # Обработка аватара (загрузка файла)
        if 'avatar' in validated_data:
            avatar = validated_data['avatar']
            if hasattr(avatar, 'read'):  # Если файл - загружаем
                instance.use_custom_avatar()
                instance.avatar = avatar
                instance.avatar_type = 2
                instance.avatar_updated_at = timezone.now()
                instance.save()
                from .utils import create_avatar_thumbnail
                create_avatar_thumbnail(instance)
            validated_data.pop('avatar', None)

        # Обработка фона хедера
        if 'header_bg_image' in validated_data:
            new_image = validated_data['header_bg_image']
            if isinstance(new_image, str):
                # Абсолютный путь (пресет/рамка) или относительный (пользовательский хедер) — сохраняем как есть
                validated_data['header_bg_image'] = new_image
            elif hasattr(new_image, 'read'):
                # Удаляем старый файл, если был и не пресет
                if instance.header_bg_image and not instance.header_bg_image.startswith('/static/header_presets/'):
                    try:
                        filename = instance.header_bg_image
                        if default_storage.exists(filename):
                            default_storage.delete(filename)
                    except Exception:
                        pass
                ext = os.path.splitext(new_image.name)[1] or '.jpg'
                filename = f"users/{instance.username[0]}/{instance.username[:2]}/{instance.username}/pics/header/header_{instance.username}{ext}"
                if default_storage.exists(filename):
                    default_storage.delete(filename)
                path = default_storage.save(filename, ContentFile(new_image.read()))
                validated_data['header_bg_image'] = filename
            elif new_image == '' or new_image is None:
                validated_data['header_bg_image'] = ''

        # Если меняется пол и у пользователя системный аватар — обновляем пресеты
        if 'gender' in validated_data and instance.avatar_type == 1:
            instance.gender = validated_data['gender']
            instance.reset_to_system_avatar()
            instance.refresh_from_db()

        return super().update(instance, validated_data)

class MiniUserSerializer(serializers.ModelSerializer):
    system_avatar_url = serializers.SerializerMethodField()
    system_avatar_thumbnail_url = serializers.SerializerMethodField()
    avatar_url = serializers.SerializerMethodField()
    avatar_thumbnail_url = serializers.SerializerMethodField()
    is_online = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    total_rating = serializers.SerializerMethodField()
    display_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'username', 'display_name', 'avatar', 'avatar_thumbnail',
            'system_avatar_url', 'system_avatar_thumbnail_url',
            'avatar_url', 'avatar_thumbnail_url',
            'avatar_type', 'gender', 'is_online', 'status', 'avatar_updated_at',
            'reader_rating', 'author_rating', 'total_rating',
        ]

    def get_system_avatar_url(self, obj):
        preset = obj.get_system_avatar_path()
        if preset:
            return f'{settings.STATIC_URL}{preset}'
        return None

    def get_system_avatar_thumbnail_url(self, obj):
        preset = obj.get_system_avatar_path()
        if preset:
            return f'{settings.STATIC_URL}{preset}'
        return None

    def get_display_name(self, obj):
        """Возвращает отображаемое имя пользователя для мини-сериализатора"""
        if getattr(obj, 'is_deleted', False):
            return 'Аккаунт удален'
        return obj.display_name

    def get_avatar_url(self, obj):
        # Используем кэшированный URL (уже учитывает is_deleted в модели)
        return obj.avatar_url_cached

    def get_avatar_thumbnail_url(self, obj):
        # Используем кэшированный URL (уже учитывает is_deleted в модели)
        return obj.avatar_thumbnail_url_cached

    def get_avatar_thumbnail_url(self, obj):
        if getattr(obj, 'avatar_type', 1) == 1:
            return self.get_system_avatar_thumbnail_url(obj)
        if obj.avatar_thumbnail:
            return obj.avatar_thumbnail.url
        return None

    def get_is_online(self, obj):
        if not obj.last_activity:
            return False
        return (timezone.now() - obj.last_activity).total_seconds() < 120

    def get_status(self, obj):
        now = timezone.now()
        last_activity = obj.last_activity
        if last_activity:
            time_diff = now - last_activity
            if time_diff.total_seconds() < 120:
                return 'В сети'
            else:
                if obj.gender == 'M':
                    prefix = 'Был'
                elif obj.gender == 'F':
                    prefix = 'Была'
                else:
                    prefix = 'Был(а)'
                if time_diff.total_seconds() < 3600:
                    minutes = int(time_diff.total_seconds() / 60)
                    return f'{prefix} {minutes} мин. назад'
                elif time_diff.total_seconds() < 86400:
                    hours = int(time_diff.total_seconds() / 3600)
                    return f'{prefix} {hours} ч. назад'
                else:
                    days = int(time_diff.total_seconds() / 86400)
                    return f'{prefix} {days} д. назад'
        return 'Статус неизвестен'

    def get_total_rating(self, obj):
        return (obj.reader_rating or 0) + (obj.author_rating or 0)

class MessageReplyShortSerializer(serializers.ModelSerializer):
    image_thumbnail_url = serializers.SerializerMethodField()
    class Meta:
        model = Message
        fields = ['id', 'text', 'image_thumbnail_url', 'created_at', 'gif', 'is_deleted']
    def get_image_thumbnail_url(self, obj):
        if obj.is_deleted:
            return None
        attachment = obj.attachments.first()
        if attachment and attachment.thumb:
            return attachment.thumb.url
        return None

class MessageSerializer(serializers.ModelSerializer):
    sender = MiniUserSerializer(read_only=True)
    recipient = MiniUserSerializer(read_only=True)
    sender_id = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), source='sender', write_only=True)
    recipient_id = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), source='recipient', write_only=True)
    image_url = serializers.SerializerMethodField()
    image_thumbnail_url = serializers.SerializerMethodField()
    is_deleted = serializers.BooleanField(read_only=True)
    had_image = serializers.SerializerMethodField()
    reply_to = MessageReplyShortSerializer(read_only=True)
    reply_to_id = serializers.PrimaryKeyRelatedField(queryset=Message.objects.all(), source='reply_to', write_only=True, required=False, allow_null=True)
    image = serializers.ImageField(write_only=True, required=False)
    image_thumbnail = serializers.ImageField(write_only=True, required=False)

    class Meta:
        model = Message
        fields = [
            'id', 'dialog', 'sender', 'recipient', 'sender_id', 'recipient_id',
            'text', 'image', 'image_thumbnail', 'image_url', 'image_thumbnail_url',
            'gif', 'created_at', 'is_read', 'read_at', 'uuid', 'emoji_only',
            'is_deleted', 'had_image', 'reply_to', 'reply_to_id'
        ]
        read_only_fields = [
            'id', 'created_at', 'uuid', 'is_read', 'read_at',
            'image_url', 'image_thumbnail_url', 'is_deleted', 'had_image', 'reply_to'
        ]

    def get_image_url(self, obj):
        if obj.is_deleted:
            return None
        attachment = obj.attachments.first()
        if attachment and attachment.file:
            return attachment.file.url
        return None

    def get_image_thumbnail_url(self, obj):
        if obj.is_deleted:
            return None
        attachment = obj.attachments.first()
        if attachment and attachment.thumb:
            return attachment.thumb.url
        return None

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        if instance.is_deleted:
            rep['text'] = ''
        return rep

    def get_had_image(self, obj):
        return bool(obj.attachments.exists())

    def create(self, validated_data):
        image = validated_data.pop('image', None)
        image_thumbnail = validated_data.pop('image_thumbnail', None)
        
        # Создаем сообщение
        message = super().create(validated_data)
        
        # Если есть изображения, создаем вложение
        if image or image_thumbnail:
            MessageAttachment.objects.create(
                dialog=message.dialog,
                message=message,
                file=image,
                thumb=image_thumbnail
            )
        
        return message
        
    def update(self, instance, validated_data):
        # Логируем данные для отладки
        print("DEBUG: MessageSerializer.update - validated_data =", validated_data)
        
        # Обновляем только текст сообщения
        if 'text' in validated_data:
            instance.text = validated_data.get('text', instance.text)
            # Устанавливаем время последнего редактирования
            instance.updated_at = timezone.now()
            
        # Удаляем поля, которые могут вызвать ошибки
        if 'image' in validated_data:
            validated_data.pop('image')
        if 'image_thumbnail' in validated_data:
            validated_data.pop('image_thumbnail')
            
        # Сохраняем обновленное сообщение
        instance.save()
        
        return instance

class DialogSerializer(serializers.ModelSerializer):
    participants = MiniUserSerializer(many=True, read_only=True)
    last_message = serializers.SerializerMethodField()
    updated_at = serializers.DateTimeField(read_only=True)
    participant = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()

    class Meta:
        model = Dialog
        fields = ['id', 'participant', 'participants', 'updated_at', 'last_message', 'unread_count']

    def get_last_message(self, obj):
        # Берём последнее неудалённое сообщение
        last_msg = obj.messages.filter(is_deleted=False).order_by('-created_at').first()
        if last_msg:
            return MessageSerializer(last_msg).data
        return None

    def get_participant(self, obj):
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            user = obj.participants.exclude(id=request.user.id).first()
            if user:
                return MiniUserSerializer(user).data
        return None

    def get_unread_count(self, obj):
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            return obj.messages.filter(recipient=request.user, is_read=False).count()
        return 0

    def update(self, instance, validated_data):
        request = self.context.get('request')
        import os, uuid
        from django.core.files.storage import default_storage
        from django.core.files.base import ContentFile
        from django.conf import settings

        print("DEBUG: validated_data =", validated_data)
        print("DEBUG: avatar in validated_data =", 'avatar' in validated_data)
        print("DEBUG: avatar value =", validated_data.get('avatar'))

        # Обработка системного аватара
        if 'avatar' in validated_data:
            avatar = validated_data['avatar']
            print("DEBUG: Processing avatar =", avatar)
            if avatar is None:  # Если avatar = None - сбрасываем к системному
                print("DEBUG: Resetting to system avatar")
                instance.reset_to_system_avatar()
                instance.refresh_from_db()
                # Явно прописываем большой системный аватар
                instance.avatar_preset = instance.get_system_avatar_path()
                instance.avatar_preset_mini = instance.get_system_avatar_thumbnail_path()
                instance.save(update_fields=['avatar_preset', 'avatar_preset_mini'])
                print("DEBUG: After reset - avatar_type =", instance.avatar_type)
                print("DEBUG: After reset - avatar =", instance.avatar)
                print("DEBUG: After reset - avatar_thumbnail =", instance.avatar_thumbnail)
                validated_data.pop('avatar', None)
                return super().update(instance, validated_data)

        avatar_preset = validated_data.get('avatar_preset', None)
        if avatar_preset is not None:
            # ВСЕГДА сбрасываем кастомные поля, но не удаляем файлы с S3
            instance.avatar = None
            instance.avatar_thumbnail = None
            instance.avatar_type = 1
            instance.avatar_updated_at = timezone.now()
            if avatar_preset == '':
                # Сброс к дефолтному системному аватару по полу
                instance.reset_to_system_avatar()  # Не удаляем файлы с S3
            else:
                # Убираем /media/ если есть
                preset_path = avatar_preset.replace('/media/', '')
                instance.avatar_preset = preset_path
                # Миниатюра: если явно указана, используем её, иначе по шаблону
                avatar_preset_mini = validated_data.get('avatar_preset_mini')
                if avatar_preset_mini:
                    mini_path = avatar_preset_mini.replace('/media/', '')
                elif preset_path.endswith('_s.webp'):
                    mini_path = preset_path
                else:
                    mini_path = preset_path.replace('.webp', '_s.webp')
                instance.avatar_preset_mini = mini_path
            instance.save(update_fields=['avatar', 'avatar_thumbnail', 'avatar_preset', 'avatar_preset_mini', 'avatar_updated_at', 'avatar_type'])
            validated_data.pop('avatar', None)
            validated_data.pop('avatar_thumbnail', None)
            validated_data.pop('avatar_preset', None)
            validated_data.pop('avatar_preset_mini', None)

        # Обработка аватара
        if 'avatar' in validated_data:
            if hasattr(avatar, 'read'):  # Если файл - загружаем
                instance.use_custom_avatar()
                instance.avatar = avatar
                instance.avatar_type = 2
                instance.avatar_updated_at = timezone.now()
                instance.save()
                from .utils import create_avatar_thumbnail
                create_avatar_thumbnail(instance)
            validated_data.pop('avatar', None)

        # Обработка фона хедера
        if 'header_bg_image' in validated_data:
            new_image = validated_data['header_bg_image']
            if isinstance(new_image, str):
                # Абсолютный путь (пресет/рамка) или относительный (пользовательский хедер) — сохраняем как есть
                validated_data['header_bg_image'] = new_image
            elif hasattr(new_image, 'read'):
                # Удаляем старый файл, если был и не пресет
                if instance.header_bg_image and not instance.header_bg_image.startswith('/static/header_presets/'):
                    try:
                        filename = instance.header_bg_image
                        if default_storage.exists(filename):
                            default_storage.delete(filename)
                    except Exception:
                        pass
                ext = os.path.splitext(new_image.name)[1] or '.jpg'
                filename = f"users/{instance.username[0]}/{instance.username[:2]}/{instance.username}/pics/header/header_{instance.username}{ext}"
                if default_storage.exists(filename):
                    default_storage.delete(filename)
                path = default_storage.save(filename, ContentFile(new_image.read()))
                validated_data['header_bg_image'] = filename
            elif new_image == '' or new_image is None:
                validated_data['header_bg_image'] = ''

        # Если меняется пол и у пользователя системный аватар — обновляем пресеты
        if 'gender' in validated_data and instance.avatar_type == 1:
            instance.gender = validated_data['gender']
            instance.reset_to_system_avatar()
            instance.refresh_from_db()

        return super().update(instance, validated_data)

class UserHeaderImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserHeaderImage
        fields = ['id', 'path', 'created_at']

class UserAvatarSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserAvatar
        fields = ['id', 'path', 'thumb_path', 'created_at']

# === СЕРИАЛИЗАТОРЫ ДЛЯ РЕЙТИНГОВ ===

class UserStatsSerializer(serializers.ModelSerializer):
    """Сериализатор для статистики пользователя."""
    
    class Meta:
        model = UserStats
        fields = [
            'total_rating', 'reader_rating', 'author_rating',
            'friends_count', 'subscribers_count', 'subscriptions_count',
            'books_count', 'published_books_count', 'finished_books_count',
            # УДАЛЕНО: 'total_chapters_count' - рейтинг по главам больше не нужен
            'total_words_count',
            'books_likes_count', 'books_reviews_count', 'books_comments_count',
            'comments_left_count', 'reviews_left_count', 'likes_left_count',
            'reading_time_minutes', 'books_read_count',
            'profile_views_count', 'messages_sent_count', 'days_active_count',
            'last_updated'
        ]
        read_only_fields = ['last_updated']


class UserMetricHistorySerializer(serializers.ModelSerializer):
    """Сериализатор для истории метрик."""
    action_type_display = serializers.CharField(source='get_action_type_display', read_only=True)
    
    class Meta:
        model = UserMetricHistory
        fields = [
            'id', 'action_type', 'action_type_display', 'metric_name',
            'old_value', 'new_value', 'change_delta',
            'reader_rating_before', 'reader_rating_after',
            'author_rating_before', 'author_rating_after',
            'total_rating_before', 'total_rating_after',
            'created_at', 'created_by'
        ]
        read_only_fields = '__all__'


class RatingCalculationRuleSerializer(serializers.ModelSerializer):
    """Сериализатор для правил расчета рейтингов."""
    rating_type_display = serializers.CharField(source='get_rating_type_display', read_only=True)
    
    class Meta:
        model = RatingCalculationRule
        fields = [
            'id', 'rating_type', 'rating_type_display', 'metric_name',
            'weight', 'max_contribution', 'is_active', 'description', 'created_at'
        ]


class LeaderboardItemSerializer(serializers.ModelSerializer):
    """Сериализатор для элемента лидерборда."""
    user = UserSerializer(read_only=True)
    position = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = UserStats
        fields = [
            'position', 'user', 'total_rating', 'reader_rating', 'author_rating',
            'published_books_count', 'books_likes_count', 'comments_left_count'
        ]


class UserWithStatsSerializer(UserSerializer):
    """Расширенный сериализатор пользователя со статистикой."""
    stats = UserStatsSerializer(read_only=True)
    rating_position = serializers.SerializerMethodField()
    
    class Meta(UserSerializer.Meta):
        fields = UserSerializer.Meta.fields + ['stats', 'rating_position']
    
    def get_rating_position(self, obj):
        """Получить позицию пользователя в общем рейтинге."""
        try:
            from .rating_service import RatingService
            return RatingService.get_user_rating_position(obj, 'total')
        except:
            return None

class UserLibrarySerializer(serializers.ModelSerializer):
    """Серализатор для библиотеки пользователя"""
    book = BookListSerializer(read_only=True)
    book_id = serializers.IntegerField(write_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    reading_duration_days = serializers.SerializerMethodField()
    
    class Meta:
        model = UserLibrary
        fields = [
            'id', 'book', 'book_id', 'status', 'status_display',
            'added_at', 'updated_at', 'reading_started_at', 'reading_finished_at',
            'reading_progress', 'current_chapter', 'user_rating', 'private_notes',
            'reading_duration_days'
        ]
        extra_kwargs = {
            'private_notes': {'write_only': True},  # Личные заметки только для владельца
        }
    
    def get_reading_duration_days(self, obj):
        """Количество дней чтения"""
        if obj.reading_started_at:
            end_date = obj.reading_finished_at or timezone.now()
            return (end_date.date() - obj.reading_started_at.date()).days
        return 0
    
    def validate_book_id(self, value):
        """Проверяем существование книги"""
        from books.models import Book
        try:
            Book.objects.get(id=value, is_published=True)
            return value
        except Book.DoesNotExist:
            raise serializers.ValidationError("Книга не найдена или не опубликована")
    
    def create(self, validated_data):
        """Создание записи в библиотеке"""
        user = self.context['request'].user
        book_id = validated_data.pop('book_id')
        
        from books.models import Book
        book = Book.objects.get(id=book_id)
        
        library_entry, created = UserLibrary.objects.get_or_create(
            user=user,
            book=book,
            defaults=validated_data
        )
        
        if not created:
            # Обновляем существующую запись
            for attr, value in validated_data.items():
                setattr(library_entry, attr, value)
            library_entry.save()
        
        return library_entry


class ReadingSessionSerializer(serializers.ModelSerializer):
    """Сериализатор для сессий чтения"""
    book_title = serializers.CharField(source='book.title', read_only=True)
    chapter_title = serializers.CharField(source='chapter.title', read_only=True)
    
    class Meta:
        model = ReadingSession
        fields = [
            'id', 'book', 'book_title', 'chapter', 'chapter_title',
            'started_at', 'last_activity', 'duration_minutes',
            'is_active', 'auto_added_to_library'
        ]
        read_only_fields = ['duration_minutes', 'auto_added_to_library']


class UserLibraryStatsSerializer(serializers.Serializer):
    """Статистика библиотеки пользователя"""
    total_books = serializers.IntegerField()
    reading_books = serializers.IntegerField()
    want_to_read_books = serializers.IntegerField()
    read_books = serializers.IntegerField()
    total_reading_time = serializers.IntegerField(help_text='В минутах')
    average_rating = serializers.DecimalField(max_digits=3, decimal_places=1)
    books_with_notes = serializers.IntegerField()
    
    def to_representation(self, instance):
        """Формируем статистику из queryset библиотеки"""
        if not instance:
            return {
                'total_books': 0,
                'reading_books': 0,
                'want_to_read_books': 0,
                'read_books': 0,
                'total_reading_time': 0,
                'average_rating': 0,
                'books_with_notes': 0,
            }
        
        stats = {
            'total_books': instance.count(),
            'reading_books': instance.filter(status='reading').count(),
            'want_to_read_books': instance.filter(status='want_to_read').count(),
            'read_books': instance.filter(status='read').count(),
            'total_reading_time': sum([
                (entry.reading_finished_at - entry.reading_started_at).total_seconds() / 60
                for entry in instance
                if entry.reading_started_at and entry.reading_finished_at
            ]),
            'books_with_notes': instance.exclude(private_notes='').count(),
        }
        
        # Средняя оценка
        ratings = [entry.user_rating for entry in instance if entry.user_rating]
        stats['average_rating'] = sum(ratings) / len(ratings) if ratings else 0
        
        return stats