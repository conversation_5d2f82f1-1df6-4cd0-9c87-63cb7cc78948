import { useEffect, useRef, useState } from 'react';
import { useAuth } from '../context/AuthContext';

export const useDialogWebSocket = (dialogId) => {
    const { isAuthenticated } = useAuth();
    const [isConnected, setIsConnected] = useState(false);
    const [newMessage, setNewMessage] = useState(null);
    const [messagesRead, setMessagesRead] = useState(null);
    const [userTyping, setUserTyping] = useState(null);
    const wsRef = useRef(null);

    useEffect(() => {
        if (!isAuthenticated || !dialogId) {
            setIsConnected(false);
            setNewMessage(null);
            setMessagesRead(null);
            setUserTyping(null);
            return;
        }

        // Создаем WebSocket соединение для диалога
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//localhost:8000/ws/dialog/${dialogId}/`;
        
        const ws = new WebSocket(wsUrl);
        wsRef.current = ws;

        ws.onopen = () => {
            console.log(`Dialog WebSocket connected: dialog ${dialogId}`);
            setIsConnected(true);
        };

        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                console.log('Dialog WebSocket message:', data);
                
                switch (data.type) {
                    case 'new_message':
                        setNewMessage(data.message);
                        break;
                    case 'messages_read':
                        console.log('🔔 DialogWebSocket received messages_read:', data);
                        setMessagesRead({
                            messageIds: data.message_ids,
                            readBy: data.read_by,
                            timestamp: Date.now()
                        });
                        break;
                    case 'user_typing':
                        setUserTyping({
                            userId: data.user_id,
                            username: data.username,
                            timestamp: Date.now()
                        });
                        break;
                    default:
                        console.warn('Unknown dialog WebSocket message type:', data.type);
                }
            } catch (error) {
                console.error('Error parsing dialog WebSocket message:', error);
            }
        };

        ws.onclose = (event) => {
            console.warn(`Dialog WebSocket disconnected: dialog ${dialogId}`, event.code, event.reason);
            setIsConnected(false);
        };

        ws.onerror = (error) => {
            console.error(`Dialog WebSocket error: dialog ${dialogId}`, error);
            setIsConnected(false);
        };

        // Очистка при размонтировании
        return () => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        };
    }, [isAuthenticated, dialogId]);

    // Функция для отправки уведомления о печати
    const sendTyping = () => {
        if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
                type: 'typing'
            }));
        }
    };

    // Функция для сброса состояния нового сообщения (после обработки)
    const clearNewMessage = () => {
        setNewMessage(null);
    };

    // Функция для сброса состояния прочтения сообщений (после обработки)
    const clearMessagesRead = () => {
        setMessagesRead(null);
    };

    // Функция для сброса состояния печати (после обработки)
    const clearUserTyping = () => {
        setUserTyping(null);
    };

    return {
        isConnected,
        newMessage,
        messagesRead,
        userTyping,
        sendTyping,
        clearNewMessage,
        clearMessagesRead,
        clearUserTyping
    };
}; 