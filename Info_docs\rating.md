# 📊 Система рейтингов и метрик пользователей

## 🎯 Общая концепция

Система рейтингов LitPortal основана на комплексном подходе к оценке активности пользователей как читателей и авторов. Общий рейтинг пользователя складывается из двух основных компонентов:

- **Читательский рейтинг** - активность пользователя как читателя
- **Авторский рейтинг** - активность пользователя как автора

## 🏗️ Архитектура системы

### Основные принципы

1. **Гибридная модель с денормализацией**
   - Отдельная модель `UserStats` для кэшированных метрик
   - Сохранение базовых полей в `User` для обратной совместимости
   - Полная история изменений в `UserMetricHistory`

2. **Конфигурируемые правила расчета**
   - Модель `RatingCalculationRule` для гибкой настройки формул
   - Возможность изменения весов метрик без изменения кода
   - Ограничения максимального вклада каждой метрики

3. **Асинхронная обработка**
   - Celery задачи для пересчета рейтингов
   - Очередь задач `RatingRecalculationTask`
   - Неблокирующие обновления через сигналы

## 📋 Модели данных

### UserStats - Основная статистика пользователя

```python
class UserStats(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='stats')
    
    # === ОБЩИЕ ПОКАЗАТЕЛИ ===
    total_rating = models.IntegerField(default=0)      # Общий рейтинг
    reader_rating = models.IntegerField(default=0)     # Читательский рейтинг
    author_rating = models.IntegerField(default=0)     # Авторский рейтинг
    
    # === СОЦИАЛЬНЫЕ ПОКАЗАТЕЛИ ===
    friends_count = models.IntegerField(default=0)     # Количество друзей
    subscribers_count = models.IntegerField(default=0) # Количество подписчиков
    subscriptions_count = models.IntegerField(default=0) # Количество подписок
    
    # === АВТОРСКИЕ ПОКАЗАТЕЛИ ===
    books_count = models.IntegerField(default=0)               # Количество книг
    published_books_count = models.IntegerField(default=0)     # Опубликованных книг
    finished_books_count = models.IntegerField(default=0)      # Завершенных книг
    total_chapters_count = models.IntegerField(default=0)      # Общее количество глав
    total_words_count = models.BigIntegerField(default=0)      # Общее количество слов
    books_likes_count = models.IntegerField(default=0)         # Лайки на книгах
    books_reviews_count = models.IntegerField(default=0)       # Отзывы на книгах
    books_comments_count = models.IntegerField(default=0)      # Комментарии на книгах
    
    # === ЧИТАТЕЛЬСКИЕ ПОКАЗАТЕЛИ ===
    comments_left_count = models.IntegerField(default=0)       # Оставленные комментарии
    reviews_left_count = models.IntegerField(default=0)        # Оставленные отзывы
    likes_left_count = models.IntegerField(default=0)          # Поставленные лайки
    reading_time_minutes = models.BigIntegerField(default=0)   # Время чтения в минутах
    books_read_count = models.IntegerField(default=0)          # Прочитанные книги
    
    # === АКТИВНОСТЬ ===
    profile_views_count = models.IntegerField(default=0)       # Просмотры профиля
    messages_sent_count = models.IntegerField(default=0)       # Отправленные сообщения
    days_active_count = models.IntegerField(default=0)         # Дни активности
    
    # === СЛУЖЕБНЫЕ ПОЛЯ ===
    last_updated = models.DateTimeField(auto_now=True)
    recalculation_scheduled = models.BooleanField(default=False)
```

### RatingCalculationRule - Правила расчета рейтингов

```python
class RatingCalculationRule(models.Model):
    RATING_TYPES = [
        ('reader', 'Читательский рейтинг'),
        ('author', 'Авторский рейтинг'),
        ('total', 'Общий рейтинг'),
    ]
    
    rating_type = models.CharField(max_length=20, choices=RATING_TYPES)
    metric_name = models.CharField(max_length=50)               # Название метрики
    weight = models.DecimalField(max_digits=5, decimal_places=2) # Вес метрики
    max_contribution = models.IntegerField(null=True, blank=True) # Максимальный вклад
    is_active = models.BooleanField(default=True)
    description = models.CharField(max_length=200, blank=True)
```

### UserMetricHistory - История изменений

```python
class UserMetricHistory(models.Model):
    ACTION_TYPES = [
        ('comment_added', 'Добавлен комментарий'),
        ('comment_removed', 'Удален комментарий'),
        ('review_added', 'Добавлен отзыв'),
        ('review_removed', 'Удален отзыв'),
        ('like_added', 'Поставлен лайк'),
        ('like_removed', 'Убран лайк'),
        ('book_published', 'Книга опубликована'),
        ('book_finished', 'Книга завершена'),
        ('friend_added', 'Добавлен друг'),
        ('friend_removed', 'Удален друг'),
        ('subscriber_added', 'Добавлен подписчик'),
        ('subscriber_removed', 'Удален подписчик'),
        ('reading_time', 'Время чтения'),
        ('manual_adjustment', 'Ручная корректировка'),
        ('recalculation', 'Пересчет статистики'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    action_type = models.CharField(max_length=30, choices=ACTION_TYPES)
    metric_name = models.CharField(max_length=50)
    old_value = models.BigIntegerField()
    new_value = models.BigIntegerField()
    change_delta = models.IntegerField()
    
    # Рейтинги до и после изменения
    reader_rating_before = models.IntegerField(default=0)
    reader_rating_after = models.IntegerField(default=0)
    author_rating_before = models.IntegerField(default=0)
    author_rating_after = models.IntegerField(default=0)
    total_rating_before = models.IntegerField(default=0)
    total_rating_after = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=50, default='system')
```

## ⚙️ Формулы расчета рейтингов

### Текущие правила расчета (по умолчанию)

#### Читательский рейтинг

| Метрика | Вес | Макс. вклад | Описание |
|---------|-----|-------------|----------|
| `comments_left_count` | 2.0 | 100 | За каждый комментарий |
| `reviews_left_count` | 5.0 | 200 | За каждый отзыв |
| `likes_left_count` | 1.0 | 50 | За каждый лайк |
| `reading_time_minutes` | 0.1 | 500 | За время чтения (0.1 за минуту) |
| `books_read_count` | 10.0 | 1000 | За прочитанную книгу |
| `friends_count` | 1.0 | 100 | За каждого друга |

**Формула:** `reader_rating = min(comments_left_count * 2.0, 100) + min(reviews_left_count * 5.0, 200) + ...`

#### Авторский рейтинг

| Метрика | Вес | Макс. вклад | Описание |
|---------|-----|-------------|----------|
| `published_books_count` | 50.0 | 2000 | За опубликованную книгу |
| `finished_books_count` | 100.0 | 3000 | За завершенную книгу |
| `total_chapters_count` | 5.0 | 1000 | За каждую главу |
| `total_words_count` | 0.01 | 2000 | За количество слов |
| `books_likes_count` | 3.0 | 1500 | За лайки на книгах |
| `books_reviews_count` | 8.0 | 2000 | За отзывы на книгах |
| `books_comments_count` | 4.0 | 1000 | За комментарии на книгах |
| `subscribers_count` | 2.0 | 500 | За подписчиков |

**Формула:** `author_rating = min(published_books_count * 50.0, 2000) + min(finished_books_count * 100.0, 3000) + ...`

#### Общий рейтинг

| Метрика | Вес | Макс. вклад | Описание |
|---------|-----|-------------|----------|
| `reader_rating` | 1.0 | None | Читательский рейтинг |
| `author_rating` | 1.0 | None | Авторский рейтинг |
| `friends_count` | 0.5 | 50 | Социальная активность |

**Формула:** `total_rating = reader_rating + author_rating + min(friends_count * 0.5, 50)`

### ОКОНЧАТЕЛЬНЫЕ ФОРМУЛЫ РАСЧЕТА РЕЙТИНГОВ

## 🎯 Общие принципы

**Общий рейтинг** = Читательский рейтинг + Авторский рейтинг

### Уровни пользователей:
- **Начальный (Н)**: 0-4999 баллов
- **Повышенный (П)**: 5000-49999 баллов  
- **Профессиональный (Про)**: 50000+ баллов

> **Важно**: Уровень пользователя влияет на вес его лайков и комментариев к другим пользователям!

## 📖 Читательский рейтинг

### Базовые начисления:

| Действие | Баллы | Условия |
|----------|-------|---------|
| **Регистрация** | +10 | Приветственный бонус |
| **Комментарий к произведению** | Н: +5, П: +10, Про: +15 | Мин. 100 знаков, макс. 20/день для читательского рейтинга |
| **Комментарий к блогу** | +2 | Исключая записи только для друзей |
| **Комментарий в клубе** | +2 | При наличии функции клубов |
| **Чтение книг** | +60 | За каждые 30 минут чтения |
| **Покупка книги** | +150 | Цена не влияет |
| **Награда произведению/автору** | +70 | Донат автору |

### Влияние лайков на комментарии:

**К произведениям:**
- Лайк: Н: +1, П: +3, Про: +10
- Дизлайк: Н: -1, П: -3, Про: -10

**К блогам и клубам:**
- Лайк: Н: +1, П: +2, Про: +4  
- Дизлайк: Н: -1, П: -2, Про: -4

### Ограничения:
- ✅ Учитываются только первые 20 комментариев в сутки **для читательского рейтинга комментатора**
- ✅ **Авторский рейтинг владельца произведения начисляется с любого количества комментариев**
- ✅ Минимальный размер комментария: 100 знаков с пробелами
- ❌ Не начисляется за оценки к записям в блоге только для друзей/подписчиков
- ❌ Не начисляется за оценки к собственным комментариям

## ✍️ Авторский рейтинг

### Базовые начисления:

| Действие | Баллы | Условия |
|----------|-------|---------|
| **Покупка вашей книги** | +100 | За каждую покупку |
| **Награда от читателя** | +100 | Донат читателя |
| **Публикация поста** | +20 | Исключая "Самопиар" и только для друзей |
| **Лайк к вашей книге** | +15 | За каждый лайк |
| **Комментарий к вашему контенту** | Н: +5, П: +10, Про: +15 | Исключая ваши комментарии, без ограничений по количеству |
| **Новый подписчик** | +20 | При подписке |
| **Отписка** | -20 | При отписке |

### Обратный эффект:
> ⚠️ **Все начисления имеют обратный эффект** - при удалении комментария, лайка или отписке соответствующие баллы вычитаются!

---

## 🔄 Автоматическое обновление метрик

### События, влияющие на рейтинг

#### Читательские действия
- ✅ **Комментарий добавлен/удален** → `comments_left_count` ±1
- ✅ **Отзыв добавлен/удален** → `reviews_left_count` ±1  
- ✅ **Лайк поставлен/убран** → `likes_left_count` ±1
- 🔄 **Сессия чтения** → `reading_time_minutes` +время
- 🔄 **Книга прочитана** → `books_read_count` +1

#### Авторские действия
- ✅ **Книга создана** → `books_count` +1
- ✅ **Книга опубликована** → `published_books_count` +1
- ✅ **Книга завершена** → `finished_books_count` +1
- ✅ **Глава добавлена** → `total_chapters_count` +1
- ✅ **Лайк получен на книге** → `books_likes_count` +1
- ✅ **Отзыв получен на книге** → `books_reviews_count` +1
- ✅ **Комментарий получен на книге** → `books_comments_count` +1

#### Социальные действия
- ✅ **Заявка в друзья принята** → `friends_count` +1 (у обоих)
- ✅ **Дружба разорвана** → `friends_count` -1 (у обоих)
- ✅ **Подписка создана** → `subscriptions_count` +1, `subscribers_count` +1
- ✅ **Отписка** → `subscriptions_count` -1, `subscribers_count` -1

#### Активность
- ✅ **Сообщение отправлено** → `messages_sent_count` +1
- 🔄 **Просмотр профиля** → `profile_views_count` +1
- 🔄 **Активность в день** → `days_active_count` +1

*Условные обозначения:*
- ✅ Реализовано
- 🔄 Требует дополнительной реализации

## 🛠️ API системы рейтингов

### Основные эндпоинты

```http
# Статистика пользователя
GET /api/users/stats/                    # Текущий пользователь
GET /api/users/{user_id}/stats/          # Конкретный пользователь

# История метрик
GET /api/users/metric-history/           # История текущего пользователя
GET /api/users/{user_id}/metric-history/ # История конкретного пользователя

# Лидерборды
GET /api/leaderboard/?type=total&limit=100
GET /api/leaderboard/?type=reader&limit=50
GET /api/leaderboard/?type=author&limit=50

# Позиция в рейтинге
GET /api/users/rating-position/?type=total
GET /api/users/{user_id}/rating-position/?type=total

# Пересчет рейтинга
POST /api/users/recalculate-rating/
{
  "user_id": 123,  // опционально
  "async": true    // опционально
}

# Правила расчета
GET /api/rating-rules/?type=reader

# Общая статистика
GET /api/rating-stats/
```

### Примеры ответов

#### Статистика пользователя
```json
{
  "total_rating": 1250,
  "reader_rating": 750,
  "author_rating": 500,
  "friends_count": 25,
  "subscribers_count": 100,
  "subscriptions_count": 50,
  "books_count": 5,
  "published_books_count": 3,
  "finished_books_count": 2,
  "total_chapters_count": 45,
  "total_words_count": 125000,
  "books_likes_count": 150,
  "books_reviews_count": 25,
  "books_comments_count": 75,
  "comments_left_count": 200,
  "reviews_left_count": 30,
  "likes_left_count": 300,
  "reading_time_minutes": 12000,
  "books_read_count": 25,
  "profile_views_count": 1500,
  "messages_sent_count": 500,
  "days_active_count": 150,
  "last_updated": "2024-01-15T10:30:00Z"
}
```

#### Лидерборд
```json
[
  {
    "position": 1,
    "user": {
      "id": 123,
      "username": "top_author",
      "display_name": "Топовый Автор"
    },
    "total_rating": 5000,
    "reader_rating": 2000,
    "author_rating": 3000,
    "published_books_count": 10,
    "books_likes_count": 500
  }
]
```

## 🔧 Управление системой

### Management команды

```bash
# Полная настройка системы
python manage.py setup_rating_system --create-rules --create-stats --recalculate-all

# Создание правил по умолчанию
python manage.py setup_rating_system --create-rules

# Создание статистики для новых пользователей
python manage.py setup_rating_system --create-stats

# Пересчет всех рейтингов
python manage.py setup_rating_system --recalculate-all --batch-size=50

# Симуляция (без изменений)
python manage.py setup_rating_system --create-rules --dry-run
```

### Celery задачи

```python
# Пересчет рейтинга конкретного пользователя
from users.tasks import recalculate_user_rating
recalculate_user_rating.delay(user_id=123)

# Массовый пересчет
from users.tasks import bulk_recalculate_ratings
bulk_recalculate_ratings.delay(limit=500)

# Обновление лидербордов
from users.tasks import recalculate_leaderboards
recalculate_leaderboards.delay()
```

### Настройки в settings.py

```python
# Включить асинхронное обновление рейтингов
USE_ASYNC_RATING_UPDATE = True

# Celery расписание
CELERY_BEAT_SCHEDULE = {
    'daily-rating-update': {
        'task': 'users.tasks.daily_rating_update',
        'schedule': crontab(hour=2, minute=0),  # Каждый день в 2:00
    },
    'process-rating-queue': {
        'task': 'users.tasks.process_rating_queue',
        'schedule': 60.0,  # Каждую минуту
    },
    'recalculate-leaderboards': {
        'task': 'users.tasks.recalculate_leaderboards',
        'schedule': crontab(minute=0),  # Каждый час
    },
    'cleanup-old-rating-tasks': {
        'task': 'users.tasks.cleanup_old_rating_tasks',
        'schedule': crontab(hour=3, minute=0),  # Каждый день в 3:00
    },
}
```

## 📊 Использование в коде

### Базовые операции

```python
from users.rating_service import RatingService

# Получить/создать статистику пользователя
stats = RatingService.get_or_create_user_stats(user)

# Пересчитать рейтинг пользователя
stats = RatingService.recalculate_user_stats(user)

# Обновить метрику
RatingService.update_metric(
    user=user,
    action_type='comment_added',
    metric_name='comments_left_count',
    change_delta=1,
    related_object=comment
)

# Получить лидерборд
top_users = RatingService.get_leaderboard('total', 100)

# Получить позицию пользователя
position = RatingService.get_user_rating_position(user, 'total')

# Запланировать пересчет
task = RatingService.schedule_recalculation(user)
```

### Обработчики событий

```python
from users.rating_service import RatingEventHandlers

# При создании комментария
RatingEventHandlers.on_comment_added(comment)

# При публикации книги  
RatingEventHandlers.on_book_published(book)

# При добавлении лайка
RatingEventHandlers.on_like_added(like)
```

## 🎯 Преимущества архитектуры

1. **Масштабируемость**
   - Асинхронная обработка не блокирует основные операции
   - Денормализованные данные для быстрых запросов
   - Кэширование лидербордов

2. **Гибкость**
   - Конфигурируемые правила расчета через админку
   - Возможность A/B тестирования формул
   - Легкое добавление новых метрик

3. **Надежность**
   - Полная история изменений для аудита
   - Транзакционность операций
   - Обработка ошибок и повторные попытки

4. **Производительность**
   - Индексы на часто используемых полях
   - Кэширование результатов
   - Батчевая обработка

5. **Мониторинг**
   - Логирование всех операций
   - Метрики производительности
   - Отслеживание задач пересчета

## 🔮 Планы развития

### Дополнительные метрики (в разработке)

1. **Время чтения**
   - Модель `ReadingSession` для отслеживания сессий
   - Интеграция с фронтендом для учета времени

2. **Качество контента**
   - Средние оценки книг автора
   - Коэффициент завершения чтения
   - Рейтинг от модераторов

3. **Социальная активность**
   - Активность в комментариях (ответы, упоминания)
   - Участие в обсуждениях
   - Организация мероприятий

4. **Достижения и значки**
   - Система достижений
   - Специальные статусы
   - Временные бонусы

### Аналитика и отчеты

1. **Дашборд администратора**
   - Общая статистика системы
   - Топ пользователей по различным метрикам
   - Динамика рейтингов

2. **Персональная аналитика**
   - График изменения рейтинга пользователя
   - Детализация по категориям активности
   - Рекомендации по улучшению

3. **Экспорт данных**
   - CSV/JSON экспорт статистики
   - API для внешних сервисов
   - Интеграция с аналитическими системами 