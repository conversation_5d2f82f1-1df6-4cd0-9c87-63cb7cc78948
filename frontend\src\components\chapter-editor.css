.tiptap-editor {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  /* background: transparent !important; */
  min-height: 100px;
  padding-bottom: 48px;
}
.tiptap-editor .ProseMirror {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  /* background: transparent !important; */
  line-height: 1.5;
  padding-bottom: 48px;
}
.tiptap-editor .ProseMirror p {
  margin-top: 0;
  margin-bottom: 0.6em;
  position: relative;
}
/* ========== СИМВОЛЫ ПАРАГРАФА - ИСПРАВЛЕННОЕ CSS РЕШЕНИЕ ========== */

/* ❌ ПРОБЛЕМА: DOM элементы становились частью контента редактора */
/* ✅ РЕШЕНИЕ: CSS псевдоэлементы с правильной логикой позиционирования */

/* ========== OVERLAY СИМВОЛЫ ПАРАГРАФА ========== */

/* Стили для overlay символов (созданных через JavaScript) */
.overlay-paragraph-symbol {
  color: #b0b0b0 !important;
  opacity: 0.6 !important;
  font-family: serif !important;
  user-select: none !important;
  pointer-events: none !important;
}

/* Для темной темы */
html.dark .overlay-paragraph-symbol {
  color: #6b7280 !important;
}

/* СТАРЫЕ ЭКСПЕРИМЕНТАЛЬНЫЕ СТИЛИ (оставлены для совместимости) */

/* Самые простые селекторы без всяких условий */
p.paragraph-has-text::after {
  content: '¶AFTER' !important;
  color: red !important;
  font-size: 40px !important;
  background: yellow !important;
  border: 3px solid green !important;
  padding: 10px !important;
  margin: 10px !important;
  display: inline-block !important;
  position: relative !important;
  z-index: 999 !important;
}

p.paragraph-empty::before {
  content: '¶BEFORE' !important;
  color: blue !important;
  font-size: 40px !important;
  background: pink !important;
  border: 3px solid orange !important;
  padding: 10px !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  z-index: 999 !important;
  display: block !important;
}

/* Дублируем с классом для верности */
.show-paragraph-symbols p.paragraph-has-text::after {
  content: '¶AFTER' !important;
  color: red !important;
  font-size: 40px !important;
  background: yellow !important;
  border: 3px solid green !important;
  padding: 10px !important;
  margin: 10px !important;
  display: inline-block !important;
  position: relative !important;
  z-index: 999 !important;
}

.show-paragraph-symbols p.paragraph-empty::before {
  content: '¶BEFORE' !important;
  color: blue !important;
  font-size: 40px !important;
  background: pink !important;
  border: 3px solid orange !important;
  padding: 10px !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  z-index: 999 !important;
  display: block !important;
}

/* Для темной темы */
html.dark .tiptap-editor.show-paragraph-symbols .ProseMirror p.paragraph-has-text::after,
html.dark .tiptap-editor.show-paragraph-symbols .ProseMirror p.paragraph-empty::before {
  color: #6b7280;
}

/* Минимальная высота для пустых параграфов */
.tiptap-editor.show-paragraph-symbols .ProseMirror p.paragraph-empty {
  min-height: 1.5em;
  position: relative;
}

/* Обработка абзацных отступов (красная строка) */
.tiptap-editor.show-paragraph-symbols .ProseMirror p.tiptap-indent.paragraph-empty::before {
  left: 1.5em; /* Символ после отступа для пустых параграфов */
}

/* Убираем старые стили для .paragraph-symbol (больше не используем) */

/* Стили для комбинаций клавиш в тултипах */
.editor-shortcut {
  font-size: 12px;
  color: #a0a0a0;
  margin-top: 4px;
  padding: 2px 4px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  display: inline-block;
}

/* Стили для темного режима */
html.dark .editor-shortcut,
.dark-tooltip .editor-shortcut {
  background-color: rgba(255, 255, 255, 0.1);
  color: #d0d0d0;
}

/* Центрирование текста в тултипах */
.tooltip-content {
  text-align: center;
  width: 100%;
}

.tooltip-content .editor-shortcut {
  margin-left: auto;
  margin-right: auto;
}

/* Центрирование для всех тултипов */
.ant-tooltip-inner {
  text-align: center;
}

.dark-tooltip .ant-tooltip-inner {
  text-align: center;
}

.tiptap-indent p {
  text-indent: 1.5em;
}
.tiptap-fontsize-option {
  padding-left: 8px;
}
.color-dropdown-menu.dark .ant-dropdown-menu {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
  border-radius: 8px !important;
}
.color-dropdown-menu.dark .ant-dropdown-menu-item {
  color: #fff !important;
  font-size: 15px;
  border-radius: 6px;
}
.color-dropdown-menu.dark .ant-dropdown-menu-item-selected,
.color-dropdown-menu.dark .ant-dropdown-menu-item-active {
  background: #374151 !important;
  color: #60A5FA !important;
}
.color-dropdown-menu.light .ant-dropdown-menu {
  background: #fff !important;
  color: #222 !important;
  border: 1.5px solid #d1d5db !important;
  border-radius: 8px !important;
}
.color-dropdown-menu.light .ant-dropdown-menu-item {
  color: #222 !important;
  font-size: 15px;
  border-radius: 6px;
}
.color-dropdown-menu.light .ant-dropdown-menu-item-selected,
.color-dropdown-menu.light .ant-dropdown-menu-item-active {
  background: #e5e7eb !important;
  color: #2563eb !important;
}
.dark-link-popover .ant-popover-inner {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
  border-radius: 10px !important;
}
.dark-link-popover .ant-popover-title {
  color: #fff !important;
}
.dark-link-popover .ant-input {
  background: #181c23 !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
}
.dark-link-popover .ant-input::placeholder {
  color: #888 !important;
}
.dark-link-popover .ant-checkbox-inner {
  background: #23272f !important;
  border-color: #374151 !important;
}
.dark-link-popover .ant-checkbox-checked .ant-checkbox-inner {
  background: #2563eb !important;
  border-color: #2563eb !important;
}
.dark-link-popover .ant-btn {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
}
.dark-link-popover .ant-btn-primary {
  background: #2563eb !important;
  border-color: #2563eb !important;
  color: #fff !important;
}
.dark-link-popover .ant-btn-dangerous {
  background: #ef4444 !important;
  border: none !important;
  color: #fff !important;
}
.dark-link-modal .ant-modal-content {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
  border-radius: 12px !important;
}
.dark-link-modal .ant-modal-title {
  background: #23272f !important;
  color: #fff !important;
  border-bottom: 1.5px solid #23272f !important;
}
.dark-link-modal .ant-input {
  background: #181c23 !important;
  color: #fff !important;
  border: 1.5px solid #181c23 !important;
}
.dark-link-modal .ant-input::placeholder {
  color: #bfc9d1 !important;
  opacity: 1 !important;
}
.dark-link-modal .ant-input-affix-wrapper {
  background: #181c23 !important;
  color: #fff !important;
  border: 1.5px solid #181c23 !important;
}
.dark-link-modal .ant-input-affix-wrapper input {
  background: #181c23 !important;
  color: #fff !important;
}
.dark-link-modal .ant-input-affix-wrapper .ant-input-clear-icon {
  color: #fff !important;
  opacity: 0.8 !important;
}
.dark-link-modal .ant-checkbox-inner {
  background: #23272f !important;
  border-color: #374151 !important;
}
.dark-link-modal .ant-checkbox-checked .ant-checkbox-inner {
  background: #2563eb !important;
  border-color: #2563eb !important;
}
.dark-link-modal .ant-checkbox + span, .dark-link-modal .ant-checkbox-wrapper span {
  color: #fff !important;
}
.dark-link-modal .ant-btn {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
}
.dark-link-modal .ant-btn-primary {
  background: #2563eb !important;
  border-color: #2563eb !important;
  color: #fff !important;
}
.dark-link-modal .ant-btn-dangerous {
  background: #ef4444 !important;
  border: none !important;
  color: #fff !important;
}
.dark-link-modal .ant-modal-close {
  color: #fff !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}
.dark-link-modal .ant-modal-close-x {
  color: #fff !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}
.ant-modal-content {
  box-shadow: none !important;
  border: none !important;
} 