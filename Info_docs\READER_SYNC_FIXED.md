# Исправления синхронизации стилей читалки с редактором

## Что исправлено:

### 1. Размеры изображений
- ✅ Изображения теперь используют проценты от ширины контейнера (как в редакторе)
- ✅ Атрибут `data-width` правильно применяется как `width: X%`
- ✅ Сохранена пропорциональность и адаптивность

### 2. Подписи к изображениям  
- ✅ Подписи отображаются точно как в редакторе
- ✅ Стили: курсив, центрирование, серый цвет
- ✅ Правильное позиционирование под изображением
- ✅ Поддержка темной темы

### 3. Обтекание текстом
- ✅ Float-left и float-right работают как в редакторе
- ✅ Shape-outside для плавного обтекания
- ✅ Правильная очистка float после параграфов
- ✅ Автоматическое отключение обтекания на мобильных

### 4. Выравнивание изображений
- ✅ Классы align-left, align-center, align-right
- ✅ Правильные отступы и позиционирование
- ✅ Поддержка всех комбинаций выравнивания

### 5. CSS классы
- ✅ Точное соответствие классам из редактора
- ✅ custom-resizable-image контейнеры
- ✅ Обратная совместимость со старыми классами

## Результат:
Теперь читалка полностью соответствует редактору по отображению изображений, включая размеры в процентах, подписи и обтекание текстом.