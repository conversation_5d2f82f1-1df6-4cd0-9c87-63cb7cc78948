import React, { useState, useEffect, useCallback } from 'react';
import { Switch } from 'antd';
import { useTheme } from '../theme/ThemeContext';

const AgeRatingSelector = ({ value, onChange, disabled = false }) => {
  const { theme } = useTheme();

  // Инициализируем значения из пропса value или по умолчанию
  const currentValue = value || { age_rating: '0+', has_profanity: false };
  const [selectedRating, setSelectedRating] = useState(currentValue.age_rating);
  const [hasProfanity, setHasProfanity] = useState(currentValue.has_profanity);

  // Конфигурация возрастных рейтингов с цветами
  const ageRatings = [
    { value: '0+', label: '0+', color: '#ffffff', textColor: '#333333', borderColor: '#d1d5db' },
    { value: '6+', label: '6+', color: '#22c55e', textColor: '#ffffff', borderColor: '#22c55e' },
    { value: '12+', label: '12+', color: '#16a34a', textColor: '#ffffff', borderColor: '#16a34a' },
    { value: '16+', label: '16+', color: '#f97316', textColor: '#ffffff', borderColor: '#f97316' },
    { value: '18+', label: '18+', color: '#ef4444', textColor: '#ffffff', borderColor: '#ef4444' },
  ];

  // Синхронизируем внутреннее состояние с внешним значением только при изменении извне
  useEffect(() => {
    if (value && (value.age_rating !== selectedRating || value.has_profanity !== hasProfanity)) {
      setSelectedRating(value.age_rating || '0+');
      setHasProfanity(value.has_profanity || false);
    }
  }, [value?.age_rating, value?.has_profanity]);

  const handleRatingClick = (rating) => {
    if (disabled) return;

    // Если включен тумблер ненормативной лексики, можно выбрать только 18+
    if (hasProfanity && rating !== '18+') return;

    setSelectedRating(rating);
    const newValue = {
      age_rating: rating,
      has_profanity: hasProfanity
    };

    console.log('AgeRatingSelector: rating changed to', newValue);
    if (onChange) {
      onChange(newValue);
    }
  };

  const handleProfanityChange = (checked) => {
    if (disabled) return;

    setHasProfanity(checked);

    // Автоматически устанавливаем 18+ при включении тумблера
    const newRating = checked ? '18+' : selectedRating;
    if (checked && selectedRating !== '18+') {
      setSelectedRating('18+');
    }

    const newValue = {
      age_rating: newRating,
      has_profanity: checked
    };

    console.log('AgeRatingSelector: profanity changed to', newValue);
    if (onChange) {
      onChange(newValue);
    }
  };

  return (
    <div className="space-y-4">
      {/* Круглые кнопки возрастных рейтингов */}
      <div className="flex flex-wrap gap-3">
        {ageRatings.map((rating) => (
          <button
            key={rating.value}
            type="button"
            onClick={() => handleRatingClick(rating.value)}
            disabled={disabled}
            className={`
              relative w-12 h-12 rounded-full border-2 font-bold text-sm
              transition-all duration-200 transform flex items-center justify-center
              ${disabled || (hasProfanity && rating.value !== '18+') ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-105'}
              ${selectedRating === rating.value ? 'ring-4 ring-blue-300 ring-opacity-50' : ''}
            `}
            style={{
              backgroundColor: selectedRating === rating.value ? rating.color : (theme === 'dark' ? '#374151' : '#f9fafb'),
              borderColor: selectedRating === rating.value ? rating.borderColor : (theme === 'dark' ? '#6b7280' : '#d1d5db'),
              color: selectedRating === rating.value ? rating.textColor : (theme === 'dark' ? '#f3f4f6' : '#374151')
            }}
          >
            {rating.label}
            
            {/* Индикатор выбора */}
            {selectedRating === rating.value && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </button>
        ))}
      </div>

      {/* Тумблер ненормативной лексики */}
      <div className="pt-2">
        <div className="flex items-center gap-3">
          <Switch
            checked={hasProfanity}
            onChange={handleProfanityChange}
            disabled={disabled}
            size="default"
          />
          <span className={`font-medium ${theme === 'dark' ? 'text-gray-100' : 'text-gray-900'}`}>
            Содержит ненормативную лексику
          </span>
        </div>

        {hasProfanity && (
          <div className={`mt-2 text-sm ${theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600'}`}>
            ⚠️ При включении автоматически устанавливается рейтинг 18+ и блокируется выбор других рейтингов
          </div>
        )}
      </div>

      {/* Описание выбранного рейтинга */}
      <div className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
        {selectedRating === '0+' && 'Контент подходит для всех возрастов'}
        {selectedRating === '6+' && 'Контент может содержать материалы, не рекомендуемые детям до 6 лет'}
        {selectedRating === '12+' && 'Контент может содержать материалы, не рекомендуемые детям до 12 лет'}
        {selectedRating === '16+' && 'Контент может содержать материалы, не рекомендуемые лицам до 16 лет'}
        {selectedRating === '18+' && 'Контент предназначен только для совершеннолетних лиц'}
      </div>
    </div>
  );
};

export default AgeRatingSelector;
