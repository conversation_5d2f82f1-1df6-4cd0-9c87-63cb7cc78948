/* Стили для читалки книг - полная поддержка всех стилей редактора */

/* Основные стили для контента главы */
.chapter-content {
  line-height: 1.5;
  color: inherit;
  white-space: pre-wrap !important;
  word-break: break-word;
  hyphens: auto;
  font-family: inherit;
  overflow: visible;
  width: 100% !important;
  max-width: none !important;
  /* Защита от выделения и копирования */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.chapter-content p {
  margin-top: 0;
  margin-bottom: 0.6em;
  line-height: inherit; /* Наследуем от родительского элемента */
  overflow: visible;
  white-space: pre-wrap !important;
  position: relative;
}

.chapter-content p:empty {
  min-height: 1.2em;
  margin-bottom: 0.6em;
}

.chapter-content br {
  line-height: inherit;
}

.chapter-content p:empty, .chapter-content p:blank, .chapter-content p:empty:before {
  min-height: 1.2em;
  content: '\00a0';
  display: block;
}

/* Поддержка красной строки (отступов для абзацев) */
.chapter-content.tiptap-indent p {
  text-indent: 1.5em;
}

/* Поддержка всех базовых стилей текста */
.chapter-content strong {
  font-weight: bold;
}

.chapter-content em {
  font-style: italic;
}

.chapter-content u {
  text-decoration: underline;
}

.chapter-content s {
  text-decoration: line-through;
}

/* Поддержка выравнивания текста */
.chapter-content p[style*="text-align: left"] {
  text-align: left !important;
}

.chapter-content p[style*="text-align: center"] {
  text-align: center !important;
}

.chapter-content p[style*="text-align: right"] {
  text-align: right !important;
}

.chapter-content p[style*="text-align: justify"] {
  text-align: justify !important;
}

/* Поддержка ссылок */
.chapter-content a {
  color: #2563eb;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.chapter-content a:hover {
  color: #1d4ed8;
}

.dark .chapter-content a {
  color: #60a5fa;
}

.dark .chapter-content a:hover {
  color: #93c5fd;
}

/* Поддержка списков */
.chapter-content ul {
  list-style-type: disc;
  margin: 1rem 0;
  padding-left: 2rem;
}

.chapter-content ol {
  list-style-type: decimal;
  margin: 1rem 0;
  padding-left: 2rem;
}

.chapter-content li {
  margin: 0.5rem 0;
}

/* Поддержка заголовков */
.chapter-content h1 {
  font-size: 2rem;
  font-weight: bold;
  margin: 1.5rem 0 1rem 0;
  line-height: 1.2;
}

.chapter-content h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1.25rem 0 0.75rem 0;
  line-height: 1.3;
}

.chapter-content h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 1rem 0 0.5rem 0;
  line-height: 1.4;
}

/* Поддержка цитат */
.chapter-content blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

.dark .chapter-content blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

/* Поддержка кода */
.chapter-content code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875em;
}

.dark .chapter-content code {
  background-color: #374151;
  color: #e5e7eb;
}

.chapter-content pre {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.dark .chapter-content pre {
  background-color: #374151;
  color: #e5e7eb;
}

/* Поддержка горизонтальных линий */
.chapter-content hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 2rem 0;
}

.dark .chapter-content hr {
  border-top-color: #4b5563;
}

/* Полная поддержка изображений как в редакторе */
.chapter-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1rem auto;
  border-radius: 8px;
  /* Защита изображений */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  pointer-events: none;
  -webkit-touch-callout: none;
}

/* Принудительное применение процентных размеров */
.chapter-content img[style*="width:"] {
  /* Процентные размеры применяются через inline стили */
}

/* Поддержка атрибутов размера изображений */
.chapter-content img[data-width] {
  width: attr(data-width %);
}

.chapter-content img[width] {
  width: attr(width);
}

.chapter-content img[height] {
  height: attr(height);
}

/* Поддержка выравнивания изображений */
.chapter-content img[data-align="left"],
.chapter-content .float-left img,
.chapter-content .align-left img {
  float: left;
  margin: 0 1rem 1rem 0;
  max-width: 50%;
}

.chapter-content img[data-align="right"],
.chapter-content .float-right img,
.chapter-content .align-right img {
  float: right;
  margin: 0 0 1rem 1rem;
  max-width: 50%;
}

.chapter-content img[data-align="center"],
.chapter-content .align-center img {
  display: block;
  margin: 1rem auto;
  float: none;
}

/* Очистка float после изображений с обтеканием */
.chapter-content img[data-align="left"] + p,
.chapter-content img[data-align="right"] + p,
.chapter-content .float-left + p,
.chapter-content .float-right + p {
  clear: both;
}

/* Поддержка обтекания текстом - точно как в редакторе */
.chapter-content img[data-text-wrap="wrap"],
.chapter-content img[data-text-wrap="true"],
.chapter-content .custom-resizable-image.float-left img,
.chapter-content .custom-resizable-image.float-right img {
  shape-outside: margin-box;
  -webkit-shape-outside: margin-box;
}



/* Обеспечиваем правильное обтекание для параграфов после floating изображений */
.chapter-content .custom-resizable-image.float-left + p,
.chapter-content .custom-resizable-image.float-right + p {
  margin-top: 0;
}

/* Стили для обычного выравнивания (без обтекания) */
.chapter-content .custom-resizable-image.align-left + p,
.chapter-content .custom-resizable-image.align-right + p,
.chapter-content .custom-resizable-image.align-center + p {
  margin-top: 1em;
}

/* Очистка после последнего параграфа */
.chapter-content::after {
  content: '';
  display: table;
  clear: both;
}

/* Обеспечиваем правильное обтекание для всех параграфов после floating изображений */
.chapter-content p {
  position: relative;
  z-index: 1;
}

/* Обеспечиваем правильное обтекание для всех элементов после floating изображений */
.chapter-content .custom-resizable-image.float-left ~ *,
.chapter-content .custom-resizable-image.float-right ~ * {
  position: relative;
  z-index: 1;
}

/* Обеспечиваем правильное обтекание для следующих элементов */
.chapter-content .custom-resizable-image.float-left + *,
.chapter-content .custom-resizable-image.float-right + * {
  clear: none !important;
}

/* Обеспечиваем правильное обтекание для всех параграфов рядом с floating изображениями */
.chapter-content .custom-resizable-image.float-left ~ p,
.chapter-content .custom-resizable-image.float-right ~ p {
  margin-top: 0 !important;
  clear: none !important;
}

/* Обеспечиваем правильное обтекание для первого параграфа после floating изображения */
.chapter-content .custom-resizable-image.float-left + p,
.chapter-content .custom-resizable-image.float-right + p {
  margin-top: 0 !important;
  clear: none !important;
  display: block !important;
}



/* Поддержка подписей к изображениям */
.chapter-content figure {
  margin: 1rem auto;
  display: inline-block;
  max-width: 100%;
}

.chapter-content figure img {
  margin: 0;
  width: 100%;
}

/* Подписи к изображениям - точно как в редакторе */
.chapter-content figcaption,
.chapter-content .image-caption {
  font-size: 13px;
  font-style: italic;
  text-align: center;
  color: #6b7280;
  margin-top: 8px;
  margin-bottom: 0;
  padding: 0 8px;
  line-height: 1.4;
  width: 100%;
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  opacity: 0.85;
  font-weight: 400;
  transition: opacity 0.2s ease;
}

.chapter-content figcaption:hover,
.chapter-content .image-caption:hover {
  opacity: 0.9;
}

.dark .chapter-content figcaption,
.dark .chapter-content .image-caption {
  color: #9ca3af;
}

/* Стили для span с цветами и размерами шрифтов */
.chapter-content span[style*="color"] {
  /* Сохраняем цвет из редактора */
}

.chapter-content span[style*="background-color"] {
  /* Сохраняем цвет фона из редактора */
}

.chapter-content span[style*="font-size"] {
  /* Сохраняем размер шрифта из редактора */
}

/* Поддержка комбинированных стилей textStyle */
.chapter-content span[style*="textStyle"] {
  /* Сохраняем все стили из textStyle */
}

/* Поддержка стилей из TipTap редактора */
.chapter-content span[style*="color:"] {
  /* Применяем цвет текста */
}

.chapter-content span[style*="background-color:"] {
  /* Применяем цвет фона */
}

.chapter-content span[style*="font-size:"] {
  /* Применяем размер шрифта */
}

/* Универсальная поддержка всех цветов текста */
.chapter-content span[style*="color:"] {
  /* Цвет будет применен через inline стили */
}

/* Универсальная поддержка всех цветов фона */
.chapter-content span[style*="background-color:"] {
  /* Цвет фона будет применен через inline стили */
}

/* Универсальная поддержка всех размеров шрифтов */
.chapter-content span[style*="font-size:"] {
  /* Размер шрифта будет применен через inline стили */
}

/* Поддержка конкретных цветов из палитры редактора */
.chapter-content span[style*="color: #ffcccc"] { color: #ffcccc !important; }
.chapter-content span[style*="color: #ff0000"] { color: #ff0000 !important; }
.chapter-content span[style*="color: #cc0000"] { color: #cc0000 !important; }
.chapter-content span[style*="color: #800000"] { color: #800000 !important; }
.chapter-content span[style*="color: #ffcc99"] { color: #ffcc99 !important; }
.chapter-content span[style*="color: #ff9900"] { color: #ff9900 !important; }
.chapter-content span[style*="color: #ffffcc"] { color: #ffffcc !important; }
.chapter-content span[style*="color: #ffff00"] { color: #ffff00 !important; }
.chapter-content span[style*="color: #cccc00"] { color: #cccc00 !important; }
.chapter-content span[style*="color: #ccffcc"] { color: #ccffcc !important; }
.chapter-content span[style*="color: #00cc00"] { color: #00cc00 !important; }
.chapter-content span[style*="color: #008000"] { color: #008000 !important; }
.chapter-content span[style*="color: #99ff99"] { color: #99ff99 !important; }
.chapter-content span[style*="color: #808000"] { color: #808000 !important; }
.chapter-content span[style*="color: #ccffff"] { color: #ccffff !important; }
.chapter-content span[style*="color: #00ffff"] { color: #00ffff !important; }
.chapter-content span[style*="color: #0000ff"] { color: #0000ff !important; }
.chapter-content span[style*="color: #000080"] { color: #000080 !important; }
.chapter-content span[style*="color: #800080"] { color: #800080 !important; }
.chapter-content span[style*="color: #e6e6e6"] { color: #e6e6e6 !important; }
.chapter-content span[style*="color: #808080"] { color: #808080 !important; }
.chapter-content span[style*="color: #404040"] { color: #404040 !important; }
.chapter-content span[style*="color: #000000"] { color: #000000 !important; }
.chapter-content span[style*="color: #ffffff"] { color: #ffffff !important; }

/* Поддержка цветов фона из палитры редактора */
.chapter-content span[style*="background-color: #ffcccc"] { background-color: #ffcccc !important; }
.chapter-content span[style*="background-color: #ff0000"] { background-color: #ff0000 !important; }
.chapter-content span[style*="background-color: #cc0000"] { background-color: #cc0000 !important; }
.chapter-content span[style*="background-color: #800000"] { background-color: #800000 !important; }
.chapter-content span[style*="background-color: #ffcc99"] { background-color: #ffcc99 !important; }
.chapter-content span[style*="background-color: #ff9900"] { background-color: #ff9900 !important; }
.chapter-content span[style*="background-color: #ffffcc"] { background-color: #ffffcc !important; }
.chapter-content span[style*="background-color: #ffff00"] { background-color: #ffff00 !important; }
.chapter-content span[style*="background-color: #cccc00"] { background-color: #cccc00 !important; }
.chapter-content span[style*="background-color: #ccffcc"] { background-color: #ccffcc !important; }
.chapter-content span[style*="background-color: #00cc00"] { background-color: #00cc00 !important; }
.chapter-content span[style*="background-color: #008000"] { background-color: #008000 !important; }
.chapter-content span[style*="background-color: #99ff99"] { background-color: #99ff99 !important; }
.chapter-content span[style*="background-color: #808000"] { background-color: #808000 !important; }
.chapter-content span[style*="background-color: #ccffff"] { background-color: #ccffff !important; }
.chapter-content span[style*="background-color: #00ffff"] { background-color: #00ffff !important; }
.chapter-content span[style*="background-color: #0000ff"] { background-color: #0000ff !important; }
.chapter-content span[style*="background-color: #000080"] { background-color: #000080 !important; }
.chapter-content span[style*="background-color: #800080"] { background-color: #800080 !important; }
.chapter-content span[style*="background-color: #e6e6e6"] { background-color: #e6e6e6 !important; }
.chapter-content span[style*="background-color: #808080"] { background-color: #808080 !important; }
.chapter-content span[style*="background-color: #404040"] { background-color: #404040 !important; }
.chapter-content span[style*="background-color: #000000"] { background-color: #000000 !important; }
.chapter-content span[style*="background-color: #ffffff"] { background-color: #ffffff !important; }

/* Поддержка размеров шрифтов из редактора */
.chapter-content span[style*="font-size: 13px"] { font-size: 13px !important; }
.chapter-content span[style*="font-size: 16px"] { font-size: 16px !important; }
.chapter-content span[style*="font-size: 20px"] { font-size: 20px !important; }
.chapter-content span[style*="font-size: 36px"] { font-size: 36px !important; }
.chapter-content span[style*="font-size: 12pt"] { font-size: 12pt !important; }
.chapter-content span[style*="font-size: 14pt"] { font-size: 14pt !important; }
.chapter-content span[style*="font-size: 18pt"] { font-size: 18pt !important; }
.chapter-content span[style*="font-size: 24pt"] { font-size: 24pt !important; }
.chapter-content span[style*="font-size: 28pt"] { font-size: 28pt !important; }
.chapter-content span[style*="font-size: 32pt"] { font-size: 32pt !important; }

/* Поддержка темной темы */
.dark .chapter-content {
  color: #e5e7eb;
}

.dark .chapter-content img {
  filter: brightness(0.9) contrast(1.1);
}

/* Улучшенная поддержка изображений в темной теме */
.dark .chapter-content img[data-align="left"],
.dark .chapter-content img[data-align="right"] {
  filter: brightness(0.9) contrast(1.1);
}

.dark .chapter-content img[data-align="center"] {
  filter: brightness(0.9) contrast(1.1);
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
  .chapter-content img[data-align="left"],
  .chapter-content img[data-align="right"],
  .chapter-content .custom-resizable-image.float-left,
  .chapter-content .custom-resizable-image.float-right,
  .chapter-content .custom-resizable-image.align-left,
  .chapter-content .custom-resizable-image.align-right {
    float: none !important;
    margin: 16px auto !important;
    max-width: 100% !important;
    display: block !important;
    text-align: center !important;
  }
  
  .chapter-content.tiptap-indent p {
    text-indent: 1em;
  }
}

@media (max-width: 480px) {
  /* На очень маленьких экранах все изображения отображаются как блочные */
  .chapter-content .custom-resizable-image {
    display: block !important;
    float: none !important;
    margin: 16px auto !important;
    max-width: 100% !important;
  }
}

/* Стили для prose (если используется Tailwind Typography) */
.chapter-content.prose {
  max-width: none !important;
  width: 100% !important;
}

.chapter-content.prose img {
  margin: 1rem auto;
}

.chapter-content.prose p {
  margin-bottom: 0.6em;
  margin-top: 0;
}

/* Поддержка всех размеров шрифтов читалки */
.chapter-content[style*="font-size: 14px"] { font-size: 14px !important; }
.chapter-content[style*="font-size: 18px"] { font-size: 18px !important; }
.chapter-content[style*="font-size: 22px"] { font-size: 22px !important; }
.chapter-content[style*="font-size: 26px"] { font-size: 26px !important; }

/* Поддержка всех шрифтов */
.chapter-content[style*="font-family: Georgia"] { font-family: Georgia, serif !important; }
.chapter-content[style*="font-family: Arial"] { font-family: Arial, sans-serif !important; }
.chapter-content[style*="font-family: Verdana"] { font-family: Verdana, sans-serif !important; }
.chapter-content[style*="font-family: Times"] { font-family: "Times New Roman", serif !important; }

/* Поддержка различных межстрочных интервалов */
.chapter-content[style*="line-height: 1.4"] {
  line-height: 1.4 !important;
}

.chapter-content[style*="line-height: 1.6"] {
  line-height: 1.6 !important;
}

.chapter-content[style*="line-height: 1.8"] {
  line-height: 1.8 !important;
}

.chapter-content[style*="line-height: 2"] {
  line-height: 2 !important;
}

/* Классы выравнивания изображений - точное соответствие редактору */
.chapter-content .custom-resizable-image {
  display: block;
  position: relative;
  margin: 16px auto;
  clear: both;
}

/* CSS классы для обтекания (float) - ТОЧНО как в редакторе */
.chapter-content .custom-resizable-image.float-left {
  float: left !important;
  margin: 0 16px 16px 0 !important;
  clear: none !important;
  z-index: 10;
  position: relative;
  cursor: pointer;
  display: block !important;
}

.chapter-content .custom-resizable-image.float-right {
  float: right !important;
  margin: 0 0 16px 16px !important;
  clear: none !important;
  z-index: 10;
  position: relative;
  cursor: pointer;
  display: block !important;
}

/* Улучшение взаимодействия для floating изображений */
.chapter-content .custom-resizable-image.float-left img,
.chapter-content .custom-resizable-image.float-right img {
  pointer-events: auto;
  cursor: pointer;
  user-select: none;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  shape-outside: margin-box !important;
  -webkit-shape-outside: margin-box !important;
}

/* CSS классы для выравнивания без обтекания */
.chapter-content .custom-resizable-image.align-left {
  float: none;
  margin: 16px 0 16px 0;
  text-align: left;
  clear: both;
}

.chapter-content .custom-resizable-image.align-right {
  float: none;
  margin: 16px 0 16px auto;
  text-align: right;
  clear: both;
}

.chapter-content .custom-resizable-image.align-center {
  float: none;
  margin: 16px auto;
  text-align: center;
  clear: both;
}

/* Обратная совместимость со старыми классами */
.chapter-content .float-left:not(.custom-resizable-image) { 
  float: left !important; 
  margin: 0 16px 16px 0 !important; 
  clear: none !important;
}

.chapter-content .float-right:not(.custom-resizable-image) { 
  float: right !important; 
  margin: 0 0 16px 16px !important; 
  clear: none !important;
}

.chapter-content .center-block { 
  display: block; 
  margin-left: auto; 
  margin-right: auto; 
}

/* Обеспечиваем правильное обтекание для параграфов после floating изображений - ТОЧНО как в редакторе */
.chapter-content .custom-resizable-image.float-left + p,
.chapter-content .custom-resizable-image.float-right + p {
  margin-top: 0 !important;
  clear: none !important;
}

/* Стили для обычного выравнивания (без обтекания) */
.chapter-content .custom-resizable-image.align-left + p,
.chapter-content .custom-resizable-image.align-right + p,
.chapter-content .custom-resizable-image.align-center + p {
  margin-top: 1em !important;
  clear: both !important;
}

/* Очистка float после последнего параграфа */
.chapter-content p:last-child::after {
  content: '';
  display: table;
  clear: both;
}
.image-caption {
  font-size: 13px;
  font-style: italic;
  text-align: center;
  color: #6b7280;
  margin-top: 8px;
  margin-bottom: 0;
  padding: 0 8px;
  line-height: 1.4;
  width: 100%;
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  opacity: 0.85;
  font-weight: 400;
}
.dark .image-caption { color: #9ca3af; }
figure { display: inline-block; margin: 0; }

/* --- Стили для изображений и подписей как в редакторе --- */
.custom-resizable-image {
  display: block;
  position: relative;
  margin: 16px auto;
  clear: both;
}
.custom-resizable-image.float-left {
  float: left;
  margin: 0 16px 16px 0;
  clear: none;
  z-index: 10;
  position: relative;
  cursor: pointer;
}
.custom-resizable-image.float-right {
  float: right;
  margin: 0 0 16px 16px;
  clear: none;
  z-index: 10;
  position: relative;
  cursor: pointer;
}
.custom-resizable-image.align-left {
  float: none;
  margin: 16px 0 16px 0;
  text-align: left;
  clear: both;
}
.custom-resizable-image.align-right {
  float: none;
  margin: 16px 0 16px auto;
  text-align: right;
  clear: both;
}
.custom-resizable-image.align-center {
  float: none;
  margin: 16px auto;
  text-align: center;
  clear: both;
}
.image-caption {
  font-size: 13px;
  font-style: italic;
  text-align: center;
  color: #6b7280;
  margin-top: 8px;
  margin-bottom: 0;
  padding: 0 8px;
  line-height: 1.4;
  width: 100%;
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  opacity: 0.85;
  font-weight: 400;
}
.dark .image-caption { color: #9ca3af; }
figure { display: inline-block; margin: 0; }
@media (max-width: 768px) {
  .custom-resizable-image.float-left,
  .custom-resizable-image.float-right,
  .custom-resizable-image.align-left,
  .custom-resizable-image.align-right {
    float: none !important;
    display: block !important;
    margin: 16px auto !important;
    text-align: center !important;
    max-width: 100% !important;
  }
} 