from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _
import os
import shutil
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.utils import timezone
from uuid import uuid4
from users.storage_backends import PublicMediaStorage, PrivateMediaStorage
import boto3
from django.conf import settings

# --- Вспомогательные функции ---
def get_group_folder(obj_id):
    group = obj_id // 500
    letter_index = group // 1000
    letter = chr(ord('a') + letter_index)
    number = f"{group % 1000:03d}"
    return f"{letter}{number}"

def random_prefix():
    return uuid4().hex[:8]

# Функция для генерации пути к папке пользователя
def get_user_media_path(username):
    username = username.lower()
    if len(username) < 2:
        return os.path.join('users', username[0], username)
    elif len(username) < 3:
        return os.path.join('users', username[0], username[:2], username)
    else:
        return os.path.join('users', username[0], username[:2], username)

def user_avatar_path(user_id):
    group_folder = get_group_folder(user_id)
    rand = random_prefix()
    return f"avatars/{group_folder}/{user_id}/{user_id}_ava_{rand}.webp"

def user_avatar_thumb_path(user_id):
    group_folder = get_group_folder(user_id)
    rand = random_prefix()
    return f"avatars/{group_folder}/{user_id}/{user_id}_avathumb_{rand}.webp"

def user_header_path(instance, filename):
    user_id = instance.id
    group_folder = get_group_folder(user_id)
    rand = random_prefix()
    return f"headers/{group_folder}/{user_id}/{user_id}_header_{rand}.webp"

def user_profile_pic_path(instance, filename):
    user_id = instance.id
    group_folder = get_group_folder(user_id)
    rand = random_prefix()
    return f"profile_pics/{group_folder}/{user_id}/{user_id}_{rand}.jpg"

# --- upload_to для вложений сообщений ---
def message_attachment_path(instance, filename):
    dialog_id = instance.dialog.id
    group_folder = get_group_folder(dialog_id)
    rand = random_prefix()
    ext = filename.split('.')[-1].lower()
    if ext == 'jpg':
        return f"messages/{group_folder}/{dialog_id}/{dialog_id}_pic_{rand}.jpg"
    else:
        return f"messages/{group_folder}/{dialog_id}/{dialog_id}_file_{rand}_{filename}"

def message_attachment_thumb_path(instance, filename):
    dialog_id = instance.dialog.id
    group_folder = get_group_folder(dialog_id)
    rand = random_prefix()
    # Миниатюра только для jpg
    return f"messages/{group_folder}/{dialog_id}/{dialog_id}_picm_{rand}.jpg"

class User(AbstractUser):
    GENDER_CHOICES = [
        ('', 'Не указано'),
        ('M', 'Мужской'),
        ('F', 'Женский'),
    ]
    
    username = models.CharField(
        _('username'),
        max_length=30,
        unique=True,
        help_text=_('Длина логина не должна превышать 30 символов. Допустимы латинские буквы и цифры.'),
        validators=[
            RegexValidator(
                regex='^[a-z0-9]+$',
                message='Логин должен содержать только строчные латинские буквы и цифры.',
                code='invalid_username'
            ),
        ],
        error_messages={
            'unique': _("Пользователь с таким логином уже существует."),
        },
    )
    display_name = models.CharField(
        _('display name'),
        max_length=150,
        help_text=_('Имя или псевдоним, который будет отображаться на сайте.'),
        default='Пользователь',
    )
    email = models.EmailField(_('email address'), unique=True)
    password = models.CharField(_('password'), max_length=128)
    reader_rating = models.IntegerField(default=0)
    author_rating = models.IntegerField(default=0)
    bio = models.TextField(blank=True)
    avatar = models.ImageField(storage=PublicMediaStorage(), upload_to=user_avatar_path, blank=True, null=True)
    avatar_thumbnail = models.ImageField(storage=PublicMediaStorage(), upload_to=user_avatar_thumb_path, blank=True, null=True)
    header_bg_image = models.ImageField(storage=PublicMediaStorage(), upload_to=user_header_path, blank=True, null=True)
    motto = models.CharField(_('motto'), max_length=140, blank=True, default='')
    hide_email = models.BooleanField(default=True, help_text='Скрывать e-mail в профиле')

    # Фон хедера профиля
    header_bg_type = models.CharField(max_length=16, blank=True, default='', help_text='Тип фона: solid, gradient, image')
    header_bg_color1 = models.CharField(max_length=16, blank=True, default='', help_text='Основной цвет фона (hex)')
    header_bg_color2 = models.CharField(max_length=16, blank=True, default='', help_text='Второй цвет для градиента (hex)')
    header_frame = models.CharField(max_length=255, blank=True, null=True, help_text='Путь к PNG-рамке для хедера')
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, default='', blank=True, help_text='Пол пользователя')
    last_activity = models.DateTimeField(auto_now=True, help_text='Время последней активности пользователя')

    auto_accept_friends = models.BooleanField(
        default=True,
        help_text='Автоматически принимать заявки в друзья (по умолчанию — автоматическое подтверждение)'
    )
    auto_remove_on_unfriend = models.BooleanField(
        default=False,
        help_text='Автоматически удалять из подписчиков, если прекращена дружба (по умолчанию — отключено)'
    )

    show_unsubscribes_in_feed = models.BooleanField(default=False, help_text='Показывать уведомления об отписках в ленте')
    show_removed_from_friends_in_feed = models.BooleanField(default=False, help_text='Показывать уведомления об удалении из друзей в ленте')

    avatar_preset = models.CharField(max_length=255, blank=True, default='', help_text='Путь к системному аватару')
    avatar_preset_mini = models.CharField(max_length=255, blank=True, default='', help_text='Путь к миниатюре системного аватара')

    avatar_updated_at = models.DateTimeField(null=True, blank=True, help_text='Время последнего изменения аватара')

    avatar_type = models.PositiveSmallIntegerField(default=1, choices=[(1, 'system'), (2, 'custom')], help_text='Тип аватара: 1 — системный, 2 — пользовательский')

    avatar_url_cached = models.CharField(max_length=255, blank=True, null=True, help_text='Кэшированный полный URL текущего аватара')
    avatar_thumbnail_url_cached = models.CharField(max_length=255, blank=True, null=True, help_text='Кэшированный полный URL текущей миниатюры')

    birth_date = models.DateField(null=True, blank=True, verbose_name='Дата рождения')

    show_birth_date = models.BooleanField(default=False, verbose_name='Показывать возраст в профиле')

    timezone = models.CharField(max_length=40, default='Europe/Moscow', verbose_name='Часовой пояс')
    timezone_display_value = models.CharField(max_length=40, default='Europe/Moscow_msk', verbose_name='Выбранное значение часового пояса', help_text='Сохраняет выбранный пользователем вариант (например, Europe/Moscow_msk или Europe/Moscow_spb)')

    # Поле для мягкого удаления аккаунта
    is_deleted = models.BooleanField(default=False, verbose_name='Аккаунт удален', help_text='Помечает аккаунт как удаленный (мягкое удаление)')
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='Дата удаления', help_text='Когда аккаунт был удален')

    # Поле для мягкого удаления аккаунта
    is_deleted = models.BooleanField(default=False, verbose_name='Аккаунт удален', help_text='Помечает аккаунт как удаленный (мягкое удаление)')
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='Дата удаления', help_text='Когда аккаунт был удален')

    books_blocks_order = models.JSONField(
        default=list,
        blank=True,
        help_text='Порядок блоков книг: например, ["in_progress", "finished"]'
    )

    @property
    def is_author(self):
        return self.books.exists()

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')

    def __str__(self):
        return self.display_name

    def set_default_avatar(self, gender=None):
        """
        Устанавливает системный аватар и миниатюру в зависимости от пола.
        gender: 'M', 'F' или '' (None = текущий пол)
        """
        g = gender if gender is not None else self.gender
        if g == 'M':
            self.avatar_preset = 'ava_presets/ava_m.jpg'
            self.avatar_preset_mini = 'ava_presets/ava_m_s.jpg'
        elif g == 'F':
            self.avatar_preset = 'ava_presets/ava_f.jpg'
            self.avatar_preset_mini = 'ava_presets/ava_f_s.jpg'
        else:
            self.avatar_preset = 'ava_presets/ava_n.jpg'
            self.avatar_preset_mini = 'ava_presets/ava_n_s.jpg'
        self.avatar = ''  # Очищаем пользовательский аватар
        self.avatar_type = 1
        self.avatar_updated_at = timezone.now()
        self.save(update_fields=['avatar_preset', 'avatar_preset_mini', 'avatar', 'avatar_type', 'avatar_updated_at'])
        self.update_avatar_urls()

    def get_system_avatar_path(self):
        """Get path to system avatar based on gender or deleted status"""
        # Если пользователь удален, возвращаем аватар удаленного пользователя
        if getattr(self, 'is_deleted', False):
            return 'ava_presets/ava_del.webp'

        if self.gender == 'M':
            return 'ava_presets/ava_M.webp'
        elif self.gender == 'F':
            return 'ava_presets/ava_F.webp'
        else:
            return 'ava_presets/ava_U.webp'

    def get_system_avatar_thumbnail_path(self):
        """Get path to system avatar thumbnail based on gender or deleted status"""
        # Если пользователь удален, возвращаем миниатюру аватара удаленного пользователя
        if getattr(self, 'is_deleted', False):
            return 'ava_presets/ava_del_s.webp'

        if self.gender == 'M':
            return 'ava_presets/ava_M_s.webp'
        elif self.gender == 'F':
            return 'ava_presets/ava_F_s.webp'
        else:
            return 'ava_presets/ava_U_s.webp'

    def reset_to_system_avatar(self):
        """Reset to system avatar"""
        # Не удаляем файлы с S3, только очищаем поля
        self.avatar = None
        self.avatar_thumbnail = None
        # Устанавливаем системные пресеты
        self.avatar_preset = self.get_system_avatar_path()
        self.avatar_preset_mini = self.get_system_avatar_thumbnail_path()
        # Устанавливаем тип аватара и время обновления
        self.avatar_type = 1
        self.avatar_updated_at = timezone.now()
        # Сохраняем изменения
        self.save(update_fields=[
            'avatar', 'avatar_thumbnail', 'avatar_preset', 
            'avatar_preset_mini', 'avatar_type', 'avatar_updated_at'
        ])
        self.update_avatar_urls()

    def update_system_avatar(self):
        """Update system avatar based on current gender"""
        self.avatar_preset = self.get_system_avatar_path()
        self.avatar_preset_mini = self.get_system_avatar_thumbnail_path()

    def use_custom_avatar(self):
        """
        Очищает системные аватары, используется при загрузке своего аватара.
        """
        self.avatar_preset = ''
        self.avatar_preset_mini = ''
        self.save(update_fields=['avatar_preset', 'avatar_preset_mini'])

    def update_avatar_urls(self):
        """Обновляет кэшированные URL аватаров"""
        print(f"Updating avatar URLs for user {self.username}")
        print(f"Current state: avatar_type={self.avatar_type}, avatar={self.avatar}, avatar_thumbnail={self.avatar_thumbnail}, is_deleted={getattr(self, 'is_deleted', False)}")

        # Если пользователь удален, устанавливаем аватары удаленного пользователя
        if getattr(self, 'is_deleted', False):
            self.avatar_url_cached = 'https://storage.yandexcloud.net/lpo-test/dist/ava_presets/ava_del.webp'
            self.avatar_thumbnail_url_cached = 'https://storage.yandexcloud.net/lpo-test/dist/ava_presets/ava_del_s.webp'
            print(f"Setting deleted user avatar URLs: {self.avatar_url_cached}, {self.avatar_thumbnail_url_cached}")
        elif self.avatar_type == 1:
            # Для системных аватаров используем полные пути к файлам в Yandex Cloud
            gender = self.gender.upper() if self.gender else 'U'
            self.avatar_url_cached = f'https://storage.yandexcloud.net/lpo-test/dist/ava_presets/ava_{gender}.webp'
            self.avatar_thumbnail_url_cached = f'https://storage.yandexcloud.net/lpo-test/dist/ava_presets/ava_{gender}_s.webp'
            print(f"Setting system avatar URLs: {self.avatar_url_cached}, {self.avatar_thumbnail_url_cached}")
        elif self.avatar and self.avatar_thumbnail:
            # Для пользовательских аватаров используем полные URL из Yandex Cloud
            self.avatar_url_cached = f'https://storage.yandexcloud.net/lpo-test/media/public/{self.avatar}'
            self.avatar_thumbnail_url_cached = f'https://storage.yandexcloud.net/lpo-test/media/public/{self.avatar_thumbnail}'
            print(f"Setting custom avatar URLs: {self.avatar_url_cached}, {self.avatar_thumbnail_url_cached}")
        else:
            # Если что-то пошло не так, сбрасываем на системный аватар
            gender = self.gender.upper() if self.gender else 'U'
            self.avatar_url_cached = f'https://storage.yandexcloud.net/lpo-test/dist/ava_presets/ava_{gender}.jpg'
            self.avatar_thumbnail_url_cached = f'https://storage.yandexcloud.net/lpo-test/dist/ava_presets/ava_{gender}_s.jpg'
            self.avatar_type = 1
            print(f"Resetting to system avatar URLs: {self.avatar_url_cached}, {self.avatar_thumbnail_url_cached}")

        print(f"Before save: avatar_url_cached={self.avatar_url_cached}, avatar_thumbnail_url_cached={self.avatar_thumbnail_url_cached}")
        self.save(update_fields=['avatar_url_cached', 'avatar_thumbnail_url_cached'])
        print(f"After save: avatar_url_cached={self.avatar_url_cached}, avatar_thumbnail_url_cached={self.avatar_thumbnail_url_cached}")

    def get_display_name(self):
        """Возвращает отображаемое имя пользователя (для удаленных аккаунтов возвращает 'Аккаунт удален')"""
        if self.is_deleted:
            return 'Аккаунт удален'
        return self.display_name

    def get_avatar_urls_for_deleted(self):
        """Возвращает URL аватаров для удаленного аккаунта"""
        return {
            'avatar_url': 'https://storage.yandexcloud.net/lpo-test/dist/ava_presets/ava_del.webp',
            'avatar_thumbnail_url': 'https://storage.yandexcloud.net/lpo-test/dist/ava_presets/ava_del_s.webp'
        }

    def get_avatar_url(self):
        """Возвращает URL аватара с учетом статуса удаления"""
        if self.is_deleted:
            return self.get_avatar_urls_for_deleted()['avatar_url']
        return self.avatar_url_cached

    def get_avatar_thumbnail_url(self):
        """Возвращает URL миниатюры аватара с учетом статуса удаления"""
        if self.is_deleted:
            return self.get_avatar_urls_for_deleted()['avatar_thumbnail_url']
        return self.avatar_thumbnail_url_cached

# Сигнал для удаления папки пользователя при удалении пользователя
@receiver(post_delete, sender=User)
def delete_user_media_folders(sender, instance, **kwargs):
    """
    При удалении пользователя удаляем все его папки с файлами (аватары, хедеры, profile_pics и т.д.)
    по новой структуре, кроме папок с диалогами и вложениями (messages).
    """
    user_id = instance.id
    group_folder = get_group_folder(user_id)
    s3 = boto3.resource(
        's3',
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        endpoint_url=settings.AWS_S3_ENDPOINT_URL,
        region_name=settings.AWS_S3_REGION_NAME,
    )
    bucket = s3.Bucket(settings.AWS_STORAGE_BUCKET_NAME)
    # Список префиксов для удаления
    prefixes = [
        f"{settings.AWS_S3_PUBLIC_MEDIA_PREFIX}/avatars/{group_folder}/{user_id}/",
        f"{settings.AWS_S3_PUBLIC_MEDIA_PREFIX}/headers/{group_folder}/{user_id}/",
        f"{settings.AWS_S3_PUBLIC_MEDIA_PREFIX}/profile_pics/{group_folder}/{user_id}/",
        # Добавьте другие публичные папки, если нужно
    ]
    for prefix in prefixes:
        bucket.objects.filter(Prefix=prefix).delete()

class Subscription(models.Model):
    from_user = models.ForeignKey(User, related_name='user_subscriptions', on_delete=models.CASCADE)
    to_user = models.ForeignKey(User, related_name='user_subscribers', on_delete=models.CASCADE)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('from_user', 'to_user')

class FriendRequest(models.Model):
    from_user = models.ForeignKey(User, related_name='sent_friend_requests', on_delete=models.CASCADE)
    to_user = models.ForeignKey(User, related_name='received_friend_requests', on_delete=models.CASCADE)
    created = models.DateTimeField(auto_now_add=True)
    accepted = models.BooleanField(default=False)

    class Meta:
        unique_together = ('from_user', 'to_user')

class FeedEvent(models.Model):
    EVENT_TYPES = (
        ('friend_request', 'Запрос в друзья'),
        ('friend_accepted', 'Дружба принята'),
        ('new_post', 'Новый пост'),
        ('new_comment', 'Новый комментарий'),
        ('new_book', 'Новая книга'),
        ('new_review', 'Новый отзыв'),
        ('subscribed', 'Подписка'),
        ('unsubscribed', 'Отписка'),
        ('removed_from_friends', 'Удаление из друзей'),
    )

    status_choices = [
        ('new', 'Новое'),
        ('accepted', 'Принято'),
        ('declined', 'Отклонено'),
        ('cancelled', 'Отменено'),
        ('read', 'Прочитано'),
    ]

    status = models.CharField(max_length=20, choices=status_choices, default='new')

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='feed_events')
    event_type = models.CharField(max_length=20, choices=EVENT_TYPES)
    actor = models.ForeignKey(User, on_delete=models.CASCADE, related_name='caused_events')
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['user', 'is_read']),
        ]

    def __str__(self):
        return f"{self.actor.username} - {self.get_event_type_display()} - {self.created_at}"

class Dialog(models.Model):
    participants = models.ManyToManyField(User, related_name='dialogs')
    last_message = models.ForeignKey('Message', null=True, blank=True, on_delete=models.SET_NULL, related_name='last_message_dialog')
    updated_at = models.DateTimeField(auto_now=True)
    user_states = models.ManyToManyField(
        User,
        through='DialogUserState',
        through_fields=('dialog', 'user'),
        related_name='dialogs_user_states'
    )

    def __str__(self):
        return f"Диалог: {', '.join([u.username for u in self.participants.all()])}"

    class Meta:
        ordering = ['-updated_at']

class Message(models.Model):
    dialog = models.ForeignKey(Dialog, related_name='messages', on_delete=models.CASCADE)
    sender = models.ForeignKey('User', related_name='sent_messages', on_delete=models.CASCADE)
    recipient = models.ForeignKey('User', related_name='received_messages', on_delete=models.CASCADE)
    text = models.TextField(blank=True)
    gif = models.CharField(max_length=255, blank=True, null=True, help_text='URL или путь к gif')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(null=True, blank=True, help_text='Время последнего редактирования')
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    emoji_only = models.BooleanField(default=False, help_text='True если сообщение состоит только из одного emoji')
    uuid = models.UUIDField(default=uuid4, editable=False, unique=True)
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    reply_to = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL, related_name='replies')

    def __str__(self):
        return f"{self.sender} -> {self.recipient}: {self.text[:30]}"

    class Meta:
        ordering = ['created_at']

class DialogUserState(models.Model):
    dialog = models.ForeignKey(Dialog, on_delete=models.CASCADE, related_name='dialog_user_states')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='dialog_states')
    is_deleted = models.BooleanField(default=False)
    is_left = models.BooleanField(default=False)
    left_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='deleted_dialogs')

    class Meta:
        unique_together = ('dialog', 'user')

class MessageAttachment(models.Model):
    dialog = models.ForeignKey('Dialog', on_delete=models.CASCADE, related_name='attachments')
    message = models.ForeignKey('Message', on_delete=models.CASCADE, related_name='attachments', null=True, blank=True)
    file = models.FileField(storage=PrivateMediaStorage(), upload_to=message_attachment_path)
    thumb = models.ImageField(storage=PrivateMediaStorage(), upload_to=message_attachment_thumb_path, blank=True, null=True)

class UserHeaderImage(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='header_images')
    path = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']

class UserAvatar(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='avatars')
    path = models.CharField(max_length=255)        # путь к полному аватару
    thumb_path = models.CharField(max_length=255)  # путь к миниатюре
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

class UserNotification(models.Model):
    NOTIFICATION_TYPES = [
        ('unread_messages', 'Непрочитанные сообщения'),
        ('feed_events', 'События в ленте'),
        ('friend_requests', 'Запросы в друзья'),
        ('new_messages', 'Новые сообщения'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    data = models.JSONField(default=dict, help_text='Данные уведомления (количество, ID объектов и т.д.)')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('user', 'notification_type')
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"{self.user.username} - {self.notification_type}: {self.data}"
    
    @classmethod
    def get_or_create_notification(cls, user, notification_type, data=None):
        """Получить или создать уведомление для пользователя"""
        notification, created = cls.objects.get_or_create(
            user=user,
            notification_type=notification_type,
            defaults={'data': data or {}}
        )
        if not created and data is not None:
            notification.data = data
            notification.save(update_fields=['data', 'updated_at'])
        return notification
    
    @classmethod
    def update_notification(cls, user, notification_type, data):
        """Обновить уведомление для пользователя"""
        return cls.get_or_create_notification(user, notification_type, data)

# ================================
# СИСТЕМА РЕЙТИНГОВ И МЕТРИК
# ================================

class RatingHistory(models.Model):
    """История изменений рейтинга пользователя"""
    METRIC_CHOICES = [
        ('reader_rating', 'Рейтинг читателя'),
        ('author_rating', 'Авторский рейтинг'),
    ]

    ACTION_CHOICES = [
        # Читательский рейтинг
        ('welcome_bonus', 'Приветственный бонус'),
        ('comment_added', 'Новый комментарий к книге'),
        ('reply_to_comment_added', 'Ответ на комментарий'),
        ('reply_to_reply_added', 'Ответ на ответный комментарий'),
        ('comment_like_received', 'Получен лайк к комментарию'),
        ('comment_dislike_received', 'Получен дизлайк к комментарию'),
        ('book_purchased', 'Покупка книги'),
        ('reading_session', 'Сессия чтения'),
        ('award_given', 'Выдача награды'),

        # Авторский рейтинг
        ('comment_received', 'Новый комментарий к книге'),
        ('book_like_received', 'Получен лайк к книге'),
        ('book_like_removed', 'Лайк к книге отозван'),
        ('book_sold', 'Продажа книги'),
        ('award_received', 'Получена награда'),
        ('blog_post_published', 'Опубликован пост в блоге'),
        ('book_status_changed', 'Изменение статуса книги'),

        # Социальные действия
        ('subscriber_added', 'Новый подписчик/друг'),
        ('subscriber_removed', 'Отписка/Разрыв дружбы'),

        # Изменения статуса рассказов
        ('story_finished_to_partial', 'Статус рассказа изменен из завершенного в процесс публикации'),
        ('story_partial_to_draft', 'Статус рассказа изменен из процесса публикации в черновик'),
        ('story_draft_to_partial', 'Статус рассказа изменен из черновика в процесс публикации'),
        ('story_partial_to_finished', 'Рассказ завершен из процесса публикации'),
        ('story_finished_to_draft', 'Статус рассказа изменен из завершенного в черновик'),
        ('story_draft_to_finished', 'Рассказ завершен из черновика'),
        ('story_delete_in_progress', 'Удаление книги (рассказ в процессе публикации)'),

        # Изменения статуса повестей/романов
        ('novel_finished_to_partial', 'Статус Повести/Романа изменен из завершенного в процесс публикации'),
        ('novel_partial_to_draft', 'Статус Повести/Романа изменен из процесса публикации в черновик'),
        ('novel_draft_to_partial', 'Статус Повести/Романа изменен из черновика в процесс публикации'),
        ('novel_partial_to_finished', 'Повесть/Роман завершен(а) из процесса публикации'),
        ('novel_finished_to_draft', 'Статус Повести/Романа изменен из завершенного(нной) в черновик'),
        ('novel_draft_to_finished', 'Повесть/Роман завершен(а) из черновика'),

        # Отзывы рейтинга
        ('comment_removed', 'Комментарий удален'),
        ('comment_like_revoked', 'Лайк к комментарию отозван'),
        ('comment_like_removed', 'Лайк к комментарию отозван'),
        ('comment_dislike_removed', 'Дизлайк к комментарию отозван'),
        ('comment_received_revoked', 'Отозван комментарий к книге'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='rating_history')
    metric_name = models.CharField(max_length=20, choices=METRIC_CHOICES)
    action_type = models.CharField(max_length=30, choices=ACTION_CHOICES)
    change_delta = models.IntegerField(help_text='Изменение рейтинга (может быть отрицательным)')
    old_value = models.IntegerField(help_text='Значение до изменения')
    new_value = models.IntegerField(help_text='Значение после изменения')
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=20, default='system', help_text='Кто создал запись')

    # Связь с объектом, который вызвал изменение (полиморфная)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    related_object = GenericForeignKey('content_type', 'object_id')

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'metric_name', '-created_at']),
            models.Index(fields=['user', '-created_at']),
        ]

    def __str__(self):
        sign = '+' if self.change_delta > 0 else ''
        return f'{self.user.username}: {self.get_metric_name_display()} {sign}{self.change_delta} ({self.get_action_type_display()})'


class UserStats(models.Model):
    """
    Кэшированные агрегированные показатели пользователя.
    Обновляются через Celery задачи для производительности.
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='stats')
    
    # === ОБЩИЕ ПОКАЗАТЕЛИ ===
    total_rating = models.IntegerField(default=0, help_text='Общий рейтинг пользователя')
    reader_rating = models.IntegerField(default=0, help_text='Читательский рейтинг')
    author_rating = models.IntegerField(default=0, help_text='Авторский рейтинг')
    
    # === СОЦИАЛЬНЫЕ ПОКАЗАТЕЛИ ===
    friends_count = models.IntegerField(default=0, help_text='Количество друзей')
    subscribers_count = models.IntegerField(default=0, help_text='Количество подписчиков')
    subscriptions_count = models.IntegerField(default=0, help_text='Количество подписок')
    
    # === АВТОРСКИЕ ПОКАЗАТЕЛИ ===
    books_count = models.IntegerField(default=0, help_text='Количество книг')
    published_books_count = models.IntegerField(default=0, help_text='Количество опубликованных книг')
    finished_books_count = models.IntegerField(default=0, help_text='Количество завершенных книг')
    # УДАЛЕНО: total_chapters_count - рейтинг по главам больше не нужен
    total_words_count = models.BigIntegerField(default=0, help_text='Общее количество слов в произведениях')
    books_likes_count = models.IntegerField(default=0, help_text='Общее количество лайков на книгах')
    books_reviews_count = models.IntegerField(default=0, help_text='Общее количество отзывов на книгах')
    books_comments_count = models.IntegerField(default=0, help_text='Общее количество комментариев на книгах')
    
    # === ЧИТАТЕЛЬСКИЕ ПОКАЗАТЕЛИ ===
    comments_left_count = models.IntegerField(default=0, help_text='Количество оставленных комментариев')
    reviews_left_count = models.IntegerField(default=0, help_text='Количество оставленных отзывов')
    likes_left_count = models.IntegerField(default=0, help_text='Количество поставленных лайков')
    reading_time_minutes = models.BigIntegerField(default=0, help_text='Время чтения в минутах')
    books_read_count = models.IntegerField(default=0, help_text='Количество прочитанных книг')
    
    # === АКТИВНОСТЬ ===
    profile_views_count = models.IntegerField(default=0, help_text='Количество просмотров профиля')
    messages_sent_count = models.IntegerField(default=0, help_text='Количество отправленных сообщений')
    days_active_count = models.IntegerField(default=0, help_text='Количество дней активности')
    
    # === ДОПОЛНИТЕЛЬНЫЕ МЕТРИКИ ===
    registration_bonus_given = models.BooleanField(default=False, help_text='Приветственный бонус выдан')
    daily_comments_count = models.IntegerField(default=0, help_text='Комментарии за сегодня (сбрасывается)')
    last_comment_date = models.DateField(null=True, blank=True, help_text='Дата последнего комментария')
    book_purchases_count = models.IntegerField(default=0, help_text='Покупки книг')
    awards_given_count = models.IntegerField(default=0, help_text='Награды произведениям')
    awards_received_count = models.IntegerField(default=0, help_text='Полученные награды')
    blog_posts_count = models.IntegerField(default=0, help_text='Посты в блоге')
    comment_likes_received = models.IntegerField(default=0, help_text='Лайки полученные на комментарии')
    comment_dislikes_received = models.IntegerField(default=0, help_text='Дизлайки полученные на комментарии')
    
    # === СЛУЖЕБНЫЕ ПОЛЯ ===
    last_updated = models.DateTimeField(auto_now=True, help_text='Время последнего обновления статистики')
    recalculation_scheduled = models.BooleanField(default=False, help_text='Запланирован пересчет статистики')
    
    class Meta:
        verbose_name = 'Статистика пользователя'
        verbose_name_plural = 'Статистика пользователей'
        indexes = [
            models.Index(fields=['total_rating']),
            models.Index(fields=['reader_rating']),
            models.Index(fields=['author_rating']),
            models.Index(fields=['last_updated']),
        ]
    
    def __str__(self):
        return f"Статистика {self.user.display_name}"
    
    @property
    def user_level(self):
        """Определить уровень пользователя на основе общего рейтинга."""
        if self.total_rating >= 50000:
            return 'pro'
        elif self.total_rating >= 5000:
            return 'advanced'
        else:
            return 'beginner'
    
    @property
    def user_level_display(self):
        """Отображаемое название уровня пользователя."""
        levels = {
            'beginner': 'Начальный',
            'advanced': 'Повышенный', 
            'pro': 'Профессиональный'
        }
        return levels.get(self.user_level, 'Начальный')
    
    def get_comment_base_points(self, comment_type='book'):
        """Получить базовые баллы за комментарий в зависимости от уровня."""
        level = self.user_level
        
        if comment_type == 'book':
            return {'beginner': 5, 'advanced': 10, 'pro': 15}[level]
        elif comment_type in ['blog', 'club']:
            return 2
        return 0
    
    def get_like_weight(self, content_type='book'):
        """Получить вес лайка от этого пользователя."""
        level = self.user_level
        
        if content_type == 'book':
            return {'beginner': 1, 'advanced': 3, 'pro': 10}[level]
        elif content_type in ['blog', 'club']:
            return {'beginner': 1, 'advanced': 2, 'pro': 4}[level]
        return 1
    
    def can_comment_today(self):
        """Проверить, может ли пользователь оставить еще комментарий сегодня."""
        from django.utils import timezone
        
        today = timezone.now().date()
        if self.last_comment_date != today:
            return True
        return self.daily_comments_count < 20
    
    def reset_daily_comments_if_needed(self):
        """Сбросить счетчик дневных комментариев если наступил новый день."""
        from django.utils import timezone
        
        today = timezone.now().date()
        if self.last_comment_date != today:
            self.daily_comments_count = 0
            self.last_comment_date = today
            self.save(update_fields=['daily_comments_count', 'last_comment_date'])


class RatingCalculationRule(models.Model):
    """
    Конфигурация правил расчета рейтингов.
    Позволяет изменять формулы без изменения кода.
    """
    RATING_TYPES = [
        ('reader', 'Рейтинг читателя'),
        ('author', 'Авторский рейтинг'),
        ('total', 'Общий рейтинг'),
    ]
    
    rating_type = models.CharField(max_length=20, choices=RATING_TYPES)
    metric_name = models.CharField(max_length=50, help_text='Название метрики (поле в UserStats)')
    weight = models.DecimalField(max_digits=5, decimal_places=2, default=1.0, help_text='Вес метрики в расчете')
    max_contribution = models.IntegerField(null=True, blank=True, help_text='Максимальный вклад метрики в рейтинг')
    is_active = models.BooleanField(default=True, help_text='Учитывать ли метрику в расчете')
    description = models.CharField(max_length=200, blank=True, help_text='Описание правила')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'Правило расчета рейтинга'
        verbose_name_plural = 'Правила расчета рейтингов'
        unique_together = ['rating_type', 'metric_name']
        indexes = [
            models.Index(fields=['rating_type', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.get_rating_type_display()}: {self.metric_name} (вес: {self.weight})"


class UserMetricHistory(models.Model):
    """
    История изменений метрик пользователя.
    Для аудита и возможности восстановления данных.
    """
    ACTION_TYPES = [
        ('comment_added', 'Добавлен комментарий'),
        ('comment_removed', 'Удален комментарий'),
        ('review_added', 'Добавлен отзыв'),
        ('review_removed', 'Удален отзыв'),
        ('like_added', 'Поставлен лайк'),
        ('like_removed', 'Убран лайк'),
        ('book_published', 'Книга опубликована'),
        ('book_finished', 'Книга завершена'),
        ('friend_added', 'Добавлен друг'),
        ('friend_removed', 'Удален друг'),
        ('subscriber_added', 'Добавлен подписчик'),
        ('subscriber_removed', 'Удален подписчик'),
        ('reading_time', 'Время чтения'),
        ('manual_adjustment', 'Ручная корректировка'),
        ('recalculation', 'Пересчет статистики'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='metric_history')
    action_type = models.CharField(max_length=30, choices=ACTION_TYPES)
    metric_name = models.CharField(max_length=50, help_text='Название изменившейся метрики')
    old_value = models.BigIntegerField(help_text='Старое значение')
    new_value = models.BigIntegerField(help_text='Новое значение')
    change_delta = models.IntegerField(help_text='Изменение (+/-)')
    
    # Связанные объекты
    related_object_type = models.CharField(max_length=50, blank=True, help_text='Тип связанного объекта')
    related_object_id = models.PositiveIntegerField(null=True, blank=True, help_text='ID связанного объекта')
    
    # Рейтинги до и после изменения
    reader_rating_before = models.IntegerField(default=0)
    reader_rating_after = models.IntegerField(default=0)
    author_rating_before = models.IntegerField(default=0)
    author_rating_after = models.IntegerField(default=0)
    total_rating_before = models.IntegerField(default=0)
    total_rating_after = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=50, default='system', help_text='Кто инициировал изменение')
    
    class Meta:
        verbose_name = 'История метрик'
        verbose_name_plural = 'История метрик'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['action_type', '-created_at']),
            models.Index(fields=['metric_name', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name}: {self.metric_name} {self.old_value}→{self.new_value}"


class RatingRecalculationTask(models.Model):
    """
    Очередь задач для пересчета рейтингов.
    Позволяет управлять нагрузкой на систему.
    """
    TASK_TYPES = [
        ('user_stats', 'Пересчет статистики пользователя'),
        ('user_rating', 'Пересчет рейтинга пользователя'),
        ('bulk_recalculation', 'Массовый пересчет'),
        ('daily_update', 'Ежедневное обновление'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Ожидает'),
        ('processing', 'Выполняется'),
        ('completed', 'Завершено'),
        ('failed', 'Ошибка'),
        ('cancelled', 'Отменено'),
    ]
    
    task_type = models.CharField(max_length=30, choices=TASK_TYPES)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, 
                           related_name='rating_tasks', help_text='Пользователь для пересчета (если не массовый)')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.IntegerField(default=5, help_text='Приоритет задачи (1-10, 1=высший)')
    
    # Параметры задачи
    parameters = models.JSONField(default=dict, help_text='Дополнительные параметры задачи')
    
    # Результаты выполнения
    celery_task_id = models.CharField(max_length=100, blank=True, null=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True, help_text='Сообщение об ошибке')
    
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=50, default='system')
    
    class Meta:
        verbose_name = 'Задача пересчета рейтинга'
        verbose_name_plural = 'Задачи пересчета рейтингов'
        ordering = ['priority', 'created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['task_type', 'created_at']),
            models.Index(fields=['user', 'status']),
        ]
    
    def __str__(self):
        user_str = f" для {self.user.display_name}" if self.user else ""
        return f"{self.get_task_type_display()}{user_str} ({self.get_status_display()})"


class UserLibrary(models.Model):
    """
    Библиотека пользователя - полки с книгами
    """
    READING_STATUS_CHOICES = [
        ('reading', 'Читаю'),
        ('want_to_read', 'Отложено на потом'),
        ('read', 'Прочитано'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='library')
    book = models.ForeignKey('books.Book', on_delete=models.CASCADE, related_name='in_libraries')
    status = models.CharField(max_length=20, choices=READING_STATUS_CHOICES)
    added_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Дополнительные поля для статистики чтения
    reading_started_at = models.DateTimeField(null=True, blank=True, help_text='Когда начал читать')
    reading_finished_at = models.DateTimeField(null=True, blank=True, help_text='Когда закончил читать')
    reading_progress = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text='Прогресс чтения в процентах')
    current_chapter = models.ForeignKey('books.BookChapter', on_delete=models.SET_NULL, null=True, blank=True, help_text='Текущая глава')
    
    # Пользовательские оценки и заметки
    user_rating = models.PositiveSmallIntegerField(null=True, blank=True, help_text='Личная оценка от 1 до 10')
    private_notes = models.TextField(blank=True, help_text='Личные заметки о книге')
    
    class Meta:
        unique_together = ('user', 'book')
        verbose_name = 'Книга в библиотеке'
        verbose_name_plural = 'Книги в библиотеке'
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['user', '-updated_at']),
            models.Index(fields=['book', 'status']),
        ]
    
    def __str__(self):
        return f'{self.user.username} - {self.book.title} ({self.get_status_display()})'
    
    def mark_as_reading(self):
        """Помечает книгу как читаемую"""
        self.status = 'reading'
        if not self.reading_started_at:
            self.reading_started_at = timezone.now()
        self.save()
    
    def mark_as_read(self):
        """Помечает книгу как прочитанную"""
        self.status = 'read'
        self.reading_finished_at = timezone.now()
        self.reading_progress = 100
        self.save()
    
    def update_progress(self, progress_percent):
        """Обновляет прогресс чтения"""
        self.reading_progress = min(100, max(0, progress_percent))
        if self.reading_progress >= 100:
            self.mark_as_read()
        elif self.reading_progress > 0 and self.status == 'want_to_read':
            self.mark_as_reading()
        self.save()


class ReadingSession(models.Model):
    """
    Сессия чтения для автоматического добавления книг в библиотеку
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reading_sessions')
    book = models.ForeignKey('books.Book', on_delete=models.CASCADE, related_name='reading_sessions')
    chapter = models.ForeignKey('books.BookChapter', on_delete=models.CASCADE, null=True, blank=True)
    
    started_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    duration_minutes = models.PositiveIntegerField(default=0, help_text='Длительность сессии в минутах')
    
    # Флаги состояния
    is_active = models.BooleanField(default=True)
    auto_added_to_library = models.BooleanField(default=False, help_text='Автоматически добавлена в библиотеку после 5 минут')
    
    class Meta:
        verbose_name = 'Сессия чтения'
        verbose_name_plural = 'Сессии чтения'
        ordering = ['-started_at']
        indexes = [
            models.Index(fields=['user', 'book', '-started_at']),
            models.Index(fields=['user', 'is_active']),
        ]
    
    def __str__(self):
        return f'{self.user.username} читает {self.book.title} ({self.duration_minutes} мин)'
    
    def update_duration(self):
        """Обновляет длительность сессии"""
        if self.is_active:
            delta = timezone.now() - self.started_at
            self.duration_minutes = int(delta.total_seconds() / 60)
            
            # Автоматически добавляем в библиотеку после 5 минут чтения
            if self.duration_minutes >= 5 and not self.auto_added_to_library:
                self.add_to_library_if_needed()
            
            self.save()
    
    def add_to_library_if_needed(self):
        """Добавляет книгу в библиотеку если её там нет"""
        library_entry, created = UserLibrary.objects.get_or_create(
            user=self.user,
            book=self.book,
            defaults={'status': 'reading'}
        )
        
        if created or library_entry.status == 'want_to_read':
            library_entry.mark_as_reading()
        
        self.auto_added_to_library = True
        self.save()
    
    def close_session(self):
        """Закрывает сессию чтения"""
        self.is_active = False
        self.update_duration()
        self.save()
