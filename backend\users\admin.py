from django.contrib import admin
from django.contrib.auth import get_user_model
from .models import User, Dialog, Message, UserHeaderImage

User = get_user_model()

@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'username', 'display_name', 'date_joined', 'last_login',
        'is_staff', 'is_active', 'email'
    ]
    search_fields = ['username', 'email', 'id']
    list_filter = ['is_active', 'is_staff', 'is_superuser']
    ordering = ['id']

@admin.register(Dialog)
class DialogAdmin(admin.ModelAdmin):
    list_display = ('id', 'updated_at')
    filter_horizontal = ('participants',)

@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'dialog', 'sender', 'recipient', 'text', 'is_read', 'is_deleted', 'created_at')
    list_filter = ('is_read', 'is_deleted', 'dialog', 'sender', 'recipient')
    search_fields = ('text', 'sender__username', 'recipient__username')
    readonly_fields = ('id', 'created_at', 'uuid')

@admin.register(UserHeaderImage)
class UserHeaderImageAdmin(admin.ModelAdmin):
    list_display = [field.name for field in UserHeaderImage._meta.fields]
    search_fields = ['user__username', 'path', 'id']
    list_filter = ['user']
    ordering = ['id']
