import React, { useState, useEffect } from 'react';
import { useTheme } from '../theme/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { getUserAvatar } from '../utils/avatarUtils';
import Messages from './Messages';
import { useUserSettings } from '../context/UserSettingsContext';
import { formatDateWithTimezone } from '../utils/formatDate';

const Profile = () => {
    const { theme } = useTheme();
    const { user } = useAuth();
    const [activeTab, setActiveTab] = useState('profile');
    const [profile, setProfile] = useState(null);
    const [friends, setFriends] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const { timezone } = useUserSettings();

    useEffect(() => {
        const fetchProfile = async () => {
            try {
                const response = await fetch(`${process.env.REACT_APP_API_URL}/api/profile/`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                if (!response.ok) throw new Error('Failed to fetch profile');
                const data = await response.json();
                setProfile(data);
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchProfile();
    }, []);

    useEffect(() => {
        const fetchFriends = async () => {
            try {
                const response = await fetch(`${process.env.REACT_APP_API_URL}/api/friends/`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                if (!response.ok) throw new Error('Failed to fetch friends');
                const data = await response.json();
                setFriends(data.results);
            } catch (err) {
                setError(err.message);
            }
        };

        if (activeTab === 'friends') {
            fetchFriends();
        }
    }, [activeTab]);

    if (loading) return <div className="text-center p-4">Загрузка...</div>;
    if (error) return <div className="text-red-500 p-4">{error}</div>;
    if (!profile) return <div className="text-center p-4">Профиль не найден</div>;

    return (
        <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
            {/* Вкладки */}
            <div className={`border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <nav className="flex space-x-8">
                        <button
                            onClick={() => setActiveTab('profile')}
                            className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'profile'
                                    ? theme === 'dark'
                                        ? 'border-blue-500 text-blue-400'
                                        : 'border-blue-500 text-blue-600'
                                    : theme === 'dark'
                                        ? 'border-transparent text-gray-400 hover:text-gray-300'
                                        : 'border-transparent text-gray-500 hover:text-gray-700'
                            }`}
                        >
                            Профиль
                        </button>
                        <button
                            onClick={() => setActiveTab('friends')}
                            className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'friends'
                                    ? theme === 'dark'
                                        ? 'border-blue-500 text-blue-400'
                                        : 'border-blue-500 text-blue-600'
                                    : theme === 'dark'
                                        ? 'border-transparent text-gray-400 hover:text-gray-300'
                                        : 'border-transparent text-gray-500 hover:text-gray-700'
                            }`}
                        >
                            Друзья
                        </button>
                        <button
                            onClick={() => setActiveTab('messages')}
                            className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'messages'
                                    ? theme === 'dark'
                                        ? 'border-blue-500 text-blue-400'
                                        : 'border-blue-500 text-blue-600'
                                    : theme === 'dark'
                                        ? 'border-transparent text-gray-400 hover:text-gray-300'
                                        : 'border-transparent text-gray-500 hover:text-gray-700'
                            }`}
                        >
                            Сообщения
                        </button>
                    </nav>
                </div>
            </div>

            {/* Контент вкладок */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                {activeTab === 'profile' && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="md:col-span-2">
                            <div className={`rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} p-6`}>
                                <h2 className="text-xl font-bold mb-4">Информация профиля</h2>
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium mb-1">Отображаемое имя</label>
                                        <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                                            {profile.display_name}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium mb-1">Email</label>
                                        <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                                            {profile.email}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium mb-1">Дата регистрации</label>
                                        <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                                            {formatDateWithTimezone(profile.date_joined, timezone, 'DD.MM.YYYY')}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div className={`rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} p-6`}>
                                <h2 className="text-xl font-bold mb-4">Статистика</h2>
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium mb-1">Друзей</label>
                                        <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                                            {profile.friends_count}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium mb-1">Постов</label>
                                        <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                                            {profile.posts_count}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {activeTab === 'friends' && (
                    <div className={`rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} p-6`}>
                        <h2 className="text-xl font-bold mb-4">Список друзей</h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            {friends.map(friend => (
                                <div
                                    key={friend.id}
                                    className={`p-4 rounded-lg ${
                                        theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'
                                    }`}
                                >
                                    <div className="flex items-center space-x-3">
                                        <img
                                            src={getUserAvatar(friend)}
                                            alt={friend.display_name}
                                            className="w-12 h-12 rounded-full"
                                        />
                                        <div>
                                            <p className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                                                {friend.display_name === 'Аккаунт удален' ? (
                                                    <span className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                                                        Аккаунт удален
                                                    </span>
                                                ) : (
                                                    friend.display_name
                                                )}
                                            </p>
                                            <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                                                {friend.is_online ? 'В сети' : 'Не в сети'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
                {activeTab === 'messages' && (
                    <div className="h-[calc(100vh-200px)]">
                        <Messages />
                    </div>
                )}
            </div>
        </div>
    );
};

export default Profile; 