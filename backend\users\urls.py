from django.urls import path, include
from .views import (
    UserListView,
    UserDetailView,
    UserProfileView,
    CheckAuthView,
    LogoutView,
    get_csrf_token,
    CheckUsernameView,
    RegisterView,
    PublicUserView,
    header_presets_list,
    UploadHeaderImageView,
    upload_avatar,
    reset_avatar,
    change_gender,
    header_frames_list,
    UserPingView,
    SubscribeToggleView,
    FriendRequestView,
    FriendRequestAcceptView,
    FriendRequestDeclineView,
    RemoveFriendView,
    UserStatsView,
    FriendRequestsInboxView,
    UserRelationStatusView,
    friends_list, subscriptions_list, subscribers_list,
    FeedEventsView,
    FeedEventsCountView,
    MarkFeedEventReadView,
    feed_settings,
    system_avatars_list,
    DialogListView, MessageListView, UnreadMessagesCountView, MarkMessagesReadView, MessageDetailView,
    DialogLeaveView,
    UserHeaderImageListCreateView,
    UserHeaderImageDeleteView,
    UserAvatarListCreateView,
    UserAvatarDeleteView,
    upload_bio_image,
    delete_bio_image,
    get_bio_image_links,
    cleanup_bio_images,
    UserSearchView,
    select_avatar,
    UserBooksBlocksOrderView,
    UserNotificationsView,
    UserMetricHistoryView,
    LeaderboardView,
    RecalculateRatingView,
    RatingRulesView,
    UserRatingStatsView,
    UserRatingPositionView,
    RatingStatsView,
    RatingUserStatsView,
    UserLibraryViewSet,
    ReadingSessionViewSet,
    quick_add_to_library,
    check_library_status,
    # OAuth Views
    VKLoginView,
    YandexLoginView,
    GoogleLoginView,
    OKLoginView,
    # Account deletion
    DeleteAccountView,
)
from rest_framework.routers import DefaultRouter

# Настройка роутера для ViewSet'ов (если понадобится в будущем)
# router = DefaultRouter()
# router.register(r'reading-sessions', ReadingSessionViewSet, basename='reading-sessions')

urlpatterns = [
    path('csrf/', get_csrf_token, name='get-csrf-token'),
    path('check/', CheckAuthView.as_view(), name='check-auth'),

    path('logout/', LogoutView.as_view(), name='logout'),
    path('delete-account/', DeleteAccountView.as_view(), name='delete-account'),
    path('check-username/', CheckUsernameView.as_view(), name='check-username'),
    path('register/', RegisterView.as_view(), name='register'),
    path('header-presets/', header_presets_list),
    path('header-frames/', header_frames_list, name='header-frames-list'),
    path('', UserListView.as_view(), name='user-list'),
    path('<int:pk>/', UserDetailView.as_view(), name='user-detail'),
    path('profile/', UserProfileView.as_view(), name='user-profile'),
    path('public/<str:username>/', PublicUserView.as_view(), name='public-user'),
    path('upload-header/', UploadHeaderImageView.as_view(), name='upload-header'),
    path('upload-avatar/', upload_avatar, name='upload-avatar'),
    path('reset-avatar/', reset_avatar, name='reset-avatar'),
    path('change-gender/', change_gender, name='change-gender'),
    path('ping/', UserPingView.as_view(), name='user-ping'),
    path('subscribe/', SubscribeToggleView.as_view(), name='subscribe-toggle'),
    path('friend-request/', FriendRequestView.as_view(), name='friend-request'),
    path('friend-request/accept/', FriendRequestAcceptView.as_view(), name='friend-request-accept'),
    path('friend-request/decline/', FriendRequestDeclineView.as_view(), name='friend-request-decline'),
    path('remove-friend/', RemoveFriendView.as_view(), name='remove-friend'),
    path('stats/<str:username>/', UserStatsView.as_view(), name='user-stats'),
    path('friend-requests/inbox/', FriendRequestsInboxView.as_view(), name='friend-requests-inbox'),
    path('relation-status/<str:username>/', UserRelationStatusView.as_view(), name='user-relation-status'),
    path('friends/<str:username>/', friends_list, name='friends-list'),
    path('subscriptions/<str:username>/', subscriptions_list, name='subscriptions-list'),
    path('subscribers/<str:username>/', subscribers_list, name='subscribers-list'),
    path('feed/events/', FeedEventsView.as_view(), name='feed-events'),
    path('feed/events/count/', FeedEventsCountView.as_view(), name='feed-events-count'),
    path('feed/events/mark-read/', MarkFeedEventReadView.as_view(), name='feed-events-mark-read'),
    path('feed/settings/', feed_settings, name='feed-settings'),
    path('system-avatars/', system_avatars_list, name='system-avatars'),
    path('dialogs/', DialogListView.as_view(), name='dialogs-list'),
    path('dialogs/<int:dialog_id>/messages/', MessageListView.as_view(), name='dialog-messages'),
    path('dialogs/unread_count/', UnreadMessagesCountView.as_view(), name='dialogs-unread-count'),
    path('dialogs/<int:dialog_id>/mark_read/', MarkMessagesReadView.as_view(), name='dialogs-mark-read'),
    path('messages/<int:message_id>/', MessageDetailView.as_view(), name='message-detail'),
    path('user-header-images/', UserHeaderImageListCreateView.as_view(), name='user-header-images'),
    path('user-header-images/<int:pk>/', UserHeaderImageDeleteView.as_view(), name='user-header-image-delete'),
    path('dialogs/<int:dialog_id>/leave/', DialogLeaveView.as_view(), name='dialog-leave'),
    path('user-avatars/', UserAvatarListCreateView.as_view()),
    path('user-avatars/<int:pk>/', UserAvatarDeleteView.as_view()),
    path('bio/upload-image/', upload_bio_image, name='upload-bio-image'),
    path('bio/delete-image/', delete_bio_image, name='delete-bio-image'),
    path('bio/get-image-links/', get_bio_image_links, name='get-bio-image-links'),
    path('bio/cleanup-images/', cleanup_bio_images, name='cleanup-bio-images'),
    path('search-users/', UserSearchView.as_view(), name='search-users'),
    path('select-avatar/', select_avatar, name='select-avatar'),
    path('notifications/', UserNotificationsView.as_view(), name='user-notifications'),
    path('api/users/stats/', RatingUserStatsView.as_view(), name='user-rating-stats'),
    path('api/users/<int:user_id>/stats/', RatingUserStatsView.as_view(), name='user-rating-stats-by-id'),
    path('api/users/metric-history/', UserMetricHistoryView.as_view(), name='user-metric-history'),
    path('api/users/<int:user_id>/metric-history/', UserMetricHistoryView.as_view(), name='user-metric-history-by-id'),
    path('api/leaderboard/', LeaderboardView.as_view(), name='leaderboard'),
    path('api/users/recalculate-rating/', RecalculateRatingView.as_view(), name='recalculate-rating'),
    path('api/rating-rules/', RatingRulesView.as_view(), name='rating-rules'),
    path('api/users/rating-position/', UserRatingPositionView.as_view(), name='user-rating-position'),
    path('api/users/<int:user_id>/rating-position/', UserRatingPositionView.as_view(), name='user-rating-position-by-id'),
    path('api/rating-stats/', RatingStatsView.as_view(), name='rating-stats'),
    # Библиотека пользователя - прямые URL без роутера
    path('library/', UserLibraryViewSet.as_view({'get': 'list', 'post': 'create'}), name='library-list'),
    path('library/<int:pk>/', UserLibraryViewSet.as_view({'get': 'retrieve', 'put': 'update', 'patch': 'partial_update', 'delete': 'destroy'}), name='library-detail'),
    path('library/<int:pk>/update_status/', UserLibraryViewSet.as_view({'patch': 'update_status'}), name='library-update-status'),
    path('library/<int:pk>/update_progress/', UserLibraryViewSet.as_view({'patch': 'update_progress'}), name='library-update-progress'),
    path('library/<int:pk>/add_note/', UserLibraryViewSet.as_view({'patch': 'add_note'}), name='library-add-note'),
    path('library/<int:pk>/rate_book/', UserLibraryViewSet.as_view({'patch': 'rate_book'}), name='library-rate-book'),
    path('library/stats/', UserLibraryViewSet.as_view({'get': 'stats'}), name='library-stats'),
    path('library/update-activity/', UserLibraryViewSet.as_view({'post': 'update_activity'}), name='library-update-activity'),
    path('quick-add-to-library/', quick_add_to_library, name='quick-add-to-library'),
    path('check-library-status/<int:book_id>/', check_library_status, name='check-library-status'),
    # Эти паттерны должны быть в конце, так как они перехватывают всё
    path('<str:username>/', PublicUserView.as_view(), name='public-user-by-username'),
    path('<str:username>/books-blocks-order/', UserBooksBlocksOrderView.as_view(), name='user-books-blocks-order'),
    path('<str:username>/stat/', UserRatingStatsView.as_view(), name='user-rating-stats'),
    # OAuth endpoints
    path('auth/', include('dj_rest_auth.urls')),
    path('auth/registration/', include('dj_rest_auth.registration.urls')),
    path('auth/vk/', VKLoginView.as_view(), name='vk_login'),
    path('auth/yandex/', YandexLoginView.as_view(), name='yandex_login'),
    path('auth/google/', GoogleLoginView.as_view(), name='google_login'),
    path('auth/ok/', OKLoginView.as_view(), name='ok_login'),
]