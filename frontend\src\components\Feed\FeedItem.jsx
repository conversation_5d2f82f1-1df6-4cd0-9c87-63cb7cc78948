import React, { useEffect, useRef, useCallback, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { ru } from 'date-fns/locale';
import { useOnScreen } from '../../hooks/useOnScreen';
import { getCachedUserAvatar } from '../../utils/avatarCache';
import { useUserSettings } from '../../context/UserSettingsContext';
import { formatDateWithTimezone } from '../../utils/formatDate';

const FeedItem = memo(({ event, onMarkAsRead, backendUrl, onFriendRequest }) => {
    const [ref, isOnScreen] = useOnScreen(
        { threshold: 0.3, rootMargin: '50px' }, 
        `${event.id}`
    );
    const navigate = useNavigate();
    const { timezone } = useUserSettings();
    const markAsReadTimeoutRef = useRef(null);
    const hasMarkedAsReadRef = useRef(false);

    useEffect(() => {
        // console.log(`FeedItem ${event.id}: isOnScreen=${isOnScreen}, is_read=${event.is_read}`); // Removed for performance
        
        if (isOnScreen && !event.is_read) {
            // console.log(`Setting timeout to mark event ${event.id} as read`); // Removed for performance
            
            // Добавляем небольшую задержку для более естественного поведения
            markAsReadTimeoutRef.current = setTimeout(() => {
                if (!event.is_read) { // Дополнительная проверка перед вызовом
                    // console.log(`Executing markAsRead for event ${event.id}`); // Removed for performance
                    onMarkAsRead(event.id);
                }
            }, 1000); // Увеличиваем задержку до 1 секунды
            
            return () => {
                if (markAsReadTimeoutRef.current) {
                    clearTimeout(markAsReadTimeoutRef.current);
                    markAsReadTimeoutRef.current = null;
                }
            };
        }
    }, [isOnScreen, event.id, event.is_read, onMarkAsRead]);

    // Очищаем таймаут при размонтировании
    useEffect(() => {
        return () => {
            if (markAsReadTimeoutRef.current) {
                clearTimeout(markAsReadTimeoutRef.current);
                markAsReadTimeoutRef.current = null;
            }
        };
    }, []);

    // Reset hasMarkedAsRead when event changes or is_read changes
    useEffect(() => {
        hasMarkedAsReadRef.current = false;
    }, [event.id, event.is_read]);

    const getEventIcon = useCallback((type) => {
        switch (type) {
            case 'friend_request':
                return '🤝';
            case 'friend_accepted':
            case 'friend_added':
                return '💕';
            case 'subscribed':
                return '⭐';
            case 'unsubscribed':
                return '🚫';
            case 'removed_from_friends':
                return '💔';
            case 'new_post':
                return '📝';
            default:
                return '🔔';
        }
    }, []);

    const getGenderedMessage = useCallback((event) => {
        const gender = event.actor?.gender;
        switch (event.type) {
            case 'friend_request':
                if (gender === 'M') return 'отправил вам запрос в друзья';
                if (gender === 'F') return 'отправила вам запрос в друзья';
                return 'отправил(а) вам запрос в друзья';
            case 'friend_accepted':
            case 'friend_added':
                return 'теперь ваш друг!';
            default:
                return event.content?.message || 'Новое уведомление';
        }
    }, []);

    const handleNavigateToProfile = useCallback(() => {
        navigate(`/lpu/${event.actor?.username}`);
    }, [navigate, event.actor?.username]);

    const handleAcceptRequest = useCallback(() => {
        onFriendRequest(event.actor.username, 'accept', event.id);
    }, [onFriendRequest, event.actor.username, event.id]);

    const handleDeclineRequest = useCallback(() => {
        onFriendRequest(event.actor.username, 'decline', event.id);
    }, [onFriendRequest, event.actor.username, event.id]);

    const renderEventContent = useCallback(() => {
        const eventIcon = getEventIcon(event.type);
        switch (event.type) {
            case 'friend_request':
                return (
                    <div className="space-y-2">
                        <div className="flex items-center gap-2">
                            <span className="text-2xl">{eventIcon}</span>
                            <span>{getGenderedMessage(event)}</span>
                        </div>
                        {event.status === 'new' && (
                            <div className="flex gap-2 mt-2">
                                <button
                                    className="px-3 py-1 rounded font-medium transition-colors duration-200
                                        bg-green-100 hover:bg-green-200 text-green-800
                                        dark:bg-green-900 dark:hover:bg-green-800 dark:text-green-100
                                        border border-green-300 dark:border-green-700 shadow-sm"
                                    onClick={handleAcceptRequest}
                                >
                                    Принять
                                </button>
                                <button
                                    className="px-3 py-1 rounded font-medium transition-colors duration-200
                                        bg-red-100 hover:bg-red-200 text-red-800
                                        dark:bg-red-900 dark:hover:bg-red-800 dark:text-red-100
                                        border border-red-300 dark:border-red-700 shadow-sm"
                                    onClick={handleDeclineRequest}
                                >
                                    Отклонить
                                </button>
                            </div>
                        )}
                        {event.status === 'accepted' && (
                            <div className="text-green-600 font-semibold">Принято</div>
                        )}
                        {event.status === 'declined' && (
                            <div className="text-red-600 font-semibold">Отклонено</div>
                        )}
                        {event.status === 'cancelled' && (
                            <div className="text-gray-500 font-semibold">Отменено</div>
                        )}
                    </div>
                );
            case 'friend_accepted':
            case 'friend_added':
            case 'subscribed':
            case 'unsubscribed':
            case 'removed_from_friends':
                return (
                    <div className="flex items-center gap-2">
                        <span className="text-2xl">{eventIcon}</span>
                        <span>{event.content?.message}</span>
                    </div>
                );
            default:
                return (
                    <div className="flex items-center gap-2">
                        <span className="text-2xl">{eventIcon}</span>
                        <span>{event.content?.message || 'Новое уведомление'}</span>
                    </div>
                );
        }
    }, [event, getEventIcon, getGenderedMessage, handleAcceptRequest, handleDeclineRequest, handleNavigateToProfile]);

    return (
        <div
            ref={ref}
            className={`p-4 transition-colors duration-200 ${
                event.is_read 
                    ? 'bg-white dark:bg-gray-800' 
                    : 'bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-500 dark:border-blue-400'
            }`}
        >
            <div className="flex items-start space-x-4">
                <img
                    src={getCachedUserAvatar(event.actor, 'mini', backendUrl, 0)}
                    alt={event.actor?.display_name === 'Аккаунт удален' ? 'Аккаунт удален' : (event.actor?.display_name || 'Пользователь')}
                    className={`w-12 h-12 rounded-full border-2 border-white dark:border-gray-700 shadow ${
                        event.actor?.display_name === 'Аккаунт удален' ? 'cursor-default' : 'cursor-pointer'
                    }`}
                    onClick={event.actor?.display_name === 'Аккаунт удален' ? undefined : handleNavigateToProfile}
                    onError={e => { e.target.onerror = null; e.target.src = '/lpo/ava.webp'; }}
                />
                <div className="flex-1">
                    <div className="flex items-center space-x-2">
                        {event.actor?.display_name === 'Аккаунт удален' ? (
                            <span className="font-semibold text-gray-500 dark:text-gray-400">
                                Аккаунт удален
                            </span>
                        ) : (
                            <span
                                className="font-semibold cursor-pointer hover:text-blue-500 dark:hover:text-blue-400"
                            onClick={handleNavigateToProfile}
                        >
                            {event.actor?.display_name || 'Пользователь'}
                        </span>
                        )}
                        <span className="text-gray-500 dark:text-gray-400 text-sm">
                            {formatDateWithTimezone(event.created_at, timezone, 'DD.MM.YYYY HH:mm')}
                        </span>
                    </div>
                    {renderEventContent()}
                </div>
            </div>
        </div>
    );
});

FeedItem.displayName = 'FeedItem';

export default FeedItem; 