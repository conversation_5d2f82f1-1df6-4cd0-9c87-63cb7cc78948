import React, { useState, useRef, forwardRef, useImperativeHandle, useEffect, useCallback } from 'react';
import { Button, Input, message, Popconfirm, Tooltip, Dropdown, Menu, Modal, Checkbox, Popover, Select, Slider, Radio, InputNumber } from 'antd';
import { EditorContent, useEditor, NodeViewWrapper, NodeViewContent, ReactNodeViewRenderer } from '@tiptap/react';
import { <PERSON><PERSON><PERSON>, PluginKey } from '@tiptap/pm/state';
import { Decoration, DecorationSet } from '@tiptap/pm/view';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Underline from '@tiptap/extension-underline';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import './chapter-editor.css';
import { useTheme } from '../theme/ThemeContext';
import { PictureOutlined, SettingOutlined } from '@ant-design/icons';
import ResizableImageNodeView from './ResizableImageNodeView.jsx';
import { Mark, mergeAttributes, Node, Extension } from '@tiptap/core';
import { 
  BoldOutlined, 
  ItalicOutlined, 
  UnderlineOutlined, 
  OrderedListOutlined, 
  UnorderedListOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons';

// ✅ ПРАВИЛЬНОЕ РЕШЕНИЕ: ParagraphSymbols Extension с decorations
const ParagraphSymbols = Extension.create({
  name: 'paragraphSymbols',

  addOptions() {
    return {
      visible: false,
    };
  },

  addStorage() {
    return {
      visible: this.options.visible,
    };
  },

  addCommands() {
    return {
      showParagraphSymbols: () => ({ editor }) => {
        this.storage.visible = true;
        editor.view.dispatch(editor.state.tr);
        return true;
      },
      hideParagraphSymbols: () => ({ editor }) => {
        this.storage.visible = false;
        editor.view.dispatch(editor.state.tr);
        return true;
      },
      toggleParagraphSymbols: () => ({ editor }) => {
        this.storage.visible = !this.storage.visible;
        editor.view.dispatch(editor.state.tr);
        return true;
      },
    };
  },

  addProseMirrorPlugins() {
    const extension = this;

    return [
      new Plugin({
        key: new PluginKey('paragraphSymbols'),
        
        state: {
          init() {
            return DecorationSet.empty;
          },
          
          apply(tr, decorationSet) {
            // Только если символы включены
            if (!extension.storage.visible) {
              return DecorationSet.empty;
            }

            const decorations = [];
            const { doc } = tr;

            // Проходим по всем paragraph нодам
            doc.descendants((node, pos) => {
              if (node.type.name === 'paragraph') {
                // Определяем позицию для символа
                let symbolPos;
                
                if (node.content.size === 0 || (node.content.size === 1 && node.firstChild?.type.name === 'hardBreak')) {
                  // Пустой параграф или содержит только <br> - символ в начале
                  symbolPos = pos + 1;
                } else {
                  // Непустой параграф - символ в самом конце, перед закрывающим </p>
                  symbolPos = pos + node.nodeSize - 1;
                }
                
                // Создаём widget decoration с символом ¶
                const decoration = Decoration.widget(symbolPos, () => {
                  const span = document.createElement('span');
                  span.className = 'paragraph-symbol';
                  span.textContent = '¶';
                  span.style.cssText = `
                    color: #b0b0b0;
                    opacity: 0.6;
                    font-family: serif;
                    font-size: 0.9em;
                    margin-left: 2px;
                    pointer-events: none;
                    user-select: none;
                    display: inline;
                  `;
                  return span;
                }, {
                  side: 1, // Размещаем справа от позиции
                  ignoreSelection: true, // Игнорируем при выделении
                });

                decorations.push(decoration);
              }
            });

            return DecorationSet.create(doc, decorations);
          },
        },
        
        props: {
          decorations(state) {
            return this.getState(state);
          },
        },
      }),
    ];
  },
});

// Создаём отдельный mark для backgroundColor по best practice tiptap
const BackgroundColor = Mark.create({
  name: 'backgroundColor',
  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },
  addAttributes() {
    return {
      backgroundColor: {
        default: null,
        parseHTML: element => element.style.backgroundColor || null,
        renderHTML: attributes => {
          if (!attributes.backgroundColor) return {};
          return { style: `background-color: ${attributes.backgroundColor}` };
        },
      },
    }
  },
  parseHTML() {
    return [
      {
        style: 'background-color',
      },
    ]
  },
  renderHTML({ HTMLAttributes }) {
    return ['span', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]
  },
  addCommands() {
    return {
      setBackgroundColor: color => ({ chain }) => chain().setMark('backgroundColor', { backgroundColor: color }).run(),
      unsetBackgroundColor: () => ({ chain }) => chain().setMark('backgroundColor', { backgroundColor: null }).run(),
    }
  },
});

const FontSize = Mark.create({
  name: 'fontSize',
  addOptions() {
    return {
      types: ['textStyle'],
    };
  },
  addAttributes() {
    return {
      fontSize: {
        default: null,
        parseHTML: element => element.style.fontSize || null,
        renderHTML: attributes => {
          if (!attributes.fontSize) return {};
          return { style: `font-size: ${attributes.fontSize}` };
        },
      },
    };
  },
  parseHTML() {
    return [
      {
        style: 'font-size',
      },
    ];
  },
  renderHTML({ HTMLAttributes }) {
    return ['span', mergeAttributes(HTMLAttributes), 0];
  },
  addCommands() {
    return {
      setFontSize: size => ({ chain }) => chain().setMark('fontSize', { fontSize: size }).run(),
      unsetFontSize: () => ({ chain }) => chain().setMark('fontSize', { fontSize: null }).run(),
    };
  },
});

// Расширяем TextStyle для поддержки color и backgroundColor
const CustomTextStyle = TextStyle.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      color: {
        default: null,
        parseHTML: element => element.style.color || null,
        renderHTML: attributes => {
          if (!attributes.color) return {};
          return { style: `color: ${attributes.color}` };
        },
      },
      backgroundColor: {
        default: null,
        parseHTML: element => element.style.backgroundColor || null,
        renderHTML: attributes => {
          if (!attributes.backgroundColor) return {};
          return { style: `background-color: ${attributes.backgroundColor}` };
        },
      },
      fontSize: {
        default: null,
        parseHTML: element => element.style.fontSize || null,
        renderHTML: attributes => {
          if (!attributes.fontSize) return {};
          return { style: `font-size: ${attributes.fontSize}` };
        },
      },
    }
  },
});

const ChapterEditor = forwardRef(({
  initialTitle = '',
  initialContent = '',
  onSave,
  onCancel,
  loading = false,
  bookId,
  chapterOrder = 1,
  chapterId = undefined,
  username,
  imgIndex = 1,
  showToolbar = true,
  autoIndent = false,
  disableSave = false,
  disableSaveTooltip = '',
  onContentChange,
  isFullWidth = false,
  onToggleFullWidth,
}, ref) => {
  const [title, setTitle] = useState(initialTitle);
  const imgCounter = useRef(imgIndex);
  const [isFocused, setIsFocused] = useState(false);
  const { theme } = useTheme();
  const [presignedUrlMap, setPresignedUrlMap] = useState({});
  const colorInputRef = useRef();
  const [showLinkWarn, setShowLinkWarn] = useState(false);
  const linkBtnRef = useRef();
  const [savedSelection, setSavedSelection] = useState(null);
  const fileInputRef = useRef();
  const [showParagraphSymbols, setShowParagraphSymbols] = useState(false);
  const [linkModal, setLinkModal] = useState({
    open: false,
    href: '',
    blank: false,
    from: null,
    to: null,
  });

  // Состояния для настроек размера значков
  const [iconSizeMode, setIconSizeMode] = useState(() => {
    const saved = localStorage.getItem('story-editor-icon-size-mode');
    return saved || 'medium';
  });
  const [customIconSize, setCustomIconSize] = useState(() => {
    const saved = localStorage.getItem('story-editor-custom-icon-size');
    return saved ? parseInt(saved) : 14;
  });
  const [showSizeSettings, setShowSizeSettings] = useState(false);
  const toolbarRef = useRef();

  const ThemedResizableImageNodeView = (props) => {
    // Передаем функцию загрузки для глав
    const chapterUploadFunction = async (file) => {
      const formData = new FormData();
      formData.append('image', file);
      formData.append('chapter_order', chapterOrder);
      formData.append('img_index', imgCounter.current);
      formData.append('username', username);
      if (chapterId) {
        formData.append('chapter_id', chapterId);
      }
      
      const csrfToken = getCookie('csrftoken');
      
      const res = await fetch(`/api/books/${bookId}/chapters/upload_image/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        body: formData,
        credentials: 'include',
      });
      
      if (!res.ok) throw new Error('Ошибка загрузки');
      
      const data = await res.json();
      imgCounter.current += 1;
      
      return data;
    };
    
    return <ResizableImageNodeView {...props} uploadImageFunction={chapterUploadFunction} />;
  };

  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.extend({
        addAttributes() {
          return {
            src: { 
              default: null,
              parseHTML: element => element.getAttribute('src'),
              renderHTML: attributes => {
                if (!attributes.src) return {};
                return { src: attributes.src };
              },
            },
            alt: { 
              default: null,
              parseHTML: element => element.getAttribute('alt'),
              renderHTML: attributes => {
                if (!attributes.alt) return {};
                return { alt: attributes.alt };
              },
            },
            title: { 
              default: null,
              parseHTML: element => element.getAttribute('title'),
              renderHTML: attributes => {
                if (!attributes.title) return {};
                return { title: attributes.title };
              },
            },
            width: { 
              default: '100',
              parseHTML: element => {
                // Пробуем получить из data-width, потом из style.width, потом дефолт
                const dataWidth = element.getAttribute('data-width');
                if (dataWidth) return dataWidth;
                
                const styleWidth = element.style.width;
                if (styleWidth) {
                  // Конвертируем из пикселей или процентов в проценты
                  return styleWidth.replace('%', '').replace('px', '');
                }
                
                return '100';
              },
              renderHTML: attributes => {
                if (!attributes.width) return {};
                return { 
                  'data-width': attributes.width,
                  style: `width: ${attributes.width}%`
                };
              },
            },
            align: { 
              default: 'center',
              parseHTML: element => element.getAttribute('data-align') || 'center',
              renderHTML: attributes => {
                if (!attributes.align) return {};
                return { 'data-align': attributes.align };
              },
            },
            textWrap: { 
              default: 'break',
              parseHTML: element => element.getAttribute('data-text-wrap') || 'break',
              renderHTML: attributes => {
                if (!attributes.textWrap) return {};
                return { 'data-text-wrap': attributes.textWrap };
              },
            },
            caption: { 
              default: '',
              parseHTML: element => element.getAttribute('data-caption') || '',
              renderHTML: attributes => {
                if (!attributes.caption) return {};
                return { 'data-caption': attributes.caption };
              },
            },
            class: {
              default: 'align-center',
              parseHTML: element => element.getAttribute('class') || 'align-center',
              renderHTML: attributes => {
                if (!attributes.class) return {};
                return { class: attributes.class };
              },
            },
          };
        },
        // Убираем inline: true, оставляем как блочный элемент
        group: 'block',
        atom: true, // Изображение атомарно - не содержит внутреннего контента
        selectNodeOnClick: true,
        addNodeView() {
          return ReactNodeViewRenderer(ThemedResizableImageNodeView);
        },
        addCommands() {
          return {
            setImage: (options) => ({ tr, dispatch, state }) => {
              const { schema } = state;
              const imageNode = schema.nodes.image.create(options);
              const paragraphNode = schema.nodes.paragraph.create();
              
              if (dispatch) {
                const transaction = tr.replaceSelectionWith(imageNode).insert(tr.selection.to, paragraphNode);
                dispatch(transaction);
              }
              
              return true;
            },
          };
        },
      }),
      Underline,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-500 hover:text-blue-700 underline',
        },
      }),
      TextAlign.configure({ types: ['heading', 'paragraph'] }),
      CustomTextStyle,
      Color,
      FontSize,
      BackgroundColor,
      ParagraphSymbols,
    ],
    content: initialContent,
    onUpdate: ({ editor }) => {
      
    },
    editorProps: {
      handleDrop(view, event, _slice, moved) {
        if (moved) return false;
        const files = Array.from(event.dataTransfer.files || []);
        if (files.length > 0 && files[0].type.startsWith('image/')) {
          event.preventDefault();
          uploadImage(files[0]);
          return true;
        }
        return false;
      },
      handleClick(view, pos, event) {
        const a = event.target.closest('a');
        if (a && view.editable) {
          event.preventDefault();
          const { href, target } = a;
          const from = view.posAtDOM(a, 0);
          openLinkModal({
            href,
            blank: target === '_blank',
            from,
            to: from + a.textContent.length,
          });
          return true;
        }
        return false;
      },
      handleDoubleClick(view, pos, event) {
        // Обработчик двойного клика для создания параграфов
        const { state } = view;
        const { doc, schema, selection } = state;
        const clickPos = doc.resolve(pos);
        
        // Вспомогательная функция для проверки пустого места
        const isEmptyArea = (resolvedPos) => {
          const node = resolvedPos.nodeAfter;
          const parent = resolvedPos.parent;
          
          return (
            // Пустой параграф
            (parent.type.name === 'paragraph' && parent.content.size === 0) ||
            // Конец документа
            (!node && parent.type.name === 'doc') ||
            // Между блоками
            (!node && resolvedPos.parentOffset === parent.content.size)
          );
        };
        
        // Определяем, где мы кликнули
        const clickedInEmpty = isEmptyArea(clickPos);
        
        if (clickedInEmpty) {
          // Простая логика: создаем один новый параграф в месте клика
          const paragraph = schema.nodes.paragraph.create();
          
          // Определяем позицию для вставки
          let insertPos;
          if (clickPos.parent.type.name === 'paragraph' && clickPos.parent.content.size === 0) {
            // Если кликнули в пустой параграф, вставляем после него
            insertPos = clickPos.after(clickPos.depth);
          } else {
            // Иначе вставляем в текущую позицию
            insertPos = pos;
          }
          
          // Создаем и применяем транзакцию
          const transaction = state.tr.insert(insertPos, paragraph);
          view.dispatch(transaction);
          
          // Устанавливаем курсор в новый параграф
          setTimeout(() => {
            const newState = view.state;
            const newPos = insertPos + 1;
            if (newPos <= newState.doc.content.size) {
              const newSelection = newState.selection.constructor.create(newState.doc, newPos);
              view.dispatch(newState.tr.setSelection(newSelection));
              view.focus();
            }
          }, 10);
          
          // Обновляем классы параграфов после создания нового параграфа
          setTimeout(() => {
            if (window.updateParagraphClasses) {
              window.updateParagraphClasses();
            }
          }, 50);
          
          return true;
        }
        
        return false;
      },
    },
  });

  useEffect(() => {
    if (editor) {
      window.tiptapEditor = editor;
      //console.log('window.tiptapEditor set:', editor);
      
                      // УПРОЩЕННАЯ РЕАЛИЗАЦИЯ: Только добавляем классы для пустых параграфов
        // ❌ CSS ПСЕВДОЭЛЕМЕНТЫ НЕ РАБОТАЮТ В TIPTAP CONTENTEDITABLE!
        // ✅ ФИНАЛЬНОЕ РЕШЕНИЕ: Overlay символы поверх редактора
        // ✅ ОЧИСТКА: Удаляем старые символы из HTML (если остались от предыдущих версий)
        const cleanupCorruptedSymbols = () => {
          const htmlContent = editor.getHTML();
          const symbolCount = (htmlContent.match(/¶/g) || []).length;
          if (symbolCount > 0) {
    
            const cleanedContent = htmlContent.replace(/¶+/g, '');
            editor.commands.setContent(cleanedContent);
          }
        };
      
      // Создаем функцию для очистки повреждённых символов
      window.cleanupCorruptedSymbols = cleanupCorruptedSymbols;
      
      // Создаем глобальную функцию для обратной совместимости
      window.uploadImageFunction = async (file) => {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('chapter_order', chapterOrder);
        formData.append('img_index', imgCounter.current);
        formData.append('username', username);
        if (chapterId) {
          formData.append('chapter_id', chapterId);
        }
        
        const csrfToken = getCookie('csrftoken');
        
        const res = await fetch(`/api/books/${bookId}/chapters/upload_image/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': csrfToken,
          },
          body: formData,
          credentials: 'include',
        });
        
        if (!res.ok) throw new Error('Ошибка загрузки');
        
        const data = await res.json();
        imgCounter.current += 1;
        
        return data;
      };
      
      // Отслеживаем изменения контента и передаем их родительскому компоненту
      if (onContentChange) {
        const updateContent = () => {
          const content = editor.getHTML();
          onContentChange(content);
          
          // Очищаем повреждённые символы после изменения контента
          setTimeout(cleanupCorruptedSymbols, 100);
        };
        
        editor.on('update', updateContent);
        
        // Добавляем debouncing для cleanupCorruptedSymbols
        let updateTimeout = null;
        const debouncedCleanupSymbols = () => {
          if (updateTimeout) clearTimeout(updateTimeout);
          updateTimeout = setTimeout(cleanupCorruptedSymbols, 300);
        };
        
        // Добавляем обновление при изменении селекции (движение курсора)
        // НО НЕ обновляем, если выбрано изображение (чтобы не сбрасывать фокус)
        editor.on('selectionUpdate', () => {
          // Проверяем, выбрано ли изображение
          const { state } = editor;
          const { selection } = state;
          const node = selection.$anchor.nodeAfter || selection.$anchor.nodeBefore;
          
          // Если выбрано изображение - НЕ обновляем классы (чтобы не потерять фокус)
          if (node && node.type && node.type.name === 'image') {
    
            return;
          }
          
          // Очищаем символы только если не работаем с изображением
          debouncedCleanupSymbols();
        });
        
        // Также очищаем символы при изменении размера окна или прокрутке
        const handleResize = debouncedCleanupSymbols;
        window.addEventListener('resize', handleResize);
        
        // Автоматически устанавливаем фокус на редактор при его инициализации
        setTimeout(() => {
          editor.commands.focus();
          cleanupCorruptedSymbols(); // Очищаем старые символы при инициализации
        }, 100);
        // ✅ ПРАВИЛЬНОЕ РЕШЕНИЕ: ParagraphSymbols extension с decorations

        
        return () => {
          editor.off('update', updateContent);
          editor.off('selectionUpdate');
          window.removeEventListener('resize', handleResize);
        };
      }
    }
  }, [editor, onContentChange, bookId, chapterOrder, username, chapterId]);

  // Подмена src на актуальные presigned URL при открытии редактора
  useEffect(() => {
    if (!editor || !initialContent) return;

    

    // 1. Привести initialContent к относительным путям (на всякий случай)
    // Обновленное регулярное выражение для поддержки как старого, так и нового формата путей + WebP
    const htmlWithRelative = initialContent.replace(/https?:\/\/[^\"]+(\/media\/private\/book_pics\/[\w\/-]+\/chapics\/(?:[\w\/-]*\/)?[\w\d_]+\.(jpg|jpeg|png|gif|webp))/g, '$1');

    // 2. Извлечь относительные пути к изображениям
    const paths = extractImagePathsFromHtml(htmlWithRelative);
    
    
    if (paths.length === 0) {
      editor.commands.setContent(htmlWithRelative);
      return;
    }

    // 3. Запросить новые presigned URL и подменить src
    fetchPresignedUrls(paths).then(urlMap => {
      
      setPresignedUrlMap(urlMap);
      const newHtml = replaceImageSrcInHtml(htmlWithRelative, urlMap);
      editor.commands.setContent(newHtml);
    }).catch((error) => {
      console.error('Ошибка получения presigned URL:', error);
      // fallback: просто отдать исходный контент
      editor.commands.setContent(htmlWithRelative);
    });
  }, [editor, initialContent, chapterId]); // Добавляем chapterId в зависимости

  // Сброс presignedUrlMap при смене главы
  useEffect(() => {
    
    setPresignedUrlMap({});
  }, [chapterId]);

  const uploadImage = async (file) => {
    
    if (!bookId || !username) {
      console.error('Отсутствуют необходимые данные:', { bookId, username });
      message.error('Ошибка: не хватает данных для загрузки');
      return;
    }
    
    if (!file) {
      console.error('Файл не передан в функцию uploadImage');
      message.error('Ошибка: файл не выбран');
      return;
    }
    
    const formData = new FormData();
    formData.append('image', file);
    formData.append('chapter_order', chapterOrder);
    formData.append('img_index', imgCounter.current);
    formData.append('username', username);
    if (chapterId) {
      formData.append('chapter_id', chapterId);
    }
    
    
    message.loading({ content: 'Загрузка изображения...', key: 'imageUpload' });
    
    const csrfToken = getCookie('csrftoken');
    
    
    try {
      
      const res = await fetch(`/api/books/${bookId}/chapters/upload_image/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        body: formData,
        credentials: 'include',
      });
      
      
      if (!res.ok) throw new Error('Ошибка загрузки');
      
      const data = await res.json();
      
      
      if (data.url) {
        const width = data.width ? parseInt(data.width, 10) : 400;
        
        if (editor) {
          // ✅ ИСПРАВЛЕНИЕ: Принудительно восстанавливаем фокус и проверяем состояние
          
          
          // Сначала фокусируемся на редакторе
          editor.commands.focus();
          
          // Ждём небольшую задержку для восстановления фокуса
          setTimeout(() => {

            
            // Вставляем изображение
            const success = editor.chain().focus().setImage({ 
              src: data.url, 
              width,
              align: 'center',
              textWrap: 'break',
              caption: '',
              class: 'align-center'
            }).run();
            

            
            if (success) {
              // Увеличиваем счетчик изображений только после успешной вставки
              imgCounter.current += 1;
              
              // ✅ Добавляем загруженное изображение в presignedUrlMap для корректного сохранения
              if (data.path) {
                setPresignedUrlMap(prevMap => ({
                  ...prevMap,
                  [data.path]: data.url
                }));
    
              }
              
              // ✅ Символы параграфа теперь обновляются автоматически через decorations!
              
              message.success({ content: 'Изображение успешно загружено', key: 'imageUpload' });
            } else {
              console.error('Не удалось вставить изображение - команда не выполнилась');
              message.error({ content: 'Ошибка вставки изображения в редактор', key: 'imageUpload' });
            }
          }, 50);
        } else {
          console.error('Редактор не инициализирован!');
          message.warning({ content: 'Редактор еще не готов. Попробуйте еще раз через секунду.', key: 'imageUpload' });
          console.warn('Редактор еще не смонтирован, вставка невозможна!');
        }
      } else {
        console.error('URL изображения отсутствует в ответе:', data);
        message.error({ content: 'Ошибка: не получен url изображения', key: 'imageUpload' });
      }
    } catch (e) {
      console.error('Error uploading image:', e);
      message.error({ content: 'Ошибка загрузки изображения', key: 'imageUpload' });
    }
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    // Проверяем размер файла (максимум 10MB)
    const maxFileSize = 10 * 1024 * 1024; // 10MB в байтах
    if (file.size > maxFileSize) {
      message.error('Размер файла превышает 10MB');
      e.target.value = '';
      return;
    }
    
    // Проверяем формат файла (поддерживаемые растровые форматы)
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/bmp', 'image/tiff', 'image/gif'];
    if (!allowedTypes.includes(file.type.toLowerCase())) {
      message.error('Неподдерживаемый формат файла. Поддерживаются: JPEG, PNG, WebP, BMP, TIFF, GIF');
      e.target.value = '';
      return;
    }
    
    // Сразу загружаем файл, сервер сам обработает изображение
    uploadImage(file);
    
    // Очищаем input для возможности повторной загрузки того же файла
    e.target.value = '';
  };

  const handleSave = () => {
    if (!initialTitle.trim()) {
      message.error('Введите название главы');
      return;
    }
    if (!editor) return;
    
    const originalHtml = editor.getHTML();
    
    
    // Перед сохранением заменяем presigned URL обратно на относительные пути (расширенно)
    const htmlWithRelative = revertPresignedUrlsInHtml(originalHtml, presignedUrlMap);
    
    
    
    // Если редактор в полноэкранном режиме, выходим из него
    if (isFullWidth && onToggleFullWidth) {
      onToggleFullWidth();
    }
    
    onSave({
      title: initialTitle,
      content: htmlWithRelative,
    });
  };

  // Панель инструментов
  const toolbarBg = theme === 'dark' ? '#23272f' : '#f3f4f6';
  const iconColor = theme === 'dark' ? '#fff' : '#222';
  const iconActiveBg = '#2563eb';
  const iconActiveColor = '#fff';

  const selectBg = theme === 'dark' ? '#23272f' : '#fff';
  const selectColor = theme === 'dark' ? '#fff' : '#222';
  const selectBorder = theme === 'dark' ? '#374151' : '#d1d5db';
  const selectOptionBg = theme === 'dark' ? '#23272f' : '#fff';
  const selectOptionHover = theme === 'dark' ? '#374151' : '#e5e7eb';
  const selectOptionColor = theme === 'dark' ? '#fff' : '#222';

  // Функция для расчета размера значков
  const getIconSize = () => {
    if (iconSizeMode === 'custom') {
      return customIconSize;
    }

    if (iconSizeMode === 'auto' && toolbarRef.current) {
      const toolbarWidth = toolbarRef.current.offsetWidth;
      if (toolbarWidth < 600) return 12;
      if (toolbarWidth < 900) return 14;
      return 16;
    }

    const sizeMap = {
      small: 12,
      medium: 14,
      large: 16
    };

    return sizeMap[iconSizeMode] || 14;
  };

  // Сохранение настроек в localStorage
  useEffect(() => {
    localStorage.setItem('story-editor-icon-size-mode', iconSizeMode);
  }, [iconSizeMode]);

  useEffect(() => {
    localStorage.setItem('story-editor-custom-icon-size', customIconSize.toString());
  }, [customIconSize]);

  // Пересчет размера при изменении ширины окна для автоматического режима
  useEffect(() => {
    if (iconSizeMode === 'auto') {
      const handleResize = () => {
        // Принудительно обновляем компонент для пересчета размера
        setShowSizeSettings(prev => prev);
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [iconSizeMode]);

  const saveEditorState = () => {
    if (!editor) return;
    return editor.state;
  };
  
  const restoreEditorState = (state) => {
    if (!editor || !state) return;
    editor.view.updateState(state);
  };

  // Создаем специальный компонент для выпадающего меню, который не теряет выделение
  const FontSizeSelector = () => {
    const [isOpen, setIsOpen] = useState(false);
    const currentSize = editor.getAttributes('textStyle').fontSize || '16px';
    const sizeOptions = [
      { value: '16px', label: 'Обычный' },
      { value: '13px', label: 'Мелкий' },
      { value: '20px', label: 'Крупный' },
      { value: '28px', label: 'Заголовок' },
    ];
    
    const handleSizeChange = (value) => {
      // Получаем текущие атрибуты textStyle
      const { color, backgroundColor, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus().setMark('textStyle', {
        color,
        backgroundColor,
        fontSize: value,
      }).run();
      setIsOpen(false);
    };
    
    const toggleDropdown = (e) => {
      e.preventDefault(); // Важно! Предотвращает потерю фокуса и выделения
      setIsOpen(!isOpen);
    };
    
    const handleOptionMouseDown = (e, value) => {
      e.preventDefault(); // Предотвращает потерю выделения
      handleSizeChange(value);
    };
    
    return (
      <div className="relative inline-block" style={{ marginLeft: 4, marginRight: 4 }}>
        <button
          onMouseDown={toggleDropdown}
          className="flex items-center justify-between px-2 py-1 border rounded"
          style={{
            background: theme === 'dark' ? '#1f2937' : '#ffffff',
            color: theme === 'dark' ? '#ffffff' : '#000000',
            borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
            minWidth: '90px',
            height: '32px',
            fontSize: '14px',
          }}
        >
          <span>{sizeOptions.find(opt => opt.value === currentSize)?.label || 'Обычный'}</span>
          <span style={{ marginLeft: '4px' }}>▼</span>
        </button>
        
        {isOpen && (
          <div
            className="absolute left-0 mt-1 border rounded shadow-lg z-10"
            style={{
              background: theme === 'dark' ? '#1f2937' : '#ffffff',
              borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
              minWidth: '90px',
              zIndex: 1000,
            }}
          >
            {sizeOptions.map(option => (
              <div
                key={option.value}
                onMouseDown={(e) => handleOptionMouseDown(e, option.value)}
                className="px-3 py-1 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                style={{
                  background: currentSize === option.value 
                    ? (theme === 'dark' ? '#374151' : '#f3f4f6') 
                    : 'transparent',
                  color: theme === 'dark' ? '#ffffff' : '#000000',
                  fontSize: option.value,
                }}
              >
                {option.label}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Добавляю функцию для обработки выравнивания текста
  const handleTextAlign = (alignment) => {
    // Если текст уже имеет это выравнивание, сбрасываем на левое выравнивание (по умолчанию)
    if (editor.isActive({ textAlign: alignment })) {
      editor.chain().focus().setTextAlign('left').run();
    } else {
      editor.chain().focus().setTextAlign(alignment).run();
    }
  };

  // Функция для создания кастомного тултипа
  const renderTooltip = (title, shortcut) => (
    <div className="tooltip-content">
      <div>{title}</div>
      {shortcut && <div className="editor-shortcut">{shortcut}</div>}
    </div>
  );

  // Компонент настроек размера значков
  const ToolbarSizeSettings = () => {
    const currentIconSize = getIconSize();

    const sizeOptions = [
      { label: 'Маленький', value: 'small' },
      { label: 'Средний', value: 'medium' },
      { label: 'Большой', value: 'large' },
      { label: 'Авто', value: 'auto' },
      { label: 'Пользовательский', value: 'custom' }
    ];

    const handleSizeChange = (e) => {
      setIconSizeMode(e.target.value);
    };

    const handleCustomSizeChange = (value) => {
      if (value && value >= 10 && value <= 32) {
        setCustomIconSize(value);
      }
    };

    return (
      <Modal
        open={showSizeSettings}
        onCancel={() => setShowSizeSettings(false)}
        footer={null}
        width={400}
        className={theme === 'dark' ? 'dark-modal' : ''}
        styles={{
          content: {
            background: theme === 'dark' ? '#23272f' : '#ffffff',
            borderRadius: '12px',
            padding: '24px',
          },
          mask: {
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        <div className="mb-6">
          <h3 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Настройки размера значков панели
          </h3>
          <p className={`mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Текущий размер значков: <strong className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>{currentIconSize}px</strong>
          </p>
        </div>
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8, fontWeight: 500, color: theme === 'dark' ? '#fff' : '#222' }}>
            Выберите размер:
          </div>
          <Radio.Group
            value={iconSizeMode}
            onChange={handleSizeChange}
            style={{ width: '100%' }}
          >
            {sizeOptions.map(option => (
              <Radio
                key={option.value}
                value={option.value}
                style={{
                  display: 'block',
                  marginBottom: 8,
                  color: theme === 'dark' ? '#fff' : '#222'
                }}
              >
                {option.label}
              </Radio>
            ))}
          </Radio.Group>
        </div>

        {iconSizeMode === 'custom' && (
          <div style={{ marginBottom: 16 }}>
            <div style={{ marginBottom: 8, fontWeight: 500, color: theme === 'dark' ? '#fff' : '#222' }}>
              Пользовательский размер (10-32px):
            </div>
            <InputNumber
              min={10}
              max={32}
              value={customIconSize}
              onChange={handleCustomSizeChange}
              style={{
                width: '100%',
                ...(theme === 'dark' ? {
                  borderColor: '#4b5563',
                  color: '#fff',
                  background: '#23272f'
                } : {})
              }}
              addonAfter="px"
            />
          </div>
        )}

        <div className="mb-6">
          <div className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            <strong>Размеры значков:</strong>
          </div>
          <ul className={`list-disc list-inside text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} space-y-1 mt-2`}>
            <li>Маленький: 12px</li>
            <li>Средний: 14px</li>
            <li>Большой: 16px</li>
            <li>Авто: размер зависит от ширины панели</li>
          </ul>
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            onClick={() => setShowSizeSettings(false)}
            className={theme === 'dark' ? 'bg-[#23272f] text-white border-[#4b5563] hover:bg-[#374151]' : 'bg-white text-gray-900 border-gray-300 hover:bg-gray-100'}
            style={theme === 'dark' ? { borderColor: '#4b5563', color: '#fff', background: '#23272f' } : {}}
          >
            Закрыть
          </Button>
        </div>
      </Modal>
    );
  };

  const Toolbar = () => editor && (
    <div
      ref={toolbarRef}
      className="flex flex-wrap gap-1 mb-2 rounded px-2 py-1 items-center"
      style={{
        background: toolbarBg,
        border: '1.5px solid #e5e7eb',
        minHeight: 40,
        justifyContent: 'flex-start',
      }}
    >
      <div className="flex items-center gap-1">
        <Tooltip 
          title={renderTooltip("Жирный", "Ctrl+B")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().toggleBold().run();
            }}
            className={`px-2 py-1 rounded ${editor.isActive('bold') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('bold') ? iconActiveBg : 'transparent',
              color: editor.isActive('bold') ? iconActiveColor : iconColor,
              fontSize: `${getIconSize()}px`,
            }}
          >
            <span style={{ fontWeight: 'bold' }}>Ж</span>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("Курсив", "Ctrl+I")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().toggleItalic().run();
            }}
            className={`px-2 py-1 rounded ${editor.isActive('italic') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('italic') ? iconActiveBg : 'transparent',
              color: editor.isActive('italic') ? iconActiveColor : iconColor,
              fontSize: `${getIconSize()}px`,
            }}
          >
            <span style={{ fontStyle: 'italic' }}>К</span>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("Подчёркнутый", "Ctrl+U")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().toggleUnderline().run();
            }}
            className={`px-2 py-1 rounded ${editor.isActive('underline') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('underline') ? iconActiveBg : 'transparent',
              color: editor.isActive('underline') ? iconActiveColor : iconColor,
              fontSize: `${getIconSize()}px`,
            }}
          >
            <span style={{ textDecoration: 'underline' }}>П</span>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("Зачёркнутый", null)} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().toggleStrike().run();
            }}
            className={`px-2 py-1 rounded ${editor.isActive('strike') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('strike') ? iconActiveBg : 'transparent',
              color: editor.isActive('strike') ? iconActiveColor : iconColor,
              fontSize: `${getIconSize()}px`,
            }}
          >
            <span style={{ textDecoration: 'line-through' }}>З</span>
          </button>
        </Tooltip>
      </div>

      <FontSizeSelector />

      <CustomColorDropdown editor={editor} theme={theme} getIconSize={getIconSize} />

      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

      {/* Кнопки отмены/повтора */}
      <div className="flex items-center gap-1">
        <Tooltip 
          title={renderTooltip("Отменить", "Ctrl+Z")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().undo().run();
            }}
            className="px-2 py-1 rounded"
            style={{
              background: 'transparent',
              color: iconColor,
              opacity: editor.can().undo() ? 1 : 0.5,
              cursor: editor.can().undo() ? 'pointer' : 'default',
            }}
            disabled={!editor.can().undo()}
          >
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
              <path d="M7 15L2 10L7 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2 10H11C13.2091 10 15 11.7909 15 14V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("Повторить", "Ctrl+Y")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().redo().run();
            }}
            className="px-2 py-1 rounded"
            style={{
              background: 'transparent',
              color: iconColor,
              opacity: editor.can().redo() ? 1 : 0.5,
              cursor: editor.can().redo() ? 'pointer' : 'default',
            }}
            disabled={!editor.can().redo()}
          >
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
              <path d="M11 5L16 10L11 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M16 10H7C4.79086 10 3 11.7909 3 14V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </button>
        </Tooltip>
      </div>

      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

      {/* Кнопки выравнивания текста */}
      <div className="flex items-center gap-1">
        <Tooltip 
          title={renderTooltip("По левому краю", "Ctrl+Shift+L")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('left');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'left' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'left' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M2 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("По центру", "Ctrl+Shift+E")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('center');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'center' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'center' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M4 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm2-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("По правому краю", "Ctrl+Shift+R")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('right');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'right' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'right' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M6 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-4-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm4-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-4-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("По ширине", "Ctrl+Shift+J")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('justify');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'justify' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'justify' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M2 12.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
      </div>

      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

      {/* Кнопка отображения символов параграфа */}
      <Tooltip 
        title={renderTooltip(showParagraphSymbols ? "Скрыть символы параграфа" : "Показать символы параграфа")} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            if (editor && editor.extensionManager.extensions.find(ext => ext.name === 'paragraphSymbols')) {
              editor.commands.toggleParagraphSymbols();
              setShowParagraphSymbols(!showParagraphSymbols);
            }
          }}
          className={`px-2 py-1 rounded`}
          style={{
            background: showParagraphSymbols ? iconActiveBg : 'transparent',
            color: showParagraphSymbols ? iconActiveColor : iconColor,
            fontSize: `${getIconSize()}px`,
          }}
        >
          <span style={{ fontFamily: 'serif', fontWeight: 'bold' }}>¶</span>
        </button>
      </Tooltip>

      <Popover
        open={showLinkWarn}
        onOpenChange={setShowLinkWarn}
        trigger="click"
        placement="bottom"
        content={
          <div style={{ maxWidth: 300, padding: '8px 12px' }}>
            <p style={{ margin: 0, fontSize: 14, lineHeight: 1.5 }}>
              Используйте кнопку "Ссылка" для добавления ссылок. Вставка URL напрямую не будет работать.
            </p>
          </div>
        }
      >
        <Tooltip 
          title={renderTooltip("Добавить ссылку", null)} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            ref={linkBtnRef}
            onMouseDown={handleLinkButton}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive('link') ? iconActiveBg : 'transparent',
              color: editor.isActive('link') ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M4.715 6.542 3.343 7.914a3 3 0 1 0 4.243 4.243l1.828-1.829A3 3 0 0 0 8.586 5.5L8 6.086a1.002 1.002 0 0 0-.154.199 2 2 0 0 1 .861 3.337L6.88 11.45a2 2 0 1 1-2.83-2.83l.793-.792a4.018 4.018 0 0 1-.128-1.287z"/>
              <path d="M6.586 4.672A3 3 0 0 0 7.414 9.5l.775-.776a2 2 0 0 1-.896-3.346L9.12 3.55a2 2 0 1 1 2.83 2.83l-.793.792c.112.42.155.855.128 1.287l1.372-1.372a3 3 0 1 0-4.243-4.243L6.586 4.672z"/>
            </svg>
          </button>
        </Tooltip>
      </Popover>

      <Tooltip 
        title={renderTooltip("Добавить изображение", null)} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onClick={(e) => {
            fileInputRef.current?.click();
          }}
          className="px-2 py-1 rounded"
          style={{
            background: 'transparent',
            color: iconColor,
            border: 'none',
            cursor: 'pointer',
          }}
        >
          <PictureOutlined style={{ fontSize: `${getIconSize()}px` }} />
        </button>
      </Tooltip>
      <input 
        ref={fileInputRef}
        type="file" 
        accept=".jpg,.jpeg,.png,.webp,.bmp,.tiff,.gif,image/jpeg,image/png,image/webp,image/bmp,image/tiff,image/gif" 
        style={{ display: 'none' }} 
        onChange={handleImageUpload} 
      />
      
      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

      {/* Кнопка настроек размера значков */}
      <Tooltip
        title={renderTooltip("Настройки размера значков")}
        placement="bottom"
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            setShowSizeSettings(true);
          }}
          className={`px-2 py-1 rounded`}
          style={{
            background: 'transparent',
            color: iconColor,
          }}
        >
          <SettingOutlined style={{ fontSize: `${getIconSize()}px` }} />
        </button>
      </Tooltip>

      {/* Кнопка полноэкранного режима */}
      <Tooltip 
        title={renderTooltip(isFullWidth ? "Обычный режим" : "Полноэкранный режим")} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            onToggleFullWidth && onToggleFullWidth();
          }}
          className={`px-2 py-1 rounded`}
          style={{
            background: isFullWidth ? iconActiveBg : 'transparent',
            color: isFullWidth ? iconActiveColor : iconColor,
          }}
        >
          {isFullWidth ?
            <FullscreenExitOutlined style={{ fontSize: `${getIconSize()}px` }} /> :
            <FullscreenOutlined style={{ fontSize: `${getIconSize()}px` }} />
          }
        </button>
      </Tooltip>
    </div>
  );

  useImperativeHandle(ref, () => ({
    handleSave,
  }));

  // Открыть модалку для ссылки
  const openLinkModal = (init = {}) => {
    setLinkModal({
      open: true,
      href: init.href || '',
      blank: !!init.blank,
      from: init.from ?? null,
      to: init.to ?? null,
    });
  };
  
  // Закрыть модалку
  const closeLinkModal = () => setLinkModal(l => ({ ...l, open: false }));

  // Клик по ссылке в редакторе — не переходить, а редактировать
  useEffect(() => {
    if (!editor) return;
    const handler = e => {
      const a = e.target.closest('a');
      if (a && editor.isEditable) {
        e.preventDefault();
        const pos = editor.view.posAtDOM(a, 0);
        const { href, target } = a;
        openLinkModal({
          href,
          blank: target === '_blank',
          from: pos,
          to: pos + a.textContent.length,
        });
      }
    };
    const dom = editor.view.dom;
    dom.addEventListener('click', handler);
    return () => dom.removeEventListener('click', handler);
  }, [editor]);

  // Упрощенный обработчик кнопки ссылки
  const handleLinkButton = () => {
    if (!editor) return;
    
    // Фокусируемся на редакторе и проверяем выделение
    editor.commands.focus();
    const { from, to, empty } = editor.state.selection;
    const attrs = editor.getAttributes('link');
    
    if (attrs.href) {
      openLinkModal({ href: attrs.href, blank: attrs.target === '_blank', from, to });
    } else if (!empty) {
      openLinkModal({ from, to });
    } else {
      // ничего не выделено и не на ссылке
      setShowLinkWarn(true);
      setTimeout(() => setShowLinkWarn(false), 2000);
    }
  };

  // Упрощенный метод сохранения ссылки
  const saveLink = () => {
    if (!linkModal.href) return;
    if (!/^https?:\/\//.test(linkModal.href)) {
      message.warning('Ссылка должна начинаться с http:// или https://');
      return;
    }
    
    if (linkModal.from !== null && linkModal.to !== null) {
      editor.commands.setTextSelection({ from: linkModal.from, to: linkModal.to });
      editor.chain().focus().extendMarkRange('link').setLink({ href: linkModal.href, target: linkModal.blank ? '_blank' : null }).run();
    } else {
      editor.chain().focus().extendMarkRange('link').setLink({ href: linkModal.href, target: linkModal.blank ? '_blank' : null }).run();
    }
    
    closeLinkModal();
  };
  
  // Упрощенный метод удаления ссылки
  const removeLink = () => {
    if (linkModal.from !== null && linkModal.to !== null) {
      editor.commands.setTextSelection({ from: linkModal.from, to: linkModal.to });
    }
    editor.chain().focus().extendMarkRange('link').unsetLink().run();
    closeLinkModal();
  };

  // ✅ Синхронизация состояния extension с React state
  useEffect(() => {
    if (editor && editor.extensionManager.extensions.find(ext => ext.name === 'paragraphSymbols')) {
      const storage = editor.extensionStorage.paragraphSymbols;
      if (storage && storage.visible !== showParagraphSymbols) {
        if (showParagraphSymbols) {
          editor.commands.showParagraphSymbols();
        } else {
          editor.commands.hideParagraphSymbols();
        }
      }
    }
  }, [showParagraphSymbols, editor]);

  return (
    <div 
      className={isFullWidth ? "p-6 mx-auto" : "p-4 rounded-lg mb-4"} 
      style={{ 
        background: theme === 'dark' ? '#181c23' : '#f9fafb', 
        boxShadow: isFullWidth ? 'none' : (theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22'),
        minHeight: isFullWidth ? '100vh' : 'auto',
        borderRadius: isFullWidth ? 0 : 8,
        marginBottom: isFullWidth ? 0 : 16,
        maxWidth: isFullWidth ? '1200px' : '100%',
        width: isFullWidth ? '100%' : 'auto',
        margin: isFullWidth ? '0 auto' : undefined,
        padding: isFullWidth ? '20px 30px' : undefined,
        position: 'relative'
      }}>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 12 }}>
        <Input
          value={title}
          onChange={e => setTitle(e.target.value)}
          placeholder="Название главы"
          className={theme === 'dark' ? 'bg-[#111827] text-white border border-[#374151] placeholder-gray-400' : ''}
          style={{
            background: theme === 'dark' ? '#111827' : '#fff',
            color: theme === 'dark' ? '#fff' : '#222',
            border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb',
            borderRadius: 8,
            fontSize: '1rem',
            flex: 1,
            marginBottom: 0,
          }}
          maxLength={200}
        />
        <Popconfirm
          title={<span style={{ color: theme === 'dark' ? '#fff' : '#222', fontWeight: 600 }}>Закрыть редактор?</span>}
          description={<span style={{ color: theme === 'dark' ? '#fff' : '#222', fontSize: 13 }}>Выберите действие:</span>}
          okText="Сохранить и закрыть"
          cancelText="Закрыть без сохранения"
          onConfirm={handleSave}
          onCancel={onCancel}
          okButtonProps={{ style: { background: '#ef4444', color: '#fff', border: 'none' } }}
          cancelButtonProps={{ style: { background: theme === 'dark' ? '#23272f' : '#fff', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb' } }}
          classNames={{ root: theme === 'dark' ? 'dark-popconfirm' : '' }}
        >
          <span
            style={{
              marginLeft: 12,
              color: '#ef4444',
              fontSize: 28,
              cursor: 'pointer',
              transition: 'color 0.2s',
              userSelect: 'none',
            }}
            onMouseOver={e => e.target.style.color = '#b91c1c'}
            onMouseOut={e => e.target.style.color = '#ef4444'}
          >
            &#10006;
          </span>
        </Popconfirm>
      </div>
      <div className="mb-3 flex flex-col gap-2">
        {showToolbar && <Toolbar />}
        <div
          className={`tiptap-editor${autoIndent ? ' tiptap-indent' : ''}`}
          style={{
            minHeight: isFullWidth ? 'calc(100vh - 180px)' : 320,
            maxHeight: isFullWidth ? 'calc(100vh - 180px)' : 600,
            background: theme === 'dark' ? '#111827' : '#fff',
            color: theme === 'dark' ? '#fff' : '#222',
            border: theme === 'dark' ? '2px solid #374151' : '2px solid #d1d5db',
            borderRadius: 8,
            boxShadow: isFullWidth ? (theme === 'dark' ? '0 4px 12px rgba(0, 0, 0, 0.2)' : '0 4px 12px rgba(0, 0, 0, 0.1)') : (theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22'),
            outline: isFocused ? '2px solid #2563eb' : 'none',
            padding: isFullWidth ? '20px 24px' : '12px 16px',
            fontSize: '1rem',
            transition: 'background 0.2s, color 0.2s, outline 0.2s',
            overflowY: 'auto',
          }}
          onClick={() => {
            if (editor && !editor.isFocused) {
              editor.commands.focus('end');
            }
          }}
        >
          <EditorContent editor={editor} />
        </div>
      </div>
      <div className="flex gap-2 mt-2">
        <Tooltip title={disableSave ? disableSaveTooltip : ''} classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
          <Button type="primary" onClick={handleSave} loading={loading} disabled={disableSave}>
            Сохранить
          </Button>
        </Tooltip>
        <Button onClick={onCancel} disabled={loading} style={{ background: theme === 'dark' ? '#23272f' : '#fff', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb' }}>
          Отмена
        </Button>
      </div>
      <Modal
        open={linkModal.open}
        onCancel={closeLinkModal}
        footer={null}
        centered
        closable={false}
      >
        <div style={{ fontSize: 16, color: theme === 'dark' ? '#e5e7eb' : '#374151', fontWeight: 600, marginBottom: 20, textAlign: 'center' }}>
          Добавить ссылку
        </div>
        
        <div style={{ marginBottom: 16 }}>
          <Input
            placeholder="https://example.com"
            value={linkModal.href}
            onChange={e => setLinkModal(l => ({ ...l, href: e.target.value }))}
            style={{ 
              marginBottom: 12,
              background: theme === 'dark' ? '#111827' : '#ffffff',
              borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
              color: theme === 'dark' ? '#ffffff' : '#111827',
            }}
            allowClear
          />
          
          <Checkbox
            checked={linkModal.blank}
            onChange={e => setLinkModal(l => ({ ...l, blank: e.target.checked }))}
            style={{
              color: theme === 'dark' ? '#ffffff' : '#374151',
            }}
          >
            <span style={{ color: theme === 'dark' ? '#ffffff' : '#374151' }}>
              Открывать в новом окне
            </span>
          </Checkbox>
        </div>
        
        {linkModal.href && !/^https?:\/\//.test(linkModal.href) && (
          <div style={{ color: '#ef4444', fontSize: 13, marginBottom: 16, textAlign: 'center' }}>
            Ссылка должна начинаться с http:// или https://
          </div>
        )}
        
        <div style={{ display: 'flex', justifyContent: 'space-between', gap: 16 }}>
          <Button 
            onClick={removeLink} 
            disabled={!editor?.isActive('link')}
            style={{ 
              minWidth: 100,
              background: theme === 'dark' ? '#181c23' : '#f3f4f6', 
              color: theme === 'dark' ? '#ef4444' : '#dc2626', 
              border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', 
              fontWeight: 500 
            }}
          >
            Удалить
          </Button>
          
          <div style={{ display: 'flex', gap: 8 }}>
            <Button 
              onClick={closeLinkModal}
              style={{ 
                minWidth: 80,
                background: theme === 'dark' ? '#181c23' : '#f3f4f6', 
                color: theme === 'dark' ? '#fff' : '#222', 
                border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', 
                fontWeight: 500 
              }}
            >
              Отмена
            </Button>
            <Button 
              type="primary" 
              onClick={saveLink} 
              disabled={!linkModal.href || !/^https?:\/\//.test(linkModal.href)}
              style={{ 
                minWidth: 100, 
                background: (!linkModal.href || !/^https?:\/\//.test(linkModal.href)) ? '#d1d5db' : '#60A5FA', 
                color: '#fff', 
                border: 'none', 
                fontWeight: 500 
              }}
            >
              Сохранить
            </Button>
          </div>
        </div>
      </Modal>
      <style>{`
/* Убираем синий фокус везде */
.ant-input:focus,
.ant-input-focused,
.ant-input:focus-within,
.ant-select:focus,
.ant-select-focused,
.ant-select:focus-within,
.ant-btn:focus,
.ant-btn-focused,
.ant-btn:focus-within {
  box-shadow: none !important;
  outline: none !important;
}

/* Крестик очистки в поле ввода - серый цвет */
.ant-input-clear-icon {
  color: ${theme === 'dark' ? '#9ca3af' : '#6b7280'} !important;
  opacity: 1 !important;
}
.ant-input-clear-icon:hover {
  color: ${theme === 'dark' ? '#ffffff' : '#111827'} !important;
}
.tiptap-editor details {
  background: ${theme === 'dark' ? '#23272f' : '#f3f4f6'};
  border: 1.5px solid ${theme === 'dark' ? '#374151' : '#d1d5db'};
  border-radius: 8px;
  margin: 12px 0;
  padding: 0 0 0 0;
  transition: background 0.2s, border 0.2s;
}
.tiptap-editor summary {
  cursor: pointer;
  font-weight: 600;
  padding: 10px 16px;
  color: ${theme === 'dark' ? '#60A5FA' : '#2563eb'};
  background: none;
  border-radius: 8px 8px 0 0;
  outline: none;
  user-select: none;
}
.tiptap-editor details[open] summary {
  border-bottom: 1.5px solid ${theme === 'dark' ? '#374151' : '#d1d5db'};
}
.tiptap-editor details > *:not(summary) {
  padding: 12px 16px 16px 16px;
  color: ${theme === 'dark' ? '#fff' : '#222'};
}
`}</style>

      {/* Компонент настроек размера значков */}
      <ToolbarSizeSettings />
    </div>
  );
});

export default ChapterEditor;

function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

// Утилита: извлечь пути к изображениям из HTML
function extractImagePathsFromHtml(html) {
  
  const div = document.createElement('div');
  div.innerHTML = html;
  const imgs = div.querySelectorAll('img');
  
  
  const paths = [];
  imgs.forEach((img, index) => {
    const src = img.getAttribute('src');
    
    if (src && !/^https?:\/\//.test(src)) {
      paths.push(src);
      
    } else if (src) {
      
    }
  });
  
  const uniquePaths = Array.from(new Set(paths));
  
  return uniquePaths;
}

// Утилита: заменить src в HTML на presigned URL
function replaceImageSrcInHtml(html, urlMap) {
  const div = document.createElement('div');
  div.innerHTML = html;
  div.querySelectorAll('img').forEach(img => {
    const origSrc = img.getAttribute('src');
    if (urlMap[origSrc]) {
      img.setAttribute('src', urlMap[origSrc]);
    }
  });
  return div.innerHTML;
}

// Утилита: заменить presigned URL обратно на относительные пути (расширенная)
function revertPresignedUrlsInHtml(html, urlMap) {
  
  
  const div = document.createElement('div');
  div.innerHTML = html;
  const images = div.querySelectorAll('img');
  
  
  images.forEach((img, index) => {
    const src = img.getAttribute('src');
    
    
    // Найти ключ по значению (presigned url)
    const origPath = Object.keys(urlMap).find(key => urlMap[key] === src);
    if (origPath) {
      
      img.setAttribute('src', origPath);
    } else {
      
      // Попробовать извлечь относительный путь из presigned URL по шаблону
      // Регулярное выражение для поддержки как старого, так и нового формата путей
      const match = src && src.match(/\/media\/private\/book_pics\/[\w\/-]+\/chapics\/(?:[\w\/-]*\/)?[\w\d_]+\.(jpg|jpeg|png|gif|webp)/);
      if (match) {
        
        img.setAttribute('src', match[0]);
      } else {
        
      }
    }
  });
  
  const resultHtml = div.innerHTML;
  
  return resultHtml;
}

// Утилита: получить presigned URL с бэка
async function fetchPresignedUrls(paths) {
  const csrfToken = getCookie('csrftoken');
  const res = await fetch('/api/books/get_image_links/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken },
    credentials: 'include',
    body: JSON.stringify({ paths }),
  });
  if (!res.ok) throw new Error('Ошибка получения ссылок');
  return await res.json();
}

// Создаем новый компонент для выбора цвета, который не теряет выделение
function CustomColorDropdown({ editor, theme, getIconSize }) {
  const [isOpen, setIsOpen] = useState(false);
  const [isColorPickerOpen, setIsColorPickerOpen] = useState(false);
  const [selectionState, setSelectionState] = useState(null);
  const [activeTab, setActiveTab] = useState('text'); // 'text' или 'background'
  
  // Получаем текущие цвета текста и фона
  const currentTextColor = editor.getAttributes('textStyle').color;
  const currentBgColor = editor.getAttributes('backgroundColor').backgroundColor;
  
  // Определяем, используются ли автоматические цвета
  const isTextAuto = !currentTextColor || currentTextColor === '#000000' || currentTextColor === '#fff' || currentTextColor === '#ffffff';
  const isBgAuto = !currentBgColor;
  
  // Определяем цвета и метки в зависимости от активной вкладки
  const isAuto = activeTab === 'text' ? isTextAuto : isBgAuto;
  const currentColor = activeTab === 'text' ? currentTextColor : currentBgColor;
  const autoColor = theme === 'dark' ? '#fff' : '#222';
  
  // Определяем метку для кнопки
  let label = 'Цвет';
  if (activeTab === 'text') {
    label = isTextAuto ? 'Текст' : 'Текст';
  } else {
    label = isBgAuto ? 'Фон' : 'Фон';
  }
  
  // Цвет для отображения в кнопке
  const labelColor = activeTab === 'text' 
    ? (isTextAuto ? autoColor : currentTextColor)
    : (isBgAuto ? 'transparent' : currentBgColor);

  // Организованная палитра цветов 5x5
  const colorPalette = [
    // Ряд 1: Автоматический и красные оттенки
    [
      { name: 'Автоматический', value: 'auto', isAuto: true },
      { name: 'Светло-красный', value: '#ffcccc' },
      { name: 'Красный', value: '#ff0000' },
      { name: 'Темно-красный', value: '#cc0000' },
      { name: 'Бордовый', value: '#800000' }
    ],
    // Ряд 2: Оранжевые и желтые оттенки
    [
      { name: 'Светло-оранжевый', value: '#ffcc99' },
      { name: 'Оранжевый', value: '#ff9900' },
      { name: 'Светло-желтый', value: '#ffffcc' },
      { name: 'Желтый', value: '#ffff00' },
      { name: 'Темно-желтый', value: '#cccc00' }
    ],
    // Ряд 3: Зеленые оттенки
    [
      { name: 'Светло-зеленый', value: '#ccffcc' },
      { name: 'Зеленый', value: '#00cc00' },
      { name: 'Темно-зеленый', value: '#008000' },
      { name: 'Салатовый', value: '#99ff99' },
      { name: 'Оливковый', value: '#808000' }
    ],
    // Ряд 4: Синие и фиолетовые оттенки
    [
      { name: 'Светло-голубой', value: '#ccffff' },
      { name: 'Голубой', value: '#00ffff' },
      { name: 'Синий', value: '#0000ff' },
      { name: 'Темно-синий', value: '#000080' },
      { name: 'Фиолетовый', value: '#800080' }
    ],
    // Ряд 5: Серые оттенки, черный и белый
    [
      { name: 'Светло-серый', value: '#e6e6e6' },
      { name: 'Серый', value: '#808080' },
      { name: 'Темно-серый', value: '#404040' },
      { name: 'Черный', value: '#000000' },
      { name: 'Белый', value: '#ffffff' }
    ]
  ];

  const toggleDropdown = (e) => {
    e.preventDefault(); // Важно! Предотвращает потерю фокуса и выделения
    
    if (!isOpen) {
      // При открытии меню сохраняем текущее состояние выделения
      setSelectionState(editor.state);
    }
    
    setIsOpen(!isOpen);
    // Закрываем color picker при закрытии основного меню
    if (!isOpen === false) {
      setIsColorPickerOpen(false);
    }
  };

  const handleAutoColor = (e) => {
    e.preventDefault();
    
    // Если было сохранено состояние выделения, восстанавливаем его
    if (selectionState) {
      editor.view.updateState(selectionState);
    }
    
    // Сбрасываем цвет в зависимости от активной вкладки
    if (activeTab === 'text') {
      const { backgroundColor, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus().setMark('textStyle', {
        color: null,
        backgroundColor,
        fontSize,
      }).run();
    } else {
      const { color, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus().setMark('textStyle', {
        color,
        backgroundColor: null,
        fontSize,
      }).run();
    }
    
    setIsOpen(false);
  };
  
  const handleResetAllColors = (e) => {
    e.preventDefault();
    
    // Если было сохранено состояние выделения, восстанавливаем его
    if (selectionState) {
      editor.view.updateState(selectionState);
    }
    
    // Сбрасываем оба цвета
    editor.chain().focus()
      .extendMarkRange('textStyle').unsetColor()
      .extendMarkRange('backgroundColor').unsetBackgroundColor()
      .run();
    
    setIsOpen(false);
  };
  
  const applyPresetColor = (colorObj, e) => {
    e.preventDefault();
    
    // Если было сохранено состояние выделения, восстанавливаем его
    if (selectionState) {
      editor.view.updateState(selectionState);
    }
    
    // Если это автоматический цвет
    if (colorObj.isAuto) {
      handleAutoColor(e);
      return;
    }
    
    // Применяем цвет в зависимости от активной вкладки
    if (activeTab === 'text') {
      const { backgroundColor, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus()
        .setMark('textStyle', { color: null, backgroundColor, fontSize }) // сбросить цвет
        .setMark('textStyle', { color: colorObj.value, backgroundColor, fontSize }) // применить новый
        .run();
    } else {
      const { color, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus()
        .setMark('textStyle', { color, backgroundColor: null, fontSize }) // сбросить фон
        .setMark('textStyle', { color, backgroundColor: colorObj.value, fontSize }) // применить новый
        .run();
    }
    
    setIsOpen(false);
  };
  
  const switchTab = (tab, e) => {
    e.preventDefault();
    setActiveTab(tab);
  };

  return (
    <div className="relative inline-block" style={{ marginLeft: 4, marginRight: 4 }}>
      <Tooltip
        title={
          <span style={{ fontSize: '12px', lineHeight: '1.5' }}>
            Рекомендуется использовать стандартные цвета для текста и фона, они адаптируются автоматически на светлой и темной теме.
          </span>
        }
        placement="top"
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={toggleDropdown}
          className="flex items-center px-2 py-1 border rounded"
          style={{
            background: 'transparent',
            border: 'none',
            minWidth: '54px',
            height: '32px',
            cursor: 'pointer',
          }}
        >
          <span
            style={{
              display: 'inline-block',
              width: getIconSize(),
              height: getIconSize(),
              borderRadius: Math.max(4, getIconSize() / 3),
              border: '1.5px solid #fff',
              background: labelColor,
              marginRight: 6,
              boxShadow: theme === 'dark' ? '0 0 0 1.5px #fff' : '0 0 0 1.5px #222',
            }}
          />
          <span style={{ color: activeTab === 'text' ? (isTextAuto ? autoColor : currentTextColor) : autoColor, fontWeight: 600, fontSize: getIconSize() }}>{label}</span>
        </button>
      </Tooltip>
      
      {isOpen && (
        <div
          className="absolute left-0 mt-1 border rounded shadow-lg z-10"
          style={{
            background: theme === 'dark' ? '#1f2937' : '#ffffff',
            borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
            minWidth: '240px',
            zIndex: 1000,
          }}
        >
          <div
            onMouseDown={handleResetAllColors}
            className="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
            style={{
              background: (isTextAuto && isBgAuto) ? (theme === 'dark' ? '#374151' : '#f3f4f6') : 'transparent',
              color: autoColor,
              fontWeight: (isTextAuto && isBgAuto) ? 700 : 500,
              fontSize: getIconSize(),
            }}
          >
            Сбросить оба цвета
          </div>
          
          {/* Вкладки для переключения между текстом и фоном */}
          <div className="flex border-t border-gray-200 dark:border-gray-700">
            <div
              onMouseDown={(e) => switchTab('text', e)}
              className="flex-1 px-3 py-2 text-center cursor-pointer"
              style={{
                background: activeTab === 'text' ? (theme === 'dark' ? '#374151' : 'transparent') : (theme === 'dark' ? 'transparent' : '#f3f4f6'),
                color: autoColor,
                fontWeight: activeTab === 'text' ? 700 : 500,
                fontSize: getIconSize(),
                borderBottom: activeTab === 'text' ? `2px solid ${theme === 'dark' ? '#3b82f6' : '#2563eb'}` : 'none',
              }}
            >
              Текст
            </div>
            <div
              onMouseDown={(e) => switchTab('background', e)}
              className="flex-1 px-3 py-2 text-center cursor-pointer"
              style={{
                background: activeTab === 'background' ? (theme === 'dark' ? '#374151' : 'transparent') : (theme === 'dark' ? 'transparent' : '#f3f4f6'),
                color: autoColor,
                fontWeight: activeTab === 'background' ? 700 : 500,
                fontSize: getIconSize(),
                borderBottom: activeTab === 'background' ? `2px solid ${theme === 'dark' ? '#3b82f6' : '#2563eb'}` : 'none',
              }}
            >
              Фон
            </div>
          </div>
          
          {/* Компактная палитра цветов 5x5 */}
          <div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700">
            <div className="text-gray-500 dark:text-gray-400 mb-2" style={{ fontSize: Math.max(10, getIconSize() - 2) }}>
              {activeTab === 'text' ? 'Выберите цвет текста:' : 'Выберите цвет фона:'}
            </div>
            
            <div className="grid grid-cols-5 gap-2">
              {colorPalette.flat().map((color, index) => (
                <div
                  key={index}
                  onMouseDown={(e) => applyPresetColor(color, e)}
                  className="rounded cursor-pointer hover:opacity-80 flex items-center justify-center"
                  style={{
                    width: Math.max(24, getIconSize() + 8),
                    height: Math.max(24, getIconSize() + 8),
                    background: color.isAuto
                      ? 'transparent'
                      : color.value,
                    boxShadow: '0 0 0 1px rgba(0,0,0,0.2)',
                    border: ((activeTab === 'text' && isTextAuto && color.isAuto) ||
                            (activeTab === 'background' && isBgAuto && color.isAuto) ||
                            (activeTab === 'text' && currentTextColor === color.value) ||
                            (activeTab === 'background' && currentBgColor === color.value))
                      ? '2px solid #3b82f6'
                      : 'none',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                  title={color.name}
                >
                  {color.isAuto && (
                    <>
                      {activeTab === 'background' && (
                        <>
                          {/* Шахматный узор для обозначения прозрачности фона */}
                          <div style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#374151' : '#e5e7eb',
                          }} />
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#374151' : '#e5e7eb',
                          }} />
                          <div style={{
                            position: 'absolute',
                            top: 0,
                            left: '50%',
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#1f2937' : '#f3f4f6',
                          }} />
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            left: 0,
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#1f2937' : '#f3f4f6',
                          }} />
                        </>
                      )}
                      <span style={{ 
                        fontWeight: 'bold', 
                        color: theme === 'dark' ? '#fff' : '#000',
                        fontSize: '14px',
                        position: 'relative',
                        zIndex: 1
                      }}>
                        A
                      </span>
                    </>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 