from django.core.management.base import BaseCommand
from django.utils import timezone
from books.models import BookChapter
from celery.result import AsyncResult
from celery import current_app
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Проверка состояния Celery воркеров и запланированных задач публикации'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Подробный вывод',
        )

    def handle(self, *args, **options):
        verbose = options['verbose']
        
        self.stdout.write(self.style.SUCCESS('=== Проверка состояния Celery ==='))
        
        # Проверяем активные воркеры
        try:
            inspect = current_app.control.inspect()
            active_workers = inspect.active()
            registered_workers = inspect.registered()
            
            if active_workers:
                self.stdout.write(self.style.SUCCESS(f'✓ Активных воркеров: {len(active_workers)}'))
                if verbose:
                    for worker, tasks in active_workers.items():
                        self.stdout.write(f'  Воркер: {worker}')
                        for task in tasks:
                            self.stdout.write(f'    - {task["name"]} (ID: {task["id"]})')
            else:
                self.stdout.write(self.style.WARNING('⚠ Нет активных воркеров!'))
                
            if registered_workers:
                self.stdout.write(self.style.SUCCESS(f'✓ Зарегистрированных воркеров: {len(registered_workers)}'))
                if verbose:
                    for worker, tasks in registered_workers.items():
                        self.stdout.write(f'  Воркер: {worker}')
                        for task in tasks:
                            self.stdout.write(f'    - {task}')
            else:
                self.stdout.write(self.style.WARNING('⚠ Нет зарегистрированных воркеров!'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Ошибка при проверке воркеров: {e}'))
        
        # Проверяем запланированные задачи
        self.stdout.write(self.style.SUCCESS('\n=== Запланированные задачи публикации ==='))
        
        scheduled_chapters = BookChapter.objects.filter(
            scheduled_publish_at__isnull=False,
            is_published=False
        ).order_by('scheduled_publish_at')
        
        if scheduled_chapters.exists():
            self.stdout.write(self.style.SUCCESS(f'✓ Запланированных глав: {scheduled_chapters.count()}'))
            
            for chapter in scheduled_chapters:
                time_diff = (chapter.scheduled_publish_at - timezone.now()).total_seconds()
                status = "✓ Время пришло" if time_diff <= 0 else f"⏳ Осталось {time_diff/60:.1f} мин"
                
                self.stdout.write(
                    f'  Глава {chapter.id} (порядок: {chapter.order}): {status}'
                )
                
                if chapter.celery_task_id:
                    try:
                        result = AsyncResult(chapter.celery_task_id)
                        task_status = result.status
                        self.stdout.write(f'    Задача: {chapter.celery_task_id} - {task_status}')
                    except Exception as e:
                        self.stdout.write(f'    Задача: {chapter.celery_task_id} - Ошибка: {e}')
                else:
                    self.stdout.write(f'    Задача: Не создана')
                    
                if verbose:
                    self.stdout.write(f'    Время публикации: {chapter.scheduled_publish_at}')
                    self.stdout.write(f'    Книга: {chapter.book.title}')
        else:
            self.stdout.write(self.style.SUCCESS('✓ Нет запланированных глав'))
        
        # Проверяем статистику
        self.stdout.write(self.style.SUCCESS('\n=== Статистика ==='))
        total_chapters = BookChapter.objects.count()
        published_chapters = BookChapter.objects.filter(is_published=True).count()
        unpublished_chapters = BookChapter.objects.filter(is_published=False).count()
        
        self.stdout.write(f'Всего глав: {total_chapters}')
        self.stdout.write(f'Опубликовано: {published_chapters}')
        self.stdout.write(f'Не опубликовано: {unpublished_chapters}')
        
        self.stdout.write(self.style.SUCCESS('\n=== Проверка завершена ===')) 