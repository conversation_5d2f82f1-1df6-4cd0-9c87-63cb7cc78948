from django.core.management.base import BaseCommand
from users.models import User
from django.db import transaction

class Command(BaseCommand):
    help = 'Обновляет кэшированные URL аватаров для всех пользователей'

    def handle(self, *args, **options):
        users = User.objects.all()
        total = users.count()
        updated = 0

        self.stdout.write(f'Начинаем обновление URL аватаров для {total} пользователей...')

        with transaction.atomic():
            for user in users:
                try:
                    user.update_avatar_urls()
                    updated += 1
                    if updated % 100 == 0:
                        self.stdout.write(f'Обновлено {updated} из {total} пользователей...')
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Ошибка при обновлении пользователя {user.username}: {str(e)}'))

        self.stdout.write(self.style.SUCCESS(f'Обновление завершено. Обновлено {updated} из {total} пользователей.')) 