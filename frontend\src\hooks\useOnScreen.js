import { useEffect, useRef, useState } from 'react';

export function useOnScreen(options = { threshold: 0.3, rootMargin: '50px' }, key = null) {
    const ref = useRef();
    const [isIntersecting, setIntersecting] = useState(false);
    const observerRef = useRef(null);

    useEffect(() => {
        // Создаем новый observer только если его еще нет
        if (!observerRef.current) {
            observerRef.current = new window.IntersectionObserver(
                ([entry]) => {
                    const isVisible = entry.isIntersecting;
                    // console.log(`IntersectionObserver: isVisible=${isVisible}, key=${key}`); // Removed for performance
                    setIntersecting(isVisible);
                },
                options
            );
        }
        
        const currentElement = ref.current;
        const currentObserver = observerRef.current;
        
        if (currentElement && currentObserver) {
            currentObserver.observe(currentElement);
        }
        
        return () => {
            if (currentElement && currentObserver) {
                currentObserver.unobserve(currentElement);
            }
        };
    }, [options, key]); // key в зависимостях для пересоздания observer'а при изменении элемента

    // Сбрасываем состояние при изменении ключа
    useEffect(() => {
        setIntersecting(false);
    }, [key]);

    return [ref, isIntersecting];
} 