import React, { useState, useEffect, useContext, useRef } from 'react';
import { useOutletContext, useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../theme/ThemeContext';
import Cropper from 'react-easy-crop';
import { getCachedUserAvatar } from '../utils/avatarCache';
import ProfileTimezoneSelect from '../components/ProfileTimezoneSelect';
import { getCSRFToken, csrfFetch } from '../utils/csrf';
import { message, Modal } from 'antd';

/** SVG-аватары для разных полов */
const defaultAvatars = {
  M: (
    <svg viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-full">
      <circle cx="60" cy="60" r="60" fill="#c7d2fe" />
      <ellipse cx="60" cy="70" rx="32" ry="28" fill="#6366f1" />
      <ellipse cx="60" cy="54" rx="22" ry="20" fill="#f1f5f9" />
      <ellipse cx="60" cy="54" rx="16" ry="15" fill="#a5b4fc" />
      <ellipse cx="60" cy="54" rx="10" ry="9" fill="#6366f1" />
    </svg>
  ),
  F: (
    <svg viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-full">
      <circle cx="60" cy="60" r="60" fill="#fbcfe8" />
      <ellipse cx="60" cy="70" rx="32" ry="28" fill="#f472b6" />
      <ellipse cx="60" cy="54" rx="22" ry="20" fill="#f1f5f9" />
      <ellipse cx="60" cy="54" rx="16" ry="15" fill="#f9a8d4" />
      <ellipse cx="60" cy="54" rx="10" ry="9" fill="#f472b6" />
    </svg>
  ),
  U: (
    <svg viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-full">
      <circle cx="60" cy="60" r="60" fill="#e5e7eb" />
      <ellipse cx="60" cy="70" rx="32" ry="28" fill="#9ca3af" />
      <ellipse cx="60" cy="54" rx="22" ry="20" fill="#f1f5f9" />
      <ellipse cx="60" cy="54" rx="16" ry="15" fill="#d1d5db" />
      <ellipse cx="60" cy="54" rx="10" ry="9" fill="#9ca3af" />
    </svg>
  ),
};

function ProfileSettings() {
  const { userData, setUserData, updateProfile, refreshUserData, imageVersion } = useOutletContext();
  const { username } = useParams();
  const { user, refreshUser } = useAuth();
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [hasAccess, setHasAccess] = useState(null);
  const [isCheckingAccess, setIsCheckingAccess] = useState(true);

  const [name, setName] = useState(userData?.display_name || '');
  const [nameError, setNameError] = useState('');
  const [saving, setSaving] = useState(false);
  const [hideEmail, setHideEmail] = useState(userData?.hide_email !== false);
  const [gender, setGender] = useState(userData?.gender || '');
  const profileUrl = `/${window.location.host}/lpu/${userData?.username || ''}`;
  const [error, setError] = useState('');

  // --- Аватар ---
  const [avatarPreview, setAvatarPreview] = useState(userData?.avatar || null);
  const [avatarFile, setAvatarFile] = useState(null);
  const [avatarError, setAvatarError] = useState('');
  const [showCropper, setShowCropper] = useState(false);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [minZoom, setMinZoom] = useState(1);
  const [cropBoxSize, setCropBoxSize] = useState(300);
  const cropperContainerRef = useRef(null);

  const [birthDate, setBirthDate] = useState(userData?.birth_date || '');
  const [showBirthDate, setShowBirthDate] = useState(userData?.show_birth_date || false);
  const [timezone, setTimezone] = useState(userData?.timezone || 'Europe/Moscow');
  const [timezoneDisplayValue, setTimezoneDisplayValue] = useState(userData?.timezone_display_value || 'Europe/Moscow_msk');

  // Состояние для удаления аккаунта
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState('');

  const [userAvatars, setUserAvatars] = useState([]);
  const [showAvatarTooltip, setShowAvatarTooltip] = useState(false);
  const [showAvatarLimitModal, setShowAvatarLimitModal] = useState(false);
  const [avatarActionLoading, setAvatarActionLoading] = useState(false);
  const [avatarActionError, setAvatarActionError] = useState('');

  const backendUrl = import.meta.env.VITE_API_BASE_URL || '';

  // Функция для получения списка аватаров
  async function fetchUserAvatars() {
    try {
      const res = await csrfFetch('/api/auth/user-avatars/', {
        credentials: 'include',
        headers: { 'X-CSRFToken': getCSRFToken() }
      });
      if (res.ok) {
        const data = await res.json();
        setUserAvatars(data);
      }
    } catch (e) {
      console.error('Error fetching avatars:', e);
    }
  }

  // Загружаем список аватаров при монтировании компонента
  useEffect(() => {
    fetchUserAvatars();
  }, []);

  useEffect(() => {
    setIsCheckingAccess(true);
    if (user && user.username === username) {
      setHasAccess(true);
    } else if (user) {
      setHasAccess(false);
    }
    setIsCheckingAccess(false);
  }, [user, username]);

  useEffect(() => {
    setAvatarPreview(userData?.avatar || null);
  }, [userData?.avatar]);

  useEffect(() => {
    // Синхронизируем zoom с minZoom при смене изображения или minZoom
    if (showCropper && minZoom) {
      setZoom(minZoom);
    }
  }, [showCropper, minZoom]);

  useEffect(() => {
    if (!avatarPreview) return;
    const img = new window.Image();
    img.onload = () => {
      // Рассчитываем, как изображение впишется в 300x300
      const ratio = img.width / img.height;
      let boxSize = 300;
      if (ratio > 1) {
        // Широкое изображение — crop box по высоте
        boxSize = 300;
      } else {
        // Высокое изображение — crop box по ширине
        boxSize = 300;
      }
      setCropBoxSize(boxSize);
    };
    img.src = avatarPreview;
  }, [avatarPreview]);

  // Добавляем эффект для синхронизации gender с userData
  useEffect(() => {
    if (userData?.gender) {
      setGender(userData.gender);
    }
  }, [userData?.gender]);

  // Добавляем эффект для синхронизации timezone с userData
  useEffect(() => {
    if (userData?.timezone) {
      setTimezone(userData.timezone);
    }
    if (userData?.timezone_display_value) {
      setTimezoneDisplayValue(userData.timezone_display_value);
    }
  }, [userData?.timezone, userData?.timezone_display_value]);

  if (isCheckingAccess) {
    return (
      <div className="mt-8 max-w-2xl">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex justify-center items-center h-32">
            <div className="text-gray-500 dark:text-gray-400">
              Загрузка...
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (hasAccess === false) {
    return (
      <div className="mt-8 max-w-2xl">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex justify-center items-center h-32">
            <div className="text-red-500">
              У вас нет доступа к этой странице
            </div>
          </div>
        </div>
      </div>
    );
  }

  function handleAvatarChange(e) {
    setAvatarError('');
    const file = e.target.files[0];
    if (!file) return;
    const img = new window.Image();
    const reader = new FileReader();
    reader.onload = ev => {
      img.onload = () => {
        if (img.width < 300 || img.height < 300) {
          setAvatarError('Минимальный размер изображения: 300x300 пикселей');
          setAvatarFile(null);
          setAvatarPreview(null);
        } else {
          setAvatarFile(file);
          setAvatarPreview(ev.target.result);
          setShowCropper(true);
          setZoom(1);
        }
      };
      img.src = ev.target.result;
    };
    reader.readAsDataURL(file);
  }

  const onCropComplete = (croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  };

  async function getCroppedImg(imageSrc, cropPixels, size = 300) {
    const image = new window.Image();
    image.src = imageSrc;
    await new Promise(resolve => { image.onload = resolve; });
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(
      image,
      cropPixels.x, cropPixels.y, cropPixels.width, cropPixels.height,
      0, 0, size, size
    );
    return new Promise(resolve => {
      canvas.toBlob(blob => {
        resolve(blob);
      }, 'image/webp', 0.9);
    });
  }

  async function handleAvatarUpload() {
    if (!avatarPreview || !croppedAreaPixels) return;
    setUploading(true);
    setAvatarError('');
    try {
      // 1. Кроп 300x300
      const blob = await getCroppedImg(avatarPreview, croppedAreaPixels, 300);
      
      // Создаем файлы из blob
      const avatarFile = new File([blob], `ava_${userData.username}.webp`, { type: 'image/webp' });
      const thumbnailFile = new File([blob], `ava_${userData.username}_s.webp`, { type: 'image/webp' });
      
      const formData = new FormData();
      formData.append('file', avatarFile);
      formData.append('thumbnail', thumbnailFile);
      
      console.log('Отправляем запрос на загрузку аватара...');
      const res = await csrfFetch('/api/auth/user-avatars/', {
        method: 'POST',
        headers: { 'X-CSRFToken': getCSRFToken() },
        credentials: 'include',
        body: formData
      });
      
      console.log('Получен ответ от сервера:', res.status);
      const responseData = await res.json();
      console.log('Данные ответа:', responseData);
      
      if (!res.ok) {
        throw new Error(responseData.error || 'Ошибка при загрузке аватара');
      }
      
      // Проверяем, что получили данные аватара
      if (responseData && responseData.path && responseData.thumb_path) {
        console.log('Обновляем данные пользователя...');
        // Обновляем пути в users_user
        const updateResult = await updateProfile({
          avatar: responseData.path,
          avatar_thumbnail: responseData.thumb_path,
          avatar_type: 2, // Тип 2 - загруженный аватар
          avatar_updated_at: new Date().toISOString()
        });
        console.log('Результат обновления профиля:', updateResult);

        // Обновляем кэшированные URL
        userData.avatar_url_cached = `${backendUrl}/media/${responseData.path}`;
        userData.avatar_thumbnail_url_cached = `${backendUrl}/media/${responseData.thumb_path}`;
        userData.avatar_type = 2;
        userData.avatar_updated_at = new Date().toISOString();
        
        console.log('Обновляем список аватаров...');
        // Обновляем список аватаров
        await fetchUserAvatars();
        
        console.log('Обновляем данные пользователя...');
        // Обновляем данные пользователя
        await refreshUser();
        
        message.success('Аватар успешно загружен');
        setShowCropper(false);
        setAvatarFile(null);
        setAvatarPreview(null);
      } else {
        console.error('Неверный формат ответа:', responseData);
        throw new Error('Неверный формат ответа от сервера');
      }
    } catch (err) {
      console.error('Ошибка при загрузке аватара:', err);
      setAvatarError('Ошибка при загрузке аватара');
      message.error(err.message || 'Ошибка при загрузке аватара');
    } finally {
      setUploading(false);
    }
  }

  function validateName(value) {
    if (value.length < 4 || value.length > 40) {
      return 'Имя должно быть от 4 до 40 символов';
    }
    if (!/^[A-Za-zА-Яа-яЁё\-\s]+$/.test(value)) {
      return 'Можно использовать только буквы и тире';
    }
    return '';
  }

  async function handleSave(e) {
    e.preventDefault();
    setError('');
    const error = validateName(name.trim());
    setNameError(error);
    if (error) return;
    setSaving(true);
    const res = await updateProfile({ 
      display_name: name.trim(), 
      hide_email: hideEmail,
      gender: gender,
      birth_date: birthDate || null,
      show_birth_date: showBirthDate,
      timezone: timezone,
      timezone_display_value: timezoneDisplayValue,
      auto_remove_on_unfriend: !!userData?.auto_remove_on_unfriend,
      auto_accept_friends: userData?.auto_accept_friends !== false
    });
    if (!res.success) setError(res.error || 'Ошибка при сохранении');
    setSaving(false);
    if (res.success) {
      await refreshUser();
      // Больше не перезагружаем страницу - настройки применяются на лету
      // window.location.reload();
      
      // Показываем красивое уведомление через antd
      message.success('Настройки успешно сохранены!');
    }
  }

  const handleCancel = () => {
    navigate(`/lpu/${username}`);
  };

  const handleDeleteAccount = async () => {
    if (deleteConfirmText.toLowerCase() !== 'удалить') {
      setDeleteError('Для подтверждения введите слово "удалить"');
      return;
    }

    setIsDeleting(true);
    setDeleteError('');

    try {
      const response = await csrfFetch('/api/auth/delete-account/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          confirmation: deleteConfirmText
        })
      });

      if (response.ok) {
        // Успешное удаление - выходим из системы и перенаправляем
        await csrfFetch('/api/auth/logout/', { method: 'POST' });
        navigate('/');
        window.location.reload(); // Полная перезагрузка для очистки состояния
      } else {
        const data = await response.json();
        setDeleteError(data.error || 'Ошибка при удалении аккаунта');
      }
    } catch (error) {
      setDeleteError('Ошибка сети при удалении аккаунта');
    } finally {
      setIsDeleting(false);
    }
  };

  function getAvatarUrl(avatar, imageVersion) {
    if (!avatar) return null;
    let url = avatar;
    if (url.startsWith('http')) return `${url}?v=${imageVersion}`;
    if (url.startsWith('/media/') || url.startsWith('/static/')) return `${backendUrl}${url}?v=${imageVersion}`;
    return `${backendUrl}/media/${url.replace(/^\/+/,'')}?v=${imageVersion}`;
  }

  // Добавим функцию сброса аватара
  async function handleSetDefaultAvatar() {
    if (avatarActionLoading) return;
    setAvatarActionLoading(true);
    setAvatarError('');
    try {
      // Обновляем профиль с системным аватаром
      await updateProfile({ 
        avatar: null,
        avatar_thumbnail: null,
        avatar_type: 0,
        avatar_updated_at: new Date().toISOString()
      });
      
      // Обновляем кэшированные URL
      userData.avatar_url_cached = `/static/${userData.avatar_preset}`;
      userData.avatar_thumbnail_url_cached = `/static/${userData.avatar_preset_mini}`;
      userData.avatar_type = 0;
      userData.avatar_updated_at = new Date().toISOString();
      
      // Обновляем данные пользователя
      await refreshUser();
      
      setAvatarPreview(null);
      setAvatarFile(null);
      setShowCropper(false);
      
      message.success('Системный аватар успешно применён');
    } catch (err) {
      setAvatarError('Ошибка при сбросе аватара');
      message.error(err.message || 'Ошибка при сбросе аватара');
    } finally {
      setAvatarActionLoading(false);
    }
  }

  // Утилита для приведения пути к относительному виду
  function getRelativeAvatarPath(path) {
    if (!path) return '';
    // Убираем абсолютный S3 url
    const s3Prefix = 'https://storage.yandexcloud.net/lpo-test/media/public/';
    if (path.startsWith(s3Prefix)) return path.replace(s3Prefix, '');
    // Убираем /media/ или /static/
    return path.replace(/^\/media\//, '').replace(/^\/static\//, '');
  }

  async function handleApplyUserAvatar(ava) {
    if (avatarActionLoading) return;
    setAvatarActionLoading(true);
    setAvatarActionError('');
    try {
      // Обновляем профиль с выбранным аватаром
      await updateProfile({ 
        avatar: ava.path,
        avatar_thumbnail: ava.thumb_path,
        avatar_type: 2,
        avatar_updated_at: new Date().toISOString()
      });
      
      // Обновляем кэшированные URL
      userData.avatar_url_cached = `${backendUrl}/media/${ava.path}`;
      userData.avatar_thumbnail_url_cached = `${backendUrl}/media/${ava.thumb_path}`;
      userData.avatar_type = 2;
      userData.avatar_updated_at = new Date().toISOString();
      
      // Обновляем данные пользователя
      await refreshUser();
      
      message.success('Аватар успешно применён');
    } catch (err) {
      setAvatarActionError('Ошибка при применении аватара');
      message.error(err.message || 'Ошибка при применении аватара');
    } finally {
      setAvatarActionLoading(false);
    }
  }

  async function handleDeleteUserAvatar(id) {
    if (avatarActionLoading) return;
    if (!window.confirm('Удалить этот аватар?')) return;
    setAvatarActionLoading(true);
    setAvatarActionError('');
    try {
      // Найдём удаляемый и оставшиеся аватары
      const deletedAva = userAvatars.find(a => a.id === id);
      const isActive = deletedAva && (getRelativeAvatarPath(userData.avatar) === getRelativeAvatarPath(deletedAva.path));
      const remaining = userAvatars.filter(a => a.id !== id);
      
      const res = await csrfFetch(`/api/auth/user-avatars/${id}/`, {
        method: 'DELETE',
        headers: { 'X-CSRFToken': getCSRFToken() },
        credentials: 'include'
      });

      const responseData = await res.json();
      
      if (!res.ok) {
        throw new Error(responseData.error || 'Ошибка при удалении');
      }

      if (responseData.status === 'success') {
        // Обновляем список аватаров
        await fetchUserAvatars();
        
        if (isActive) {
          if (remaining.length > 0) {
            // Применить оставшийся аватар
            await updateProfile({ 
              avatar: remaining[0].path, 
              avatar_thumbnail: remaining[0].thumb_path,
              avatar_updated_at: new Date().toISOString() // Обновляем версию изображения
            });
            await refreshUser();
            message.info('Аватар был удалён, применён оставшийся.');
          } else {
            // Сбросить на системный
            await updateProfile({ 
              avatar: null,
              avatar_updated_at: new Date().toISOString() // Обновляем версию изображения
            });
            await refreshUser();
            message.info('Аватар сброшен на системный.');
          }
        } else {
          // Просто обновить профиль
          await refreshUser();
          message.success('Аватар успешно удалён');
        }
      } else {
        throw new Error('Ошибка при удалении аватара');
      }
    } catch (err) {
      setAvatarActionError('Ошибка при удалении аватара');
      message.error(err.message || 'Ошибка при удалении аватара');
    } finally {
      setAvatarActionLoading(false);
    }
  }

  async function handleGenderChange(newGender) {
    if (avatarActionLoading) return;
    setAvatarActionLoading(true);
    setAvatarActionError('');
    try {
      // Сначала обновляем локальное состояние
      setGender(newGender);

      // Если текущий аватар системный, обновляем его на соответствующий новому полу
      let avatarUpdate = {};
      if (userData.avatar_type === 0) { // Системный аватар
        const newPreset = newGender === 'M' ? 'ava_presets/ava_M.webp' : 
                         newGender === 'F' ? 'ava_presets/ava_F.webp' : 
                         'ava_presets/ava_U.webp';
        const newPresetMini = newGender === 'M' ? 'ava_presets/ava_M_s.webp' : 
                            newGender === 'F' ? 'ava_presets/ava_F_s.webp' : 
                            'ava_presets/ava_U_s.webp';
        avatarUpdate = {
          avatar_preset: newPreset,
          avatar_preset_mini: newPresetMini
        };
      }

      // Обновляем пол пользователя и аватар если нужно
      await updateProfile({ 
        gender: newGender || 'U', // Если пустое значение, используем 'U'
        ...avatarUpdate,
        avatar_updated_at: new Date().toISOString() // Обновляем версию изображения
      });
      
      // Обновляем данные пользователя
      await refreshUser();
      
      // Если был системный аватар, обновляем его URL в данных пользователя
      if (userData.avatar_type === 0) {
        const newPreset = newGender === 'M' ? 'ava_presets/ava_M.webp' : 
                         newGender === 'F' ? 'ava_presets/ava_F.webp' : 
                         'ava_presets/ava_U.webp';
        const newPresetMini = newGender === 'M' ? 'ava_presets/ava_M_s.webp' : 
                            newGender === 'F' ? 'ava_presets/ava_F_s.webp' : 
                            'ava_presets/ava_U_s.webp';
        
        userData.avatar_url_cached = `/static/${newPreset}`;
        userData.avatar_thumbnail_url_cached = `/static/${newPresetMini}`;
        userData.system_avatar_url = `/static/${newPreset}`;
        userData.system_avatar_thumbnail_url = `/static/${newPresetMini}`;
      }
      
      message.success('Пол успешно изменён');
    } catch (err) {
      // В случае ошибки возвращаем предыдущее значение
      setGender(userData.gender);
      setAvatarActionError('Ошибка при изменении пола');
      message.error(err.message || 'Ошибка при изменении пола');
    } finally {
      setAvatarActionLoading(false);
    }
  }

  async function handleUseSystemAvatar() {
    if (avatarActionLoading) return;
    setAvatarActionLoading(true);
    setAvatarActionError('');
    try {
      // Обновляем профиль с системным аватаром
      await updateProfile({ 
        avatar: null,
        avatar_thumbnail: null,
        avatar_type: 0,
        avatar_updated_at: new Date().toISOString()
      });
      
      // Обновляем кэшированные URL
      userData.avatar_url_cached = `/static/${userData.avatar_preset}`;
      userData.avatar_thumbnail_url_cached = `/static/${userData.avatar_preset_mini}`;
      userData.avatar_type = 0;
      userData.avatar_updated_at = new Date().toISOString();
      
      // Обновляем данные пользователя
      await refreshUser();
      
      message.success('Системный аватар успешно применён');
    } catch (err) {
      setAvatarActionError('Ошибка при применении системного аватара');
      message.error(err.message || 'Ошибка при применении системного аватара');
    } finally {
      setAvatarActionLoading(false);
    }
  }

  return (
    <div className="mt-8 w-full pr-[10px]">
      <h2 className="text-2xl font-bold mb-8">Настройки профиля</h2>
      <form className="space-y-8" onSubmit={handleSave} autoComplete="off">
        {/* Имя */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2">
          <label htmlFor="display_name" className="text-lg font-semibold text-gray-900 dark:text-gray-100">ФИО или Псевдоним</label>
          <input
            id="display_name"
            type="text"
            className="max-w-md w-1/2 px-3 py-2 rounded border bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-400"
            value={name}
            onChange={e => setName(e.target.value)}
            maxLength={40}
            minLength={4}
            pattern="[A-Za-zА-Яа-яЁё\-\s]+"
            placeholder="Введите ФИО или псевдоним"
          />
          <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">(Длина от 4 до 40 символов, допускаются буквы и тире)</span>
          {nameError && <div className="text-red-500 text-sm">{nameError}</div>}
        </div>
        {/* Аватар */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-4">
          <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Фото профиля</span>
          <div className="flex items-center gap-6">
            <div className="w-[200px] h-[200px] rounded-full border-4 border-white shadow-lg flex-shrink-0 overflow-hidden flex items-center justify-center">
              {getCachedUserAvatar(userData, 'full', backendUrl, imageVersion) ? (
                <img src={getCachedUserAvatar(userData, 'full', backendUrl, imageVersion)} alt="avatar" className="w-full h-full object-cover" />
              ) : (
                defaultAvatars[gender || 'U']
              )}
            </div>
            <div className="flex flex-col gap-2 mt-2">
              {/* --- Миниатюры аватаров --- */}
              {userAvatars.length > 0 && (
                <div className="flex gap-2 mb-2">
                  {userAvatars.map(ava => (
                    <div key={ava.id} className="relative group">
                      <img
                        src={`https://storage.yandexcloud.net/lpo-test/media/public/${ava.thumb_path}`}
                        alt=""
                        className="w-14 h-14 rounded-full border-2 border-blue-400 cursor-pointer object-cover"
                        onClick={() => handleApplyUserAvatar(ava)}
                        disabled={avatarActionLoading}
                      />
                      <button
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-base shadow opacity-60 hover:opacity-100 hover:scale-125 transition-all duration-150"
                        onClick={() => handleDeleteUserAvatar(ava.id)}
                        type="button"
                        style={{lineHeight: 1}}
                        disabled={avatarActionLoading}
                      >×</button>
                    </div>
                  ))}
                </div>
              )}
              {/* --- Конец миниатюр --- */}
              <div className="relative w-max">
                <button
                  type="button"
                  className={`bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 px-4 py-2 rounded cursor-pointer w-max border border-gray-300 dark:border-gray-600 transition-colors duration-150 ${userAvatars.length >= 2 ? 'opacity-60 cursor-not-allowed' : 'hover:bg-gray-200 dark:hover:bg-gray-600'}`}
                  onClick={() => {
                    if (userAvatars.length < 2) {
                      document.getElementById('avatar-file-input').click();
                    } else {
                      setShowAvatarLimitModal(true);
                    }
                  }}
                  disabled={saving || uploading || avatarActionLoading}
                >
                  Загрузить свой
                </button>
                <input
                  id="avatar-file-input"
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  className="hidden"
                />
                {/* Модалка-алерт для лимита аватаров */}
                {showAvatarLimitModal && (
                  <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
                    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-xs w-full text-center border border-gray-200 dark:border-gray-700">
                      <div className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Лимит аватаров</div>
                      <div className="text-gray-700 dark:text-gray-300 mb-4 text-sm">
                        Для загрузки нового аватара, удалите один из ранее загруженных.
                      </div>
                      <button
                        className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                        onClick={() => setShowAvatarLimitModal(false)}
                      >Понятно</button>
                    </div>
                  </div>
                )}
              </div>
              <button
                type="button"
                className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 px-4 py-2 rounded cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 w-max border border-gray-300 dark:border-gray-600 transition-colors duration-150"
                onClick={handleSetDefaultAvatar}
                disabled={saving || uploading || avatarActionLoading}
              >
                Использовать системный
              </button>
              {avatarFile && <span className="ml-2 text-sm text-gray-600 dark:text-gray-300">{avatarFile.name}</span>}
              {avatarError && <div className="text-red-500 text-sm">{avatarError}</div>}
              {showCropper && avatarPreview && (
                <div className="mt-2">
                  <div className="mb-2 text-xs text-gray-500">Обрежьте область 1:1 (минимум 300x300):</div>
                  <div style={{ width: 300, height: 300, position: 'relative', background: '#222', borderRadius: 8, overflow: 'hidden' }}>
                    <Cropper
                      image={avatarPreview}
                      crop={crop}
                      zoom={zoom}
                      aspect={1}
                      minZoom={1}
                      maxZoom={3}
                      cropShape="rect"
                      showGrid={true}
                      onCropChange={setCrop}
                      onZoomChange={setZoom}
                      onCropComplete={onCropComplete}
                      restrictPosition={true}
                    />
                  </div>
                  <div className="flex items-center gap-2 mt-2">
                    <button
                      type="button"
                      className="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300"
                      onClick={() => setZoom(z => Math.max(1, +(z - 0.05).toFixed(2)))}
                      disabled={zoom <= 1}
                    >
                      –
                    </button>
                    <input
                      type="range"
                      min={1}
                      max={3}
                      step={0.02}
                      value={zoom}
                      onChange={e => setZoom(Number(e.target.value))}
                      className="w-40 mx-2"
                    />
                    <button
                      type="button"
                      className="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300"
                      onClick={() => setZoom(z => Math.min(3, +(z + 0.05).toFixed(2)))}
                      disabled={zoom >= 3}
                    >
                      +
                    </button>
                  </div>
                  <button className="mt-3 px-4 py-2 rounded bg-blue-500 text-white hover:bg-blue-600 disabled:bg-gray-300" disabled={uploading || avatarActionLoading} onClick={handleAvatarUpload} type="button">
                    {uploading ? 'Сохраняю...' : avatarActionLoading ? 'Применяю...' : 'Сохранить аватар'}
                  </button>
                  <button className="mt-3 ml-2 px-4 py-2 rounded bg-gray-300 text-gray-700 hover:bg-gray-400" type="button" onClick={() => setShowCropper(false)}>Отмена</button>
                </div>
              )}
            </div>
          </div>
        </div>
        {/* Пол (селект) */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2">
          <label className="text-lg font-semibold text-gray-900 dark:text-gray-100">Укажите ваш пол</label>
          <div className="w-auto inline-block">
            <select
              value={gender}
              onChange={e => handleGenderChange(e.target.value)}
              className="w-auto min-w-[140px] px-3 py-2 rounded border bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-400"
            >
              <option value="M">Мужской</option>
              <option value="F">Женский</option>
              <option value="">Не указан</option>
            </select>
          </div>
          <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">(Выберите ваш пол, если хотите отображать его в профиле. Стандартный аватар зависит от выбранного пола)</span>
        </div>
        {/* Дата рождения */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2 mt-6">
          <label className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Дата рождения:
            {!birthDate && <span style={{ color: 'red', marginLeft: 4 }}>*</span>}
          </label>
          <input
            type="date"
            className="max-w-xs w-full px-3 py-2 rounded border bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-400"
            value={birthDate || ''}
            onChange={e => setBirthDate(e.target.value)}
            max={new Date().toISOString().split('T')[0]}
          />
          <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">(Для корректного отображения контента, укажите свой возраст)</span>
          {!birthDate && error && (
            <span className="text-xs italic mt-1" style={{ color: 'red' }}>
              *Для корректного отображения книг и рекомендаций на сайте, укажите дату своего дня рождения. Её можно скрыть от других пользователей в настройках ниже!
            </span>
          )}
        </div>
        {/* Тумблер показывать возраст */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-start gap-4 mt-6">
          <label className="relative inline-flex items-center cursor-pointer mt-1">
            <input
              type="checkbox"
              checked={showBirthDate}
              onChange={e => setShowBirthDate(e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 rounded-full peer peer-checked:bg-blue-500 dark:peer-checked:bg-blue-400 transition-colors"></div>
            <div className="absolute left-1 top-1 bg-white w-4 h-4 rounded-full shadow transition-transform peer-checked:translate-x-5"></div>
          </label>
          <div className="flex flex-col">
            <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Показывать ваш возраст на странице профиля
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">(Если включено, ваш возраст будет виден другим пользователям. По умолчанию выключено)</span>
          </div>
        </div>
        {/* Электронная почта (тумблер) */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-start gap-4">
          <label className="relative inline-flex items-center cursor-pointer mt-1">
            <input
              type="checkbox"
              checked={!hideEmail}
              onChange={e => setHideEmail(!e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 rounded-full peer peer-checked:bg-blue-500 dark:peer-checked:bg-blue-400 transition-colors"></div>
            <div className="absolute left-1 top-1 bg-white w-4 h-4 rounded-full shadow transition-transform peer-checked:translate-x-5"></div>
          </label>
          <div className="flex flex-col">
            <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Показывать ваш e-mail: <span className="italic">{userData?.email}</span> на странице профиля
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">(Если включено, ваш email будет виден другим пользователям. По умолчанию выключено)</span>
          </div>
        </div>
        {/* Часовой пояс */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex flex-col gap-2">
          <ProfileTimezoneSelect
            value={timezone}
            savedValue={timezoneDisplayValue}
            onChange={(tz, displayValue) => {
              setTimezone(tz);
              setTimezoneDisplayValue(displayValue);
            }}
          />
        </div>
        {/* Автоматическая отписка при разрыве дружбы (тумблер) */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-start gap-4">
          <label className="relative inline-flex items-center cursor-pointer mt-1">
            <input
              type="checkbox"
              checked={!!userData?.auto_remove_on_unfriend}
              onChange={e => {
                setUserData(prev => ({ ...prev, auto_remove_on_unfriend: e.target.checked }));
                updateProfile({ auto_remove_on_unfriend: e.target.checked });
              }}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 rounded-full peer peer-checked:bg-blue-500 dark:peer-checked:bg-blue-400 transition-colors"></div>
            <div className="absolute left-1 top-1 bg-white w-4 h-4 rounded-full shadow transition-transform peer-checked:translate-x-5"></div>
          </label>
          <div className="flex flex-col">
            <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Автоматическая отписка при разрыве дружбы</span>
            <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">(Если включено, при удалении из друзей вы автоматически отпишетесь от пользователя. По умолчанию выключено)</span>
          </div>
        </div>
        {/* Автоматическое добавление в друзья при запросе (тумблер) */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-start gap-4">
          <label className="relative inline-flex items-center cursor-pointer mt-1">
            <input
              type="checkbox"
              checked={userData?.auto_accept_friends !== false}
              onChange={e => {
                setUserData(prev => ({ ...prev, auto_accept_friends: e.target.checked }));
                updateProfile({ auto_accept_friends: e.target.checked });
              }}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 rounded-full peer peer-checked:bg-blue-500 dark:peer-checked:bg-blue-400 transition-colors"></div>
            <div className="absolute left-1 top-1 bg-white w-4 h-4 rounded-full shadow transition-transform peer-checked:translate-x-5"></div>
          </label>
          <div className="flex flex-col">
            <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Автоматически добавлять в друзья при запросе</span>
            <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">(Если включено, заявки в друзья будут приниматься автоматически без подтверждения. По умолчанию включено)</span>
          </div>
        </div>
        {error && <div className="text-red-500 text-sm mb-2">{error}</div>}
        <div className="flex justify-between items-center mt-8">
          <div className="flex gap-4">
            <button
              type="submit"
              className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 flex items-center"
              disabled={saving}
            >
              {saving && (
                <svg className="animate-spin h-5 w-5 mr-2 text-white" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z" />
                </svg>
              )}
              {saving ? 'Сохраняю...' : 'Сохранить изменения'}
            </button>
            <button
              type="button"
              className="px-6 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600"
              onClick={handleCancel}
              disabled={saving}
            >
              Отмена
            </button>
          </div>

          {/* Кнопка удаления аккаунта */}
          <button
            type="button"
            onClick={() => setShowDeleteModal(true)}
            className={`
              p-2.5 rounded-lg transition-all duration-200 hover:scale-105 active:scale-95
              ${theme === 'dark'
                ? 'text-red-400 hover:text-red-300 bg-gray-700/50 hover:bg-red-900/40 border border-gray-600 hover:border-red-700'
                : 'text-red-600 hover:text-red-700 bg-gray-50 hover:bg-red-50 border border-gray-200 hover:border-red-300'
              }
              disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100
            `}
            title="Удалить аккаунт"
            disabled={saving}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </form>

      {/* Модальное окно удаления аккаунта */}
      <Modal
        open={showDeleteModal}
        onCancel={() => {
          setShowDeleteModal(false);
          setDeleteConfirmText('');
          setDeleteError('');
        }}
        footer={null}
        width={500}
        className={theme === 'dark' ? 'dark-modal' : ''}
        styles={{
          content: {
            background: theme === 'dark' ? '#23272f' : '#ffffff',
            borderRadius: '12px',
            padding: '24px',
          },
          mask: {
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        {/* Заголовок с иконкой */}
        <div className="flex items-center gap-3 mb-6">
          <div className="flex-shrink-0 w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Удаление аккаунта
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Это действие нельзя отменить
            </p>
          </div>
        </div>

        {/* Описание последствий */}
        <div className="mb-6">
          <p className="text-gray-700 dark:text-gray-300 mb-4">
            <strong>Внимание!</strong> При удалении аккаунта:
          </p>
          <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1 mb-4">
            <li>Все ваши книги и главы будут удалены</li>
            <li>Ваш email будет очищен</li>
            <li>Профиль будет помечен как удаленный</li>
            <li>В комментариях и сообщениях будет отображаться "Аккаунт удален"</li>
            <li>Все подписки и друзья будут удалены</li>
          </ul>
          <p className="text-gray-700 dark:text-gray-300 mb-4">
            Для подтверждения введите слово <strong>"удалить"</strong>:
          </p>
        </div>

        {/* Поле ввода подтверждения */}
        <div className="mb-6">
          <input
            type="text"
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
            placeholder="Введите: удалить"
            className={`
              w-full px-4 py-3 rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500
              ${theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
              }
              disabled:opacity-50 disabled:cursor-not-allowed
            `}
            disabled={isDeleting}
          />
        </div>

        {/* Сообщение об ошибке */}
        {deleteError && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-center gap-2">
              <svg className="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-red-700 dark:text-red-300 text-sm font-medium">
                {deleteError}
              </span>
            </div>
          </div>
        )}

        {/* Кнопки */}
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            onClick={() => {
              setShowDeleteModal(false);
              setDeleteConfirmText('');
              setDeleteError('');
            }}
            className="px-6 py-2.5 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors font-medium"
            disabled={isDeleting}
          >
            Отмена
          </button>
          <button
            onClick={handleDeleteAccount}
            disabled={isDeleting || deleteConfirmText.toLowerCase() !== 'удалить'}
            className="px-6 py-2.5 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 dark:disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium flex items-center gap-2"
          >
            {isDeleting && (
              <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z" />
              </svg>
            )}
            {isDeleting ? 'Удаляю...' : 'Удалить аккаунт'}
          </button>
        </div>
      </Modal>

      {/* Стили для модального окна в темной теме */}
      <style>{`
        .dark-modal .ant-modal-content {
          background: #23272f !important;
          border-radius: 12px !important;
          border: none !important;
        }
        .dark-modal .ant-modal-close-x {
          color: #fff !important;
        }
        .ant-modal-close {
          top: 8px !important;
          right: 8px !important;
        }
        .ant-modal-close .ant-modal-close-x {
          font-size: 16px !important;
          line-height: 1 !important;
        }
        .ant-modal-content {
          border-radius: 12px !important;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
}

export default ProfileSettings;