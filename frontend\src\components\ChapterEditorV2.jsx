import React, { useState, useRef, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Button, Input, message, Popconfirm, Tooltip, Dropdown, Menu, Modal, Checkbox, Popover, Slider, Radio, InputNumber } from 'antd';
import { EditorContent, useEditor, NodeViewWrapper, NodeViewContent, ReactNodeViewRenderer } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Underline from '@tiptap/extension-underline';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import './chapter-editor.css';
import './ImageEditor.css';
import { useTheme } from '../theme/ThemeContext';
import { PictureOutlined, FullscreenOutlined, FullscreenExitOutlined, SettingOutlined, ScissorOutlined } from '@ant-design/icons';
import ResizableImageNodeView from './ResizableImageNodeView.jsx';
import { Mark, mergeAttributes, Node, Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { Decoration, DecorationSet } from '@tiptap/pm/view';

// ParagraphSymbols Extension с decorations
const ParagraphSymbols = Extension.create({
  name: 'paragraphSymbols',

  addOptions() {
    return {
      visible: false,
    };
  },

  addStorage() {
    return {
      visible: this.options.visible,
    };
  },

  addCommands() {
    return {
      showParagraphSymbols: () => ({ editor }) => {
        this.storage.visible = true;
        editor.view.dispatch(editor.state.tr);
        return true;
      },
      hideParagraphSymbols: () => ({ editor }) => {
        this.storage.visible = false;
        editor.view.dispatch(editor.state.tr);
        return true;
      },
      toggleParagraphSymbols: () => ({ editor }) => {
        this.storage.visible = !this.storage.visible;
        editor.view.dispatch(editor.state.tr);
        return true;
      },
    };
  },

  addProseMirrorPlugins() {
    const extension = this;

    return [
      new Plugin({
        key: new PluginKey('paragraphSymbols'),
        
        state: {
          init() {
            return DecorationSet.empty;
          },
          
          apply(tr, decorationSet) {
            if (!extension.storage.visible) {
              return DecorationSet.empty;
            }

            const decorations = [];
            const { doc } = tr;

            doc.descendants((node, pos) => {
              if (node.type.name === 'paragraph') {
                let symbolPos;
                
                if (node.content.size === 0 || (node.content.size === 1 && node.firstChild?.type.name === 'hardBreak')) {
                  symbolPos = pos + 1;
                } else {
                  symbolPos = pos + node.nodeSize - 1;
                }
                
                const decoration = Decoration.widget(symbolPos, () => {
                  const span = document.createElement('span');
                  span.className = 'paragraph-symbol';
                  span.textContent = '¶';
                  span.style.cssText = `
                    color: #b0b0b0;
                    opacity: 0.6;
                    font-family: serif;
                    font-size: 0.9em;
                    margin-left: 2px;
                    pointer-events: none;
                    user-select: none;
                    display: inline;
                  `;
                  return span;
                }, {
                  side: 1,
                  ignoreSelection: true,
                });

                decorations.push(decoration);
              }
            });

            return DecorationSet.create(doc, decorations);
          },
        },
        
        props: {
          decorations(state) {
            return this.getState(state);
          },
        },
      }),
    ];
  },
});

const FontSize = Mark.create({
  name: 'fontSize',
  addOptions() {
    return {
      types: ['textStyle'],
    };
  },
  addAttributes() {
    return {
      fontSize: {
        default: null,
        parseHTML: element => element.style.fontSize || null,
        renderHTML: attributes => {
          if (!attributes.fontSize) return {};
          return { style: `font-size: ${attributes.fontSize}` };
        },
      },
    };
  },
  parseHTML() {
    return [
      {
        style: 'font-size',
      },
    ];
  },
  renderHTML({ HTMLAttributes }) {
    return ['span', mergeAttributes(HTMLAttributes), 0];
  },
  addCommands() {
    return {
      setFontSize: size => ({ chain }) => chain().setMark('fontSize', { fontSize: size }).run(),
      unsetFontSize: () => ({ chain }) => chain().setMark('fontSize', { fontSize: null }).run(),
    };
  },
});

// Создаём отдельный mark для backgroundColor
const BackgroundColor = Mark.create({
  name: 'backgroundColor',
  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },
  addAttributes() {
    return {
      backgroundColor: {
        default: null,
        parseHTML: element => element.style.backgroundColor || null,
        renderHTML: attributes => {
          if (!attributes.backgroundColor) return {};
          return { style: `background-color: ${attributes.backgroundColor}` };
        },
      },
    }
  },
  parseHTML() {
    return [
      {
        style: 'background-color',
      },
    ]
  },
  renderHTML({ HTMLAttributes }) {
    return ['span', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]
  },
  addCommands() {
    return {
      setBackgroundColor: color => ({ chain }) => chain().setMark('backgroundColor', { backgroundColor: color }).run(),
      unsetBackgroundColor: () => ({ chain }) => chain().setMark('backgroundColor', { backgroundColor: null }).run(),
    }
  },
});

// Расширяем TextStyle для поддержки color, backgroundColor и fontSize
const CustomTextStyle = TextStyle.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      color: {
        default: null,
        parseHTML: element => element.style.color || null,
        renderHTML: attributes => {
          if (!attributes.color) return {};
          return { style: `color: ${attributes.color}` };
        },
      },
      backgroundColor: {
        default: null,
        parseHTML: element => element.style.backgroundColor || null,
        renderHTML: attributes => {
          if (!attributes.backgroundColor) return {};
          return { style: `background-color: ${attributes.backgroundColor}` };
        },
      },
      fontSize: {
        default: null,
        parseHTML: element => element.style.fontSize || null,
        renderHTML: attributes => {
          if (!attributes.fontSize) return {};
          return { style: `font-size: ${attributes.fontSize}` };
        },
      },
    }
  },
});

const ChapterEditor = forwardRef(({
  initialTitle = '',
  initialContent = '',
  onSave,
  onCancel,
  loading = false,
  bookId,
  chapterOrder = 1,
  chapterId = undefined,
  username,
  imgIndex = 1,
  showToolbar = true,
  autoIndent = false,
  isFullWidth = false,
  onToggleFullWidth,
  onChapterSplit, // Новый callback для уведомления о разделении главы
  maxChapterLength = null, // Лимит символов для главы
  showCharacterCounter = false, // Показывать ли счетчик символов
}, ref) => {
  const [title, setTitle] = useState(initialTitle);
  const imgCounter = useRef(imgIndex);
  const [isFocused, setIsFocused] = useState(false);
  const { theme } = useTheme();
  const [presignedUrlMap, setPresignedUrlMap] = useState({});
  const colorInputRef = useRef();
  const [linkModal, setLinkModal] = useState({ open: false, href: '', blank: false, from: null, to: null });
  const [showLinkWarn, setShowLinkWarn] = useState(false);
  const linkBtnRef = useRef();
  const fileInputRef = useRef();
  const [contentLoading, setContentLoading] = useState(false);
  const [showParagraphSymbols, setShowParagraphSymbols] = useState(false);

  // Состояния для настроек размера значков
  const [iconSizeMode, setIconSizeMode] = useState(() => {
    const saved = localStorage.getItem('editor-icon-size-mode');
    return saved || 'medium';
  });
  const [customIconSize, setCustomIconSize] = useState(() => {
    const saved = localStorage.getItem('editor-custom-icon-size');
    return saved ? parseInt(saved) : 16;
  });
  const [showSizeSettings, setShowSizeSettings] = useState(false);
  const toolbarRef = useRef();

  const ThemedResizableImageNodeView = (props) => {
    // Передаем функцию загрузки для глав V2
    const chapterUploadFunction = async (file) => {
      const formData = new FormData();
      formData.append('image', file);
      formData.append('position', 0);
      formData.append('align', 'center');
      if (chapterId) {
        formData.append('chapter_id', chapterId);
      }
      formData.append('chapter_order', chapterOrder);
      formData.append('img_index', imgCounter.current);
      formData.append('username', username);
      
      const csrfToken = getCookie('csrftoken');
      
      const res = await fetch(`/api/books/${bookId}/chapters/upload_image/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        body: formData,
        credentials: 'include',
      });
      
      if (!res.ok) throw new Error('Ошибка загрузки');
      
      const data = await res.json();
      imgCounter.current += 1;
      
      return data;
    };
    
    return <ResizableImageNodeView {...props} theme={theme} uploadImageFunction={chapterUploadFunction} />;
  };

  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.extend({
        addAttributes() {
          return {
            src: { 
              default: null,
              parseHTML: element => element.getAttribute('src'),
              renderHTML: attributes => {
                if (!attributes.src) return {};
                return { src: attributes.src };
              },
            },
            alt: { 
              default: null,
              parseHTML: element => element.getAttribute('alt'),
              renderHTML: attributes => {
                if (!attributes.alt) return {};
                return { alt: attributes.alt };
              },
            },
            title: { 
              default: null,
              parseHTML: element => element.getAttribute('title'),
              renderHTML: attributes => {
                if (!attributes.title) return {};
                return { title: attributes.title };
              },
            },
            width: { 
              default: '100',
              parseHTML: element => {
                const dataWidth = element.getAttribute('data-width');
                if (dataWidth) return dataWidth;
                
                const styleWidth = element.style.width;
                if (styleWidth) {
                  return styleWidth.replace('%', '').replace('px', '');
                }
                
                return '100';
              },
              renderHTML: attributes => {
                if (!attributes.width) return {};
                return { 
                  'data-width': attributes.width,
                  style: `width: ${attributes.width}%`
                };
              },
            },
            align: { 
              default: 'center',
              parseHTML: element => element.getAttribute('data-align') || 'center',
              renderHTML: attributes => {
                if (!attributes.align) return {};
                return { 'data-align': attributes.align };
              },
            },
            textWrap: { 
              default: 'break',
              parseHTML: element => element.getAttribute('data-text-wrap') || 'break',
              renderHTML: attributes => {
                if (!attributes.textWrap) return {};
                return { 'data-text-wrap': attributes.textWrap };
              },
            },
            caption: { 
              default: '',
              parseHTML: element => element.getAttribute('data-caption') || '',
              renderHTML: attributes => {
                if (!attributes.caption) return {};
                return { 'data-caption': attributes.caption };
              },
            },
            class: {
              default: 'align-center',
              parseHTML: element => element.getAttribute('class') || 'align-center',
              renderHTML: attributes => {
                if (!attributes.class) return {};
                return { class: attributes.class };
              },
            },
          };
        },
        group: 'block',
        atom: true,
        selectNodeOnClick: true,
        addNodeView() {
          return ReactNodeViewRenderer(ThemedResizableImageNodeView);
        },
        addCommands() {
          return {
            setImage: (options) => ({ tr, dispatch, state }) => {
              const { schema } = state;
              const imageNode = schema.nodes.image.create(options);
              const paragraphNode = schema.nodes.paragraph.create();
              
              if (dispatch) {
                const transaction = tr.replaceSelectionWith(imageNode).insert(tr.selection.to, paragraphNode);
                dispatch(transaction);
              }
              
              return true;
            },
          };
        },
      }),
      Underline,
      Link,
      TextAlign.configure({ types: ['heading', 'paragraph'] }),
      CustomTextStyle,
      Color,
      FontSize,
      BackgroundColor,
      ParagraphSymbols,
    ],
    content: initialContent,
    onUpdate: ({ editor }) => {
      // Сохраняем текущую позицию курсора
      const { state } = editor;
      const { selection } = state;
      const pos = selection.$anchor.pos;

      // Если позиция курсора изменилась и это не было намеренным действием пользователя
      if (pos !== selection.$anchor.pos && !editor.isFocused) {
        // Создаем новую транзакцию для восстановления позиции курсора
        const tr = state.tr.setSelection(state.selection.constructor.create(state.doc, pos, pos));
        editor.view.dispatch(tr);
      }
    },
    editorProps: {
      handleDrop(view, event, _slice, moved) {
        if (moved) return false;
        const files = Array.from(event.dataTransfer.files || []);
        if (files.length > 0 && files[0].type.startsWith('image/')) {
          event.preventDefault();
          uploadImage(files[0]);
          return true;
        }
        return false;
      },
      handleClick(view, pos, event) {
        const a = event.target.closest('a');
        if (a && view.editable) {
          event.preventDefault();
          const { href, target } = a;
          const from = view.posAtDOM(a, 0);
          openLinkModal({
            href,
            blank: target === '_blank',
            from,
            to: from + a.textContent.length,
          });
          return true;
        }
        return false;
      },
      handleDoubleClick(view, pos, event) {
        const { state } = view;
        const { doc, schema } = state;
        const clickPos = doc.resolve(pos);
        
        const isEmptyArea = (resolvedPos) => {
          const node = resolvedPos.nodeAfter;
          const parent = resolvedPos.parent;
          
          return (
            (parent.type.name === 'paragraph' && parent.content.size === 0) ||
            (!node && parent.type.name === 'doc') ||
            (!node && resolvedPos.parentOffset === parent.content.size)
          );
        };
        
        if (isEmptyArea(clickPos)) {
          const paragraph = schema.nodes.paragraph.create();
          let insertPos;
          
          if (clickPos.parent.type.name === 'paragraph' && clickPos.parent.content.size === 0) {
            insertPos = clickPos.after(clickPos.depth);
          } else {
            insertPos = pos;
          }
          
          const transaction = state.tr.insert(insertPos, paragraph);
          view.dispatch(transaction);
          
          setTimeout(() => {
            const newState = view.state;
            const newPos = insertPos + 1;
            if (newPos <= newState.doc.content.size) {
              const newSelection = newState.selection.constructor.create(newState.doc, newPos);
              view.dispatch(newState.tr.setSelection(newSelection));
              view.focus();
            }
          }, 10);
          
          return true;
        }
        
        return false;
      },
    },
  });

  // Обновляем контент только при первой загрузке
  useEffect(() => {
    if (editor && initialContent && !editor.getHTML()) {
      editor.commands.setContent(initialContent);
    }
  }, [editor, initialContent]);

  // Синхронизация состояния extension с React state
  useEffect(() => {
    if (editor && editor.extensionManager.extensions.find(ext => ext.name === 'paragraphSymbols')) {
      const storage = editor.extensionStorage.paragraphSymbols;
      if (storage && storage.visible !== showParagraphSymbols) {
        if (showParagraphSymbols) {
          editor.commands.showParagraphSymbols();
        } else {
          editor.commands.hideParagraphSymbols();
        }
      }
    }
  }, [showParagraphSymbols, editor]);

  // Применяем автоматическую красную строку только при первой загрузке
  useEffect(() => {
    if (!editor || !autoIndent) return;

    const content = editor.getHTML();
    if (!content) return;

    const paragraphs = content.match(/<p[^>]*>.*?<\/p>/g) || [];
    if (paragraphs.some(p => !p.includes('text-indent'))) {
      const newContent = content.replace(/<p([^>]*)>/g, '<p$1 style="text-indent: 1.5em;">');
      editor.commands.setContent(newContent);
    }
  }, [editor, autoIndent]);

  useEffect(() => {
    if (editor) {
      window.tiptapEditor = editor;
      
      // Создаем глобальную функцию для обратной совместимости
      window.uploadImageFunction = async (file) => {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('position', 0);
        formData.append('align', 'center');
        if (chapterId) {
          formData.append('chapter_id', chapterId);
        }
        formData.append('chapter_order', chapterOrder);
        formData.append('img_index', imgCounter.current);
        formData.append('username', username);
        
        message.loading({ content: 'Загрузка изображения...', key: 'imageUpload' });
        
        const csrfToken = getCookie('csrftoken');
        
        const res = await fetch(`/api/books/${bookId}/chapters/upload_image/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': csrfToken,
          },
          body: formData,
          credentials: 'include',
        });
        
        if (!res.ok) throw new Error('Ошибка загрузки');
        
        const data = await res.json();
        imgCounter.current += 1;
        
        return data;
      };
    }
  }, [editor, bookId, chapterOrder, username, chapterId]);

  useEffect(() => {
    if (chapterId) {
      setContentLoading(true);
      const csrfToken = getCookie('csrftoken');
      fetch(`/api/books/${bookId}/chapters/${chapterId}/`, {
        headers: { 'X-CSRFToken': csrfToken },
        credentials: 'include',
      })
        .then(res => res.json())
        .then(data => {
          setTitle(data.title);
          const htmlContent = data.content || '';
          const paths = extractImagePathsFromHtml(htmlContent);
          if (paths.length === 0) {
            editor.commands.setContent(htmlContent);
            return;
          }
          fetchPresignedUrls(paths).then(urlMap => {
            setPresignedUrlMap(urlMap);
            const newHtml = replaceImageSrcInHtml(htmlContent, urlMap);
            editor.commands.setContent(newHtml);
          }).catch(() => {
            editor.commands.setContent(htmlContent);
          });
        })
        .catch(err => {
          message.error('Ошибка загрузки главы: ' + err.message);
        })
        .finally(() => setContentLoading(false));
    } else {
      setTitle(initialTitle);
      if (editor) {
        editor.commands.setContent('');
      }
    }
  }, [chapterId, editor, bookId, initialTitle]);

  const uploadImage = async (file) => {
    if (!bookId) {
      message.error('Не удалось загрузить изображение: не указан ID книги');
      return;
    }
    
    const formData = new FormData();
    formData.append('image', file);
    formData.append('position', 0);
    formData.append('align', 'center');
    // Важно передавать ID главы для всех редакторов
    if (chapterId) {
      formData.append('chapter_id', chapterId);
    }
    formData.append('chapter_order', chapterOrder);
    formData.append('img_index', imgCounter.current);
    formData.append('username', username);
    
    message.loading({ content: 'Загрузка изображения...', key: 'imageUpload' });
    
    const csrfToken = getCookie('csrftoken');
    
    try {
      const res = await fetch(`/api/books/${bookId}/chapters/upload_image/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        body: formData,
        credentials: 'include',
      });
      
      if (!res.ok) throw new Error('Ошибка загрузки');
      
      const data = await res.json();
      
      if (data.url) {
        const width = data.width ? parseInt(data.width, 10) : 400;
        
        if (editor) {
          // Сначала фокусируемся на редакторе
          editor.commands.focus();
          
          // Ждём небольшую задержку для восстановления фокуса
          setTimeout(() => {
            // Вставляем изображение
            const success = editor.chain().focus().setImage({ 
              src: data.url, 
              width: width.toString(),
              align: 'center',
              textWrap: 'break',
              caption: '',
              class: 'align-center'
            }).run();
            
            if (success) {
              // Увеличиваем счетчик изображений только после успешной вставки
              imgCounter.current += 1;
              
              // Добавляем загруженное изображение в presignedUrlMap для корректного сохранения
              if (data.path) {
                setPresignedUrlMap(prevMap => ({
                  ...prevMap,
                  [data.path]: data.url
                }));
              }
              
              message.success({ content: 'Изображение успешно загружено', key: 'imageUpload' });
            } else {
              console.error('Не удалось вставить изображение - команда не выполнилась');
              message.error({ content: 'Ошибка вставки изображения в редактор', key: 'imageUpload' });
            }
          }, 50);
        } else {
          console.error('Редактор не инициализирован!');
          message.warning({ content: 'Редактор еще не готов. Попробуйте еще раз через секунду.', key: 'imageUpload' });
          console.warn('Редактор еще не смонтирован, вставка невозможна!');
        }
      } else {
        console.error('URL изображения отсутствует в ответе:', data);
        message.error({ content: 'Ошибка: не получен url изображения', key: 'imageUpload' });
      }
    } catch (e) {
      console.error('Error uploading image:', e);
      message.error({ content: 'Ошибка загрузки изображения', key: 'imageUpload' });
    }
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    // Проверяем размер файла (максимум 10MB)
    const maxFileSize = 10 * 1024 * 1024; // 10MB в байтах
    if (file.size > maxFileSize) {
      message.error('Размер файла превышает 10MB');
      e.target.value = '';
      return;
    }
    
    // Проверяем формат файла (поддерживаемые растровые форматы)
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/bmp', 'image/tiff', 'image/gif'];
    if (!allowedTypes.includes(file.type.toLowerCase())) {
      message.error('Неподдерживаемый формат файла. Поддерживаются: JPEG, PNG, WebP, BMP, TIFF, GIF');
      e.target.value = '';
      return;
    }
    
    // Сразу загружаем файл, сервер сам обработает изображение
    uploadImage(file);
    
    // Очищаем input для возможности повторной загрузки того же файла
    e.target.value = '';
  };

  async function resizeImageFile(file, maxSize) {
    return new Promise((resolve, reject) => {
      const img = new window.Image();
      const url = URL.createObjectURL(file);
      img.onload = () => {
        let { width, height } = img;
        if (Math.max(width, height) <= maxSize) {
          URL.revokeObjectURL(url);
          resolve(file);
          return;
        }
        let newW, newH;
        if (width > height) {
          newW = maxSize;
          newH = Math.round(height * maxSize / width);
        } else {
          newH = maxSize;
          newW = Math.round(width * maxSize / height);
        }
        const canvas = document.createElement('canvas');
        canvas.width = newW;
        canvas.height = newH;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, newW, newH);
        canvas.toBlob(blob => {
          if (blob) {
            const newFile = new File([blob], file.name, { type: 'image/jpeg' });
            resolve(newFile);
          } else {
            reject(new Error('Canvas toBlob failed'));
          }
        }, 'image/jpeg', 0.92);
        URL.revokeObjectURL(url);
      };
      img.onerror = reject;
      img.src = url;
    });
  }

  // Функция разделения главы
  const handleSplitChapter = () => {
    if (!editor || editor.state.selection.empty) {
      message.warning({
        content: 'Выделите заголовок новой главы для разделения',
        duration: 4,
      });
      return;
    }

    const { state } = editor;
    const { selection } = state;
    const { from, to } = selection;

    // Получаем выделенный текст как заголовок новой главы
    const selectedText = state.doc.textBetween(from, to, ' ');

    if (!selectedText.trim()) {
      message.warning({
        content: 'Выделенный текст не может быть пустым',
        duration: 4,
      });
      return;
    }

    // Проверяем минимальную длину заголовка
    if (selectedText.trim().length < 2) {
      message.warning({
        content: 'Заголовок новой главы должен содержать минимум 2 символа',
        duration: 4,
      });
      return;
    }

    // Получаем весь HTML контент
    const fullContent = editor.getHTML();

    // Проверяем, что есть контент для разделения
    if (!fullContent || fullContent.trim().length < 10) {
      message.warning({
        content: 'Недостаточно контента для разделения главы',
        duration: 4,
      });
      return;
    }

    // Разделяем контент на две части
    const { beforeContent, afterContent } = splitContentAtSelection(fullContent, from, to, state.doc);

    console.log('Split result - before:', beforeContent);
    console.log('Split result - after:', afterContent);

    // Проверяем, что разделение прошло успешно
    // Допускаем случай, когда beforeContent пустой (заголовок в самом начале)
    if (beforeContent === null || afterContent === null) {
      message.error({
        content: 'Не удалось разделить контент. Попробуйте выделить текст по-другому.',
        duration: 5,
      });
      return;
    }

    // Проверяем, что обе части содержат достаточно контента
    const beforeTextLength = beforeContent ? beforeContent.replace(/<[^>]*>/g, '').trim().length : 0;
    const afterTextLength = afterContent ? afterContent.replace(/<[^>]*>/g, '').trim().length : 0;

    console.log('Content lengths - before:', beforeTextLength, 'after:', afterTextLength);

    // Разрешаем пустую первую часть (заголовок в самом начале главы)
    if (beforeTextLength === 0 && afterTextLength === 0) {
      message.warning({
        content: 'Не удалось определить содержимое для разделения. Попробуйте выделить текст по-другому.',
        duration: 5,
      });
      return;
    }

    if (afterTextLength < 1) {
      message.warning({
        content: 'В новой главе не будет текста. Выберите другое место для разделения.',
        duration: 5,
      });
      return;
    }

    // Форматируем заголовок для предварительного просмотра
    const previewTitle = formatChapterTitle(selectedText);
    const currentChapterOrder = chapterOrder || 1;
    const newChapterOrder = currentChapterOrder + 1;
    const newChapterFullTitle = `Глава ${newChapterOrder}: ${previewTitle}`;

    console.log('=== CHAPTER SPLIT MODAL ===');
    console.log('chapterOrder prop:', chapterOrder);
    console.log('currentChapterOrder:', currentChapterOrder);
    console.log('newChapterOrder:', newChapterOrder);
    console.log('newChapterFullTitle:', newChapterFullTitle);

    // Показываем подтверждение
    Modal.confirm({
      title: 'Разделить главу',
      width: 500,
      content: (
        <div style={{ color: theme === 'dark' ? '#fff' : '#222' }}>
          <p>Вы уверены, что хотите разделить главу?</p>
          <div style={{
            background: theme === 'dark' ? '#1f2937' : '#f8f9fa',
            padding: '12px',
            borderRadius: '6px',
            margin: '12px 0'
          }}>
            <p style={{ margin: '0 0 8px 0', fontWeight: 'bold' }}>
              Новая глава: "{newChapterFullTitle}"
            </p>
            <p style={{ margin: '0 0 4px 0', fontSize: '14px', opacity: 0.8 }}>
              Текст выше выделения ({beforeTextLength} символов) останется в текущей главе
            </p>
            <p style={{ margin: '0 0 4px 0', fontSize: '14px', opacity: 0.8 }}>
              Текст ниже выделения ({afterTextLength} символов) перейдет в новую главу
            </p>
            <p style={{ margin: '0 0 8px 0', fontSize: '13px', color: theme === 'dark' ? '#f59e0b' : '#d97706' }}>
              ⚠️ Строка с заголовком будет удалена из текущей главы
            </p>
            <p style={{ margin: '0', fontSize: '13px', color: theme === 'dark' ? '#60a5fa' : '#2563eb' }}>
              📋 Все последующие главы будут автоматически перенумерованы
            </p>
          </div>
          <p style={{ fontSize: '14px', color: theme === 'dark' ? '#fbbf24' : '#d97706' }}>
            ⚠️ Это действие нельзя отменить. Убедитесь, что выбрали правильное место для разделения.
          </p>
        </div>
      ),
      okText: 'Разделить главу',
      cancelText: 'Отмена',
      okType: 'primary',
      className: theme === 'dark' ? 'dark-modal' : '',
      styles: {
        content: {
          background: theme === 'dark' ? '#23272f' : '#ffffff',
        },
      },
      onOk: () => {
        performChapterSplit(beforeContent, afterContent, selectedText);
      },
    });
  };

  // Функция для разделения HTML контента
  const splitContentAtSelection = (htmlContent, from, to, doc) => {
    try {
      console.log('=== SPLITTING CONTENT ===');
      console.log('Selection from:', from, 'to:', to);

      // Получаем текст до, выделенный и после
      const beforeText = doc.textBetween(0, from, '\n');
      const selectedText = doc.textBetween(from, to, '\n');
      const afterText = doc.textBetween(to, doc.content.size, '\n');

      console.log('Before text:', JSON.stringify(beforeText));
      console.log('Selected text:', JSON.stringify(selectedText));
      console.log('After text:', JSON.stringify(afterText));

      // Используем улучшенный алгоритм разделения
      return splitContentByImprovedLogic(htmlContent, beforeText, selectedText, afterText);

    } catch (error) {
      console.error('Error splitting content:', error);
      // Fallback к простому разделению
      const beforeText = doc.textBetween(0, from, '\n');
      const selectedText = doc.textBetween(from, to, '\n');
      const afterText = doc.textBetween(to, doc.content.size, '\n');
      return splitContentByImprovedLogic(htmlContent, beforeText, selectedText, afterText);
    }
  };

  // Улучшенный алгоритм разделения контента
  const splitContentByImprovedLogic = (htmlContent, beforeText, selectedText, afterText) => {
    console.log('=== IMPROVED SPLIT LOGIC ===');
    console.log('HTML content:', htmlContent);

    // Используем более точный подход - разделение по HTML структуре
    return splitContentByHtmlStructure(htmlContent, selectedText);
  };

  // Разделение контента по HTML структуре
  const splitContentByHtmlStructure = (htmlContent, selectedText) => {
    console.log('=== HTML STRUCTURE SPLIT ===');
    console.log('Selected text to find:', JSON.stringify(selectedText));

    // Создаем временный div для работы с HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;

    // Ищем элемент, содержащий выделенный текст
    const elementWithSelection = findElementContainingText(tempDiv, selectedText);

    if (!elementWithSelection) {
      console.error('Could not find element containing selected text');
      return { beforeContent: htmlContent, afterContent: '' };
    }

    console.log('Found element:', elementWithSelection.element);
    console.log('Element tag:', elementWithSelection.element.tagName);
    console.log('Element text:', elementWithSelection.element.textContent);

    // Получаем все элементы контейнера
    const allElements = Array.from(tempDiv.children);
    console.log('All elements count:', allElements.length);

    // Находим индекс элемента с выделенным текстом
    const targetIndex = allElements.indexOf(elementWithSelection.element);
    console.log('Target element index:', targetIndex);

    if (targetIndex === -1) {
      console.error('Could not find target element in children');
      return { beforeContent: htmlContent, afterContent: '' };
    }

    // Разделяем элементы:
    // 1. Элементы до целевого элемента
    const beforeElements = allElements.slice(0, targetIndex);
    // 2. Элементы после целевого элемента
    const afterElements = allElements.slice(targetIndex + 1);

    console.log('Before elements count:', beforeElements.length);
    console.log('After elements count:', afterElements.length);

    // Создаем HTML для каждой части
    const beforeHtml = beforeElements.map(el => el.outerHTML).join('');
    const afterHtml = afterElements.map(el => el.outerHTML).join('');

    console.log('Before HTML:', beforeHtml);
    console.log('After HTML:', afterHtml);

    return {
      beforeContent: beforeHtml.trim(),
      afterContent: afterHtml.trim()
    };
  };

  // Функция поиска элемента, содержащего указанный текст
  const findElementContainingText = (container, targetText) => {
    console.log('Searching for text in container:', targetText);

    // Проходим по всем дочерним элементам
    const walker = document.createTreeWalker(
      container,
      NodeFilter.SHOW_ELEMENT,
      null,
      false
    );

    let currentElement;
    while (currentElement = walker.nextNode()) {
      const elementText = currentElement.textContent || '';
      console.log(`Checking element ${currentElement.tagName}: "${elementText}"`);

      if (elementText.includes(targetText)) {
        console.log('Found matching element!');
        return { element: currentElement };
      }
    }

    // Если не нашли в дочерних элементах, проверяем прямые дети
    for (const child of container.children) {
      const childText = child.textContent || '';
      console.log(`Checking direct child ${child.tagName}: "${childText}"`);

      if (childText.includes(targetText)) {
        console.log('Found matching direct child!');
        return { element: child };
      }
    }

    console.log('No element found containing the text');
    return null;
  };

  // Функция преобразования текста обратно в HTML с сохранением форматирования
  const convertTextToHtml = (targetText, originalHtml) => {
    if (!targetText || !targetText.trim()) {
      console.log('Empty text, returning empty string');
      return '';
    }

    console.log('Converting text to HTML:', JSON.stringify(targetText));

    // Разделяем на абзацы по переносам строк
    const lines = targetText.split('\n');
    const paragraphs = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line) {
        paragraphs.push(line);
      }
    }

    console.log('Paragraphs:', paragraphs);

    if (paragraphs.length === 0) {
      console.log('No paragraphs found, returning empty string');
      return '';
    }

    // Если только один абзац, возвращаем его в <p>
    if (paragraphs.length === 1) {
      const result = `<p>${paragraphs[0]}</p>`;
      console.log('Single paragraph result:', result);
      return result;
    }

    // Несколько абзацев
    const result = paragraphs.map(p => `<p>${p}</p>`).join('');
    console.log('Multiple paragraphs result:', result);
    return result;
  };





  // Функция форматирования заголовка главы
  const formatChapterTitle = (rawTitle) => {
    if (!rawTitle) return 'Новая глава';

    // Удаляем HTML теги
    let cleanTitle = rawTitle.replace(/<[^>]*>/g, '');

    // Удаляем лишние пробелы и переносы строк
    cleanTitle = cleanTitle.replace(/\s+/g, ' ').trim();

    // Убираем автоматическую нумерацию "Глава X" из заголовка, если она есть
    // Паттерны: "Глава 2", "Глава 2.", "Глава 2:", "Глава 2 -", "Глава II" и т.д.
    cleanTitle = cleanTitle.replace(/^(Глава\s+\d+[\.\:\-\s]*|Глава\s+[IVX]+[\.\:\-\s]*)/i, '');

    // Удаляем специальные символы в начале и конце
    cleanTitle = cleanTitle.replace(/^[^\w\u0400-\u04FF]+|[^\w\u0400-\u04FF]+$/g, '');

    // Ограничиваем длину заголовка
    const maxLength = 50;
    if (cleanTitle.length > maxLength) {
      cleanTitle = cleanTitle.substring(0, maxLength).trim();
      // Обрезаем по последнему пробелу, чтобы не разрывать слова
      const lastSpaceIndex = cleanTitle.lastIndexOf(' ');
      if (lastSpaceIndex > maxLength * 0.7) {
        cleanTitle = cleanTitle.substring(0, lastSpaceIndex);
      }
      cleanTitle += '...';
    }

    // Приводим к правильному регистру (первая буква заглавная)
    if (cleanTitle.length > 0) {
      cleanTitle = cleanTitle.charAt(0).toUpperCase() + cleanTitle.slice(1).toLowerCase();
    }

    // Если после очистки ничего не осталось, возвращаем дефолтное название
    if (!cleanTitle || cleanTitle.length < 2) {
      return 'Новая глава';
    }

    return cleanTitle;
  };

  // Функция выполнения разделения главы
  const performChapterSplit = async (beforeContent, afterContent, selectedTitle) => {
    const loadingMessage = message.loading({
      content: 'Разделение главы...',
      duration: 0, // Не скрывать автоматически
    });

    try {
      // Получаем информацию о текущей главе
      const currentChapterOrder = chapterOrder || 1;
      const newChapterOrder = currentChapterOrder + 1;

      // Формируем заголовок новой главы
      const cleanTitle = formatChapterTitle(selectedTitle);
      const newChapterTitle = `Глава ${newChapterOrder}: ${cleanTitle}`;

      // 1. Сначала сдвигаем все главы после текущей вверх по номерам
      console.log(`Starting chapter split. Current order: ${currentChapterOrder}, new order will be: ${newChapterOrder}`);

      const shiftResult = await shiftChaptersUpWithInfo(currentChapterOrder);

      if (!shiftResult.success) {
        message.error({
          content: 'Не удалось подготовить место для новой главы. Возможно, есть конфликт номеров глав. Попробуйте обновить страницу и повторить.',
          duration: 8,
        });
        return;
      }

      console.log('Chapters shifted successfully, creating new chapter...');
      const shiftedChapters = shiftResult.shiftedChapters;

      // 2. Создаем новую главу
      loadingMessage();
      const createLoadingMessage = message.loading({
        content: 'Создание новой главы...',
        duration: 0,
      });

      const newChapterData = await createNewChapter(newChapterTitle, afterContent, newChapterOrder);
      createLoadingMessage();

      if (!newChapterData) {
        // Откатываем сдвиг глав
        console.log('Rolling back chapter shifts due to failed chapter creation');
        await shiftChaptersDown(currentChapterOrder);
        message.error({
          content: 'Не удалось создать новую главу. Проверьте подключение к интернету и попробуйте снова.',
          duration: 6,
        });
        return;
      }

      // 3. Обновляем текущую главу с содержимым "до разделения"
      const updateLoadingMessage = message.loading({
        content: 'Обновление текущей главы...',
        duration: 0,
      });

      const updatedCurrentChapter = await updateCurrentChapter(beforeContent);
      updateLoadingMessage();

      if (!updatedCurrentChapter) {
        message.error({
          content: 'Не удалось обновить текущую главу. Новая глава создана, но содержимое текущей главы не изменено.',
          duration: 8,
        });
        return;
      }

      // Успешное завершение
      message.success({
        content: (
          <div>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
              Глава успешно разделена!
            </div>
            <div style={{ fontSize: '13px' }}>
              Создана новая глава: "{newChapterTitle}"
            </div>
          </div>
        ),
        duration: 6,
      });

      // Обновляем редактор с новым содержимым
      if (editor) {
        editor.commands.setContent(beforeContent);
        setTitle(title); // Оставляем текущий заголовок
      }

      // Уведомляем родительский компонент об изменениях
      if (onChapterSplit) {
        onChapterSplit({
          currentChapter: {
            id: chapterId,
            content: beforeContent,
            order: currentChapterOrder
          },
          newChapter: newChapterData,
          splitTitle: cleanTitle,
          // Добавляем информацию о том, что нужно обновить список глав
          needsRefresh: true,
          affectedChapters: shiftedChapters
        });
      }

    } catch (error) {
      console.error('Error performing chapter split:', error);

      // Определяем тип ошибки для более точного сообщения
      let errorMessage = 'Произошла неожиданная ошибка при разделении главы.';

      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        errorMessage = 'Ошибка подключения к серверу. Проверьте интернет-соединение.';
      } else if (error.message.includes('403') || error.message.includes('Forbidden')) {
        errorMessage = 'У вас нет прав для редактирования этой главы.';
      } else if (error.message.includes('404')) {
        errorMessage = 'Глава не найдена. Возможно, она была удалена.';
      } else if (error.message.includes('500')) {
        errorMessage = 'Ошибка сервера. Попробуйте позже.';
      }

      message.error({
        content: (
          <div>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
              Ошибка при разделении главы
            </div>
            <div style={{ fontSize: '13px' }}>
              {errorMessage}
            </div>
          </div>
        ),
        duration: 8,
      });
    } finally {
      // Убираем loading сообщение в любом случае
      loadingMessage();
    }
  };

  // Функция создания новой главы
  const createNewChapter = async (title, content, order) => {
    try {
      console.log(`Creating new chapter with order ${order} and title "${title}"`);

      const csrfToken = getCookie('csrftoken');
      const response = await fetch(`/api/books/${bookId}/chapters/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({
          title: title,
          content: content,
          order: order,
          is_published: false,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Failed to create chapter:', response.status, errorText);
        throw new Error(`Failed to create chapter: ${response.status}`);
      }

      const newChapter = await response.json();
      console.log('New chapter created successfully:', newChapter);
      return newChapter;
    } catch (error) {
      console.error('Error creating new chapter:', error);
      return null;
    }
  };

  // Функция обновления текущей главы
  const updateCurrentChapter = async (content) => {
    try {
      if (!chapterId) {
        // Если это новая глава, просто обновляем содержимое в редакторе
        return true;
      }

      const csrfToken = getCookie('csrftoken');
      const response = await fetch(`/api/books/${bookId}/chapters/${chapterId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({
          content: content,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update current chapter');
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating current chapter:', error);
      return null;
    }
  };

  // Функция сдвига глав вверх по номерам (освобождает место для новой главы)
  const shiftChaptersUp = async (currentOrder) => {
    try {
      // Получаем все главы книги
      const response = await fetch(`/api/books/${bookId}/chapters/`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch chapters');
      }

      const data = await response.json();
      console.log('Fetched chapters data:', data);
      console.log('Data type:', typeof data);
      console.log('Is array:', Array.isArray(data));
      console.log('Has results:', data && data.results);

      // API может возвращать массив или объект с массивом results
      let chapters = [];
      if (Array.isArray(data)) {
        chapters = data;
      } else if (data && Array.isArray(data.results)) {
        chapters = data.results;
      } else if (data && data.chapters && Array.isArray(data.chapters)) {
        chapters = data.chapters;
      } else {
        console.error('Unexpected data format:', data);
        throw new Error('Invalid chapters data format - expected array or object with results/chapters array');
      }

      console.log('Processed chapters:', chapters);
      console.log('Chapters count:', chapters.length);

      // Находим главы, которые нужно сдвинуть (order > currentOrder, исключая предисловие и послесловие)
      const chaptersToShift = chapters
        .filter(ch => ch.order > currentOrder && ch.order > 0 && ch.order !== 99999)
        .sort((a, b) => b.order - a.order); // ВАЖНО: сортируем в убывающем порядке!

      if (chaptersToShift.length === 0) {
        return true; // Нет глав для обновления
      }

      console.log('Chapters to shift:', chaptersToShift.map(ch => `${ch.id}: ${ch.order} -> ${ch.order + 1}`));

      const csrfToken = getCookie('csrftoken');

      // Обновляем главы в обратном порядке (от больших номеров к меньшим)
      // Это предотвращает конфликты уникальности order
      for (const chapter of chaptersToShift) {
        const newOrder = chapter.order + 1;
        const oldTitle = chapter.title;
        const newTitle = updateChapterTitleNumber(chapter.title, newOrder);

        console.log(`Updating chapter ${chapter.id}:`);
        console.log(`  Order: ${chapter.order} -> ${newOrder}`);
        console.log(`  Title: "${oldTitle}" -> "${newTitle}"`);

        const updateData = {
          order: newOrder,
          title: newTitle
        };

        console.log(`  Update data:`, updateData);

        const updateResponse = await fetch(`/api/books/${bookId}/chapters/${chapter.id}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify(updateData),
        });

        if (!updateResponse.ok) {
          const errorText = await updateResponse.text();
          console.error(`Failed to update chapter ${chapter.id}:`, errorText);
          throw new Error(`Failed to update chapter ${chapter.id}: ${updateResponse.status}`);
        }

        const updatedChapter = await updateResponse.json();
        console.log(`Successfully updated chapter ${chapter.id}:`, updatedChapter);

        // Проверяем, что обновление прошло корректно
        if (updatedChapter.order !== newOrder) {
          console.error(`Order mismatch for chapter ${chapter.id}: expected ${newOrder}, got ${updatedChapter.order}`);
        }
        if (updatedChapter.title !== newTitle) {
          console.error(`Title mismatch for chapter ${chapter.id}: expected "${newTitle}", got "${updatedChapter.title}"`);
        }
      }

      console.log('All chapters shifted successfully');
      return true;
    } catch (error) {
      console.error('Error shifting chapters up:', error);
      return false;
    }
  };

  // Функция сдвига глав с возвратом информации о сдвинутых главах
  const shiftChaptersUpWithInfo = async (currentOrder) => {
    try {
      // Получаем все главы книги
      const response = await fetch(`/api/books/${bookId}/chapters/`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch chapters');
      }

      const data = await response.json();
      console.log('Fetched chapters data:', data);

      // API может возвращать массив или объект с массивом results
      let chapters = [];
      if (Array.isArray(data)) {
        chapters = data;
      } else if (data && Array.isArray(data.results)) {
        chapters = data.results;
      } else if (data && data.chapters && Array.isArray(data.chapters)) {
        chapters = data.chapters;
      } else {
        console.error('Unexpected data format:', data);
        throw new Error('Invalid chapters data format - expected array or object with results/chapters array');
      }

      console.log('Processed chapters:', chapters);
      console.log('Chapters count:', chapters.length);

      // Находим главы, которые нужно сдвинуть (order > currentOrder, исключая предисловие и послесловие)
      const chaptersToShift = chapters
        .filter(ch => ch.order > currentOrder && ch.order > 0 && ch.order !== 99999)
        .sort((a, b) => b.order - a.order); // ВАЖНО: сортируем в убывающем порядке!

      if (chaptersToShift.length === 0) {
        return { success: true, shiftedChapters: [] }; // Нет глав для обновления
      }

      console.log('Chapters to shift:', chaptersToShift.map(ch => `${ch.id}: ${ch.order} -> ${ch.order + 1}`));

      const csrfToken = getCookie('csrftoken');
      const shiftedChaptersInfo = [];

      // Обновляем главы в обратном порядке (от больших номеров к меньшим)
      for (const chapter of chaptersToShift) {
        const newOrder = chapter.order + 1;
        const oldTitle = chapter.title;
        const newTitle = updateChapterTitleNumber(chapter.title, newOrder);

        console.log(`Updating chapter ${chapter.id}:`);
        console.log(`  Order: ${chapter.order} -> ${newOrder}`);
        console.log(`  Title: "${oldTitle}" -> "${newTitle}"`);

        const updateData = {
          order: newOrder,
          title: newTitle
        };

        const updateResponse = await fetch(`/api/books/${bookId}/chapters/${chapter.id}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify(updateData),
        });

        if (!updateResponse.ok) {
          const errorText = await updateResponse.text();
          console.error(`Failed to update chapter ${chapter.id}:`, errorText);
          throw new Error(`Failed to update chapter ${chapter.id}: ${updateResponse.status}`);
        }

        const updatedChapter = await updateResponse.json();
        console.log(`Successfully updated chapter ${chapter.id}:`, updatedChapter);

        // Сохраняем информацию о сдвинутой главе
        shiftedChaptersInfo.push({
          id: chapter.id,
          oldOrder: chapter.order,
          newOrder: newOrder,
          oldTitle: oldTitle,
          newTitle: newTitle
        });
      }

      console.log('All chapters shifted successfully with info');
      return { success: true, shiftedChapters: shiftedChaptersInfo };
    } catch (error) {
      console.error('Error shifting chapters up with info:', error);
      return { success: false, shiftedChapters: [] };
    }
  };

  // Функция сдвига глав вниз по номерам (откат при ошибке)
  const shiftChaptersDown = async (currentOrder) => {
    try {
      // Получаем все главы книги
      const response = await fetch(`/api/books/${bookId}/chapters/`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch chapters');
      }

      const data = await response.json();
      console.log('Fetched chapters data for rollback:', data);

      // API может возвращать массив или объект с массивом results
      let chapters = [];
      if (Array.isArray(data)) {
        chapters = data;
      } else if (data && Array.isArray(data.results)) {
        chapters = data.results;
      } else if (data && data.chapters && Array.isArray(data.chapters)) {
        chapters = data.chapters;
      } else {
        console.error('Unexpected data format for rollback:', data);
        return false;
      }

      // Находим главы, которые нужно сдвинуть обратно (order > currentOrder + 1)
      const chaptersToShift = chapters
        .filter(ch => ch.order > currentOrder + 1 && ch.order > 0 && ch.order !== 99999)
        .sort((a, b) => a.order - b.order); // Для отката сортируем в возрастающем порядке

      if (chaptersToShift.length === 0) {
        return true;
      }

      console.log('Rolling back chapters:', chaptersToShift.map(ch => `${ch.id}: ${ch.order} -> ${ch.order - 1}`));

      const csrfToken = getCookie('csrftoken');

      // Откатываем в прямом порядке (от меньших номеров к большим)
      for (const chapter of chaptersToShift) {
        const newOrder = chapter.order - 1;
        const newTitle = updateChapterTitleNumber(chapter.title, newOrder);

        const updateResponse = await fetch(`/api/books/${bookId}/chapters/${chapter.id}/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
          },
          credentials: 'include',
          body: JSON.stringify({
            order: newOrder,
            title: newTitle
          }),
        });

        if (!updateResponse.ok) {
          console.error(`Failed to rollback chapter ${chapter.id}`);
        }
      }

      return true;
    } catch (error) {
      console.error('Error shifting chapters down:', error);
      return false;
    }
  };

  // Функция обновления номера главы в заголовке
  const updateChapterTitleNumber = (title, newOrder) => {
    if (!title) return `Глава ${newOrder}`;

    // Если заголовок начинается с "Глава X:", заменяем номер
    const chapterPattern = /^Глава\s+\d+:\s*/i;
    if (chapterPattern.test(title)) {
      return title.replace(chapterPattern, `Глава ${newOrder}: `);
    }

    // Если это предисловие или послесловие, не трогаем
    if (title.toLowerCase().includes('предисловие') || title.toLowerCase().includes('послесловие')) {
      return title;
    }

    // Иначе добавляем номер главы
    return `Глава ${newOrder}: ${title}`;
  };



  const handleSave = () => {
    if (!title.trim()) {
      message.error('Введите название главы');
      return;
    }
    if (!editor) return;
    const htmlWithRelative = revertPresignedUrlsInHtml(editor.getHTML(), presignedUrlMap);
    
    // Если редактор в полноэкранном режиме, выходим из него
    if (isFullWidth && onToggleFullWidth) {
      onToggleFullWidth();
    }
    
    onSave({
      title: title,
      content: htmlWithRelative,
    });
  };

  const toolbarBg = theme === 'dark' ? '#23272f' : '#f3f4f6';
  const iconColor = theme === 'dark' ? '#fff' : '#222';
  const iconActiveBg = '#2563eb';
  const iconActiveColor = '#fff';

  const selectBg = theme === 'dark' ? '#23272f' : '#fff';
  const selectColor = theme === 'dark' ? '#fff' : '#222';
  const selectBorder = theme === 'dark' ? '#374151' : '#d1d5db';
  const selectOptionBg = theme === 'dark' ? '#23272f' : '#fff';
  const selectOptionHover = theme === 'dark' ? '#374151' : '#e5e7eb';
  const selectOptionColor = theme === 'dark' ? '#fff' : '#222';

  const renderTooltip = (title, shortcut) => (
    <div className="tooltip-content">
      <div>{title}</div>
      {shortcut && <div className="editor-shortcut">{shortcut}</div>}
    </div>
  );

  // Функция для расчета размера значков
  const getIconSize = () => {
    if (iconSizeMode === 'custom') {
      return customIconSize;
    }

    if (iconSizeMode === 'auto' && toolbarRef.current) {
      const toolbarWidth = toolbarRef.current.offsetWidth;
      if (toolbarWidth < 600) return 12;
      if (toolbarWidth < 900) return 14;
      return 16;
    }

    const sizeMap = {
      small: 12,
      medium: 14,
      large: 16
    };

    return sizeMap[iconSizeMode] || 16;
  };

  // Сохранение настроек в localStorage
  useEffect(() => {
    localStorage.setItem('editor-icon-size-mode', iconSizeMode);
  }, [iconSizeMode]);

  useEffect(() => {
    localStorage.setItem('editor-custom-icon-size', customIconSize.toString());
  }, [customIconSize]);

  // Пересчет размера при изменении ширины окна для автоматического режима
  useEffect(() => {
    if (iconSizeMode === 'auto') {
      const handleResize = () => {
        // Принудительно обновляем компонент для пересчета размера
        setShowSizeSettings(prev => prev);
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [iconSizeMode]);

  // Добавляю функцию для обработки выравнивания текста
  const handleTextAlign = (alignment) => {
    // Если текст уже имеет это выравнивание, сбрасываем на левое выравнивание (по умолчанию)
    if (editor.isActive({ textAlign: alignment })) {
      editor.chain().focus().setTextAlign('left').run();
    } else {
      editor.chain().focus().setTextAlign(alignment).run();
    }
  };

  // Компонент настроек размера значков
  const ToolbarSizeSettings = () => {
    const currentIconSize = getIconSize();

    const sizeOptions = [
      { label: 'Маленький', value: 'small' },
      { label: 'Средний', value: 'medium' },
      { label: 'Большой', value: 'large' },
      { label: 'Авто', value: 'auto' },
      { label: 'Пользовательский', value: 'custom' }
    ];

    const handleSizeChange = (e) => {
      setIconSizeMode(e.target.value);
    };

    const handleCustomSizeChange = (value) => {
      if (value && value >= 10 && value <= 32) {
        setCustomIconSize(value);
      }
    };

    return (
      <Modal
        open={showSizeSettings}
        onCancel={() => setShowSizeSettings(false)}
        footer={null}
        width={400}
        className={theme === 'dark' ? 'dark-modal' : ''}
        styles={{
          content: {
            background: theme === 'dark' ? '#23272f' : '#ffffff',
            borderRadius: '12px',
            padding: '24px',
          },
          mask: {
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        <div className="mb-6">
          <h3 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Настройки размера значков панели
          </h3>
          <p className={`mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Текущий размер значков: <strong className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>{currentIconSize}px</strong>
          </p>
        </div>
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8, fontWeight: 500, color: theme === 'dark' ? '#fff' : '#222' }}>
            Выберите размер:
          </div>
          <Radio.Group
            value={iconSizeMode}
            onChange={handleSizeChange}
            style={{ width: '100%' }}
          >
            {sizeOptions.map(option => (
              <Radio
                key={option.value}
                value={option.value}
                style={{
                  display: 'block',
                  marginBottom: 8,
                  color: theme === 'dark' ? '#fff' : '#222'
                }}
              >
                {option.label}
              </Radio>
            ))}
          </Radio.Group>
        </div>

        {iconSizeMode === 'custom' && (
          <div style={{ marginBottom: 16 }}>
            <div style={{ marginBottom: 8, fontWeight: 500, color: theme === 'dark' ? '#fff' : '#222' }}>
              Пользовательский размер (10-32px):
            </div>
            <InputNumber
              min={10}
              max={32}
              value={customIconSize}
              onChange={handleCustomSizeChange}
              style={{
                width: '100%',
                ...(theme === 'dark' ? {
                  borderColor: '#4b5563',
                  color: '#fff',
                  background: '#23272f'
                } : {})
              }}
              addonAfter="px"
            />
          </div>
        )}

        <div className="mb-6">
          <div className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            <strong>Размеры значков:</strong>
          </div>
          <ul className={`list-disc list-inside text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} space-y-1 mt-2`}>
            <li>Маленький: 12px</li>
            <li>Средний: 14px</li>
            <li>Большой: 16px</li>
            <li>Авто: размер зависит от ширины панели</li>
          </ul>
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            onClick={() => setShowSizeSettings(false)}
            className={theme === 'dark' ? 'bg-[#23272f] text-white border-[#4b5563] hover:bg-[#374151]' : 'bg-white text-gray-900 border-gray-300 hover:bg-gray-100'}
            style={theme === 'dark' ? { borderColor: '#4b5563', color: '#fff', background: '#23272f' } : {}}
          >
            Закрыть
          </Button>
        </div>
      </Modal>
    );
  };

  // Создаем специальный компонент для выпадающего меню, который не теряет выделение
  const FontSizeSelector = () => {
    const [isOpen, setIsOpen] = useState(false);
    const currentSize = editor.getAttributes('textStyle').fontSize || '16px';
    const sizeOptions = [
      { value: '16px', label: 'Обычный' },
      { value: '13px', label: 'Мелкий' },
      { value: '20px', label: 'Крупный' },
      { value: '28px', label: 'Заголовок' },
    ];
    
    const handleSizeChange = (value) => {
      // Получаем текущие атрибуты textStyle
      const { color, backgroundColor, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus().setMark('textStyle', {
        color,
        backgroundColor,
        fontSize: value,
      }).run();
      setIsOpen(false);
    };
    
    const toggleDropdown = (e) => {
      e.preventDefault(); // Важно! Предотвращает потерю фокуса и выделения
      setIsOpen(!isOpen);
    };
    
    const handleOptionMouseDown = (e, value) => {
      e.preventDefault(); // Предотвращает потерю выделения
      handleSizeChange(value);
    };
    
    return (
      <div className="relative inline-block" style={{ marginLeft: 4, marginRight: 4 }}>
        <button
          onMouseDown={toggleDropdown}
          className="flex items-center justify-between px-2 py-1 border rounded"
          style={{
            background: theme === 'dark' ? '#1f2937' : '#ffffff',
            color: theme === 'dark' ? '#ffffff' : '#000000',
            borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
            minWidth: '90px',
            height: '32px',
            fontSize: '14px',
          }}
        >
          <span>{sizeOptions.find(opt => opt.value === currentSize)?.label || 'Обычный'}</span>
          <span style={{ marginLeft: '4px' }}>▼</span>
        </button>
        
        {isOpen && (
          <div
            className="absolute left-0 mt-1 border rounded shadow-lg z-10"
            style={{
              background: theme === 'dark' ? '#1f2937' : '#ffffff',
              borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
              minWidth: '90px',
              zIndex: 1000,
            }}
          >
            {sizeOptions.map(option => (
              <div
                key={option.value}
                onMouseDown={(e) => handleOptionMouseDown(e, option.value)}
                className="px-3 py-1 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                style={{
                  background: currentSize === option.value 
                    ? (theme === 'dark' ? '#374151' : '#f3f4f6') 
                    : 'transparent',
                  color: theme === 'dark' ? '#ffffff' : '#000000',
                  fontSize: option.value,
                }}
              >
                {option.label}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const Toolbar = () => editor && (
    <div
      ref={toolbarRef}
      className="flex flex-wrap gap-1 mb-2 rounded px-2 py-1 items-center"
      style={{
        background: toolbarBg,
        border: '1.5px solid #e5e7eb',
        minHeight: 40,
        justifyContent: 'flex-start',
      }}
    >
      <div className="flex items-center gap-1">
        <Tooltip 
          title={renderTooltip("Жирный", "Ctrl+B")}
          placement="bottom"
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={e => { e.preventDefault(); editor.chain().focus().toggleBold().run(); }}
            className={`px-2 py-1 rounded ${editor.isActive('bold') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('bold') ? iconActiveBg : 'transparent',
              color: editor.isActive('bold') ? iconActiveColor : iconColor,
              fontSize: `${getIconSize()}px`,
            }}
            type="button"
          >
            <span style={{ fontWeight: 'bold' }}>Ж</span>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("Курсив", "Ctrl+I")}
          placement="bottom"
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={e => { e.preventDefault(); editor.chain().focus().toggleItalic().run(); }}
            className={`px-2 py-1 rounded ${editor.isActive('italic') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('italic') ? iconActiveBg : 'transparent',
              color: editor.isActive('italic') ? iconActiveColor : iconColor,
              fontSize: `${getIconSize()}px`,
            }}
            type="button"
          >
            <span style={{ fontStyle: 'italic' }}>К</span>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("Подчёркнутый", "Ctrl+U")}
          placement="bottom"
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={e => { e.preventDefault(); editor.chain().focus().toggleUnderline().run(); }}
            className={`px-2 py-1 rounded ${editor.isActive('underline') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('underline') ? iconActiveBg : 'transparent',
              color: editor.isActive('underline') ? iconActiveColor : iconColor,
              fontSize: `${getIconSize()}px`,
            }}
            type="button"
          >
            <span style={{ textDecoration: 'underline' }}>П</span>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("Зачёркнутый", null)}
          placement="bottom"
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={e => { e.preventDefault(); editor.chain().focus().toggleStrike().run(); }}
            className={`px-2 py-1 rounded ${editor.isActive('strike') ? 'bg-blue-600 text-white' : ''}`}
            style={{
              background: editor.isActive('strike') ? iconActiveBg : 'transparent',
              color: editor.isActive('strike') ? iconActiveColor : iconColor,
              fontSize: `${getIconSize()}px`,
            }}
            type="button"
          >
            <span style={{ textDecoration: 'line-through' }}>З</span>
          </button>
        </Tooltip>
      </div>
      <FontSizeSelector />
      <CustomColorDropdown editor={editor} theme={theme} getIconSize={getIconSize} />

      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

      {/* Кнопки отмены/повтора */}
      <div className="flex items-center gap-1">
        <Tooltip 
          title={renderTooltip("Отменить", "Ctrl+Z")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().undo().run();
            }}
            className="px-2 py-1 rounded"
            style={{
              background: 'transparent',
              color: iconColor,
              opacity: editor.can().undo() ? 1 : 0.5,
              cursor: editor.can().undo() ? 'pointer' : 'default',
            }}
            disabled={!editor.can().undo()}
          >
            <svg width={getIconSize()} height={getIconSize()} viewBox="0 0 18 18" fill="none">
              <path d="M7 15L2 10L7 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2 10H11C13.2091 10 15 11.7909 15 14V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("Повторить", "Ctrl+Y")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().redo().run();
            }}
            className="px-2 py-1 rounded"
            style={{
              background: 'transparent',
              color: iconColor,
              opacity: editor.can().redo() ? 1 : 0.5,
              cursor: editor.can().redo() ? 'pointer' : 'default',
            }}
            disabled={!editor.can().redo()}
          >
            <svg width={getIconSize()} height={getIconSize()} viewBox="0 0 18 18" fill="none">
              <path d="M11 5L16 10L11 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M16 10H7C4.79086 10 3 11.7909 3 14V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </button>
        </Tooltip>
      </div>

      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

      {/* Кнопки выравнивания текста */}
      <div className="flex items-center gap-1">
        <Tooltip 
          title={renderTooltip("По левому краю", "Ctrl+Shift+L")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('left');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'left' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'left' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width={getIconSize()} height={getIconSize()} fill="currentColor" viewBox="0 0 16 16">
              <path d="M2 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("По центру", "Ctrl+Shift+E")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('center');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'center' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'center' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width={getIconSize()} height={getIconSize()} fill="currentColor" viewBox="0 0 16 16">
              <path d="M4 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm2-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("По правому краю", "Ctrl+Shift+R")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('right');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'right' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'right' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width={getIconSize()} height={getIconSize()} fill="currentColor" viewBox="0 0 16 16">
              <path d="M6 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-4-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm4-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-4-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={renderTooltip("По ширине", "Ctrl+Shift+J")} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('justify');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'justify' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'justify' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width={getIconSize()} height={getIconSize()} fill="currentColor" viewBox="0 0 16 16">
              <path d="M2 12.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
      </div>

      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

      {/* Кнопка отображения символов параграфа */}
      <Tooltip 
        title={renderTooltip(showParagraphSymbols ? "Скрыть символы параграфа" : "Показать символы параграфа")} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            if (editor && editor.extensionManager.extensions.find(ext => ext.name === 'paragraphSymbols')) {
              editor.commands.toggleParagraphSymbols();
              setShowParagraphSymbols(!showParagraphSymbols);
            }
          }}
          className={`px-2 py-1 rounded`}
          style={{
            background: showParagraphSymbols ? iconActiveBg : 'transparent',
            color: showParagraphSymbols ? iconActiveColor : iconColor,
            fontSize: `${getIconSize()}px`,
          }}
        >
          <span style={{ fontFamily: 'serif', fontWeight: 'bold' }}>¶</span>
        </button>
      </Tooltip>

      <Popover
        open={showLinkWarn}
        onOpenChange={setShowLinkWarn}
        trigger="click"
        placement="bottom"
        content={
          <div style={{ maxWidth: 300, padding: '8px 12px' }}>
            <p style={{ margin: 0, fontSize: 14, lineHeight: 1.5 }}>
              Используйте кнопку "Ссылка" для добавления ссылок. Вставка URL напрямую не будет работать.
            </p>
          </div>
        }
      >
        <Tooltip 
          title={renderTooltip("Добавить ссылку", null)} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            ref={linkBtnRef}
            onMouseDown={handleLinkButton}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive('link') ? iconActiveBg : 'transparent',
              color: editor.isActive('link') ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width={getIconSize()} height={getIconSize()} fill="currentColor" viewBox="0 0 16 16">
              <path d="M4.715 6.542 3.343 7.914a3 3 0 1 0 4.243 4.243l1.828-1.829A3 3 0 0 0 8.586 5.5L8 6.086a1.002 1.002 0 0 0-.154.199 2 2 0 0 1 .861 3.337L6.88 11.45a2 2 0 1 1-2.83-2.83l.793-.792a4.018 4.018 0 0 1-.128-1.287z"/>
              <path d="M6.586 4.672A3 3 0 0 0 7.414 9.5l.775-.776a2 2 0 0 1-.896-3.346L9.12 3.55a2 2 0 1 1 2.83 2.83l-.793.792c.112.42.155.855.128 1.287l1.372-1.372a3 3 0 1 0-4.243-4.243L6.586 4.672z"/>
            </svg>
          </button>
        </Tooltip>
      </Popover>

      <Tooltip 
        title={renderTooltip("Добавить изображение", null)} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onClick={(e) => {
            fileInputRef.current?.click();
          }}
          className="px-2 py-1 rounded"
          style={{
            background: 'transparent',
            color: iconColor,
            border: 'none',
            cursor: 'pointer',
          }}
        >
          <PictureOutlined style={{ fontSize: `${getIconSize()}px` }} />
        </button>
      </Tooltip>
      <input
        ref={fileInputRef}
        type="file"
        accept=".jpg,.jpeg,.png,.webp,.bmp,.tiff,.gif,image/jpeg,image/png,image/webp,image/bmp,image/tiff,image/gif"
        style={{ display: 'none' }}
        onChange={handleImageUpload}
      />

      {/* Кнопка разделения главы */}
      <Tooltip
        title={renderTooltip("Разделить главу", "Выделите заголовок новой главы")}
        placement="bottom"
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            handleSplitChapter();
          }}
          className="px-2 py-1 rounded"
          style={{
            background: 'transparent',
            color: iconColor,
            border: 'none',
            cursor: 'pointer',
            opacity: editor && !editor.state.selection.empty ? 1 : 0.5,
          }}
          disabled={!editor || editor.state.selection.empty}
        >
          <ScissorOutlined style={{ fontSize: `${getIconSize()}px` }} />
        </button>
      </Tooltip>

      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

      {/* Кнопка настроек размера значков */}
      <Tooltip
        title={renderTooltip("Настройки размера значков")}
        placement="bottom"
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            setShowSizeSettings(true);
          }}
          className={`px-2 py-1 rounded`}
          style={{
            background: 'transparent',
            color: iconColor,
          }}
        >
          <SettingOutlined style={{ fontSize: `${getIconSize()}px` }} />
        </button>
      </Tooltip>

      {/* Кнопка полноэкранного режима */}
      <Tooltip 
        title={renderTooltip(isFullWidth ? "Обычный режим" : "Полноэкранный режим")} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            onToggleFullWidth && onToggleFullWidth();
          }}
          className={`px-2 py-1 rounded`}
          style={{
            background: isFullWidth ? iconActiveBg : 'transparent',
            color: isFullWidth ? iconActiveColor : iconColor,
          }}
        >
          {isFullWidth ?
            <FullscreenExitOutlined style={{ fontSize: `${getIconSize()}px` }} /> :
            <FullscreenOutlined style={{ fontSize: `${getIconSize()}px` }} />
          }
        </button>
      </Tooltip>
    </div>
  );

  useImperativeHandle(ref, () => ({
    handleSave,
  }));

  const openLinkModal = (init = {}) => {
    setLinkModal({
      open: true,
      href: init.href || '',
      blank: !!init.blank,
      from: init.from ?? null,
      to: init.to ?? null,
    });
  };
  const closeLinkModal = () => setLinkModal(l => ({ ...l, open: false }));

  useEffect(() => {
    if (!editor) return;
    const handler = e => {
      const a = e.target.closest('a');
      if (a && editor.isEditable) {
        e.preventDefault();
        const pos = editor.view.posAtDOM(a, 0);
        const { href, target } = a;
        openLinkModal({
          href,
          blank: target === '_blank',
          from: pos,
          to: pos + a.textContent.length,
        });
      }
    };
    const dom = editor.view.dom;
    dom.addEventListener('click', handler);
    return () => dom.removeEventListener('click', handler);
  }, [editor]);

  const handleLinkButton = () => {
    if (!editor) return;
    const { from, to, empty } = editor.state.selection;
    const attrs = editor.getAttributes('link');
    if (attrs.href) {
      openLinkModal({ href: attrs.href, blank: attrs.target === '_blank', from, to });
    } else if (!empty) {
      openLinkModal({ from, to });
    } else {
      setShowLinkWarn(true);
      setTimeout(() => setShowLinkWarn(false), 2000);
    }
  };

  const saveLink = () => {
    if (!linkModal.href) return;
    if (!/^https?:\/\//.test(linkModal.href)) {
      message.warning('Ссылка должна начинаться с http:// или https://');
      return;
    }
    editor.chain().focus().extendMarkRange('link').setLink({ href: linkModal.href, target: linkModal.blank ? '_blank' : null }).run();
    closeLinkModal();
  };
  const removeLink = () => {
    editor.chain().focus().extendMarkRange('link').unsetLink().run();
    closeLinkModal();
  };

  return (
    <div 
      className={isFullWidth ? "p-6 mx-auto" : "p-4 rounded-lg mb-4"} 
      style={{ 
        background: theme === 'dark' ? '#181c23' : '#f9fafb', 
        boxShadow: isFullWidth ? 'none' : (theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22'),
        minHeight: isFullWidth ? '100vh' : 'auto',
        borderRadius: isFullWidth ? 0 : 8,
        marginBottom: isFullWidth ? 0 : 16,
        maxWidth: isFullWidth ? '1200px' : '100%',
        width: isFullWidth ? '100%' : 'auto',
        margin: isFullWidth ? '0 auto' : undefined,
        padding: isFullWidth ? '20px 30px' : undefined,
        position: 'relative'
      }}>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 12 }}>
        <Input
          value={title}
          onChange={e => setTitle(e.target.value)}
          placeholder="Название главы"
          className={theme === 'dark' ? 'bg-[#111827] text-white border border-[#374151] placeholder-gray-400' : ''}
          style={{
            background: theme === 'dark' ? '#111827' : '#fff',
            color: theme === 'dark' ? '#fff' : '#222',
            border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb',
            borderRadius: 8,
            fontSize: '1rem',
            flex: 1,
            marginBottom: 0,
          }}
          maxLength={200}
        />
        <Popconfirm
          title={<span style={{ color: theme === 'dark' ? '#fff' : '#222', fontWeight: 600 }}>Закрыть редактор?</span>}
          description={<span style={{ color: theme === 'dark' ? '#fff' : '#222', fontSize: 13 }}>Выберите действие:</span>}
          okText="Сохранить и закрыть"
          cancelText="Закрыть без сохранения"
          onConfirm={handleSave}
          onCancel={onCancel}
          okButtonProps={{ style: { background: '#ef4444', color: '#fff', border: 'none' } }}
          cancelButtonProps={{ style: { background: theme === 'dark' ? '#23272f' : '#fff', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb' } }}
          classNames={{ root: theme === 'dark' ? 'dark-popconfirm' : '' }}
        >
          <span
            style={{
              marginLeft: 12,
              color: '#ef4444',
              fontSize: 28,
              cursor: 'pointer',
              transition: 'color 0.2s',
              userSelect: 'none',
            }}
            onMouseOver={e => e.target.style.color = '#b91c1c'}
            onMouseOut={e => e.target.style.color = '#ef4444'}
          >
            &#10006;
          </span>
        </Popconfirm>
      </div>
      <div className="mb-3 flex flex-col gap-2">
        {showToolbar && <Toolbar />}
        <div
          className={`tiptap-editor${autoIndent ? ' tiptap-indent' : ''}`}
          style={{
            minHeight: isFullWidth ? 'calc(100vh - 150px)' : 320,
            maxHeight: isFullWidth ? 'calc(100vh - 150px)' : 600,
            background: theme === 'dark' ? '#111827' : '#fff',
            color: theme === 'dark' ? '#fff' : '#222',
            border: theme === 'dark' ? '2px solid #374151' : '2px solid #d1d5db',
            borderRadius: 8,
            boxShadow: isFullWidth ? (theme === 'dark' ? '0 4px 12px rgba(0, 0, 0, 0.2)' : '0 4px 12px rgba(0, 0, 0, 0.1)') : (theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22'),
            outline: isFocused ? '2px solid #2563eb' : 'none',
            padding: isFullWidth ? '20px 24px' : '12px 16px',
            fontSize: '1rem',
            transition: 'background 0.2s, color 0.2s, outline 0.2s',
            overflowY: 'auto',
          }}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          tabIndex={-1}
        >
          <EditorContent
            editor={editor}
            className="tiptap-editor"
            style={{ outline: 'none', border: 'none', boxShadow: 'none', background: 'transparent', minHeight: 200, color: theme === 'dark' ? '#fff' : '#222' }}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
          />
        </div>
      </div>

      {/* Счетчик символов для главы */}
      {showCharacterCounter && maxChapterLength && editor && (
        <div style={{
          marginTop: 8,
          marginBottom: 8,
          textAlign: 'center',
          fontSize: 14,
          fontWeight: 500
        }}>
          {(() => {
            const content = editor.getHTML();
            const plainText = content.replace(/<[^>]*>/g, '');
            const currentLength = plainText.length;
            const isOverLimit = currentLength > maxChapterLength;

            return (
              <span style={{
                color: isOverLimit ? '#ef4444' : (theme === 'dark' ? '#a1a1aa' : '#666'),
                fontWeight: isOverLimit ? 600 : 500
              }}>
                {currentLength.toLocaleString()}/{maxChapterLength.toLocaleString()} символов
                {isOverLimit && (
                  <span style={{ color: '#ef4444', marginLeft: 8, fontWeight: 600 }}>
                    (Превышен размер главы)
                  </span>
                )}
              </span>
            );
          })()}
        </div>
      )}

      <div className="flex gap-2 mt-2">
        <Button type="primary" onClick={handleSave} loading={loading}>
          Сохранить
        </Button>
        <Button 
          onClick={() => {
            // Если редактор в полноэкранном режиме, выходим из него
            if (isFullWidth && onToggleFullWidth) {
              onToggleFullWidth();
            }
            onCancel();
          }} 
          disabled={loading} 
          style={{ background: theme === 'dark' ? '#23272f' : '#fff', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb' }}
        >
          Отмена
        </Button>
      </div>
      <Modal
        open={linkModal.open}
        onCancel={closeLinkModal}
        footer={null}
        centered
        closable={false}
      >
        <div style={{ fontSize: 16, color: theme === 'dark' ? '#e5e7eb' : '#374151', fontWeight: 600, marginBottom: 20, textAlign: 'center' }}>
          Добавить ссылку
        </div>
        
        <div style={{ marginBottom: 16 }}>
          <Input
            placeholder="https://example.com"
            value={linkModal.href}
            onChange={e => setLinkModal(l => ({ ...l, href: e.target.value }))}
            style={{ 
              marginBottom: 12,
              background: theme === 'dark' ? '#111827' : '#ffffff',
              borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
              color: theme === 'dark' ? '#ffffff' : '#111827',
            }}
            allowClear
          />
          
          <Checkbox
            checked={linkModal.blank}
            onChange={e => setLinkModal(l => ({ ...l, blank: e.target.checked }))}
            style={{
              color: theme === 'dark' ? '#ffffff' : '#374151',
            }}
          >
            <span style={{ color: theme === 'dark' ? '#ffffff' : '#374151' }}>
              Открывать в новом окне
            </span>
          </Checkbox>
        </div>
        
        {linkModal.href && !/^https?:\/\//.test(linkModal.href) && (
          <div style={{ color: '#ef4444', fontSize: 13, marginBottom: 16, textAlign: 'center' }}>
            Ссылка должна начинаться с http:// или https://
          </div>
        )}
        
        <div style={{ display: 'flex', justifyContent: 'space-between', gap: 16 }}>
          <Button 
            onClick={removeLink} 
            disabled={!editor?.isActive('link')}
            style={{ 
              minWidth: 100,
              background: theme === 'dark' ? '#181c23' : '#f3f4f6', 
              color: theme === 'dark' ? '#ef4444' : '#dc2626', 
              border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', 
              fontWeight: 500 
            }}
          >
            Удалить
          </Button>
          
          <div style={{ display: 'flex', gap: 8 }}>
            <Button 
              onClick={closeLinkModal}
              style={{ 
                minWidth: 80,
                background: theme === 'dark' ? '#181c23' : '#f3f4f6', 
                color: theme === 'dark' ? '#fff' : '#222', 
                border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', 
                fontWeight: 500 
              }}
            >
              Отмена
            </Button>
            <Button 
              type="primary" 
              onClick={saveLink} 
              disabled={!linkModal.href || !/^https?:\/\//.test(linkModal.href)}
              style={{ 
                minWidth: 100, 
                background: (!linkModal.href || !/^https?:\/\//.test(linkModal.href)) ? '#d1d5db' : '#60A5FA', 
                color: '#fff', 
                border: 'none', 
                fontWeight: 500 
              }}
            >
              Сохранить
            </Button>
          </div>
        </div>
      </Modal>
      <style>{`
/* Убираем синий фокус везде */
.ant-input:focus,
.ant-input-focused,
.ant-input:focus-within,
.ant-select:focus,
.ant-select-focused,
.ant-select:focus-within,
.ant-btn:focus,
.ant-btn-focused,
.ant-btn:focus-within {
  box-shadow: none !important;
  outline: none !important;
}

/* Крестик очистки в поле ввода - серый цвет */
.ant-input-clear-icon {
  color: ${theme === 'dark' ? '#9ca3af' : '#6b7280'} !important;
  opacity: 1 !important;
}
.ant-input-clear-icon:hover {
  color: ${theme === 'dark' ? '#ffffff' : '#111827'} !important;
}
.tiptap-editor details {
  background: ${theme === 'dark' ? '#23272f' : '#f3f4f6'};
  border: 1.5px solid ${theme === 'dark' ? '#374151' : '#d1d5db'};
  border-radius: 8px;
  margin: 12px 0;
  padding: 0 0 0 0;
  transition: background 0.2s, border 0.2s;
}
.tiptap-editor summary {
  cursor: pointer;
  font-weight: 600;
  padding: 10px 16px;
  color: ${theme === 'dark' ? '#60A5FA' : '#2563eb'};
  background: none;
  border-radius: 8px 8px 0 0;
  outline: none;
  user-select: none;
}
.tiptap-editor details[open] summary {
  border-bottom: 1.5px solid ${theme === 'dark' ? '#374151' : '#d1d5db'};
}
.tiptap-editor details > *:not(summary) {
  padding: 12px 16px 16px 16px;
  color: ${theme === 'dark' ? '#fff' : '#222'};
}
`}</style>

      {/* Компонент настроек размера значков */}
      <ToolbarSizeSettings />
    </div>
  );
});

export default ChapterEditor;

function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

function extractImagePathsFromHtml(html) {
  const div = document.createElement('div');
  div.innerHTML = html;
  const imgs = div.querySelectorAll('img');
  const paths = [];
  imgs.forEach(img => {
    const src = img.getAttribute('src');
    if (src && !/^https?:\/\//.test(src)) {
      paths.push(src);
    }
  });
  return Array.from(new Set(paths));
}

function replaceImageSrcInHtml(html, urlMap) {
  const div = document.createElement('div');
  div.innerHTML = html;
  div.querySelectorAll('img').forEach(img => {
    const origSrc = img.getAttribute('src');
    if (urlMap[origSrc]) {
      img.setAttribute('src', urlMap[origSrc]);
    }
  });
  return div.innerHTML;
}

function revertPresignedUrlsInHtml(html, urlMap) {
  const div = document.createElement('div');
  div.innerHTML = html;
  div.querySelectorAll('img').forEach(img => {
    const src = img.getAttribute('src');
    const origPath = Object.keys(urlMap).find(key => urlMap[key] === src);
    if (origPath) {
      img.setAttribute('src', origPath);
    } else {
      // Регулярное выражение для поддержки как старого, так и нового формата путей + WebP
      const match = src && src.match(/\/media\/private\/book_pics\/[\w\/-]+\/chapics\/(?:[\w\/-]*\/)?[\w\d_]+\.(jpg|jpeg|png|gif|webp)/);
      if (match) {
        img.setAttribute('src', match[0]);
      }
    }
  });
  return div.innerHTML;
}

async function fetchPresignedUrls(paths) {
  const csrfToken = getCookie('csrftoken');
  const res = await fetch('/api/books/get_image_links/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken },
    credentials: 'include',
    body: JSON.stringify({ paths }),
  });
  if (!res.ok) throw new Error('Ошибка получения ссылок');
  return await res.json();
}

// Создаем новый компонент для выбора цвета, который не теряет выделение
function CustomColorDropdown({ editor, theme, getIconSize }) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectionState, setSelectionState] = useState(null);
  const [activeTab, setActiveTab] = useState('text'); // 'text' или 'background'
  
  // Получаем текущие цвета текста и фона
  const currentTextColor = editor.getAttributes('textStyle').color;
  const currentBgColor = editor.getAttributes('backgroundColor').backgroundColor;
  
  // Определяем, используются ли автоматические цвета
  const isTextAuto = !currentTextColor || currentTextColor === '#000000' || currentTextColor === '#fff' || currentTextColor === '#ffffff';
  const isBgAuto = !currentBgColor;
  
  // Определяем цвета и метки в зависимости от активной вкладки
  const autoColor = theme === 'dark' ? '#fff' : '#222';
  
  // Определяем метку для кнопки
  let label = 'Цвет';
  if (activeTab === 'text') {
    label = isTextAuto ? 'Текст' : 'Текст';
  } else {
    label = isBgAuto ? 'Фон' : 'Фон';
  }
  
  // Цвет для отображения в кнопке
  const labelColor = activeTab === 'text' 
    ? (isTextAuto ? autoColor : currentTextColor)
    : (isBgAuto ? 'transparent' : currentBgColor);

  // Организованная палитра цветов 5x5
  const colorPalette = [
    // Ряд 1: Автоматический и красные оттенки
    [
      { name: 'Автоматический', value: 'auto', isAuto: true },
      { name: 'Светло-красный', value: '#ffcccc' },
      { name: 'Красный', value: '#ff0000' },
      { name: 'Темно-красный', value: '#cc0000' },
      { name: 'Бордовый', value: '#800000' }
    ],
    // Ряд 2: Оранжевые и желтые оттенки
    [
      { name: 'Светло-оранжевый', value: '#ffcc99' },
      { name: 'Оранжевый', value: '#ff9900' },
      { name: 'Светло-желтый', value: '#ffffcc' },
      { name: 'Желтый', value: '#ffff00' },
      { name: 'Темно-желтый', value: '#cccc00' }
    ],
    // Ряд 3: Зеленые оттенки
    [
      { name: 'Светло-зеленый', value: '#ccffcc' },
      { name: 'Зеленый', value: '#00cc00' },
      { name: 'Темно-зеленый', value: '#008000' },
      { name: 'Салатовый', value: '#99ff99' },
      { name: 'Оливковый', value: '#808000' }
    ],
    // Ряд 4: Синие и фиолетовые оттенки
    [
      { name: 'Светло-голубой', value: '#ccffff' },
      { name: 'Голубой', value: '#00ffff' },
      { name: 'Синий', value: '#0000ff' },
      { name: 'Темно-синий', value: '#000080' },
      { name: 'Фиолетовый', value: '#800080' }
    ],
    // Ряд 5: Серые оттенки, черный и белый
    [
      { name: 'Светло-серый', value: '#e6e6e6' },
      { name: 'Серый', value: '#808080' },
      { name: 'Темно-серый', value: '#404040' },
      { name: 'Черный', value: '#000000' },
      { name: 'Белый', value: '#ffffff' }
    ]
  ];

  const toggleDropdown = (e) => {
    e.preventDefault(); // Важно! Предотвращает потерю фокуса и выделения
    
    if (!isOpen) {
      // При открытии меню сохраняем текущее состояние выделения
      setSelectionState(editor.state);
    }
    
    setIsOpen(!isOpen);
  };

  const handleAutoColor = (e) => {
    e.preventDefault();
    
    // Если было сохранено состояние выделения, восстанавливаем его
    if (selectionState) {
      editor.view.updateState(selectionState);
    }
    
    // Сбрасываем цвет в зависимости от активной вкладки
    if (activeTab === 'text') {
      const { backgroundColor, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus().setMark('textStyle', {
        color: null,
        backgroundColor,
        fontSize,
      }).run();
    } else {
      const { color, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus().setMark('textStyle', {
        color,
        backgroundColor: null,
        fontSize,
      }).run();
    }
    
    setIsOpen(false);
  };
  
  const handleResetAllColors = (e) => {
    e.preventDefault();
    
    // Если было сохранено состояние выделения, восстанавливаем его
    if (selectionState) {
      editor.view.updateState(selectionState);
    }
    
    // Сбрасываем оба цвета
    editor.chain().focus()
      .extendMarkRange('textStyle').unsetColor()
      .extendMarkRange('backgroundColor').unsetBackgroundColor()
      .run();
    
    setIsOpen(false);
  };
  
  const applyPresetColor = (colorObj, e) => {
    e.preventDefault();
    
    // Если было сохранено состояние выделения, восстанавливаем его
    if (selectionState) {
      editor.view.updateState(selectionState);
    }
    
    // Если это автоматический цвет
    if (colorObj.isAuto) {
      handleAutoColor(e);
      return;
    }
    
    // Применяем цвет в зависимости от активной вкладки
    if (activeTab === 'text') {
      const { backgroundColor, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus()
        .setMark('textStyle', { color: null, backgroundColor, fontSize }) // сбросить цвет
        .setMark('textStyle', { color: colorObj.value, backgroundColor, fontSize }) // применить новый
        .run();
    } else {
      const { color, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus()
        .setMark('textStyle', { color, backgroundColor: null, fontSize }) // сбросить фон
        .setMark('textStyle', { color, backgroundColor: colorObj.value, fontSize }) // применить новый
        .run();
    }
    
    setIsOpen(false);
  };
  
  const switchTab = (tab, e) => {
    e.preventDefault();
    setActiveTab(tab);
  };

  return (
    <div className="relative inline-block" style={{ marginLeft: 4, marginRight: 4 }}>
      <Tooltip
        title={
          <span style={{ fontSize: '12px', lineHeight: '1.5' }}>
            Рекомендуется использовать стандартные цвета для текста и фона, они адаптируются автоматически на светлой и темной теме.
          </span>
        }
        placement="top"
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={toggleDropdown}
          className="flex items-center px-2 py-1 border rounded"
          style={{
            background: 'transparent',
            border: 'none',
            minWidth: '54px',
            height: '32px',
            cursor: 'pointer',
          }}
        >
          <span
            style={{
              display: 'inline-block',
              width: getIconSize(),
              height: getIconSize(),
              borderRadius: Math.max(4, getIconSize() / 3),
              border: '1.5px solid #fff',
              background: labelColor,
              marginRight: 6,
              boxShadow: theme === 'dark' ? '0 0 0 1.5px #fff' : '0 0 0 1.5px #222',
            }}
          />
          <span style={{ color: activeTab === 'text' ? (isTextAuto ? autoColor : currentTextColor) : autoColor, fontWeight: 600, fontSize: getIconSize() }}>{label}</span>
        </button>
      </Tooltip>
      
      {isOpen && (
        <div
          className="absolute left-0 mt-1 border rounded shadow-lg z-10"
          style={{
            background: theme === 'dark' ? '#1f2937' : '#ffffff',
            borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
            minWidth: '240px',
            zIndex: 1000,
          }}
        >
          <div
            onMouseDown={handleResetAllColors}
            className="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
            style={{
              background: (isTextAuto && isBgAuto) ? (theme === 'dark' ? '#374151' : '#f3f4f6') : 'transparent',
              color: autoColor,
              fontWeight: (isTextAuto && isBgAuto) ? 700 : 500,
              fontSize: getIconSize(),
            }}
          >
            Сбросить оба цвета
          </div>
          
          {/* Вкладки для переключения между текстом и фоном */}
          <div className="flex border-t border-gray-200 dark:border-gray-700">
            <div
              onMouseDown={(e) => switchTab('text', e)}
              className="flex-1 px-3 py-2 text-center cursor-pointer"
              style={{
                background: activeTab === 'text' ? (theme === 'dark' ? '#374151' : 'transparent') : (theme === 'dark' ? 'transparent' : '#f3f4f6'),
                color: autoColor,
                fontWeight: activeTab === 'text' ? 700 : 500,
                fontSize: getIconSize(),
                borderBottom: activeTab === 'text' ? `2px solid ${theme === 'dark' ? '#3b82f6' : '#2563eb'}` : 'none',
              }}
            >
              Текст
            </div>
            <div
              onMouseDown={(e) => switchTab('background', e)}
              className="flex-1 px-3 py-2 text-center cursor-pointer"
              style={{
                background: activeTab === 'background' ? (theme === 'dark' ? '#374151' : 'transparent') : (theme === 'dark' ? 'transparent' : '#f3f4f6'),
                color: autoColor,
                fontWeight: activeTab === 'background' ? 700 : 500,
                fontSize: getIconSize(),
                borderBottom: activeTab === 'background' ? `2px solid ${theme === 'dark' ? '#3b82f6' : '#2563eb'}` : 'none',
              }}
            >
              Фон
            </div>
          </div>
          
          {/* Компактная палитра цветов 5x5 */}
          <div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700">
            <div className="text-gray-500 dark:text-gray-400 mb-2" style={{ fontSize: Math.max(10, getIconSize() - 2) }}>
              {activeTab === 'text' ? 'Выберите цвет текста:' : 'Выберите цвет фона:'}
            </div>
            
            <div className="grid grid-cols-5 gap-2">
              {colorPalette.flat().map((color, index) => (
                <div
                  key={index}
                  onMouseDown={(e) => applyPresetColor(color, e)}
                  className="rounded cursor-pointer hover:opacity-80 flex items-center justify-center"
                  style={{
                    width: Math.max(24, getIconSize() + 8),
                    height: Math.max(24, getIconSize() + 8),
                    background: color.isAuto
                      ? 'transparent'
                      : color.value,
                    boxShadow: '0 0 0 1px rgba(0,0,0,0.2)',
                    border: ((activeTab === 'text' && isTextAuto && color.isAuto) ||
                            (activeTab === 'background' && isBgAuto && color.isAuto) ||
                            (activeTab === 'text' && currentTextColor === color.value) ||
                            (activeTab === 'background' && currentBgColor === color.value))
                      ? '2px solid #3b82f6'
                      : 'none',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                  title={color.name}
                >
                  {color.isAuto && (
                    <>
                      {activeTab === 'background' && (
                        <>
                          {/* Шахматный узор для обозначения прозрачности фона */}
                          <div style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#374151' : '#e5e7eb',
                          }} />
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#374151' : '#e5e7eb',
                          }} />
                          <div style={{
                            position: 'absolute',
                            top: 0,
                            left: '50%',
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#1f2937' : '#f3f4f6',
                          }} />
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            left: 0,
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#1f2937' : '#f3f4f6',
                          }} />
                        </>
                      )}
                      <span style={{ 
                        fontWeight: 'bold', 
                        color: theme === 'dark' ? '#fff' : '#000',
                        fontSize: '14px',
                        position: 'relative',
                        zIndex: 1
                      }}>
                        A
                      </span>
                    </>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}