import React, { createContext, useContext, useEffect, useState } from 'react';

const ThemeContext = createContext();

function getSystemTheme() {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'light';
}

export function ThemeProvider({ children }) {
  const [themeMode, setThemeMode] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('themeMode') || 'auto';
    }
    return 'auto';
  });
  const [theme, setTheme] = useState(() => {
    if (themeMode === 'auto') return getSystemTheme();
    return themeMode;
  });

  useEffect(() => {
    if (themeMode === 'auto') {
      const updateTheme = () => setTheme(getSystemTheme());
      updateTheme();
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', updateTheme);
      return () => window.matchMedia('(prefers-color-scheme: dark)').removeEventListener('change', updateTheme);
    } else {
      setTheme(themeMode);
    }
  }, [themeMode]);

  useEffect(() => {
    document.documentElement.classList.remove('light', 'dark');
    document.documentElement.classList.add(theme);
    localStorage.setItem('themeMode', themeMode);
  }, [theme, themeMode]);

  const setMode = (mode) => {
    setThemeMode(mode);
  };

  const toggleTheme = () => {
    if (themeMode === 'light') setThemeMode('dark');
    else if (themeMode === 'dark') setThemeMode('auto');
    else setThemeMode('light');
  };

  return (
    <ThemeContext.Provider value={{ theme, themeMode, setThemeMode: setMode, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  return useContext(ThemeContext);
} 