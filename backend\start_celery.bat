@echo off
echo Starting Celery services...

echo Checking Redis connection...
python -c "import redis; r = redis.Redis(host='localhost', port=6379, db=0); r.ping(); print('✓ Redis is running')" 2>nul
if errorlevel 1 (
    echo ✗ Redis is not running. Please start Redis first.
    pause
    exit /b 1
)

echo Starting Celery worker...
start "Celery Worker" cmd /k "celery -A config worker --loglevel=info"

timeout /t 3 /nobreak >nul

echo Starting Celery beat...
start "Celery Beat" cmd /k "celery -A config beat --loglevel=info"

echo Celery services started!
echo Worker and Beat are running in separate windows.
echo Close those windows to stop the services.
pause
