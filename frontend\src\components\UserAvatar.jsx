import React, { memo } from 'react';
import { getCachedUserAvatar } from '../utils/avatarCache';

const UserAvatar = memo(({ 
    user, 
    size = 'mini', 
    className = '', 
    alt = '', 
    onClick = null,
    onError = null,
    style = {},
    backendUrl = ''
}) => {
    if (!user) {
        return null;
    }

    const avatarUrl = getCachedUserAvatar(user, size, backendUrl);
    const displayName = user.display_name || user.username || 'User';

    const handleError = (e) => {
        // Устанавливаем fallback изображение
        e.target.onerror = null;
        e.target.src = '/lpo/ava.webp';
        
        // Вызываем пользовательский обработчик ошибки, если он есть
        if (onError) {
            onError(e);
        }
    };

    return (
        <img
            src={avatarUrl}
            alt={alt || displayName}
            className={className}
            onClick={onClick}
            onError={handleError}
            style={style}
            loading="lazy"
        />
    );
});

UserAvatar.displayName = 'UserAvatar';

export default UserAvatar;