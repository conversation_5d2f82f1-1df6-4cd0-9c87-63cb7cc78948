import React, { useState, useEffect, useRef } from 'react';
import { Button, Typography, List, Popconfirm, message, Tooltip } from 'antd';
import { EyeInvisibleOutlined, EyeOutlined, EditOutlined, DeleteOutlined, PlusOutlined, FileAddOutlined, ExclamationCircleOutlined, ClockCircleOutlined, CloseOutlined } from '@ant-design/icons';
import ChapterEditor from '../components/ChapterEditor';
import { useTheme } from '../theme/ThemeContext';

const { Title } = Typography;

const EditBook2 = ({ book, chapters, handleSaveChapter, chapterModal, setChapterModal, chapterLoading, setChapters, onBack }) => {
  const [editMode, setEditMode] = useState(null); // id главы или 'new'/'preface'
  const [isPreface, setIsPreface] = useState(false);
  const [editor<PERSON>ey, setEditorKey] = useState(0); // для сброса редактора
  const { theme } = useTheme();
  const prevChaptersCount = useRef(Array.isArray(chapters) ? chapters.length : 0);
  const editorRef = useRef(null);

  const username = book?.author?.username || '';
  const coverUrl =
    book?.cover_type === 'custom'
      ? (book?.cover_mini_url || '/static/cover_placeholder.jpg')
      : (book?.cover_temp_url || '/static/cover_placeholder.jpg');

  const safeChapters = Array.isArray(chapters) ? chapters : [];
  // Определяем ограничения по типу книги
  const isStory = book?.type === 'story';
  const canAddChapter = isStory ? safeChapters.length < (safeChapters.some(ch => ch.order === 0) ? 2 : 1) : true;
  const nextChapterNumber = safeChapters.length === 0 || (isPreface && !safeChapters.some(ch => ch.order === 0)) ? 1 : Math.max(...safeChapters.map(ch => ch.order || 1)) + 1;

  // imgIndex — максимальный среди всех глав + 1 (ищем по содержимому глав)
  const getNextImgIndex = () => {
    let maxIdx = 0;
    safeChapters.forEach(ch => {
      // ищем все картинки в тексте главы
      const matches = (ch.content || '').match(/\/media\/users\/[^"]+\.(jpg|jpeg|png|webp)/g);
      if (matches) {
        matches.forEach(url => {
          // парсим индекс из url
          const m = url.match(/chapter\/(\d+)_\d+_\d+\.(jpg|jpeg|png|webp)/);
          if (m && m[1]) {
            const idx = parseInt(m[1], 10);
            if (idx > maxIdx) maxIdx = idx;
          }
        });
      }
    });
    return maxIdx + 1;
  };

  // Добавить главу
  const handleAddChapter = (preface = false) => {
    setIsPreface(preface);
    setEditMode(preface ? 'preface' : 'new');
    setEditorKey(prev => prev + 1); // сброс редактора
  };

  useEffect(() => {
    if (Array.isArray(chapters) && chapters.length > prevChaptersCount.current) {
      setEditMode(null);
      setIsPreface(false);
    }
    prevChaptersCount.current = Array.isArray(chapters) ? chapters.length : 0;
  }, [chapters]);

  // Сохранить главу
  const handleSave = async (data) => {
    const ok = await handleSaveChapter({
      ...data,
      order: editingChapter ? editingChapter.order : (isPreface ? 0 : nextChapterNumber),
      chapterId: editingChapter ? editingChapter.id : undefined,
      isEdit: !!editingChapter,
    });
    if (ok) {
      setEditMode(null);
      setIsPreface(false);
    }
  };

  // Редактировать главу
  const handleEdit = (chapter) => {
    setIsPreface(chapter.order === 0);
    setEditMode(chapter.id);
    setEditorKey(prev => prev + 1);
  };

  // Опубликовать/скрыть главу
  const handleTogglePublish = (chapter) => {
    // TODO: реализовать API публикации/скрытия главы
    // Если это первая публикация — показать confirm
    // После публикации первой главы — статус книги меняется
  };

  // Удалить главу
  const handleDelete = async (chapter) => {
    // TODO: реализовать API удаления главы и связанных картинок
    try {
      const csrfToken = getCookie('csrftoken');
      const res = await fetch(`/api/books/${book.id}/chapters/${chapter.id}/`, {
        method: 'DELETE',
        headers: { 'X-CSRFToken': csrfToken },
        credentials: 'include',
      });
      if (res.ok) {
        message.success('Глава удалена');
        // Обновить список глав
        if (typeof window.fetchChapters === 'function') {
          window.fetchChapters();
        } else if (typeof setChapters === 'function') {
          setChapters(prev => prev.filter(ch => ch.id !== chapter.id));
        }
      } else {
        message.error('Ошибка при удалении главы');
      }
    } catch {
      message.error('Ошибка при удалении главы');
    }
  };

  // Получить номер следующей главы
  const getNextChapterNumber = () => {
    const nums = safeChapters.filter(ch => ch.order !== 0).map(ch => ch.order || 1);
    return nums.length === 0 ? 1 : Math.max(...nums) + 1;
  };

  // Для редактора: если editMode === 'new' или 'preface' — добавление, иначе — редактирование
  const editingChapter =
    editMode === 'preface'
      ? null
      : editMode === 'new'
      ? null
      : safeChapters.find(ch => ch.id === editMode);

  // 1. Получаем label типа книги
  const BOOK_TYPE_LABELS = {
    story: 'Рассказ',
    novella: 'Повесть',
    novel: 'Роман',
    story_collection: 'Сборник рассказов',
    poetry_collection: 'Сборник поэзии',
  };

  return (
    <div className={theme === 'dark' ? 'bg-gray-900 text-gray-100' : 'bg-gray-100 text-gray-900'} style={{ borderRadius: 16, padding: 24 }}>
      <div style={{ textAlign: 'center', marginBottom: 32 }}>
        <Title
          level={3}
          style={{
            margin: 0,
            color: theme === 'dark' ? '#fff' : '#222',
            transition: 'color 0.2s'
          }}
        >
          2. Работа с текстом книги
        </Title>
      </div>
      <div className="flex flex-row items-center mb-6">
        <div className="relative group cursor-pointer mr-4" style={{ width: 80, height: 112 }}>
          {/* Основная обложка книги */}
          <div className="relative h-full bg-gray-200 dark:bg-gray-700 overflow-hidden shadow-lg transform transition-transform duration-300 group-hover:scale-105" 
               style={{
                 borderRadius: '0 8px 8px 0'
               }}>
            <img
              src={coverUrl}
              alt="cover"
              className="w-full h-full object-cover"
            />
            
            {/* Корешок книги - левая полоска */}
            <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-r from-black/30 via-black/10 to-transparent"></div>
            
            {/* Дополнительная тень для глубины */}
            <div className="absolute left-0.5 top-0 bottom-0 w-0.5 bg-gradient-to-r from-black/20 to-transparent"></div>
            
            {/* Блик на обложке */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
          
          {/* Тень под книгой */}
          <div className="absolute -bottom-0.5 left-0.5 right-1 h-0.5 bg-black/20 rounded-full blur-sm transform transition-transform duration-300 group-hover:scale-110"></div>
        </div>
        <div>
          <div className="text-xl font-bold mb-1" style={{ color: theme === 'dark' ? '#fff' : '#222' }}>{book?.title}</div>
          {/* 1. label типа */}
          <div className="text-sm" style={{ color: theme === 'dark' ? '#a1a1aa' : '#888' }}>{BOOK_TYPE_LABELS[book?.type] || ''}</div>
          {/* 2. статус */}
          <div className="text-xs mt-1" style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            {book?.is_finished ? (
              <><span style={{ color: theme === 'dark' ? '#22c55e' : '#22c55e' }}>Завершено</span> <span style={{ color: '#22c55e', fontSize: 18 }}>✔</span></>
            ) : safeChapters.some(ch => ch.is_published) ? (
              <span style={{ color: theme === 'dark' ? '#60A5FA' : '#2563eb', fontWeight: 600, display: 'flex', alignItems: 'center', gap: 4, fontSize: 14, marginTop: 2 }}>
                <EditOutlined style={{ color: theme === 'dark' ? '#60A5FA' : '#2563eb', fontSize: 15, marginRight: 4 }} /> В процессе публикации
              </span>
            ) : (
              <span style={{ color: '#f59e42', fontWeight: 600, display: 'flex', alignItems: 'center', gap: 4, fontSize: 14, marginTop: 2 }}>
                <ClockCircleOutlined style={{ color: '#f59e42', fontSize: 15, marginRight: 4 }} /> Черновик
              </span>
            )}
          </div>
        </div>
      </div>
      <div className="mb-4 flex items-center gap-4">
        <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddChapter(false)}>
          Добавить главу
        </Button>
        <Button icon={<FileAddOutlined />} onClick={() => handleAddChapter(true)} style={{ background: theme === 'dark' ? '#23272f' : '#fff', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb' }}>
          Добавить предисловие
        </Button>
      </div>
      <div className="mt-8">
        <Title level={4} style={{ color: theme === 'dark' ? '#fff' : '#222' }}>Список глав</Title>
        <div style={{ display: 'grid', gridTemplateColumns: '56px 1fr', gap: 0 }}>
          {safeChapters.sort((a, b) => (a.order || 1) - (b.order || 1)).map((ch, idx) => (
            <React.Fragment key={ch.id}>
              {/* Левая колонка — фиксированный номер */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: 56,
                background: theme === 'dark' ? '#181c23' : '#e5e7eb',
                color: theme === 'dark' ? '#f59e42' : '#b45309',
                fontWeight: 700,
                fontSize: 20,
                borderRadius: '12px 0 0 12px',
                marginBottom: 8,
                border: theme === 'dark' ? '1.5px solid #23272f' : '1.5px solid #e5e7eb',
              }}>{ch.order}</div>
              {/* Правая колонка — строка с главой */}
              <div style={{
                background: theme === 'dark' ? '#23272f' : '#f9fafb',
                borderRadius: '0 12px 12px 0',
                marginBottom: 8,
                color: theme === 'dark' ? '#fff' : '#222',
                display: 'flex',
                alignItems: 'center',
                minHeight: 56,
                boxShadow: theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22',
              }}>
                <span style={{ paddingLeft: 16, flex: 1, textAlign: 'left' }}><b>{ch.order === 0 ? 'Предисловие' : ch.title}</b></span>
                {/* Кнопки */}
                <div style={{ display: 'flex', gap: 8, marginRight: 12 }}>
                  <Tooltip title="Редактировать"><Button icon={<EditOutlined />} onClick={() => handleEdit(ch)} className={theme === 'dark' ? 'dark-action-btn' : ''} /></Tooltip>
                  <Tooltip title={ch.is_published ? 'Скрыть главу' : 'Опубликовать главу'}>
                    <Button
                      icon={ch.is_published ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                      onClick={() => handleTogglePublish(ch)}
                      className={theme === 'dark' ? 'dark-action-btn' : ''}
                    />
                  </Tooltip>
                  <Popconfirm
                    icon={null}
                    title={
                      <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <ExclamationCircleOutlined style={{ color: '#f59e42', fontSize: 18 }} />
                        <span style={{ color: theme === 'dark' ? '#fff' : '#222', fontWeight: 600 }}>Удалить главу?</span>
                      </span>
                    }
                    description={<span style={{ color: '#f59e42', fontSize: 13 }}>Глава и загруженные к ней изображения будут удалены.</span>}
                    onConfirm={() => handleDelete(ch)}
                    okText="Удалить"
                    okButtonProps={{ danger: true, style: { background: theme === 'dark' ? '#ef4444' : '#ef4444', color: '#fff', border: 'none' } }}
                    cancelText="Отмена"
                    cancelButtonProps={{ style: { background: theme === 'dark' ? '#23272f' : '#fff', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb' } }}
                    classNames={{ root: theme === 'dark' ? 'dark-popconfirm' : '' }}
                  >
                    <Tooltip title="Удалить главу"><Button icon={<DeleteOutlined />} danger className={theme === 'dark' ? 'dark-action-btn' : ''} /></Tooltip>
                  </Popconfirm>
                </div>
              </div>
            </React.Fragment>
          ))}
        </div>
      </div>
      {/* Редактор главы под списком */}
      {(editMode === 'new' || editMode === 'preface' || editingChapter) && (
        <div className="mt-8" style={{ background: theme === 'dark' ? '#181c23' : '#fff', borderRadius: 12, boxShadow: theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22', padding: 24, position: 'relative' }}>
          <ChapterEditor
            ref={editorRef}
            key={editorKey}
            initialTitle={editingChapter ? editingChapter.title : editMode === 'preface' ? 'Предисловие' : `Глава ${getNextChapterNumber()}:`}
            initialContent={editingChapter ? editingChapter.content : ''}
            onSave={handleSave}
            onCancel={() => setEditMode(null)}
            loading={chapterLoading}
            bookId={book?.id}
            chapterOrder={editMode === 'preface' ? 0 : editingChapter ? editingChapter.order : getNextChapterNumber()}
            username={username}
            imgIndex={getNextImgIndex()}
            showToolbar={true}
            showCloseConfirm={true}
          />
        </div>
      )}
      <div style={{ marginTop: 24 }}>
        <Button onClick={onBack} style={{ background: theme === 'dark' ? '#23272f' : '#fff', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb' }}>Назад</Button>
      </div>
      {/* 4. Стили для кнопок в тёмной теме */}
      <style>
        {`
.dark-action-btn {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
  transition: background 0.2s, color 0.2s;
}
.dark-action-btn:hover {
  background: #374151 !important;
  color: #60A5FA !important;
}
.dark-popconfirm .ant-popover-inner {
  background: #23272f !important;
  color: #fff !important;
}
.dark-popconfirm .ant-popover-message-title {
  color: #fff !important;
}
.dark-popconfirm .ant-popover-message {
  color: #fff !important;
}
.dark-popconfirm .ant-popover-buttons .ant-btn {
  background: #23272f !important;
  color: #fff !important;
  border: 1.5px solid #374151 !important;
}
.dark-popconfirm .ant-popover-buttons .ant-btn-dangerous {
  background: #ef4444 !important;
  color: #fff !important;
  border: none !important;
}
`}
      </style>
    </div>
  );
};

// вспомогательная функция для получения CSRF токена
function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

export default EditBook2; 