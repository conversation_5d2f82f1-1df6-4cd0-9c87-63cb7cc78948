import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { csrfFetch } from '../utils/csrf';

const OAuthCallback = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const params = new URLSearchParams(location.search);
        const code = params.get('code');
        const state = params.get('state');
        const error = params.get('error');

        if (error) {
          throw new Error(`OAuth error: ${error}`);
        }

        if (!code) {
          throw new Error('Authorization code not found');
        }

        // Определяем провайдера по URL или state
        const provider = determineProvider(location.pathname, state);
        
        // Отправляем код на backend для обмена на токен
        const response = await csrfFetch(`/api/users/auth/${provider}/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ 
            code,
            redirect_uri: `${window.location.origin}/auth/callback/`
          }),
        });

        if (response.ok) {
          const userData = await response.json();
          
          // Отправляем успешный результат в родительское окно
          if (window.opener) {
            window.opener.postMessage({
              type: 'OAUTH_SUCCESS',
              user: userData.user
            }, window.location.origin);
            window.close();
          } else {
            // Если это не popup, редиректим на главную
            navigate('/');
          }
        } else {
          const errorData = await response.json();
          throw new Error(errorData.detail || 'Authentication failed');
        }
      } catch (err) {
        console.error('OAuth callback error:', err);
        setError(err.message);
        
        // Отправляем ошибку в родительское окно
        if (window.opener) {
          window.opener.postMessage({
            type: 'OAUTH_ERROR',
            error: err.message
          }, window.location.origin);
          window.close();
        }
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [location, navigate]);

  const determineProvider = (pathname, state) => {
    // Проверяем путь или state для определения провайдера
    if (pathname.includes('vk') || state?.includes('vk')) return 'vk';
    if (pathname.includes('yandex') || state?.includes('yandex')) return 'yandex';
    if (pathname.includes('google') || state?.includes('google')) return 'google';
    if (pathname.includes('ok') || state?.includes('ok')) return 'ok';
    
    // По умолчанию возвращаем vk
    return 'vk';
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Обработка авторизации...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Ошибка авторизации
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">{error}</p>
          <button
            onClick={() => window.close()}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
          >
            Закрыть
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="text-green-500 text-6xl mb-4">✅</div>
        <p className="text-gray-600 dark:text-gray-400">Авторизация завершена успешно</p>
      </div>
    </div>
  );
};

export default OAuthCallback; 