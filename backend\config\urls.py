"""
URL configuration for config project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include, re_path
from books.views import home
from books.search_views import search_view
from django.conf import settings
from django.conf.urls.static import static
from django.views.static import serve as static_serve
from django.http import FileResponse, JsonResponse
from django.utils.decorators import decorator_from_middleware
from django.middleware.common import CommonMiddleware
from django.views.decorators.csrf import csrf_exempt

# Кастомная view для media с CORS
from django.views.decorators.http import condition

@csrf_exempt
def media_serve(request, path, document_root=None):
    response = static_serve(request, path, document_root=document_root)
    response["Access-Control-Allow-Origin"] = "http://localhost:5174"
    response["Access-Control-Allow-Credentials"] = "true"
    response["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response["Pragma"] = "no-cache"
    response["Expires"] = "0"
    return response

# Health check для мониторинга и безопасного масштабирования
def health_check(request):
    """Проверка состояния всех сервисов"""
    try:
        from django.db import connection
        from django.core.cache import cache
        
        # Проверяем БД
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        # Проверяем Redis
        cache.set('health_check', 'ok', 1)
        cache.get('health_check')
        
        return JsonResponse({
            'status': 'healthy',
            'database': 'ok',
            'redis': 'ok',
            'version': '1.0.0'
        })
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e)
        }, status=500)

urlpatterns = [
    path('', home, name='home'),
    path('health/', health_check, name='health_check'),  # Endpoint для мониторинга
    path('admin/', admin.site.urls),
    path('api/search/', search_view, name='search'),  # API поиска
    path('api/', include('books.urls')),

    path('api/', include('users.urls')),      # для новых путей (например, /api/dialogs/)
    path('api/auth/', include('users.urls')), # для старых путей фронта
    path('api/users/', include('users.urls')), # для путей типа /api/users/feed/events/count/
]

if settings.DEBUG:
    import debug_toolbar
    urlpatterns = [path('__debug__/', include(debug_toolbar.urls))] + urlpatterns
    urlpatterns += [
        re_path(r'^media/(?P<path>.*)$', media_serve, {'document_root': settings.MEDIA_ROOT}),
    ]
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
