from django.core.management.base import BaseCommand
from django.conf import settings
import boto3
from botocore.exceptions import ClientError


class Command(BaseCommand):
    help = 'Test S3 connection and list some files'

    def handle(self, *args, **options):
        self.stdout.write("=== S3 Connection Test ===")
        
        # Показываем настройки
        self.stdout.write(f"Endpoint: {settings.AWS_S3_ENDPOINT_URL}")
        self.stdout.write(f"Bucket: {settings.AWS_STORAGE_BUCKET_NAME}")
        self.stdout.write(f"Access Key: {settings.AWS_ACCESS_KEY_ID[:10]}...")
        
        try:
            # Создаем клиент
            s3_client = boto3.client(
                's3',
                endpoint_url=settings.AWS_S3_ENDPOINT_URL,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name='ru-central1'
            )
            
            # Проверяем доступ к bucket
            self.stdout.write("\n=== Bucket Access Test ===")
            response = s3_client.head_bucket(Bucket=settings.AWS_STORAGE_BUCKET_NAME)
            self.stdout.write("✓ Bucket access OK")
            
            # Ищем файлы book_covers
            self.stdout.write("\n=== Book Covers Search ===")
            response = s3_client.list_objects_v2(
                Bucket=settings.AWS_STORAGE_BUCKET_NAME,
                Prefix='media/public/book_covers/',
                MaxKeys=20
            )

            if 'Contents' in response:
                self.stdout.write(f"Found {len(response['Contents'])} book_covers files:")
                for obj in response['Contents']:
                    self.stdout.write(f"  - {obj['Key']} (size: {obj['Size']})")
            else:
                self.stdout.write("No book_covers files found")

            # Также ищем без префикса media/public
            self.stdout.write("\n=== Book Covers Search (without media/public) ===")
            response = s3_client.list_objects_v2(
                Bucket=settings.AWS_STORAGE_BUCKET_NAME,
                Prefix='book_covers/',
                MaxKeys=20
            )

            if 'Contents' in response:
                self.stdout.write(f"Found {len(response['Contents'])} book_covers files:")
                for obj in response['Contents']:
                    self.stdout.write(f"  - {obj['Key']} (size: {obj['Size']})")
            else:
                self.stdout.write("No book_covers files found")
            
            # Ищем любые файлы с префиксом
            self.stdout.write("\n=== All Files Search (first 20) ===")
            response = s3_client.list_objects_v2(
                Bucket=settings.AWS_STORAGE_BUCKET_NAME,
                MaxKeys=20
            )
            
            if 'Contents' in response:
                self.stdout.write(f"Found files in bucket:")
                for obj in response['Contents']:
                    self.stdout.write(f"  - {obj['Key']} (size: {obj['Size']})")
            else:
                self.stdout.write("No files found in bucket")
                
        except ClientError as e:
            self.stdout.write(f"❌ S3 Error: {e}")
        except Exception as e:
            self.stdout.write(f"❌ General Error: {e}")
