# Generated by Django 5.0.2 on 2025-05-30 12:28

import django.db.models.deletion
import users.models
import users.storage_backends
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0031_user_timezone'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='avatar',
            field=models.ImageField(blank=True, null=True, storage=users.storage_backends.PublicMediaStorage(), upload_to=users.models.user_avatar_path),
        ),
        migrations.AlterField(
            model_name='user',
            name='avatar_thumbnail',
            field=models.ImageField(blank=True, null=True, storage=users.storage_backends.PublicMediaStorage(), upload_to=users.models.user_avatar_thumb_path),
        ),
        migrations.AlterField(
            model_name='user',
            name='header_bg_image',
            field=models.ImageField(blank=True, null=True, storage=users.storage_backends.PublicMediaStorage(), upload_to=users.models.user_header_path),
        ),
        migrations.CreateModel(
            name='MessageAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(storage=users.storage_backends.PrivateMediaStorage(), upload_to=users.models.message_attachment_path)),
                ('thumb', models.ImageField(blank=True, null=True, storage=users.storage_backends.PrivateMediaStorage(), upload_to=users.models.message_attachment_thumb_path)),
                ('dialog', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='users.dialog')),
            ],
        ),
    ]
