import { useState, useEffect, Fragment } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../theme/ThemeContext';
import Container from '../components/Container';
import Tooltip from '../components/Tooltip';
import { getRatingIcon, getReaderRatingIcon, getAuthorRatingIcon } from '../utils/ratingIcons';

/*
 * НАСТРОЙКИ ШИРИН СТОЛБЦОВ ТАБЛИЦЫ:
 *
 * Дата: 140px - фиксированная ширина для даты
 * Тип рейтинга: 120px - фиксированная ширина для типа
 * Действие: auto - растягивается на оставшееся место
 * Изменение: 100px - фиксированная ширина для числа
 * Было → Стало: 130px - фиксированная ширина для значений
 *
 * СТРУКТУРА КОНТЕЙНЕРОВ:
 * - Используется стандартный Container компонент: max-w-[1200px] mx-auto px-4
 * - Контент статистики: максимальная ширина 1180px (1200px - 20px отступы)
 *
 * Для изменения ширин найдите style={{ width: 'XXXpx' }} в коде ниже
 */

function UserRatingStats() {
  const { username } = useParams();
  const { user } = useAuth();
  const { theme } = useTheme();

  // Настройки позиционирования для иконок тултипов
  const tooltipPositioning = {
    // Синяя иконка с информацией о периоде
    periodInfo: {
      marginTop: '2.35rem',    // mt-7 = 1.75rem, можно изменить на любое значение
      marginLeft: '0.1rem',         // дополнительный сдвиг влево/вправо
      marginRight: '0'         // дополнительный сдвиг вправо
    },
    // Красная иконка с ошибкой валидации
    validationError: {
      marginTop: '1.75rem',    // mt-7 = 1.75rem, можно изменить на любое значение
      marginLeft: '0',         // дополнительный сдвиг влево/вправо
      marginRight: '0'         // дополнительный сдвиг вправо
    }
  };

  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [period, setPeriod] = useState('day');
  const [metric, setMetric] = useState('all');
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [customDateFrom, setCustomDateFrom] = useState('');
  const [customDateTo, setCustomDateTo] = useState('');
  const [showCustomPeriod, setShowCustomPeriod] = useState(false);
  const [dateValidationError, setDateValidationError] = useState('');

  // Проверяем доступ к странице
  if (!user) {
    // Незарегистрированный пользователь - на главную
    return <Navigate to="/" replace />;
  }

  if (user.username !== username) {
    // Зарегистрированный, но не владелец - на главную
    return <Navigate to="/" replace />;
  }

  useEffect(() => {
    fetchStats();
  }, [period, metric, page, pageSize, customDateFrom, customDateTo]);

  // Вычисляем ограничения для дат
  const getDateLimits = () => {
    const today = new Date();
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(today.getMonth() - 3);

    // Дата регистрации пользователя (если есть в данных)
    const registrationDate = data?.user_registration_date
      ? new Date(data.user_registration_date)
      : threeMonthsAgo;

    // Минимальная дата: позже из (дата регистрации, 3 месяца назад)
    const minDate = registrationDate > threeMonthsAgo ? registrationDate : threeMonthsAgo;

    return {
      min: minDate.toISOString().split('T')[0],
      max: today.toISOString().split('T')[0]
    };
  };

  const validateCustomDate = (dateValue, isFromDate = true) => {
    if (!dateValue || !data?.user_registration_date) return true;

    const selectedDate = new Date(dateValue);
    const registrationDate = new Date(data.user_registration_date);
    const today = new Date();
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(today.getMonth() - 3);

    // Минимальная дата: позже из (дата регистрации, 3 месяца назад)
    const minAllowedDate = registrationDate > threeMonthsAgo ? registrationDate : threeMonthsAgo;

    if (selectedDate < minAllowedDate) {
      setDateValidationError('Период вне зоны активной регистрации');
      return false;
    }

    if (selectedDate > today) {
      setDateValidationError('Нельзя выбрать будущую дату');
      return false;
    }

    // Проверяем, что дата начала не позже даты окончания
    if (!isFromDate && customDateFrom) {
      const fromDate = new Date(customDateFrom);
      if (selectedDate < fromDate) {
        setDateValidationError('Дата окончания не может быть раньше даты начала');
        return false;
      }
    }

    if (isFromDate && customDateTo) {
      const toDate = new Date(customDateTo);
      if (selectedDate > toDate) {
        setDateValidationError('Дата начала не может быть позже даты окончания');
        return false;
      }
    }

    setDateValidationError('');
    return true;
  };

  const handleCustomDateFromChange = (e) => {
    const newDate = e.target.value;
    setCustomDateFrom(newDate);
    validateCustomDate(newDate, true);
  };

  const handleCustomDateToChange = (e) => {
    const newDate = e.target.value;
    setCustomDateTo(newDate);
    validateCustomDate(newDate, false);
  };

  const getTooltipText = () => {
    if (!data?.user_registration_date) return '';

    const registrationDate = new Date(data.user_registration_date);
    const today = new Date();
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(today.getMonth() - 3);

    // Проверяем, прошло ли больше 3 месяцев с регистрации
    const isOlderThanThreeMonths = registrationDate < threeMonthsAgo;

    if (isOlderThanThreeMonths) {
      // Для пользователей, зарегистрированных больше 3 месяцев назад
      return (
        <div className="text-center min-w-0 max-w-xs sm:max-w-sm">
          <div className="whitespace-nowrap">Укажите свой период,</div>
          <div className="whitespace-nowrap">но не более 3-х месяцев.</div>
        </div>
      );
    } else {
      // Для пользователей, зарегистрированных менее 3 месяцев назад
      const registrationDateFormatted = registrationDate.toLocaleDateString('ru-RU');
      return (
        <div className="text-center min-w-0 max-w-xs sm:max-w-sm">
          <div className="whitespace-nowrap">Доступный период:</div>
          <div className="break-words">с {registrationDateFormatted} (дата регистрации) до сегодняшней даты,</div>
          <div className="whitespace-nowrap">но не более 3-х месяцев.</div>
        </div>
      );
    }
  };

  const handlePeriodChange = (newPeriod) => {
    setPeriod(newPeriod);
    setShowCustomPeriod(newPeriod === 'custom');
    setPage(1);
    setDateValidationError('');

    if (newPeriod !== 'custom') {
      setCustomDateFrom('');
      setCustomDateTo('');
    }
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(parseInt(newPageSize));
    setPage(1); // Сбрасываем на первую страницу при изменении размера
  };

  const fetchStats = async () => {
    try {
      // Не загружаем данные если есть ошибка валидации
      if (dateValidationError) {
        setLoading(false);
        return;
      }

      // Для кастомного периода проверяем, что обе даты выбраны
      if (period === 'custom' && (!customDateFrom || !customDateTo)) {
        setLoading(false);
        return;
      }

      setLoading(true);

      // Строим URL с параметрами
      let url = `/api/users/${username}/stat/?period=${period}&metric=${metric}&page=${page}&page_size=${pageSize}`;

      // Добавляем кастомные даты если выбран custom период
      if (period === 'custom' && customDateFrom && customDateTo) {
        url += `&date_from=${customDateFrom}&date_to=${customDateTo}`;
      }

      const response = await fetch(url, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        setData(result);
        setError(null);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Ошибка при загрузке данных');
      }
    } catch (err) {
      setError('Ошибка сети');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getActionColor = (change) => {
    if (change > 0) return 'text-green-600 dark:text-green-400';
    if (change < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  // Определяем, нужно ли показывать ссылку на книгу
  const shouldShowBookLink = (actionType) => {
    const bookRelatedActions = [
      'Новый комментарий к книге',
      'Ответ на комментарий',
      'Ответ на ответный комментарий',
      'Новый комментарий к книге',
      'Получен лайк к книге',
      'Изменение статуса книги',
      'Покупка книги',
      'Продажа книги',
      // Лайки и дизлайки к комментариям
      'Получен лайк к комментарию',
      'Получен дизлайк к комментарию',
      'Лайк к комментарию отозван',
      'Дизлайк к комментарию отозван',
      // Изменения статуса рассказов
      'Статус рассказа изменен из завершенного в процесс публикации',
      'Статус рассказа изменен из процесса публикации в черновик',
      'Статус рассказа изменен из черновика в процесс публикации',
      'Рассказ завершен из процесса публикации',
      'Статус рассказа изменен из завершенного в черновик',
      'Рассказ завершен из черновика',
	  'Удаление книги (рассказ в процессе публикации)',
      // Изменения статуса повестей/романов
      'Статус Повести/Романа изменен из завершенного в процесс публикации',
      'Статус Повести/Романа изменен из процесса публикации в черновик',
      'Статус Повести/Романа изменен из черновика в процесс публикации',
      'Повесть/Роман завершен(а) из процесса публикации',
      'Статус Повести/Романа изменен из завершенного(нной) в черновик',
      'Повесть/Роман завершен(а) из черновика'
    ];
    return bookRelatedActions.includes(actionType);
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Загрузка статистики...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400">{error}</p>
          <button
            onClick={fetchStats}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  return (
    <Container className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        {/* Заголовок */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Статистика рейтингов
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Детальная информация об изменениях ваших рейтингов
          </p>
        </div>

        {/* Текущие рейтинги */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center gap-2 mb-2">
              <img
                src={getReaderRatingIcon()}
                alt="Reader rating icon"
                className="w-6 h-6"
              />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Рейтинг читателя
              </h3>
            </div>
            <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {data?.current_ratings?.reader_rating || 0}
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center gap-2 mb-2">
              <img
                src={getAuthorRatingIcon()}
                alt="Author rating icon"
                className="w-6 h-6"
              />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Авторский рейтинг
              </h3>
            </div>
            <p className="text-3xl font-bold text-green-600 dark:text-green-400">
              {data?.current_ratings?.author_rating || 0}
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center gap-2 mb-2">
              <img
                src={getRatingIcon(data?.current_ratings?.total_rating || 0)}
                alt="Total rating icon"
                className="w-6 h-6"
              />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Общий рейтинг
              </h3>
            </div>
            <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">
              {data?.current_ratings?.total_rating || 0}
            </p>
          </div>
        </div>

        {/* Фильтры */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
          <div className="flex flex-wrap gap-4">
            <div className="relative">
              <div className="flex items-start gap-2">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                    Период
                  </label>
                  <select
                    value={period}
                    onChange={(e) => handlePeriodChange(e.target.value)}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="day">День</option>
                    <option value="week">Неделя</option>
                    <option value="month">Месяц</option>
                    <option value="custom">Выбрать свой</option>
                  </select>
                </div>

                {/* Тултип с информацией для кастомного периода */}
                {period === 'custom' && data?.user_registration_date && (
                  <Tooltip
                    text={getTooltipText()}
                    position="top"
                    delay={100}
                    className="flex-shrink-0"
                  >
                    <div
                      className="w-5 h-5 bg-blue-500 hover:bg-blue-600 dark:bg-blue-400 dark:hover:bg-blue-300 rounded-full flex items-center justify-center cursor-help transition-colors"
                      style={{
                        marginTop: tooltipPositioning.periodInfo.marginTop,
                        marginLeft: tooltipPositioning.periodInfo.marginLeft,
                        marginRight: tooltipPositioning.periodInfo.marginRight
                      }}
                    >
                      <span className="text-white dark:text-gray-900 text-xs font-bold">!</span>
                    </div>
                  </Tooltip>
                )}
              </div>
            </div>

            {/* Кастомный период */}
            {showCustomPeriod && (
              <>
                <div className="flex items-center gap-2">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                      Дата начала
                    </label>
                    <input
                      type="date"
                      value={customDateFrom}
                      onChange={handleCustomDateFromChange}
                      min={getDateLimits().min}
                      max={getDateLimits().max}
                      className={`w-full border rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                        dateValidationError ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'
                      }`}
                    />
                  </div>

                  {/* Тултип с ошибкой валидации */}
                  {dateValidationError && (
                    <Tooltip text={dateValidationError} position="top" delay={0}>
                      <div
                        className="w-5 h-5 bg-red-500 hover:bg-red-600 dark:bg-red-400 dark:hover:bg-red-300 rounded-full flex items-center justify-center cursor-help transition-colors"
                        style={{
                          marginTop: tooltipPositioning.validationError.marginTop,
                          marginLeft: tooltipPositioning.validationError.marginLeft,
                          marginRight: tooltipPositioning.validationError.marginRight
                        }}
                      >
                        <span className="text-white dark:text-gray-900 text-xs font-bold">!</span>
                      </div>
                    </Tooltip>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                    Дата окончания
                  </label>
                  <input
                    type="date"
                    value={customDateTo}
                    onChange={handleCustomDateToChange}
                    min={customDateFrom || getDateLimits().min}
                    max={getDateLimits().max}
                    className={`border rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                      dateValidationError ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'
                    }`}
                  />
                </div>
              </>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                Тип рейтинга
              </label>
              <select
                value={metric}
                onChange={(e) => setMetric(e.target.value)}
                className="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">Показать все</option>
                <option value="reader_rating">Рейтинг читателя</option>
                <option value="author_rating">Авторский рейтинг</option>
              </select>
            </div>
          </div>
        </div>

        {/* История изменений */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              История изменений
            </h2>
            <div className="flex items-center gap-2">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Показать:
              </label>
              <select
                value={pageSize}
                onChange={(e) => handlePageSizeChange(e.target.value)}
                className="border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full divide-y divide-gray-200 dark:divide-gray-700" style={{ tableLayout: 'fixed', minWidth: '800px' }}>
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th
                    className="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    style={{ width: '140px' }}
                  >
                    Дата
                  </th>
                  <th
                    className="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    style={{ width: '155px' }}
                  >
                    Тип рейтинга
                  </th>
                  <th
                    className="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    style={{ width: 'auto' }}
                  >
                    Список событий
                  </th>
                  <th
                    className="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    style={{ width: '100px' }}
                  >
                    Изменение
                  </th>
                  <th
                    className="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    style={{ width: '200px' }}
                  >
                    Было → Стало
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {data?.history?.items?.map((item) => (
                  <tr key={item.id}>
                    <td className="px-3 py-4 text-sm text-gray-900 dark:text-white text-center" style={{ width: '140px' }}>
                      <div className="truncate" title={formatDate(item.created_at)}>
                        {formatDate(item.created_at)}
                      </div>
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900 dark:text-white text-center" style={{ width: '145px' }}>
                      <div className="truncate" title={item.metric_name}>
                        {item.metric_name}
                      </div>
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900 dark:text-white" style={{ width: 'auto' }}>
                      <div className="flex flex-col gap-1">
                        <span className="break-words">{item.action_type}</span>
                        {shouldShowBookLink(item.action_type) && item.related_object && item.related_object.type === 'book' && (
                          <a
                            href={`/books/${item.related_object.id}`}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-xs underline break-words"
                            target="_blank"
                            rel="noopener noreferrer"
                            title={item.related_object.title}
                          >
                            📖 {item.related_object.title}
                          </a>
                        )}
                      </div>
                    </td>
                    <td className={`px-3 py-4 text-sm font-medium text-center ${getActionColor(item.change_delta)}`} style={{ width: '100px' }}>
                      <div className="truncate" title={`${item.change_delta > 0 ? '+' : ''}${item.change_delta}`}>
                        {item.change_delta > 0 ? '+' : ''}{item.change_delta}
                      </div>
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900 dark:text-white text-center" style={{ width: '170px' }}>
                      <div className="truncate" title={`${item.old_value} → ${item.new_value}`}>
                        {item.old_value} → {item.new_value}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Пагинация */}
          {data?.history && data.history.total > pageSize && (
            <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Показано {((page - 1) * pageSize) + 1}-{Math.min(page * pageSize, data.history.total)} из {data.history.total}
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setPage(page - 1)}
                    disabled={page === 1}
                    className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-gray-800 transition-colors duration-200"
                  >
                    Назад
                  </button>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.ceil(data.history.total / pageSize) }, (_, i) => i + 1)
                      .filter(pageNum => {
                        // Показываем только страницы рядом с текущей
                        return pageNum === 1 ||
                               pageNum === Math.ceil(data.history.total / pageSize) ||
                               Math.abs(pageNum - page) <= 2;
                      })
                      .map((pageNum, index, array) => {
                        // Добавляем многоточие если есть пропуск
                        const prevPageNum = array[index - 1];
                        const showEllipsis = prevPageNum && pageNum - prevPageNum > 1;

                        return (
                          <Fragment key={pageNum}>
                            {showEllipsis && (
                              <span className="px-2 py-1 text-sm text-gray-500 dark:text-gray-400">...</span>
                            )}
                            <button
                              onClick={() => setPage(pageNum)}
                              className={`px-3 py-1 text-sm rounded transition-colors duration-200 ${
                                page === pageNum
                                  ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm'
                                  : 'border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500'
                              }`}
                            >
                              {pageNum}
                            </button>
                          </Fragment>
                        );
                      })}
                  </div>

                  <button
                    onClick={() => setPage(page + 1)}
                    disabled={page >= Math.ceil(data.history.total / pageSize)}
                    className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-gray-800 transition-colors duration-200"
                  >
                    Вперед
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
    </Container>
  );
}

export default UserRatingStats;
