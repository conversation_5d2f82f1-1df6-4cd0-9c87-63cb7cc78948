import React, { useState, useRef, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Button, Input, message, Popconfirm, Tooltip, Dropdown, Menu, Modal, Checkbox, Popover } from 'antd';
import { EditorContent, useEditor, NodeViewWrapper, NodeViewContent, ReactNodeViewRenderer } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Underline from '@tiptap/extension-underline';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import "./bio-editor-x.css";
import { useTheme } from '../../theme/ThemeContext';
import { PictureOutlined } from '@ant-design/icons';
import ResizableImageNodeView from '../ResizableImageNodeView.jsx';
import EmojiMartModal from '../EmojiMartModal.jsx';
import { Mark, mergeAttributes, Node, Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { Decoration, DecorationSet } from '@tiptap/pm/view';

const FontSize = Mark.create({
  name: 'fontSize',
  addOptions() {
    return {
      types: ['textStyle'],
    };
  },
  addAttributes() {
    return {
      fontSize: {
        default: null,
        parseHTML: element => element.style.fontSize || null,
        renderHTML: attributes => {
          if (!attributes.fontSize) return {};
          return { style: `font-size: ${attributes.fontSize}` };
        },
      },
    };
  },
  parseHTML() {
    return [
      {
        style: 'font-size',
      },
    ];
  },
  renderHTML({ HTMLAttributes }) {
    return ['span', mergeAttributes(HTMLAttributes), 0];
  },
  addCommands() {
    return {
      setFontSize: size => ({ chain }) => chain().setMark('fontSize', { fontSize: size }).run(),
      unsetFontSize: () => ({ chain }) => chain().setMark('fontSize', { fontSize: null }).run(),
    };
  },
});

// ParagraphSymbols Extension с decorations
const ParagraphSymbols = Extension.create({
  name: 'paragraphSymbols',

  addOptions() {
    return {
      visible: false,
    };
  },

  addStorage() {
    return {
      visible: this.options.visible,
    };
  },

  addCommands() {
    return {
      showParagraphSymbols: () => ({ editor }) => {
        this.storage.visible = true;
        editor.view.dispatch(editor.state.tr);
        return true;
      },
      hideParagraphSymbols: () => ({ editor }) => {
        this.storage.visible = false;
        editor.view.dispatch(editor.state.tr);
        return true;
      },
      toggleParagraphSymbols: () => ({ editor }) => {
        this.storage.visible = !this.storage.visible;
        editor.view.dispatch(editor.state.tr);
        return true;
      },
    };
  },

  addProseMirrorPlugins() {
    const extension = this;

    return [
      new Plugin({
        key: new PluginKey('paragraphSymbols'),
        
        state: {
          init() {
            return DecorationSet.empty;
          },
          
          apply(tr, decorationSet) {
            if (!extension.storage.visible) {
              return DecorationSet.empty;
            }

            const decorations = [];
            const { doc } = tr;

            doc.descendants((node, pos) => {
              if (node.type.name === 'paragraph') {
                let symbolPos;
                
                if (node.content.size === 0 || (node.content.size === 1 && node.firstChild?.type.name === 'hardBreak')) {
                  symbolPos = pos + 1;
                } else {
                  symbolPos = pos + node.nodeSize - 1;
                }
                
                const decoration = Decoration.widget(symbolPos, () => {
                  const span = document.createElement('span');
                  span.className = 'paragraph-symbol';
                  span.textContent = '¶';
                  span.style.cssText = `
                    color: #b0b0b0;
                    opacity: 0.6;
                    font-family: serif;
                    font-size: 0.9em;
                    margin-left: 2px;
                    pointer-events: none;
                    user-select: none;
                    display: inline;
                  `;
                  return span;
                }, {
                  side: 1,
                  ignoreSelection: true,
                });

                decorations.push(decoration);
              }
            });

            return DecorationSet.create(doc, decorations);
          },
        },
        
        props: {
          decorations(state) {
            return this.getState(state);
          },
        },
      }),
    ];
  },
});

// Расширяем TextStyle для поддержки color и backgroundColor
const CustomTextStyle = TextStyle.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      color: {
        default: null,
        parseHTML: element => element.style.color || null,
        renderHTML: attributes => {
          if (!attributes.color) return {};
          return { style: `color: ${attributes.color}` };
        },
      },
      backgroundColor: {
        default: null,
        parseHTML: element => element.style.backgroundColor || null,
        renderHTML: attributes => {
          if (!attributes.backgroundColor) return {};
          return { style: `background-color: ${attributes.backgroundColor}` };
        },
      },
      fontSize: {
        default: null,
        parseHTML: element => element.style.fontSize || null,
        renderHTML: attributes => {
          if (!attributes.fontSize) return {};
          return { style: `font-size: ${attributes.fontSize}` };
        },
      },
    }
  },
});

const ChapterEditor = forwardRef(({
  initialTitle = '',
  initialContent = '',
  onSave,
  onCancel,
  loading = false,
  bookId,
  chapterOrder = 1,
  chapterId = undefined,
  username,
  imgIndex = 1,
  showToolbar = true,
  autoIndent = false,
}, ref) => {
  const [title, setTitle] = useState(initialTitle);
  const imgCounter = useRef(imgIndex);
  const [isFocused, setIsFocused] = useState(false);
  const { theme } = useTheme();

  const colorInputRef = useRef();
  const [linkModal, setLinkModal] = useState({ open: false, href: '', blank: false, from: null, to: null });
  const [showLinkWarn, setShowLinkWarn] = useState(false);
  const [emojiModalOpen, setEmojiModalOpen] = useState(false);
  const [showParagraphSymbols, setShowParagraphSymbols] = useState(false);
  const linkBtnRef = useRef();
  const fileInputRef = useRef();

  const ThemedResizableImageNodeView = (props) => {
    // Передаем функцию загрузки для био
    const bioUploadFunction = async (file) => {
      if (!username) {
        throw new Error('Ошибка: не хватает данных для загрузки');
      }
      
      if (!file) {
        throw new Error('Ошибка: файл не выбран');
      }
      
      const formData = new FormData();
      formData.append('image', file);
      formData.append('width', 400);
      formData.append('height', 300);
      
      const csrfToken = getCookie('csrftoken');
      
      const res = await fetch('/api/auth/bio/upload-image/', {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        body: formData,
        credentials: 'include',
      });
      
      if (!res.ok) throw new Error('Ошибка загрузки');
      
      const data = await res.json();
      
      return data;
    };
    
    return <ResizableImageNodeView {...props} uploadImageFunction={bioUploadFunction} />;
  };

  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.extend({
        addAttributes() {
          return {
            src: { 
              default: null,
              parseHTML: element => element.getAttribute('src'),
              renderHTML: attributes => {
                if (!attributes.src) return {};
                return { src: attributes.src };
              },
            },
            alt: { 
              default: null,
              parseHTML: element => element.getAttribute('alt'),
              renderHTML: attributes => {
                if (!attributes.alt) return {};
                return { alt: attributes.alt };
              },
            },
            title: { 
              default: null,
              parseHTML: element => element.getAttribute('title'),
              renderHTML: attributes => {
                if (!attributes.title) return {};
                return { title: attributes.title };
              },
            },
            width: { 
              default: '50',
              parseHTML: element => {
                // Пробуем получить из data-width, потом из style.width, потом дефолт
                const dataWidth = element.getAttribute('data-width');
                if (dataWidth) return dataWidth;
                
                const styleWidth = element.style.width;
                if (styleWidth) {
                  // Конвертируем из пикселей или процентов в проценты
                  return styleWidth.replace('%', '').replace('px', '');
                }
                
                return '50';
              },
              renderHTML: attributes => {
                if (!attributes.width) return {};
                return { 
                  'data-width': attributes.width,
                  style: `width: ${attributes.width}%`
                };
              },
            },
            align: { 
              default: 'center',
              parseHTML: element => element.getAttribute('data-align') || 'center',
              renderHTML: attributes => {
                if (!attributes.align) return {};
                return { 'data-align': attributes.align };
              },
            },
            textWrap: { 
              default: 'break',
              parseHTML: element => element.getAttribute('data-text-wrap') || 'break',
              renderHTML: attributes => {
                if (!attributes.textWrap) return {};
                return { 'data-text-wrap': attributes.textWrap };
              },
            },
            caption: { 
              default: '',
              parseHTML: element => element.getAttribute('data-caption') || '',
              renderHTML: attributes => {
                if (!attributes.caption) return {};
                return { 'data-caption': attributes.caption };
              },
            },
            class: {
              default: 'align-center',
              parseHTML: element => element.getAttribute('class') || 'align-center',
              renderHTML: attributes => {
                if (!attributes.class) return {};
                return { class: attributes.class };
              },
            },
          };
        },
        // Убираем inline: true, оставляем как блочный элемент
        group: 'block',
        atom: true, // Изображение атомарно - не содержит внутреннего контента
        selectNodeOnClick: true,
        addNodeView() {
          return ReactNodeViewRenderer(ThemedResizableImageNodeView);
        },
        addCommands() {
          return {
            setImage: (options) => ({ tr, dispatch, state }) => {
              const { schema } = state;
              const imageNode = schema.nodes.image.create(options);
              const paragraphNode = schema.nodes.paragraph.create();
              
              if (dispatch) {
                const transaction = tr.replaceSelectionWith(imageNode).insert(tr.selection.to, paragraphNode);
                dispatch(transaction);
              }
              
              return true;
            },
          };
        },
      }),
      Underline,
      Link,
      TextAlign.configure({ types: ['heading', 'paragraph'] }),
      CustomTextStyle,
      Color,
      FontSize,
      ParagraphSymbols,
    ],
    content: initialContent,
    editorProps: {
      handleDrop(view, event, _slice, moved) {
        if (moved) return false;
        const files = Array.from(event.dataTransfer.files || []);
        if (files.length > 0 && files[0].type.startsWith('image/')) {
          event.preventDefault();
          uploadImage(files[0]);
          return true;
        }
        return false;
      },
      handleClick(view, pos, event) {
        const a = event.target.closest('a');
        if (a && view.editable) {
          event.preventDefault();
          const { href, target } = a;
          const from = view.posAtDOM(a, 0);
          openLinkModal({
            href,
            blank: target === '_blank',
            from,
            to: from + a.textContent.length,
          });
          return true;
        }
        return false;
      },
      handleDoubleClick(view, pos, event) {
        // Обработчик двойного клика для создания параграфов
        const { state } = view;
        const { doc, schema, selection } = state;
        const clickPos = doc.resolve(pos);
        
        // Вспомогательная функция для проверки пустого места
        const isEmptyArea = (resolvedPos) => {
          const node = resolvedPos.nodeAfter;
          const parent = resolvedPos.parent;
          
          return (
            // Пустой параграф
            (parent.type.name === 'paragraph' && parent.content.size === 0) ||
            // Конец документа
            (!node && parent.type.name === 'doc') ||
            // Между блоками
            (!node && resolvedPos.parentOffset === parent.content.size)
          );
        };
        
        // Определяем, где мы кликнули
        const clickedInEmpty = isEmptyArea(clickPos);
        
        if (clickedInEmpty) {
          // Простая логика: создаем один новый параграф в месте клика
          const paragraph = schema.nodes.paragraph.create();
          
          // Определяем позицию для вставки
          let insertPos;
          if (clickPos.parent.type.name === 'paragraph' && clickPos.parent.content.size === 0) {
            // Если кликнули в пустой параграф, вставляем после него
            insertPos = clickPos.after(clickPos.depth);
          } else {
            // Иначе вставляем в текущую позицию
            insertPos = pos;
          }
          
          // Создаем и применяем транзакцию
          const transaction = state.tr.insert(insertPos, paragraph);
          view.dispatch(transaction);
          
          // Устанавливаем курсор в новый параграф
          setTimeout(() => {
            const newState = view.state;
            const newPos = insertPos + 1;
            if (newPos <= newState.doc.content.size) {
              const newSelection = newState.selection.constructor.create(newState.doc, newPos);
              view.dispatch(newState.tr.setSelection(newSelection));
              view.focus();
            }
          }, 10);
          
          return true;
        }
        
        return false;
      },
    },
  });

  useEffect(() => {
    if (editor) {
      window.tiptapEditor = editor;
      
      // Создаем глобальную функцию для обратной совместимости
      window.uploadImageFunction = async (file) => {
        if (!username) {
          throw new Error('Ошибка: не хватает данных для загрузки');
        }
        
        if (!file) {
          throw new Error('Ошибка: файл не выбран');
        }
        
        const formData = new FormData();
        formData.append('image', file);
        formData.append('width', 400);
        formData.append('height', 300);
        
        const csrfToken = getCookie('csrftoken');
        
        const res = await fetch('/api/auth/bio/upload-image/', {
          method: 'POST',
          headers: {
            'X-CSRFToken': csrfToken,
          },
          body: formData,
          credentials: 'include',
        });
        
        if (!res.ok) throw new Error('Ошибка загрузки');
        
        const data = await res.json();
        
        return data;
      };
    }
  }, [editor, username]);

  useEffect(() => {
    if (!editor || !initialContent) return;
    
    // Подмена src на актуальные ссылки при открытии редактора
    const htmlWithRelative = initialContent.replace(/https?:\/\/[^\"]+(\/media\/public\/[\w\/-]+\.(jpg|jpeg|png|gif|webp))/g, '$1');
    
    // Извлекаем пути к изображениям из HTML
    const paths = extractBioImagePathsFromHtml(htmlWithRelative);
    
    if (paths.length === 0) {
      const processedContent = preprocessBioHtml(htmlWithRelative);
      editor.commands.setContent(processedContent);
      return;
    }
    
    // Запрашиваем новые ссылки и подменяем src
    fetchBioImageLinks(paths).then(urlMap => {
      const newHtml = replaceBioImageSrcInHtml(htmlWithRelative, urlMap);
      const processedContent = preprocessBioHtml(newHtml);
      editor.commands.setContent(processedContent);
    }).catch((error) => {
      console.error('Ошибка получения ссылок на изображения:', error);
      // fallback: просто отдать исходный контент
      const processedContent = preprocessBioHtml(htmlWithRelative);
      editor.commands.setContent(processedContent);
    });
  }, [editor, initialContent]);



  const preprocessBioHtml = (html) => {
    if (!html) return html;
    
    const div = document.createElement('div');
    div.innerHTML = html;
    
    // Обрабатываем изображения в wrapper-элементах
    div.querySelectorAll('.custom-resizable-image').forEach(wrapper => {
      const img = wrapper.querySelector('img');
      const captionDiv = wrapper.querySelector('.image-caption');
      
      if (img && captionDiv) {
        // Переносим текст подписи в атрибут data-caption изображения
        const captionText = captionDiv.textContent || captionDiv.innerText || '';
        if (captionText.trim()) {
          img.setAttribute('data-caption', captionText.trim());
        }
        
        // Переносим настройки выравнивания и обтекания из wrapper на изображение
        if (wrapper.classList.contains('float-left')) {
          img.setAttribute('data-align', 'left');
          img.setAttribute('data-text-wrap', 'wrap');
          img.setAttribute('class', 'float-left');
        } else if (wrapper.classList.contains('float-right')) {
          img.setAttribute('data-align', 'right');
          img.setAttribute('data-text-wrap', 'wrap');
          img.setAttribute('class', 'float-right');
        } else if (wrapper.classList.contains('align-left')) {
          img.setAttribute('data-align', 'left');
          img.setAttribute('data-text-wrap', 'break');
          img.setAttribute('class', 'align-left');
        } else if (wrapper.classList.contains('align-right')) {
          img.setAttribute('data-align', 'right');
          img.setAttribute('data-text-wrap', 'break');
          img.setAttribute('class', 'align-right');
        } else {
          img.setAttribute('data-align', 'center');
          img.setAttribute('data-text-wrap', 'break');
          img.setAttribute('class', 'align-center');
        }
        
        // Переносим ширину из стиля wrapper в data-width изображения
        const wrapperWidth = wrapper.style.width;
        if (wrapperWidth && wrapperWidth.includes('%')) {
          const widthValue = wrapperWidth.replace('%', '').trim();
          if (widthValue && !isNaN(widthValue)) {
            img.setAttribute('data-width', widthValue);
          }
        }
        
        // Удаляем элемент подписи
        captionDiv.remove();
        
        // Заменяем wrapper на изображение
        wrapper.parentNode.insertBefore(img, wrapper);
        wrapper.remove();
      }
    });
    
    return div.innerHTML;
  };

  const uploadImage = async (file) => {
    if (!username) {
      console.error('Отсутствуют необходимые данные:', { username });
      message.error('Ошибка: не хватает данных для загрузки');
      return;
    }
    
    if (!file) {
      console.error('Файл не передан в функцию uploadImage');
      message.error('Ошибка: файл не выбран');
      return;
    }
    
    const formData = new FormData();
    formData.append('image', file);
    formData.append('width', 400);
    formData.append('height', 300);
    
    message.loading({ content: 'Загрузка изображения...', key: 'imageUpload' });
    
    const csrfToken = getCookie('csrftoken');
    
    try {
      const res = await fetch('/api/auth/bio/upload-image/', {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
        },
        body: formData,
        credentials: 'include',
      });
      
      if (!res.ok) throw new Error('Ошибка загрузки');
      
      const data = await res.json();
      
      if (data.url) {
        const width = data.width ? parseInt(data.width, 10) : 50;
        
        if (editor) {
          // Принудительно восстанавливаем фокус и проверяем состояние
          editor.commands.focus();
          
          // Ждём небольшую задержку для восстановления фокуса
          setTimeout(() => {
            // Вставляем изображение
            const success = editor.chain().focus().setImage({ 
              src: data.url, 
              width,
              align: 'center',
              textWrap: 'break',
              caption: '',
              class: 'align-center'
            }).run();
            
            if (success) {
              message.success({ content: 'Изображение успешно загружено', key: 'imageUpload' });
            } else {
              console.error('Не удалось вставить изображение - команда не выполнилась');
              message.error({ content: 'Ошибка вставки изображения в редактор', key: 'imageUpload' });
            }
          }, 50);
        } else {
          console.error('Редактор не инициализирован!');
          message.warning({ content: 'Редактор еще не готов. Попробуйте еще раз через секунду.', key: 'imageUpload' });
          console.warn('Редактор еще не смонтирован, вставка невозможна!');
        }
      } else {
        console.error('URL изображения отсутствует в ответе:', data);
        message.error({ content: 'Ошибка: не получен url изображения', key: 'imageUpload' });
      }
    } catch (e) {
      console.error('Error uploading image:', e);
      message.error({ content: 'Ошибка загрузки изображения', key: 'imageUpload' });
    }
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    // Проверяем размер файла (максимум 5MB)
    const maxFileSize = 5 * 1024 * 1024; // 5MB в байтах
    if (file.size > maxFileSize) {
      message.error('Размер файла превышает 5MB');
      e.target.value = '';
      return;
    }
    
    // Проверяем формат файла (поддерживаемые растровые форматы)
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/bmp', 'image/tiff', 'image/gif'];
    if (!allowedTypes.includes(file.type.toLowerCase())) {
      message.error('Неподдерживаемый формат файла. Поддерживаются: JPEG, PNG, WebP, BMP, TIFF, GIF');
      e.target.value = '';
      return;
    }
    
    // Сразу загружаем файл, сервер сам обработает изображение и конвертирует в WebP
    uploadImage(file);
    
    // Очищаем input для возможности повторной загрузки того же файла
    e.target.value = '';
  };



  const handleSave = async () => {
    if (!editor) return;
    
    // Получаем HTML из редактора
    const rawHtml = editor.getHTML();
    
    // Обрабатываем HTML перед сохранением
    const processedHtml = postprocessBioHtml(rawHtml);
    
    // Очищаем неиспользуемые изображения с S3
    try {
      const csrfToken = getCookie('csrftoken');
      await fetch('/api/auth/bio/cleanup-images/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({ html_content: processedHtml }),
      });
    } catch (error) {
      console.warn('Ошибка очистки изображений:', error);
    }
    
    onSave(processedHtml);
  };

  const postprocessBioHtml = (html) => {
    if (!html) return html;
    
    const div = document.createElement('div');
    div.innerHTML = html;
    
    // Обрабатываем все изображения в wrapper-элементах
    div.querySelectorAll('.custom-resizable-image').forEach(wrapper => {
      const img = wrapper.querySelector('img');
      if (!img) return;
      
      // Получаем настройки из атрибутов изображения
      const width = img.getAttribute('data-width') || img.getAttribute('width') || '50';
      const align = img.getAttribute('data-align') || 'center';
      const textWrap = img.getAttribute('data-text-wrap') || 'break';
      const caption = img.getAttribute('data-caption') || '';
      
      // Определяем CSS класс для wrapper
      let wrapperClass = 'custom-resizable-image';
      if (align === 'left') {
        wrapperClass += textWrap === 'wrap' ? ' float-left' : ' align-left';
      } else if (align === 'right') {
        wrapperClass += textWrap === 'wrap' ? ' float-right' : ' align-right';
      } else {
        wrapperClass += ' align-center';
      }
      
      // Обновляем класс wrapper
      wrapper.className = wrapperClass;
      
      // Устанавливаем ширину wrapper
      const widthPercent = Math.max(25, Math.min(100, parseInt(width, 10) || 50));
      wrapper.style.width = widthPercent + '%';
      wrapper.style.maxWidth = '100%';
      wrapper.style.position = 'relative';
      
      // Очищаем стили изображения
      img.style.width = '100%';
      img.style.maxWidth = '100%';
      img.style.display = 'block';
      img.style.borderRadius = '8px';
      img.style.height = 'auto';
      
      // Удаляем старые подписи
      const existingCaptions = wrapper.querySelectorAll('.image-caption');
      existingCaptions.forEach(cap => cap.remove());
      
      // Добавляем новую подпись если есть
      if (caption && caption.trim()) {
        const captionDiv = document.createElement('div');
        captionDiv.className = 'image-caption';
        captionDiv.textContent = caption.trim();
        wrapper.appendChild(captionDiv);
      }
    });
    
    return div.innerHTML;
  };

  // Панель инструментов
  const toolbarBg = theme === 'dark' ? '#23272f' : '#f3f4f6';
  const iconColor = theme === 'dark' ? '#fff' : '#222';
  const iconActiveBg = '#2563eb';
  const iconActiveColor = '#fff';

  const selectBg = theme === 'dark' ? '#23272f' : '#fff';
  const selectColor = theme === 'dark' ? '#fff' : '#222';
  const selectBorder = theme === 'dark' ? '#374151' : '#d1d5db';
  const selectOptionBg = theme === 'dark' ? '#23272f' : '#fff';
  const selectOptionHover = theme === 'dark' ? '#374151' : '#e5e7eb';
  const selectOptionColor = theme === 'dark' ? '#fff' : '#222';

  // Создаем специальный компонент для выпадающего меню, который не теряет выделение
  const FontSizeSelector = () => {
    const [isOpen, setIsOpen] = useState(false);
    const currentSize = editor.getAttributes('textStyle').fontSize || '16px';
    const sizeOptions = [
      { value: '16px', label: 'Обычный' },
      { value: '13px', label: 'Мелкий' },
      { value: '20px', label: 'Крупный' },
      { value: '28px', label: 'Заголовок' },
    ];
    
    const handleSizeChange = (value) => {
      // Получаем текущие атрибуты textStyle
      const { color, backgroundColor, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus().setMark('textStyle', {
        color,
        backgroundColor,
        fontSize: value,
      }).run();
      setIsOpen(false);
    };
    
    const toggleDropdown = (e) => {
      e.preventDefault(); // Важно! Предотвращает потерю фокуса и выделения
      setIsOpen(!isOpen);
    };
    
    const handleOptionMouseDown = (e, value) => {
      e.preventDefault(); // Предотвращает потерю выделения
      handleSizeChange(value);
    };
    
    return (
      <div className="relative inline-block" style={{ marginLeft: 4, marginRight: 4 }}>
        <button
          onMouseDown={toggleDropdown}
          className="flex items-center justify-between px-2 py-1 border rounded"
          style={{
            background: theme === 'dark' ? '#1f2937' : '#ffffff',
            color: theme === 'dark' ? '#ffffff' : '#000000',
            borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
            minWidth: '90px',
            height: '32px',
            fontSize: '14px',
          }}
        >
          <span>{sizeOptions.find(opt => opt.value === currentSize)?.label || 'Обычный'}</span>
          <span style={{ marginLeft: '4px' }}>▼</span>
        </button>
        
        {isOpen && (
          <div
            className="absolute left-0 mt-1 border rounded shadow-lg z-10"
            style={{
              background: theme === 'dark' ? '#1f2937' : '#ffffff',
              borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
              minWidth: '90px',
              zIndex: 1000,
            }}
          >
            {sizeOptions.map(option => (
              <div
                key={option.value}
                onMouseDown={(e) => handleOptionMouseDown(e, option.value)}
                className="px-3 py-1 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                style={{
                  background: currentSize === option.value 
                    ? (theme === 'dark' ? '#374151' : '#f3f4f6') 
                    : 'transparent',
                  color: theme === 'dark' ? '#ffffff' : '#000000',
                  fontSize: option.value,
                }}
              >
                {option.label}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Добавляю функцию для обработки выравнивания текста
  const handleTextAlign = (alignment) => {
    // Если текст уже имеет это выравнивание, сбрасываем на левое выравнивание (по умолчанию)
    if (editor.isActive({ textAlign: alignment })) {
      editor.chain().focus().setTextAlign('left').run();
    } else {
      editor.chain().focus().setTextAlign(alignment).run();
    }
  };

  const Toolbar = () => editor && (
    <div className="flex flex-wrap gap-1 mb-2 rounded px-2 py-1 items-center justify-start"
      style={{
        background: toolbarBg,
        border: '1.5px solid #e5e7eb',
        minHeight: 40,
        flex: 1,
        marginRight: 10,
      }}
    >
      <Tooltip 
        title={<div className="tooltip-content"><div>Жирный</div><div className="editor-shortcut">Ctrl+B</div></div>} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleBold().run();
          }}
          className={`px-2 py-1 rounded ${editor.isActive('bold') ? 'bg-blue-600 text-white' : ''}`}
          style={{
            background: editor.isActive('bold') ? iconActiveBg : 'transparent',
            color: editor.isActive('bold') ? iconActiveColor : iconColor,
          }}
        >
          <span style={{ fontWeight: 'bold' }}>Ж</span>
        </button>
      </Tooltip>
      <Tooltip 
        title={<div className="tooltip-content"><div>Курсив</div><div className="editor-shortcut">Ctrl+I</div></div>} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleItalic().run();
          }}
          className={`px-2 py-1 rounded ${editor.isActive('italic') ? 'bg-blue-600 text-white' : ''}`}
          style={{
            background: editor.isActive('italic') ? iconActiveBg : 'transparent',
            color: editor.isActive('italic') ? iconActiveColor : iconColor,
          }}
        >
          <span style={{ fontStyle: 'italic' }}>К</span>
        </button>
      </Tooltip>
      <Tooltip 
        title={<div className="tooltip-content"><div>Подчёркнутый</div><div className="editor-shortcut">Ctrl+U</div></div>} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleUnderline().run();
          }}
          className={`px-2 py-1 rounded ${editor.isActive('underline') ? 'bg-blue-600 text-white' : ''}`}
          style={{
            background: editor.isActive('underline') ? iconActiveBg : 'transparent',
            color: editor.isActive('underline') ? iconActiveColor : iconColor,
          }}
        >
          <span style={{ textDecoration: 'underline' }}>П</span>
        </button>
      </Tooltip>
      <Tooltip 
        title={<div className="tooltip-content"><div>Зачёркнутый</div></div>} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleStrike().run();
          }}
          className={`px-2 py-1 rounded ${editor.isActive('strike') ? 'bg-blue-600 text-white' : ''}`}
          style={{
            background: editor.isActive('strike') ? iconActiveBg : 'transparent',
            color: editor.isActive('strike') ? iconActiveColor : iconColor,
          }}
        >
          <span style={{ textDecoration: 'line-through' }}>З</span>
        </button>
      </Tooltip>
      <FontSizeSelector />

      <CustomColorDropdown editor={editor} theme={theme} />

      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

      {/* Кнопки отмены/повтора */}
      <div className="flex items-center gap-1">
        <Tooltip 
          title={<div className="tooltip-content"><div>Отменить</div><div className="editor-shortcut">Ctrl+Z</div></div>} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().undo().run();
            }}
            className="px-2 py-1 rounded"
            style={{
              background: 'transparent',
              color: iconColor,
              opacity: editor.can().undo() ? 1 : 0.5,
              cursor: editor.can().undo() ? 'pointer' : 'default',
            }}
            disabled={!editor.can().undo()}
          >
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
              <path d="M7 15L2 10L7 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2 10H11C13.2091 10 15 11.7909 15 14V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={<div className="tooltip-content"><div>Повторить</div><div className="editor-shortcut">Ctrl+Y</div></div>} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              editor.chain().focus().redo().run();
            }}
            className="px-2 py-1 rounded"
            style={{
              background: 'transparent',
              color: iconColor,
              opacity: editor.can().redo() ? 1 : 0.5,
              cursor: editor.can().redo() ? 'pointer' : 'default',
            }}
            disabled={!editor.can().redo()}
          >
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
              <path d="M11 5L16 10L11 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M16 10H7C4.79086 10 3 11.7909 3 14V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </button>
        </Tooltip>
      </div>

      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

      {/* Кнопки выравнивания текста */}
      <div className="flex items-center gap-1">
        <Tooltip 
          title={<div className="tooltip-content"><div>По левому краю</div><div className="editor-shortcut">Ctrl+Shift+L</div></div>} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('left');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'left' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'left' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M2 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={<div className="tooltip-content"><div>По центру</div><div className="editor-shortcut">Ctrl+Shift+E</div></div>} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('center');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'center' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'center' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M4 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm2-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={<div className="tooltip-content"><div>По правому краю</div><div className="editor-shortcut">Ctrl+Shift+R</div></div>} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('right');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'right' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'right' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M6 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-4-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm4-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-4-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
        <Tooltip 
          title={<div className="tooltip-content"><div>По ширине</div><div className="editor-shortcut">Ctrl+Shift+J</div></div>} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            onMouseDown={(e) => {
              e.preventDefault();
              handleTextAlign('justify');
            }}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive({ textAlign: 'justify' }) ? iconActiveBg : 'transparent',
              color: editor.isActive({ textAlign: 'justify' }) ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M2 12.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
            </svg>
          </button>
        </Tooltip>
      </div>

      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

      {/* Кнопка отображения символов параграфа */}
      <Tooltip 
        title={<div className="tooltip-content"><div>{showParagraphSymbols ? "Скрыть символы параграфа" : "Показать символы параграфа"}</div></div>} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={(e) => {
            e.preventDefault();
            if (editor && editor.extensionManager.extensions.find(ext => ext.name === 'paragraphSymbols')) {
              editor.commands.toggleParagraphSymbols();
              setShowParagraphSymbols(!showParagraphSymbols);
            }
          }}
          className={`px-2 py-1 rounded`}
          style={{
            background: showParagraphSymbols ? iconActiveBg : 'transparent',
            color: showParagraphSymbols ? iconActiveColor : iconColor,
          }}
        >
          <span style={{ fontFamily: 'serif', fontWeight: 'bold' }}>¶</span>
        </button>
      </Tooltip>
      <Popover
        open={showLinkWarn}
        onOpenChange={setShowLinkWarn}
        trigger="click"
        placement="bottom"
        content={
          <div style={{ maxWidth: 300, padding: '8px 12px' }}>
            <p style={{ margin: 0, fontSize: 14, lineHeight: 1.5 }}>
              Используйте кнопку "Ссылка" для добавления ссылок. Вставка URL напрямую не будет работать.
            </p>
          </div>
        }
      >
        <Tooltip 
          title={<div className="tooltip-content"><div>Добавить ссылку</div></div>} 
          placement="bottom" 
          classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
        >
          <button
            ref={linkBtnRef}
            onMouseDown={handleLinkButton}
            className={`px-2 py-1 rounded`}
            style={{
              background: editor.isActive('link') ? iconActiveBg : 'transparent',
              color: editor.isActive('link') ? iconActiveColor : iconColor,
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M4.715 6.542 3.343 7.914a3 3 0 1 0 4.243 4.243l1.828-1.829A3 3 0 0 0 8.586 5.5L8 6.086a1.002 1.002 0 0 0-.154.199 2 2 0 0 1 .861 3.337L6.88 11.45a2 2 0 1 1-2.83-2.83l.793-.792a4.018 4.018 0 0 1-.128-1.287z"/>
              <path d="M6.586 4.672A3 3 0 0 0 7.414 9.5l.775-.776a2 2 0 0 1-.896-3.346L9.12 3.55a2 2 0 1 1 2.83 2.83l-.793.792c.112.42.155.855.128 1.287l1.372-1.372a3 3 0 1 0-4.243-4.243L6.586 4.672z"/>
            </svg>
          </button>
        </Tooltip>
      </Popover>
      <Tooltip 
        title={<div className="tooltip-content"><div>Добавить изображение</div></div>} 
        placement="bottom" 
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onClick={(e) => {
            fileInputRef.current?.click();
          }}
          className="px-2 py-1 rounded"
          style={{
            background: 'transparent',
            color: iconColor,
            border: 'none',
            cursor: 'pointer',
          }}
        >
          <PictureOutlined style={{ fontSize: '16px' }} />
        </button>
      </Tooltip>
      <input 
        ref={fileInputRef}
        type="file" 
        accept=".jpg,.jpeg,.png,.webp,.bmp,.tiff,.gif,image/jpeg,image/png,image/webp,image/bmp,image/tiff,image/gif" 
        style={{ display: 'none' }} 
        onChange={handleImageUpload} 
      />
      
      {/* Разделитель */}
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>
      
      <Button size="small" onClick={() => setEmojiModalOpen(true)} style={{background: 'transparent', color: iconColor, border: 'none', marginLeft: 8}} title="Добавить эмодзи">
        😀
      </Button>
    </div>
  );

  useImperativeHandle(ref, () => ({
    handleSave,
  }));

  // Открыть модалку для ссылки
  const openLinkModal = (init = {}) => {
    setLinkModal({
      open: true,
      href: init.href || '',
      blank: !!init.blank,
      from: init.from ?? null,
      to: init.to ?? null,
    });
  };
  // Закрыть модалку
  const closeLinkModal = () => setLinkModal(l => ({ ...l, open: false }));

  // Клик по ссылке в редакторе — не переходить, а редактировать
  useEffect(() => {
    if (!editor) return;
    const handler = e => {
      const a = e.target.closest('a');
      if (a && editor.isEditable) {
        e.preventDefault();
        const pos = editor.view.posAtDOM(a, 0);
        const { href, target } = a;
        openLinkModal({
          href,
          blank: target === '_blank',
          from: pos,
          to: pos + a.textContent.length,
        });
      }
    };
    const dom = editor.view.dom;
    dom.addEventListener('click', handler);
    return () => dom.removeEventListener('click', handler);
  }, [editor]);

  // Кнопка на панели — открыть модалку для выделения или текущей ссылки
  const handleLinkButton = () => {
    if (!editor) return;
    
    // Фокусируемся на редакторе и проверяем выделение
    editor.commands.focus();
    const { from, to, empty } = editor.state.selection;
    const attrs = editor.getAttributes('link');
    
    if (attrs.href) {
      openLinkModal({ href: attrs.href, blank: attrs.target === '_blank', from, to });
    } else if (!empty) {
      openLinkModal({ from, to });
    } else {
      // ничего не выделено и не на ссылке
      setShowLinkWarn(true);
      setTimeout(() => setShowLinkWarn(false), 2000);
    }
  };

  // Применить/обновить ссылку
  const saveLink = () => {
    if (!linkModal.href) return;
    if (!/^https?:\/\//.test(linkModal.href)) {
      message.warning('Ссылка должна начинаться с http:// или https://');
      return;
    }
    
    if (linkModal.from !== null && linkModal.to !== null) {
      editor.commands.setTextSelection({ from: linkModal.from, to: linkModal.to });
      editor.chain().focus().extendMarkRange('link').setLink({ href: linkModal.href, target: linkModal.blank ? '_blank' : null }).run();
    } else {
      editor.chain().focus().extendMarkRange('link').setLink({ href: linkModal.href, target: linkModal.blank ? '_blank' : null }).run();
    }
    
    closeLinkModal();
  };
  
  // Удалить ссылку
  const removeLink = () => {
    if (linkModal.from !== null && linkModal.to !== null) {
      editor.commands.setTextSelection({ from: linkModal.from, to: linkModal.to });
    }
    editor.chain().focus().extendMarkRange('link').unsetLink().run();
    closeLinkModal();
  };

  // Синхронизация состояния extension с React state
  useEffect(() => {
    if (editor && editor.extensionManager.extensions.find(ext => ext.name === 'paragraphSymbols')) {
      const storage = editor.extensionStorage.paragraphSymbols;
      if (storage && storage.visible !== showParagraphSymbols) {
        if (showParagraphSymbols) {
          editor.commands.showParagraphSymbols();
        } else {
          editor.commands.hideParagraphSymbols();
        }
      }
    }
  }, [showParagraphSymbols, editor]);

  return (
    <div className="p-4 rounded-lg mb-4" style={{ background: theme === 'dark' ? '#181c23' : '#f9fafb', boxShadow: theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22' }}>
      <div className="mb-3 flex flex-col gap-2">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {showToolbar && <Toolbar />}
          <Popconfirm
            title="Сохранить изменения?"
            onConfirm={handleSave}
            onCancel={onCancel}
            okText="Сохранить"
            cancelText="Отмена"
            placement="left"
          >
            <span
              style={{
                marginLeft: 3,
                marginTop: -12,
                color: '#ef4444',
                fontSize: 28,
                cursor: 'pointer',
                transition: 'color 0.2s',
                userSelect: 'none',
              }}
              title="Закрыть редактор"
              onMouseOver={e => e.currentTarget.style.color = '#b91c1c'}
              onMouseOut={e => e.currentTarget.style.color = '#ef4444'}
            >
              &#10006;
            </span>
          </Popconfirm>
        </div>
        <div
          className={`tiptap-editor${autoIndent ? ' tiptap-indent' : ''}`}
          style={{
            minHeight: 320,
            maxHeight: 600,
            background: theme === 'dark' ? '#111827' : '#fff',
            color: theme === 'dark' ? '#fff' : '#222',
            border: theme === 'dark' ? '2px solid #374151' : '2px solid #d1d5db',
            borderRadius: 8,
            boxShadow: theme === 'dark' ? '0 1px 4px #11182722' : '0 1px 4px #e5e7eb22',
            outline: isFocused ? '2px solid #2563eb' : 'none',
            padding: '12px 16px',
            fontSize: '1rem',
            transition: 'background 0.2s, color 0.2s, outline 0.2s',
            overflowY: 'auto',
          }}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          tabIndex={-1}
        >
          <EditorContent
            editor={editor}
            className="tiptap-editor"
            spellCheck={true}
            style={{ outline: 'none', border: 'none', boxShadow: 'none', background: 'transparent', minHeight: 200, color: theme === 'dark' ? '#fff' : '#222' }}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
          />
        </div>
      </div>
      <div className="flex gap-2 mt-2">
        <Button type="primary" onClick={handleSave} loading={loading}>
          Сохранить
        </Button>
        <Button onClick={onCancel} disabled={loading} style={{ background: theme === 'dark' ? '#23272f' : '#fff', color: theme === 'dark' ? '#fff' : '#222', border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb' }}>
          Отмена
        </Button>
      </div>
      <Modal
        open={linkModal.open}
        onCancel={closeLinkModal}
        footer={null}
        centered
        closable={false}
      >
        <div style={{ fontSize: 16, color: theme === 'dark' ? '#e5e7eb' : '#374151', fontWeight: 600, marginBottom: 20, textAlign: 'center' }}>
          Добавить ссылку
        </div>
        
        <div style={{ marginBottom: 16 }}>
          <Input
            placeholder="https://example.com"
            value={linkModal.href}
            onChange={e => setLinkModal(l => ({ ...l, href: e.target.value }))}
            style={{ 
              marginBottom: 12,
              background: theme === 'dark' ? '#111827' : '#ffffff',
              borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
              color: theme === 'dark' ? '#ffffff' : '#111827',
            }}
            allowClear
          />
          
          <Checkbox
            checked={linkModal.blank}
            onChange={e => setLinkModal(l => ({ ...l, blank: e.target.checked }))}
            style={{
              color: theme === 'dark' ? '#ffffff' : '#374151',
            }}
          >
            <span style={{ color: theme === 'dark' ? '#ffffff' : '#374151' }}>
              Открывать в новом окне
            </span>
          </Checkbox>
        </div>
        
        {linkModal.href && !/^https?:\/\//.test(linkModal.href) && (
          <div style={{ color: '#ef4444', fontSize: 13, marginBottom: 16, textAlign: 'center' }}>
            Ссылка должна начинаться с http:// или https://
          </div>
        )}
        
        <div style={{ display: 'flex', justifyContent: 'space-between', gap: 16 }}>
          <Button 
            onClick={removeLink} 
            disabled={!editor?.isActive('link')}
            style={{ 
              minWidth: 100,
              background: theme === 'dark' ? '#181c23' : '#f3f4f6', 
              color: theme === 'dark' ? '#ef4444' : '#dc2626', 
              border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', 
              fontWeight: 500 
            }}
          >
            Удалить
          </Button>
          
          <div style={{ display: 'flex', gap: 8 }}>
            <Button 
              onClick={closeLinkModal}
              style={{ 
                minWidth: 80,
                background: theme === 'dark' ? '#181c23' : '#f3f4f6', 
                color: theme === 'dark' ? '#fff' : '#222', 
                border: theme === 'dark' ? '1.5px solid #374151' : '1.5px solid #e5e7eb', 
                fontWeight: 500 
              }}
            >
              Отмена
            </Button>
            <Button 
              type="primary" 
              onClick={saveLink} 
              disabled={!linkModal.href || !/^https?:\/\//.test(linkModal.href)}
              style={{ 
                minWidth: 100, 
                background: (!linkModal.href || !/^https?:\/\//.test(linkModal.href)) ? '#d1d5db' : '#60A5FA', 
                color: '#fff', 
                border: 'none', 
                fontWeight: 500 
              }}
            >
              Сохранить
            </Button>
          </div>
        </div>
      </Modal>
      <EmojiMartModal
        open={emojiModalOpen}
        onClose={() => setEmojiModalOpen(false)}
        onSelect={(emoji) => {
          if (editor) {
            editor.chain().focus().insertContent(emoji).run();
          }
          setEmojiModalOpen(false);
        }}
        theme={theme === 'dark' ? 'dark' : 'light'}
      />
      <style>{`
.tooltip-content {
  text-align: center;
  line-height: 1.4;
}
.editor-shortcut {
  font-size: 11px;
  opacity: 0.8;
  margin-top: 2px;
  font-weight: 400;
}
.tiptap-editor details {
  background: ${theme === 'dark' ? '#23272f' : '#f3f4f6'};
  border: 1.5px solid ${theme === 'dark' ? '#374151' : '#d1d5db'};
  border-radius: 8px;
  margin: 12px 0;
  padding: 0 0 0 0;
  transition: background 0.2s, border 0.2s;
}
.tiptap-editor summary {
  cursor: pointer;
  font-weight: 600;
  padding: 10px 16px;
  color: ${theme === 'dark' ? '#60A5FA' : '#2563eb'};
  background: none;
  border-radius: 8px 8px 0 0;
  outline: none;
  user-select: none;
}
.tiptap-editor details[open] summary {
  border-bottom: 1.5px solid ${theme === 'dark' ? '#374151' : '#d1d5db'};
}
.tiptap-editor details > *:not(summary) {
  padding: 12px 16px 16px 16px;
  color: ${theme === 'dark' ? '#fff' : '#222'};
}
`}</style>
    </div>
  );
});

export default ChapterEditor;

function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

// Утилита: извлечь пути к изображениям био из HTML
function extractBioImagePathsFromHtml(html) {
  const div = document.createElement('div');
  div.innerHTML = html;
  const imgs = div.querySelectorAll('img');
  
  const paths = [];
  imgs.forEach((img) => {
    const src = img.getAttribute('src');
    
    if (src && !/^https?:\/\//.test(src)) {
      paths.push(src);
    }
  });
  
  const uniquePaths = Array.from(new Set(paths));
  return uniquePaths;
}

// Утилита: заменить src в HTML на актуальные ссылки
function replaceBioImageSrcInHtml(html, urlMap) {
  const div = document.createElement('div');
  div.innerHTML = html;
  div.querySelectorAll('img').forEach(img => {
    const origSrc = img.getAttribute('src');
    if (urlMap[origSrc]) {
      img.setAttribute('src', urlMap[origSrc]);
    }
  });
  return div.innerHTML;
}

// Утилита: получить ссылки на изображения био с бэка
async function fetchBioImageLinks(paths) {
  const csrfToken = getCookie('csrftoken');
  const res = await fetch('/api/auth/bio/get-image-links/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken },
    credentials: 'include',
    body: JSON.stringify({ paths }),
  });
  if (!res.ok) throw new Error('Ошибка получения ссылок');
  return await res.json();
}



// Создаем новый компонент для выбора цвета, который не теряет выделение
function CustomColorDropdown({ editor, theme }) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectionState, setSelectionState] = useState(null);
  const [activeTab, setActiveTab] = useState('text'); // 'text' или 'background'
  
  // Получаем текущие цвета текста и фона
  const currentTextColor = editor.getAttributes('textStyle').color;
  const currentBgColor = editor.getAttributes('textStyle').backgroundColor;
  
  // Определяем, используются ли автоматические цвета
  const isTextAuto = !currentTextColor || currentTextColor === '#000000' || currentTextColor === '#fff' || currentTextColor === '#ffffff';
  const isBgAuto = !currentBgColor;
  
  // Определяем цвета и метки в зависимости от активной вкладки
  const autoColor = theme === 'dark' ? '#fff' : '#222';
  
  // Определяем метку для кнопки
  let label = 'Цвет';
  if (activeTab === 'text') {
    label = isTextAuto ? 'Текст' : 'Текст';
  } else {
    label = isBgAuto ? 'Фон' : 'Фон';
  }
  
  // Цвет для отображения в кнопке
  const labelColor = activeTab === 'text' 
    ? (isTextAuto ? autoColor : currentTextColor)
    : (isBgAuto ? 'transparent' : currentBgColor);

  // Организованная палитра цветов 5x5
  const colorPalette = [
    // Ряд 1: Автоматический и красные оттенки
    [
      { name: 'Автоматический', value: 'auto', isAuto: true },
      { name: 'Светло-красный', value: '#ffcccc' },
      { name: 'Красный', value: '#ff0000' },
      { name: 'Темно-красный', value: '#cc0000' },
      { name: 'Бордовый', value: '#800000' }
    ],
    // Ряд 2: Оранжевые и желтые оттенки
    [
      { name: 'Светло-оранжевый', value: '#ffcc99' },
      { name: 'Оранжевый', value: '#ff9900' },
      { name: 'Светло-желтый', value: '#ffffcc' },
      { name: 'Желтый', value: '#ffff00' },
      { name: 'Темно-желтый', value: '#cccc00' }
    ],
    // Ряд 3: Зеленые оттенки
    [
      { name: 'Светло-зеленый', value: '#ccffcc' },
      { name: 'Зеленый', value: '#00cc00' },
      { name: 'Темно-зеленый', value: '#008000' },
      { name: 'Салатовый', value: '#99ff99' },
      { name: 'Оливковый', value: '#808000' }
    ],
    // Ряд 4: Синие и фиолетовые оттенки
    [
      { name: 'Светло-голубой', value: '#ccffff' },
      { name: 'Голубой', value: '#00ffff' },
      { name: 'Синий', value: '#0000ff' },
      { name: 'Темно-синий', value: '#000080' },
      { name: 'Фиолетовый', value: '#800080' }
    ],
    // Ряд 5: Серые оттенки, черный и белый
    [
      { name: 'Светло-серый', value: '#e6e6e6' },
      { name: 'Серый', value: '#808080' },
      { name: 'Темно-серый', value: '#404040' },
      { name: 'Черный', value: '#000000' },
      { name: 'Белый', value: '#ffffff' }
    ]
  ];

  const toggleDropdown = (e) => {
    e.preventDefault(); // Важно! Предотвращает потерю фокуса и выделения
    
    if (!isOpen) {
      // При открытии меню сохраняем текущее состояние выделения
      setSelectionState(editor.state);
    }
    
    setIsOpen(!isOpen);
  };

  const handleAutoColor = (e) => {
    e.preventDefault();
    
    // Если было сохранено состояние выделения, восстанавливаем его
    if (selectionState) {
      editor.view.updateState(selectionState);
    }
    
    // Сбрасываем цвет в зависимости от активной вкладки
    if (activeTab === 'text') {
      const { backgroundColor, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus().setMark('textStyle', {
        color: null,
        backgroundColor,
        fontSize,
      }).run();
    } else {
      const { color, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus().setMark('textStyle', {
        color,
        backgroundColor: null,
        fontSize,
      }).run();
    }
    
    setIsOpen(false);
  };
  
  const handleResetAllColors = (e) => {
    e.preventDefault();
    
    // Если было сохранено состояние выделения, восстанавливаем его
    if (selectionState) {
      editor.view.updateState(selectionState);
    }
    
    // Сбрасываем оба цвета
    const { fontSize } = editor.getAttributes('textStyle');
    editor.chain().focus().setMark('textStyle', {
      color: null,
      backgroundColor: null,
      fontSize,
    }).run();
    
    setIsOpen(false);
  };
  
  const applyPresetColor = (colorObj, e) => {
    e.preventDefault();
    
    // Если было сохранено состояние выделения, восстанавливаем его
    if (selectionState) {
      editor.view.updateState(selectionState);
    }
    
    // Если это автоматический цвет
    if (colorObj.isAuto) {
      handleAutoColor(e);
      return;
    }
    
    // Применяем цвет в зависимости от активной вкладки
    if (activeTab === 'text') {
      const { backgroundColor, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus().setMark('textStyle', {
        color: colorObj.value,
        backgroundColor,
        fontSize,
      }).run();
    } else {
      const { color, fontSize } = editor.getAttributes('textStyle');
      editor.chain().focus().setMark('textStyle', {
        color,
        backgroundColor: colorObj.value,
        fontSize,
      }).run();
    }
    
    setIsOpen(false);
  };
  
  const switchTab = (tab, e) => {
    e.preventDefault();
    setActiveTab(tab);
  };

  return (
    <div className="relative inline-block" style={{ marginLeft: 4, marginRight: 4 }}>
      <Tooltip
        title={
          <span style={{ fontSize: '12px', lineHeight: '1.5' }}>
            Рекомендуется использовать стандартные цвета для текста и фона, они адаптируются автоматически на светлой и темной теме.
          </span>
        }
        placement="top"
        classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}
      >
        <button
          onMouseDown={toggleDropdown}
          className="flex items-center px-2 py-1 border rounded"
          style={{
            background: 'transparent',
            border: 'none',
            minWidth: '54px',
            height: '32px',
            cursor: 'pointer',
          }}
        >
          <span
            style={{
              display: 'inline-block',
              width: 18,
              height: 18,
              borderRadius: 6,
              border: '1.5px solid #fff',
              background: labelColor,
              marginRight: 6,
              boxShadow: theme === 'dark' ? '0 0 0 1.5px #fff' : '0 0 0 1.5px #222',
            }}
          />
          <span style={{ color: activeTab === 'text' ? (isTextAuto ? autoColor : currentTextColor) : autoColor, fontWeight: 600, fontSize: 15 }}>{label}</span>
        </button>
      </Tooltip>
      
      {isOpen && (
        <div
          className="absolute left-0 mt-1 border rounded shadow-lg z-10"
          style={{
            background: theme === 'dark' ? '#1f2937' : '#ffffff',
            borderColor: theme === 'dark' ? '#374151' : '#d1d5db',
            minWidth: '240px',
            zIndex: 1000,
          }}
        >
          <div
            onMouseDown={handleResetAllColors}
            className="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
            style={{
              background: (isTextAuto && isBgAuto) ? (theme === 'dark' ? '#374151' : '#f3f4f6') : 'transparent',
              color: autoColor,
              fontWeight: (isTextAuto && isBgAuto) ? 700 : 500,
            }}
          >
            Сбросить оба цвета
          </div>
          
          {/* Вкладки для переключения между текстом и фоном */}
          <div className="flex border-t border-gray-200 dark:border-gray-700">
            <div
              onMouseDown={(e) => switchTab('text', e)}
              className="flex-1 px-3 py-2 text-center cursor-pointer"
              style={{
                background: activeTab === 'text' ? (theme === 'dark' ? '#374151' : 'transparent') : (theme === 'dark' ? 'transparent' : '#f3f4f6'),
                color: autoColor,
                fontWeight: activeTab === 'text' ? 700 : 500,
                borderBottom: activeTab === 'text' ? `2px solid ${theme === 'dark' ? '#3b82f6' : '#2563eb'}` : 'none',
              }}
            >
              Текст
            </div>
            <div
              onMouseDown={(e) => switchTab('background', e)}
              className="flex-1 px-3 py-2 text-center cursor-pointer"
              style={{
                background: activeTab === 'background' ? (theme === 'dark' ? '#374151' : 'transparent') : (theme === 'dark' ? 'transparent' : '#f3f4f6'),
                color: autoColor,
                fontWeight: activeTab === 'background' ? 700 : 500,
                borderBottom: activeTab === 'background' ? `2px solid ${theme === 'dark' ? '#3b82f6' : '#2563eb'}` : 'none',
              }}
            >
              Фон
            </div>
          </div>
          
          {/* Компактная палитра цветов 5x5 */}
          <div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700">
            <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
              {activeTab === 'text' ? 'Выберите цвет текста:' : 'Выберите цвет фона:'}
            </div>
            
            <div className="grid grid-cols-5 gap-2">
              {colorPalette.flat().map((color, index) => (
                <div
                  key={index}
                  onMouseDown={(e) => applyPresetColor(color, e)}
                  className="w-8 h-8 rounded cursor-pointer hover:opacity-80 flex items-center justify-center"
                  style={{
                    background: color.isAuto 
                      ? 'transparent' 
                      : color.value,
                    boxShadow: '0 0 0 1px rgba(0,0,0,0.2)',
                    border: ((activeTab === 'text' && isTextAuto && color.isAuto) || 
                            (activeTab === 'background' && isBgAuto && color.isAuto) ||
                            (activeTab === 'text' && currentTextColor === color.value) ||
                            (activeTab === 'background' && currentBgColor === color.value))
                      ? '2px solid #3b82f6' 
                      : 'none',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                  title={color.name}
                >
                  {color.isAuto && (
                    <>
                      {activeTab === 'background' && (
                        <>
                          {/* Шахматный узор для обозначения прозрачности фона */}
                          <div style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#374151' : '#e5e7eb',
                          }} />
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#374151' : '#e5e7eb',
                          }} />
                          <div style={{
                            position: 'absolute',
                            top: 0,
                            left: '50%',
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#1f2937' : '#f3f4f6',
                          }} />
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            left: 0,
                            width: '50%',
                            height: '50%',
                            background: theme === 'dark' ? '#1f2937' : '#f3f4f6',
                          }} />
                        </>
                      )}
                      <span style={{ 
                        fontWeight: 'bold', 
                        color: theme === 'dark' ? '#fff' : '#000',
                        fontSize: '14px',
                        position: 'relative',
                        zIndex: 1
                      }}>
                        A
                      </span>
                    </>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}