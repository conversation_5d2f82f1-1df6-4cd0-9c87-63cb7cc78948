from django.db import models
from django.db.models.signals import post_delete
from django.dispatch import receiver
from .models import User, get_group_folder, random_prefix
from .storage_backends import PublicMediaStorage


def user_bio_image_path(user_id):
    """Генерирует путь для изображения в bio пользователя"""
    group_folder = get_group_folder(user_id)
    rand = random_prefix()
    return f"bio/{group_folder}/{user_id}/{user_id}_bio_{rand}.jpg"


class UserBioImage(models.Model):
    """Модель для хранения изображений в bio пользователя"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bio_images')
    path = models.CharField(max_length=512)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    width = models.CharField(max_length=16, blank=True, null=True)
    height = models.CharField(max_length=16, blank=True, null=True)
    
    def __str__(self):
        return f"Bio image for user {self.user_id}: {self.path}"
    
    class Meta:
        db_table = 'users_bio_image'


@receiver(post_delete, sender=UserBioImage)
def delete_bio_image_file(sender, instance, **kwargs):
    """Удаляет файл изображения из S3 при удалении записи"""
    storage = PublicMediaStorage()
    if instance.path and storage.exists(instance.path):
        storage.delete(instance.path)