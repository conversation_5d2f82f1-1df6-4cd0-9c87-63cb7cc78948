import React, { useState, useEffect, useContext } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { csrfFetch } from '../utils/csrf';
import { getUserAvatar } from '../utils/avatar';
import RatingIcon from '../components/RatingIcon';
import { Pagination, Dropdown } from 'antd';
import { useTheme } from '../theme/ThemeContext';

const SearchResults = () => {
  const { theme } = useTheme();
  const [searchParams] = useSearchParams();
  const query = searchParams.get('q');
  const [results, setResults] = useState({ books: [], authors: [] });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Состояния для пагинации
  const [currentBookPage, setCurrentBookPage] = useState(1);
  const [currentAuthorPage, setCurrentAuthorPage] = useState(1);
  const booksPerPage = 15; // 5 колонок x 3 ряда
  const authorsPerPage = 20; // 5 колонок x 4 ряда
  
  // Состояние для фильтра книг
  const [bookSortFilter, setBookSortFilter] = useState('all'); // 'all', 'popularity', 'date'

  useEffect(() => {
    if (query) {
      performSearch(query);
    }
  }, [query]);

  const performSearch = async (searchQuery) => {
    setLoading(true);
    setError('');
    // Сброс пагинации и фильтра при новом поиске
    setCurrentBookPage(1);
    setCurrentAuthorPage(1);
    setBookSortFilter('all');
    
    try {
      const response = await csrfFetch(`/api/search/?q=${encodeURIComponent(searchQuery)}`, {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setResults(data);
      } else {
        setError('Ошибка при выполнении поиска');
      }
    } catch (err) {
      setError('Ошибка сети');
      console.error('Search error:', err);
    } finally {
      setLoading(false);
    }
  };

  const BookCard = ({ book }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow w-[225px]">
      <div className="flex flex-col items-center text-center">
        {/* Обложка с 3D эффектом */}
        <div className="group relative mb-3">
          <Link to={`/book/${book.id}`}>
            <div className="relative">
                             {/* Основная обложка книги */}
               <div className="relative w-[196px] h-[280px] bg-gray-200 dark:bg-gray-700 overflow-hidden shadow-lg transform transition-transform duration-300 group-hover:scale-105" 
                   style={{
                     borderRadius: '0 8px 8px 0'
                   }}
              >
                <img
                  src={book.cover || '/covertemp/covertemp.jpg'}
                  alt={book.title}
                  className="w-full h-full object-cover cursor-pointer"
                />
                
                {/* Корешок книги - левая полоска */}
                <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-r from-black/30 via-black/10 to-transparent"></div>
                
                {/* Дополнительная тень для глубины */}
                <div className="absolute left-1 top-0 bottom-0 w-1 bg-gradient-to-r from-black/20 to-transparent"></div>
                
                {/* Блик на обложке */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              
              {/* Тень под книгой */}
              <div className="absolute -bottom-1 left-1 right-2 h-1 bg-black/20 rounded-full blur-sm transform transition-transform duration-300 group-hover:scale-110"></div>
            </div>
          </Link>
        </div>
        
        {/* Иконки статистики под обложкой */}
        <div className="flex items-center justify-center gap-2 text-gray-400 dark:text-gray-500 text-xs mb-3">
          <span className="flex items-center gap-1 cursor-default" title="Просмотры">
            <svg width="14" height="14" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
            {book.views_count || 0}
          </span>
          <span className="flex items-center gap-1 cursor-default" title="Лайки">
            <svg width="14" height="14" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path d="M4.318 6.318a4.5 4.5 0 0 1 6.364 0L12 7.636l1.318-1.318a4.5 4.5 0 1 1 6.364 6.364L12 21.682l-7.682-7.682a4.5 4.5 0 0 1 0-6.364z"/>
            </svg>
            {book.likes_count || 0}
          </span>
          <span className="flex items-center gap-1 cursor-default" title="В библиотеках">
            <svg width="14" height="14" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
            </svg>
            {book.library_count || 0}
          </span>
          <span className="flex items-center gap-1 cursor-default" title="Комментарии">
            <svg width="14" height="14" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
            </svg>
            {book.comments_count || 0}
          </span>
        </div>

        {/* Название книги */}
        <Link to={`/book/${book.id}`} className="block hover:opacity-80 transition-opacity mb-2">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white cursor-pointer leading-tight">
            {book.title}
          </h3>
        </Link>

        {/* Автор */}
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <Link to={`/lpu/${book.author.username}`} className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
            {book.author.display_name}
          </Link>
        </p>
      </div>
    </div>
  );

  const AuthorCard = ({ author }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow w-[225px]">
      <div className="flex items-center space-x-4">
        <div className="flex-shrink-0">
          <Link to={`/lpu/${author.username}`}>
            <img
              src={getUserAvatar(author, 'full') || '/ava_presets/ava_U.webp'}
              alt={author.display_name}
              className="w-16 h-16 rounded-full object-cover cursor-pointer hover:opacity-80 transition-opacity"
            />
          </Link>
        </div>
        <div className="flex-1 min-w-0">
          <Link to={`/lpu/${author.username}`} className="block hover:opacity-80 transition-opacity">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white cursor-pointer">
              {author.display_name}
            </h3>
          </Link>
          <Link to={`/lpu/${author.username}`} className="hover:opacity-80 transition-opacity">
            <p className="text-sm text-gray-600 dark:text-gray-400 cursor-pointer">
              @{author.username}
            </p>
          </Link>
          <div className="mt-1">
            <RatingIcon rating={author.total_rating || 0} size="sm" />
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">Поиск...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  // Функции для пагинации
  const handleBookPageChange = (page) => {
    setCurrentBookPage(page);
  };

  const handleAuthorPageChange = (page) => {
    setCurrentAuthorPage(page);
  };

  const handleBookSortChange = (sortType) => {
    setBookSortFilter(sortType);
    setCurrentBookPage(1); // Сброс на первую страницу при изменении сортировки
  };

  // Функция сортировки книг
  const getSortedBooks = () => {
    let sortedBooks = [...results.books];
    
    switch (bookSortFilter) {
      case 'popularity':
        // Сортировка по популярности (лайки + просмотры + библиотека + комментарии)
        sortedBooks.sort((a, b) => {
          const scoreA = (a.likes_count || 0) + (a.views_count || 0) + (a.library_count || 0) + (a.comments_count || 0);
          const scoreB = (b.likes_count || 0) + (b.views_count || 0) + (b.library_count || 0) + (b.comments_count || 0);
          return scoreB - scoreA;
        });
        break;
      case 'date':
        // Сортировка по дате (новые первые)
        sortedBooks.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        break;
      default:
        // 'all' - оригинальный порядок (уже отсортирован по дате создания на бэкенде)
        break;
    }
    
    return sortedBooks;
  };

  // Получение данных для текущих страниц
  const getCurrentPageBooks = () => {
    const sortedBooks = getSortedBooks();
    const startIndex = (currentBookPage - 1) * booksPerPage;
    const endIndex = startIndex + booksPerPage;
    return sortedBooks.slice(startIndex, endIndex);
  };

  const getCurrentPageAuthors = () => {
    const startIndex = (currentAuthorPage - 1) * authorsPerPage;
    const endIndex = startIndex + authorsPerPage;
    return results.authors.slice(startIndex, endIndex);
  };

  const hasResults = results.books.length > 0 || results.authors.length > 0;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Результаты поиска
          </h1>
          {query && (
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              По запросу: <span className="font-medium">"{query}"</span>
            </p>
          )}
        </div>

        {!hasResults ? (
          <div className="text-center py-12">
            <p className="text-gray-600 dark:text-gray-400 text-lg">
              По вашему запросу ничего не найдено
            </p>
            <p className="text-gray-500 dark:text-gray-500 mt-2">
              Попробуйте изменить запрос или использовать другие ключевые слова
            </p>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Раздел книг */}
            {results.books.length > 0 && (
              <div>
                <div className="flex items-center gap-3 mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Книги ({results.books.length})
                  </h2>
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: 'all',
                          label: 'Все',
                          onClick: () => handleBookSortChange('all'),
                        },
                        {
                          key: 'popularity',
                          label: 'По популярности',
                          onClick: () => handleBookSortChange('popularity'),
                        },
                        {
                          key: 'date',
                          label: 'По дате публикации',
                          onClick: () => handleBookSortChange('date'),
                        },
                      ],
                      selectedKeys: [bookSortFilter],
                    }}
                    trigger={['click']}
                    placement="bottomLeft"
                    overlayClassName={theme === 'dark' ? 'dark-dropdown' : ''}
                  >
                    <button className={`flex items-center gap-1 px-3 py-2 rounded-md transition-colors border ${
                      theme === 'dark' 
                        ? 'text-gray-300 hover:text-white hover:bg-gray-700/50 border-gray-600 bg-gray-800/50' 
                        : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100/50 border-gray-300 bg-white'
                    }`}>
                      <svg width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path d="M3 6h18M7 12h10m-7 6h4"/>
                      </svg>
                      <span className="text-sm font-medium">
                        {bookSortFilter === 'all' && 'Все'}
                        {bookSortFilter === 'popularity' && 'По популярности'}
                        {bookSortFilter === 'date' && 'По дате'}
                      </span>
                    </button>
                  </Dropdown>
                </div>
                <div className="flex flex-wrap justify-start gap-4">
                  {getCurrentPageBooks().map((book) => (
                    <BookCard key={book.id} book={book} />
                  ))}
                </div>
                {getSortedBooks().length > booksPerPage && (
                  <div className="mt-8 flex justify-center">
                    <Pagination
                      current={currentBookPage}
                      total={getSortedBooks().length}
                      pageSize={booksPerPage}
                      onChange={handleBookPageChange}
                      showQuickJumper
                      showSizeChanger={false}
                      className="pagination-dark"
                    />
                  </div>
                )}
              </div>
            )}

            {/* Раздел авторов */}
            {results.authors.length > 0 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  Авторы ({results.authors.length})
                </h2>
                <div className="flex flex-wrap justify-start gap-4">
                  {getCurrentPageAuthors().map((author) => (
                    <AuthorCard key={author.id} author={author} />
                  ))}
                </div>
                {results.authors.length > authorsPerPage && (
                  <div className="mt-8 flex justify-center">
                    <Pagination
                      current={currentAuthorPage}
                      total={results.authors.length}
                      pageSize={authorsPerPage}
                      onChange={handleAuthorPageChange}
                      showQuickJumper
                      showSizeChanger={false}
                      className="pagination-dark"
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchResults;

// Стили для пагинации и dropdown в темной теме
const styles = `
  .pagination-dark .ant-pagination-item {
    background-color: transparent !important;
    border: 1px solid #4b5563 !important;
  }
  
  .pagination-dark .ant-pagination-item a {
    color: #9ca3af !important;
  }
  
  .pagination-dark .ant-pagination-item:hover {
    border-color: #6b7280 !important;
  }
  
  .pagination-dark .ant-pagination-item:hover a {
    color: #d1d5db !important;
  }
  
  .pagination-dark .ant-pagination-item-active {
    background-color: #3b82f6 !important;
    border-color: #3b82f6 !important;
  }
  
  .pagination-dark .ant-pagination-item-active a {
    color: white !important;
  }
  
  .pagination-dark .ant-pagination-prev,
  .pagination-dark .ant-pagination-next {
    color: #9ca3af !important;
  }
  
  .pagination-dark .ant-pagination-prev:hover,
  .pagination-dark .ant-pagination-next:hover {
    color: #d1d5db !important;
  }
  
  .dark-dropdown .ant-dropdown-menu {
    background-color: #1f2937 !important;
    border: 1px solid #4b5563 !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  }
  
  .dark-dropdown .ant-dropdown-menu-item {
    color: #d1d5db !important;
    background-color: transparent !important;
  }
  
  .dark-dropdown .ant-dropdown-menu-item:hover {
    background-color: #374151 !important;
    color: #f9fafb !important;
  }
  
  .dark-dropdown .ant-dropdown-menu-item-selected {
    background-color: #3b82f6 !important;
    color: white !important;
  }
  
  .dark-dropdown .ant-dropdown-menu-item-selected:hover {
    background-color: #2563eb !important;
    color: white !important;
  }
  
  .dark-dropdown .ant-dropdown-menu-item:not(:last-child) {
    border-bottom: 1px solid #374151 !important;
  }
  
  .dark-dropdown .ant-dropdown-menu-item-divider {
    background-color: #374151 !important;
  }
`;

// Добавляем стили в head
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
} 