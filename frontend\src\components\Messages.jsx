import React, { useState, useEffect, useRef } from 'react';
import { useCallback } from 'react';
import { useTheme } from '../theme/ThemeContext';
import { useAuth } from '../context/AuthContext';
import UserAvatar from './UserAvatar';
import { useLocation, Link } from 'react-router-dom';
import { useMessage } from '../context/MessageContext';
import { PaperAirplaneIcon } from '@heroicons/react/24/solid';
import { PhotoIcon } from '@heroicons/react/24/outline';
import { XMarkIcon } from '@heroicons/react/24/solid';
import { PencilSquareIcon, TrashIcon } from '@heroicons/react/20/solid';
import EmojiMartModal from './EmojiMartModal';
import GiphyModal from './GiphyModal';
import { ArrowUturnLeftIcon } from '@heroicons/react/20/solid';
import { useUserSettings } from '../context/UserSettingsContext';
import { useDialogWebSocket } from '../hooks/useDialogWebSocket';
import { useMessages } from '../hooks/useMessages';
import { useScrollToLoad } from '../hooks/useScrollToLoad';
import moment from 'moment-timezone';
import { getCSRFToken, csrfFetch } from '../utils/csrf';
import { debounce } from 'lodash';
import { getCachedUserAvatar } from '../utils/avatarCache';
// import { formatDate } from '../utils/dateUtils';
// import { API_BASE_URL } from '../config';
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// Универсальная функция для формирования media-url
const getMediaUrl = (url) => {
    if (!url) return '';
    if (/^https?:\/\//.test(url)) return url;
    if (url.startsWith('/media/')) return API_BASE_URL + url;
    return url;
};

// Месяцы для русского языка
const months = [
    'января', 'февраля', 'марта', 'апреля', 'мая', 'июня',
    'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря'
];

// Форматирование времени под сообщением
const formatDate = (dateString, timezone) => {
    if (!dateString) return '';
    const date = moment(dateString).tz(timezone);
    const now = moment().tz(timezone);

    if (date.isSame(now, 'day')) {
        // Сегодня: только время
        return date.format('HH:mm');
    } else if (date.isSame(now, 'year')) {
        // В этом году: дата и время
        return `${date.date()} ${months[date.month()]}, ${date.format('HH:mm')}`;
    } else {
        // В другие года: дата, год и время
        return `${date.date()} ${months[date.month()]} ${date.year()}, ${date.format('HH:mm')}`;
    }
};

// Форматирование разделителя дат
const formatDateDivider = (dateString, timezone) => {
    if (!dateString) return '';
    const date = moment(dateString).tz(timezone);
    const now = moment().tz(timezone);
    const yesterday = moment().tz(timezone).subtract(1, 'day');

    if (date.isSame(now, 'day')) return 'Сегодня';
    if (date.isSame(yesterday, 'day')) return 'Вчера';
    if (date.isSame(now, 'year')) {
        return `${date.date()} ${months[date.month()]}`;
    }
    return `${date.date()} ${months[date.month()]} ${date.year()} г.`;
};

// Вынести DateDivider вне функции Messages
const DateDivider = ({ date, timezone }) => (
    <div className="flex items-center my-4">
        <div className="flex-grow border-t border-gray-300 dark:border-gray-600"></div>
        <span className="mx-4 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-900 px-3 py-0.5 rounded-full text-sm font-medium shadow">
            {formatDateDivider(date, timezone)}
        </span>
        <div className="flex-grow border-t border-gray-300 dark:border-gray-600"></div>
    </div>
);

const Messages = () => {
    // console.log('Messages component mounted'); // Removed for performance
    // Все хуки только здесь, до любых return и условий!
    const { theme } = useTheme();
    const { user, refreshUser } = useAuth();
    const location = useLocation();
    const [dialogs, setDialogs] = useState([]);
    const [selectedDialog, setSelectedDialog] = useState(null);
    const [newMessage, setNewMessage] = useState('');
    const [dialogsLoading, setDialogsLoading] = useState(true);
    const [error, setError] = useState(null);
    const { setUnreadMessagesCount, fetchUnreadCount } = useMessage();
    const messageRefs = useRef({});
    const [selectedFile, setSelectedFile] = useState(null);
    const [thumbPreview, setThumbPreview] = useState(null);
    const fileInputRef = useRef(null);
    const [modalImage, setModalImage] = useState(null);
    const [editingMsg, setEditingMsg] = useState(null);
    const [editText, setEditText] = useState('');
    const [toast, setToast] = useState(null);
    const [showNewDialogModal, setShowNewDialogModal] = useState(false);
    const [newDialogUsername, setNewDialogUsername] = useState('');
    const [searchInputValue, setSearchInputValue] = useState(''); // Локальное состояние для поля ввода
    const [newDialogError, setNewDialogError] = useState('');
    const [showEmojiMartModal, setShowEmojiMartModal] = useState(false);
    const [showGiphyModal, setShowGiphyModal] = useState(false);
    const textareaRef = useRef(null);
    const messagesEndRef = useRef(null);
    const [replyToMessage, setReplyToMessage] = useState(null);
    const [highlightedMsgId, setHighlightedMsgId] = useState(null);
    const [showLeaveDialogModal, setShowLeaveDialogModal] = useState(false);
    const [leaveLoading, setLeaveLoading] = useState(false);
    const [leaveError, setLeaveError] = useState('');
    const [isSending, setIsSending] = useState(false);
    const [searchResults, setSearchResults] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [hasSearched, setHasSearched] = useState(false);
    const [selectedUser, setSelectedUser] = useState(null);
    const { timezone } = useUserSettings();
    
    // Состояние для индикатора печати
    const [isTyping, setIsTyping] = useState(false);
    const [typingTimeout, setTypingTimeout] = useState(null);
    
    // --- Хуки для работы с сообщениями ---
    const {
        messages,
        loading: messagesLoading,
        loadingMore,
        hasMore,
        error: messagesError,
        isFirstLoad,
        loadMessages,
        loadMoreMessages,
        addMessage,
        updateMessage,
        markMessagesAsRead,
        resetMessages
    } = useMessages(selectedDialog?.id);

    // --- Хук для скролла и подгрузки ---
    const {
        containerRef,
        scrollToBottom,
        scrollToElement,
        focusLastMessage,
        resetPaginationFlag
    } = useScrollToLoad(loadMoreMessages, hasMore, loadingMore);

    // --- WebSocket хук для диалога ---
    const {
        isConnected: dialogWsConnected,
        newMessage: wsNewMessage,
        messagesRead: wsMessagesRead,
        userTyping: wsUserTyping,
        sendTyping,
        clearNewMessage,
        clearMessagesRead,
        clearUserTyping
    } = useDialogWebSocket(selectedDialog?.id);

    // Получаем параметр ?to=username из URL
    function getToParam() {
        const params = new URLSearchParams(location.search);
        return params.get('to');
    }

    // Диагностика авторизации и fetch
    useEffect(() => {
        // Логируем user
        console.log('[DEBUG] user:', user);
        // Логируем cookies (если доступны)
        try {
            console.log('[DEBUG] document.cookie:', document.cookie);
        } catch (e) {
            console.log('[DEBUG] document.cookie: недоступно');
        }
        // Логируем location
        console.log('[DEBUG] location:', location);
    }, [user, location]);

    // Функция для загрузки списка диалогов
    const fetchDialogs = async () => {
        if (!user) return;
        setDialogsLoading(true);
        try {
            console.log('[DEBUG] Fetching dialogs...');
            const response = await fetch(`/api/dialogs/`, {
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });
            
            console.log('[DEBUG] Response status:', response.status);
            console.log('[DEBUG] Response headers:', Object.fromEntries(response.headers.entries()));
                
                // Проверяем тип контента
                const contentType = response.headers.get('content-type');
                console.log('[DEBUG] Content-Type:', contentType);
                
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    console.error('[DEBUG] Non-JSON response:', text);
                    throw new Error('Server returned non-JSON response');
                }
                
                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('[DEBUG] Error response:', errorData);
                    throw new Error(errorData.detail || 'Failed to fetch dialogs');
                }
                
                const data = await response.json();
                // console.log('[DEBUG] Dialogs data:', data); // Removed for performance
                
                if (!data.results) {
                    console.error('[DEBUG] Invalid response format:', data);
                    throw new Error('Invalid response format');
                }
                
                setDialogs(data.results);

                // Если есть ?to=username — ищем или создаём диалог
                const toUsername = getToParam();
                if (toUsername && user && toUsername !== user.username) {
                    // Ищем диалог с этим пользователем
                    const found = data.results.find(d => d.participant?.username === toUsername);
                    console.log('[DEBUG] Dialog found by toUsername:', found);
                    if (found) {
                        setSelectedDialog(found);
                    } else {
                        // Если нет — создаём диалог
                        const res = await csrfFetch(`/api/dialogs/`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': getCSRFToken()
                            },
                            credentials: 'include',
                            body: JSON.stringify({ username: toUsername })
                        });
                        console.log('[DEBUG] POST /api/dialogs/ response:', res);
                        if (res.ok) {
                            const data = await res.json();
                            // Добавляем новый диалог в список и устанавливаем его как выбранный
                            setDialogs(prevDialogs => [...prevDialogs, data]);
                            setSelectedDialog(data);
                        }
                    }
                }
                            } catch (err) {
                console.error('Error loading dialogs:', err);
                setError(err.message || 'Ошибка загрузки диалогов');
            } finally {
                setDialogsLoading(false);
            }
        };
    
    // Загрузка списка диалогов при монтировании компонента или изменении пользователя/URL
    useEffect(() => {
        if (!user) return;
        fetchDialogs();
    }, [user, location.search]);

    // Загрузка сообщений выбранного диалога
    useEffect(() => {
        if (!user || !selectedDialog) {
            resetMessages();
            return;
        }
        
        const loadAndScroll = async () => {
            console.log('[DEBUG] loadAndScroll called for dialog:', selectedDialog?.id);
            // Сбрасываем флаг пагинации для нового диалога
            resetPaginationFlag();
            
            const result = await loadMessages();
            console.log('[DEBUG] loadMessages result:', result);
            
            // Прокручиваем к концу при первой загрузке
            if (result?.isFirstLoad && result?.hasMessages) {
                console.log('[DEBUG] Scrolling to bottom after first load');
                // Прокручиваем к элементу messagesEndRef (конец списка)
                requestAnimationFrame(() => {
                    requestAnimationFrame(() => {
                        if (messagesEndRef.current) {
                            messagesEndRef.current.scrollIntoView({ behavior: 'auto' });
                        }
                    });
                });
            }
        };
        
        loadAndScroll();
    }, [user, selectedDialog?.id, resetMessages, resetPaginationFlag]);

    // Обработка загруженных сообщений - автоматическое помечание как прочитанные
    useEffect(() => {
        if (!selectedDialog || !messages.length || messagesLoading) return;

        // Если есть непрочитанные сообщения — помечаем их как прочитанные
        if (selectedDialog.unread_count > 0) {
            const markAsRead = async () => {
                const csrfToken = getCSRFToken();
                try {
                    const markReadResponse = await csrfFetch(`/api/dialogs/${selectedDialog.id}/mark_read/`, {
                        method: 'POST',
                        credentials: 'include',
                        headers: {
                            'X-CSRFToken': csrfToken
                        }
                    });
                    
                    if (markReadResponse.ok) {
                        // Обновляем только текущий диалог в списке
                        setDialogs(prevDialogs => {
                            return prevDialogs.map(dialog => {
                                if (dialog.id === selectedDialog.id) {
                                    return { ...dialog, unread_count: 0 };
                                }
                                return dialog;
                            });
                        });
                        
                        // Обновляем счетчик непрочитанных сообщений
                        await fetchUnreadCount();
                        
                        // Обновляем счетчик в текущем диалоге (но только если диалог тот же)
                        setSelectedDialog(prev => {
                            if (prev && prev.id === selectedDialog.id) {
                                return { ...prev, unread_count: 0 };
                            }
                            return prev;
                        });
                    }
                } catch (error) {
                    console.error('Error marking messages as read:', error);
                }
            };
            
            markAsRead();
        }
    }, [selectedDialog?.id, selectedDialog?.unread_count, messagesLoading, messages.length, setDialogs, fetchUnreadCount]);

    // Восстановление позиции скролла после загрузки новых сообщений
    // Эта логика теперь встроена в handleScroll в useScrollToLoad.js

    // Автопрокрутка к низу только при первой загрузке диалога встроена в loadMessages

    // --- WebSocket обработчики ---
    
    // Обработка новых сообщений от WebSocket
    useEffect(() => {
        if (wsNewMessage && selectedDialog) {
            // Проверяем, что сообщение для текущего диалога
            if (wsNewMessage.dialog === selectedDialog.id) {
                // Проверяем позицию скролла ДО добавления сообщения
                const container = containerRef.current;
                let shouldScrollToBottom = false;
                
                if (container) {
                    const { scrollTop, scrollHeight, clientHeight } = container;
                    // Если пользователь близко к низу (в пределах 200px), будем прокручивать
                    shouldScrollToBottom = scrollHeight - scrollTop - clientHeight < 200;
                }
                
                // Добавляем сообщение
                addMessage(wsNewMessage);
                
                // Прокручиваем к новому сообщению только если пользователь был внизу
                if (shouldScrollToBottom) {
                    setTimeout(() => {
                        scrollToBottom();
                    }, 100);
                }
                
                // Если сообщение НЕ от текущего пользователя, автоматически помечаем как прочитанное
                if (wsNewMessage.sender.id !== user.id) {
                    setTimeout(async () => {
                        try {
                            const csrfToken = getCSRFToken();
                            const response = await csrfFetch(`/api/dialogs/${selectedDialog.id}/mark_read/`, {
                                method: 'POST',
                                credentials: 'include',
                                headers: {
                                    'X-CSRFToken': csrfToken
                                }
                            });
                        } catch (error) {
                            console.error('Error auto-marking message as read:', error);
                        }
                    }, 1000);
                }
            }
            
            clearNewMessage();
        }
    }, [wsNewMessage, selectedDialog, user, addMessage, clearNewMessage, containerRef, scrollToBottom]);

    // Обработка уведомлений о прочтении
    useEffect(() => {
        if (wsMessagesRead && selectedDialog && user) {
            markMessagesAsRead(wsMessagesRead.messageIds, wsMessagesRead.readBy, user.id);
            clearMessagesRead();
        }
    }, [wsMessagesRead, selectedDialog, user, markMessagesAsRead, clearMessagesRead]);

    // Обработка уведомлений о печати
    useEffect(() => {
        if (wsUserTyping && selectedDialog && user) {
            // Показываем индикатор печати только для собеседника, не для себя
            if (wsUserTyping.userId !== user.id) {
                // Устанавливаем таймер для автоматического скрытия индикатора через 4 секунды
                const hideTimer = setTimeout(() => {
                    clearUserTyping();
                }, 4000);
                
                return () => clearTimeout(hideTimer);
            } else {
                // Если уведомление о печати пришло от нас самих, сразу очищаем
                clearUserTyping();
            }
        }
    }, [wsUserTyping, selectedDialog, user, clearUserTyping]);

    // Очистка таймера печати при размонтировании
    useEffect(() => {
        return () => {
            if (typingTimeout) {
                clearTimeout(typingTimeout);
            }
        };
    }, [typingTimeout]);

    // Только после всех хуков — return с условиями:
    if (!user) return <div className="text-center p-4">Загрузка пользователя...</div>;
    if (dialogsLoading) return <div className="text-center p-4">Загрузка диалогов...</div>;
    if (error) return <div className="text-red-500 p-4">{error}</div>;

    // Функция ресайза изображения
    async function resizeImage(imageSrc, maxSize = 1200) {
        const image = new window.Image();
        image.src = imageSrc;
        await new Promise(resolve => { image.onload = resolve; });
        let { width, height } = image;
        if (width > maxSize || height > maxSize) {
            if (width > height) {
                height = Math.round((height * maxSize) / width);
                width = maxSize;
            } else {
                width = Math.round((width * maxSize) / height);
                height = maxSize;
            }
        }
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(image, 0, 0, width, height);
        return new Promise(resolve => {
            canvas.toBlob(blob => {
                resolve(blob);
            }, 'image/jpeg', 0.9);
        });
    }

    // Обработка выбора файла
    const handleFileSelect = (event) => {
        const file = event.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = async (ev) => {
                setSelectedFile({ file, dataUrl: ev.target.result });
                // Генерируем миниатюру для предпросмотра
                const thumbBlob = await resizeImage(ev.target.result, 300);
                setThumbPreview(URL.createObjectURL(thumbBlob));
            };
            reader.readAsDataURL(file);
        }
    };

    const handleTextareaInput = (e) => {
        const textarea = e.target;
        textarea.style.height = 'auto';
        
        // Максимальная высота около 7 строк (200px)
        const maxHeight = 200;
        
        if (textarea.scrollHeight <= maxHeight) {
            // Если текст помещается в 7 строк - растягиваем без скролла
            textarea.style.height = textarea.scrollHeight + 'px';
            textarea.style.overflow = 'hidden';
        } else {
            // Если текст больше 7 строк - фиксируем высоту и включаем скролл
            textarea.style.height = maxHeight + 'px';
            textarea.style.overflow = 'auto';
        }
    };

    // Функция для обработки печати
    const handleTyping = () => {
        if (!dialogWsConnected || !selectedDialog) return;
        
        // Отправляем уведомление о печати только если мы не отправляли его недавно
        if (!isTyping) {
            setIsTyping(true);
            sendTyping();
        }
        
        // Сбрасываем таймер и устанавливаем новый
        if (typingTimeout) {
            clearTimeout(typingTimeout);
        }
        
        const timeout = setTimeout(() => {
            setIsTyping(false);
        }, 3000); // Перестаем показывать статус печати через 3 секунды
        
        setTypingTimeout(timeout);
    };

    // Отправка нового сообщения с поддержкой изображения
    const handleSendMessage = async (e) => {
        e.preventDefault();
        if ((!newMessage.trim() && !selectedFile) || !selectedDialog || isSending) return;
        setIsSending(true);
        try {
            const csrfToken = getCSRFToken();
            let formData = null;
            if (selectedFile) {
                // Сжимаем оригинал и миниатюру
                const originalBlob = await resizeImage(selectedFile.dataUrl, 1200);
                const thumbBlob = await resizeImage(selectedFile.dataUrl, 300);
                formData = new FormData();
                formData.append('image', originalBlob, 'message.jpg');
                formData.append('image_thumbnail', thumbBlob, 'message_s.jpg');
                if (newMessage.trim()) formData.append('text', newMessage.trim());
                formData.append('sender_id', user.id);
                formData.append('recipient_id', selectedDialog.participant?.id);
                formData.append('dialog', selectedDialog.id);
                if (replyToMessage) formData.append('reply_to_id', replyToMessage.id);
            }
            const response = await csrfFetch(`/api/dialogs/${selectedDialog.id}/messages/`, {
                method: 'POST',
                headers: formData ? { 'X-CSRFToken': csrfToken } : {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                credentials: 'include',
                body: formData ? formData : JSON.stringify({
                    text: newMessage,
                    sender_id: user.id,
                    recipient_id: selectedDialog.participant?.id,
                    dialog: selectedDialog.id,
                    ...(replyToMessage ? { reply_to_id: replyToMessage.id } : {})
                })
            });
            if (!response.ok) throw new Error('Failed to send message');
            const sentMessage = await response.json();
            
            // --- ИЗМЕНЯЕМ: Не добавляем сообщение локально, так как оно придет через WebSocket ---
            // Но добавляем сразу, если WebSocket не подключен (fallback)
            if (!dialogWsConnected) {
                addMessage(sentMessage);
            }
            
            setNewMessage('');
            setSelectedFile(null);
            setThumbPreview(null);
            setReplyToMessage(null);
            
            // Сбрасываем размер textarea к исходному состоянию
            if (textareaRef.current) {
                textareaRef.current.style.height = '40px'; // minHeight
                textareaRef.current.style.overflow = 'hidden';
            }
        } catch (err) {
            setError(err.message);
        } finally {
            setIsSending(false);
        }
    };

    const canEdit = (msg) =>
        !msg.is_deleted && !msg.image_url && !msg.image_thumbnail_url &&
        (new Date() - new Date(msg.created_at)) < 12 * 60 * 60 * 1000 &&
        msg.sender.id === user.id;

    const canDelete = (msg) =>
        !msg.is_deleted && (new Date() - new Date(msg.created_at)) < 12 * 60 * 60 * 1000 && msg.sender.id === user.id;

    const startEdit = (msg) => {
        setEditingMsg(msg);
        setEditText(msg.text);
    };

    const handleEditSave = async () => {
        if (!editingMsg) return;
        try {
            console.log('Редактирование сообщения:', editingMsg.id, 'текст:', editText);
            const response = await csrfFetch(`${API_BASE_URL}/api/messages/${editingMsg.id}/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                credentials: 'include',
                body: JSON.stringify({ text: editText })
            });
            
            // Клонируем ответ, чтобы можно было прочитать тело дважды
            const responseClone = response.clone();
            
            if (!response.ok) {
                const errorData = await responseClone.json().catch(() => ({}));
                throw new Error(errorData.detail || 'Не удалось отредактировать сообщение');
            }
            
            const updatedMessage = await response.json();
            console.log('Сообщение успешно обновлено:', updatedMessage);
            
            // Обновляем только отредактированное сообщение в массиве
            updateMessage(editingMsg.id, updatedMessage);
            
            setEditingMsg(null);
            setEditText('');
        } catch (err) {
            console.error('Ошибка при редактировании сообщения:', err);
            setError(err.message || 'Не удалось отредактировать сообщение');
        }
    };

    const handleDelete = async (id) => {
        await csrfFetch(`${API_BASE_URL}/api/messages/${id}/`, {
            method: 'DELETE',
            headers: { 'X-CSRFToken': getCSRFToken() },
            credentials: 'include'
        });
        setToast('Сообщение удалено');
        // Обновить сообщения после удаления - просто перезагружаем
        loadMessages();
        setTimeout(() => setToast(null), 2000);
    };


    
    // Функция для проверки наличия диалога с пользователем
    const checkExistingDialog = (username) => {
        return dialogs.find(dialog => dialog.participant.username === username);
    };
    
    // Функция для поиска пользователей без автоматического создания диалога
    const searchUsers = async (query) => {
        if (!query.trim()) {
            setSearchResults([]);
            setNewDialogError('');
            setHasSearched(false);
            return;
        }
        
        // Если запрос слишком короткий, не выполняем поиск
        // Изменено с 2 на 1 символ, но в debouncedSearchUsers будет проверка на 4 символа
        if (query.trim().length < 1) {
            return;
        }
        
        setIsSearching(true);
        try {
            // Используем новый API для поиска пользователей без создания диалога
            const res = await csrfFetch(`${API_BASE_URL}/api/users/search-users/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                credentials: 'include',
                body: JSON.stringify({ username: query.trim() })
            });
            
            if (res.ok) {
                const data = await res.json();
                // API возвращает массив найденных пользователей
                if (Array.isArray(data) && data.length > 0) {
                    // Проверяем наличие существующего диалога для каждого пользователя
                    const usersWithDialogInfo = data.map(user => {
                        const existingDialog = checkExistingDialog(user.username);
                        return {
                            ...user,
                            hasExistingDialog: !!existingDialog,
                            existingDialog: existingDialog
                        };
                    });
                    setSearchResults(usersWithDialogInfo);
                    setNewDialogError('');
                } else {
                    setSearchResults([]);
                    setNewDialogError('Пользователи не найдены');
                }
            } else if (res.status === 404) {
                setNewDialogError('Пользователи не найдены');
                setSearchResults([]);
            } else {
                setNewDialogError('Ошибка при поиске пользователей');
                setSearchResults([]);
            }
            // Устанавливаем флаг, что поиск был выполнен
            setHasSearched(true);
        } catch (err) {
            console.error('Ошибка поиска:', err);
            setNewDialogError('Не удалось выполнить поиск: ' + err.message);
            setSearchResults([]);
            setHasSearched(true);
        } finally {
            setIsSearching(false);
        }
    };
    
    // Функция для поиска с задержкой (debounce) - объявляем после всех других хуков
    // Добавляем проверку на минимальную длину запроса (4 символа) перед выполнением поиска
    const debouncedSearchUsers = debounce((query) => {
        if (query.trim().length >= 4) {
            setHasSearched(true);
            searchUsers(query);
        } else if (query.trim().length > 0) {
            // Показываем сообщение только если пользователь уже выполнил поиск ранее
            if (hasSearched) {
                setNewDialogError('Введите не менее 4 символов для поиска');
            }
            setSearchResults([]);
        } else {
            setNewDialogError('');
            setSearchResults([]);
            setHasSearched(false);
        }
    }, 500);
    
    // Функция для создания диалога с выбранным пользователем
    const createDialog = async (selectedUsername) => {
        if (!selectedUsername) return;
        
        setIsSearching(true);
        setNewDialogError('');
        
        try {
            const res = await csrfFetch(`${API_BASE_URL}/api/dialogs/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                credentials: 'include',
                body: JSON.stringify({ username: selectedUsername })
            });
            
            if (res.ok) {
                const dialog = await res.json();
                // Обновляем список диалогов
                await fetchDialogs();
                // Устанавливаем созданный диалог как выбранный
                setSelectedDialog(dialog);
                // Закрываем модальное окно создания диалога
                setShowNewDialogModal(false);
                // Очищаем результаты поиска и сбрасываем состояния
                setSearchResults([]);
                setNewDialogUsername('');
                setSearchInputValue('');
                setSelectedUser(null);
                setHasSearched(false);
            } else if (res.status === 404) {
                setNewDialogError('Пользователь не найден');
            } else {
                const errorData = await res.json();
                setNewDialogError(errorData.error || 'Ошибка при создании диалога');
            }
        } catch (err) {
            console.error('Ошибка создания диалога:', err);
            setNewDialogError('Не удалось создать диалог: ' + err.message);
        } finally {
            setIsSearching(false);
        }
    };
    
    const handleCreateDialog = async () => {
        setNewDialogError('');
        
        // Если пользователь не выбран, показываем сообщение об ошибке
        if (!selectedUser) {
            setNewDialogError('Выберите пользователя из результатов поиска');
            return;
        }
        
        // Если с пользователем уже есть диалог, просто открываем его
        if (selectedUser.hasExistingDialog && selectedUser.existingDialog) {
            setSelectedDialog(selectedUser.existingDialog);
            setShowNewDialogModal(false);
            setSearchResults([]);
            setNewDialogUsername('');
            setSearchInputValue('');
            setSelectedUser(null);
            setHasSearched(false);
            return;
        }
        
        // Создаем новый диалог с выбранным пользователем
        await createDialog(selectedUser.username);
    };

    // Подготовка сообщений для рендера
    let lastDateKey = null;
    const messagesToRender = (Array.isArray(messages) ? messages : [])
        .filter(message => !message.is_deleted)
        .map((message) => {
            const msgDate = new Date(message.created_at);
            const msgDateKey = msgDate.getFullYear() + '-' + (msgDate.getMonth()+1) + '-' + msgDate.getDate();
            const needDivider = lastDateKey !== msgDateKey;
            lastDateKey = msgDateKey;
            return { message, needDivider };
        });

    const handleReplyPreviewClick = (msgId) => {
        const el = messageRefs.current[msgId];
        if (el) {
            el.scrollIntoView({ behavior: 'smooth', block: 'center' });
            setHighlightedMsgId(msgId);
            setTimeout(() => setHighlightedMsgId(null), 1500);
        }
    };

    async function handleLeaveDialog(mode) {
        if (!selectedDialog) return;
        setLeaveLoading(true);
        setLeaveError('');
        try {
            console.log('Attempting to leave dialog:', selectedDialog.id, 'mode:', mode);
            const csrfToken = getCSRFToken();
            console.log('CSRF Token:', csrfToken);
            
            const requestBody = JSON.stringify({ mode });
            console.log('Request body:', requestBody);
            
            const res = await csrfFetch(`${API_BASE_URL}/api/dialogs/${selectedDialog.id}/leave/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                credentials: 'include',
                body: requestBody
            });
            
            console.log('Response status:', res.status);
            console.log('Response headers:', Object.fromEntries(res.headers.entries()));
            
            // Проверяем тип контента
            const contentType = res.headers.get('content-type');
            console.log('Content-Type:', contentType);
            
            if (!contentType || !contentType.includes('application/json')) {
                const text = await res.text();
                console.error('Non-JSON response:', text);
                throw new Error('Server returned non-JSON response');
            }
            
            const data = await res.json();
            console.log('Response data:', data);
            
            if (!res.ok) {
                throw new Error(data.error || 'Ошибка при выходе из чата');
            }
            
            // Обновить список диалогов
            const dialogsRes = await fetch(`${API_BASE_URL}/api/dialogs/`, { 
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                }
            });
            
            if (dialogsRes.ok) {
                const dialogsData = await dialogsRes.json();
                setDialogs(dialogsData.results);
                
                // Если удалили у себя или у всех — сбросить selectedDialog
                if (mode === 'all' || mode === 'self') {
                    setSelectedDialog(null);
                }
                
                // Показываем уведомление об успешном действии
                if (mode === 'all') {
                    setToast('Диалог удален у всех участников');
                } else {
                    setToast('Вы покинули диалог');
                }
                setTimeout(() => setToast(null), 2000);
            }
            
            setShowLeaveDialogModal(false);
        } catch (err) {
            console.error('Error leaving dialog:', err);
            setLeaveError(err.message || 'Ошибка при выходе из чата');
        } finally {
            setLeaveLoading(false);
        }
    }

    return (
        <div className="flex h-[500px]">
            {/* Боковая панель с диалогами */}
            <div className={`w-1/4 min-w-[220px] max-w-[260px] border-r ${theme === 'dark' ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'}`}>
                <div className="p-4">
                    <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        Сообщения
                    </h2>
                    <div className="space-y-2">
                        {/* Кнопка "Начать новый диалог" всегда отображается вверху */}
                        <button
                            className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mb-4"
                            onClick={() => setShowNewDialogModal(true)}
                        >
                            Начать новый диалог
                        </button>
                        
                        {dialogs.length === 0 && !error && (
                            <div className="text-gray-400 p-4 flex flex-col items-center gap-2">
                                <span>У вас пока нет диалогов</span>
                            </div>
                        )}
                        {dialogs.map(dialog => (
                            <div
                                key={dialog.id}
                                onClick={() => setSelectedDialog(dialog)}
                                className={`p-3 rounded-lg cursor-pointer transition-colors flex items-center gap-3 ${
                                    selectedDialog?.id === dialog.id
                                        ? theme === 'dark'
                                            ? 'bg-gray-700'
                                            : 'bg-gray-100'
                                        : theme === 'dark'
                                            ? 'hover:bg-gray-700'
                                            : 'hover:bg-gray-50'
                                }`}
                            >
                                <UserAvatar
                                    user={{
                                      ...dialog.participant,
                                      avatar_thumbnail_url: dialog.participant.avatar_thumbnail_url || dialog.participant.avatar_thumbnail,
                                      avatar_url: dialog.participant.avatar_url || dialog.participant.avatar
                                    }}
                                    size="mini"
                                    backendUrl={API_BASE_URL}
                                    className="w-12 h-12 rounded-full border-2 object-cover flex-shrink-0"
                                    alt={dialog.participant?.display_name}
                                />
                                <div className="flex-1 min-w-0">
                                    <p className={`font-medium truncate flex items-center ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                                        {dialog.unread_count > 0 && (
                                            <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full mr-2">
                                                {dialog.unread_count}
                                            </span>
                                        )}
                                        {dialog.participant?.display_name === 'Аккаунт удален' ? (
                                            <span className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                                                Аккаунт удален
                                            </span>
                                        ) : (
                                            dialog.participant?.display_name
                                        )}
                                    </p>
                                    <p className={`text-sm truncate ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>{dialog.participant?.status || ''}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Центральная область с чатом */}
            <div className={`flex-1 flex flex-col ${theme === 'dark' ? 'bg-gray-900' : 'bg-white'}`}>
                {toast && (
                    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50">
                        {toast}
                    </div>
                )}
                {selectedDialog ? (
                    <>
                        {/* Заголовок чата */}
                        <div className={`p-4 border-b flex items-center gap-3 justify-between ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}> 
                            <div className="flex items-center gap-3">
                                <UserAvatar
                                    user={{
                                      ...selectedDialog.participant,
                                      avatar_thumbnail_url: selectedDialog.participant.avatar_thumbnail_url || selectedDialog.participant.avatar_thumbnail,
                                      avatar_url: selectedDialog.participant.avatar_url || selectedDialog.participant.avatar
                                    }}
                                    size="mini"
                                    backendUrl={API_BASE_URL}
                                    className="w-12 h-12 rounded-full border-2 object-cover flex-shrink-0"
                                    alt={selectedDialog.participant?.display_name}
                                />
                                <div>
                                    <h3 className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                                        {selectedDialog.participant?.display_name === 'Аккаунт удален' ? (
                                            <span className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                                                Аккаунт удален
                                            </span>
                                        ) : (
                                            <Link
                                                to={`/lpu/${selectedDialog.participant?.username}`}
                                                className="hover:underline focus:underline transition-colors"
                                                style={{ color: theme === 'dark' ? '#fff' : '#222' }}
                                            >
                                                {selectedDialog.participant?.display_name}
                                            </Link>
                                        )}
                                    </h3>
                                    <div className="flex items-center gap-2 mt-1">
                                        <div className={`w-3 h-3 rounded-full ${selectedDialog.participant?.is_online ? 'bg-green-500' : 'bg-red-500'}`}></div>
                                        <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>{selectedDialog.participant?.status}</span>
                                    </div>
                                </div>
                            </div>
                            <button
                                className={`px-3 py-1 rounded bg-red-500 text-white hover:bg-red-600 transition ml-auto`}
                                onClick={() => setShowLeaveDialogModal(true)}
                                title="Покинуть чат"
                            >Покинуть чат</button>
                        </div>

                        {/* Сообщения */}
                        <div ref={containerRef} className="flex-1 overflow-y-auto p-4 space-y-4">
                            {/* Индикатор загрузки дополнительных сообщений */}
                            {loadingMore && (
                                <div className="text-center p-2 text-gray-500">
                                    <div className="inline-flex items-center">
                                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Загрузка сообщений...
                                    </div>
                                </div>
                            )}
                            
                            {/* Показываем информацию о наличии дополнительных сообщений */}
                            {hasMore && !loadingMore && (
                                <div className="text-center p-2">
                                    <div className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                                        Прокрутите вверх для загрузки предыдущих сообщений
                                    </div>
                                </div>
                            )}
                            
                            {messagesLoading ? (
                                <div className="text-center p-4">Загрузка сообщений...</div>
                            ) : (
                                messagesToRender.map(({ message, needDivider }) => (
                                    <React.Fragment key={message.id}>
                                        {needDivider && <DateDivider date={message.created_at} timezone={timezone} />}
                                        <div
                                            ref={el => messageRefs.current[message.id] = el}
                                            data-message-id={message.id}
                                            className={`flex ${message.sender.id === user.id ? 'justify-end' : 'justify-start'} ${highlightedMsgId === message.id ? 'ring-2 ring-blue-400' : ''}`}
                                        >
                                            {message.sender.id !== user.id && (
                                                <UserAvatar
                                                    user={{
                                                      ...message.sender,
                                                      avatar_thumbnail_url: message.sender.avatar_thumbnail_url || message.sender.avatar_thumbnail,
                                                      avatar_url: message.sender.avatar_url || message.sender.avatar
                                                    }}
                                                    size="mini"
                                                    backendUrl={API_BASE_URL}
                                                    className="w-10 h-10 rounded-full border-2 object-cover flex-shrink-0 mr-2 mt-auto"
                                                    alt={message.sender?.display_name}
                                                />
                                            )}
                                            <div className={`max-w-[70%] rounded-lg p-3 ${
                                                message.sender.id === user.id
                                                    ? theme === 'dark'
                                                        ? 'bg-blue-600 text-white'
                                                        : 'bg-blue-500 text-white'
                                                    : theme === 'dark'
                                                        ? 'bg-gray-700 text-white'
                                                        : 'bg-gray-100 text-gray-900'
                                            }`}>
                                                {/* Изображение-миниатюра, если есть */}
                                                {message.image_thumbnail_url && (
                                                    <img
                                                        src={getMediaUrl(message.image_thumbnail_url)}
                                                        alt="Миниатюра"
                                                        className="rounded cursor-pointer max-w-[200px] max-h-[200px] mb-2 border"
                                                        onClick={() => setModalImage(getMediaUrl(message.image_url))}
                                                    />
                                                )}
                                                {/* GIF, если есть */}
                                                {message.gif && (
                                                    <img
                                                        src={message.gif}
                                                        alt="GIF"
                                                        className="rounded max-w-[200px] max-h-[200px] mb-2 border"
                                                    />
                                                )}
                                                {message.reply_to && (
                                                    <div
                                                        className="mb-1 pl-2 border-l-2 border-blue-300 bg-blue-50 dark:bg-gray-700 rounded text-xs text-gray-600 dark:text-gray-300 cursor-pointer hover:bg-blue-100 dark:hover:bg-gray-600 transition"
                                                        onClick={() => handleReplyPreviewClick(message.reply_to.id)}
                                                        title="Перейти к сообщению"
                                                    >
                                                        {message.reply_to.is_deleted ? (
                                                            <span className="inline-block text-gray-400 italic">Удалено</span>
                                                        ) : message.reply_to.gif ? (
                                                            <>
                                                                <span className="inline-block mr-1 align-middle text-pink-500 font-bold">GIF</span>
                                                                {message.reply_to.image_thumbnail_url && (
                                                                    <img src={getMediaUrl(message.reply_to.image_thumbnail_url)} alt="" className="inline-block h-5 w-5 rounded mr-1 align-middle" />
                                                                )}
                                                            </>
                                                        ) : message.reply_to.image_thumbnail_url ? (
                                                            <>
                                                                <span className="inline-block mr-1 align-middle text-blue-500 font-bold">Фото</span>
                                                                <img src={getMediaUrl(message.reply_to.image_thumbnail_url)} alt="" className="inline-block h-5 w-5 rounded mr-1 align-middle" />
                                                            </>
                                                        ) : message.reply_to.text ? (
                                                            message.reply_to.text.slice(0, 50)
                                                        ) : (
                                                            <span className="inline-block text-gray-400 italic">Удалено</span>
                                                        )}
                                                    </div>
                                                )}
                                                <p className="break-words">{message.text}</p>
                                                {message.updated_at && (
                                                    <p className={`text-xs italic mt-1 ${message.sender.id === user.id ? 'text-blue-200' : 'text-gray-400'}`}>
                                                        ред: {new Date(message.updated_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                                                    </p>
                                                )}
                                                <p className={`text-xs mt-1 flex items-center justify-end gap-1 ${
                                                    message.sender.id === user.id
                                                        ? 'text-blue-100'
                                                        : theme === 'dark'
                                                            ? 'text-gray-400'
                                                            : 'text-gray-500'
                                                }`}>
                                                    {formatDate(message.created_at, timezone)}
                                                    {message.sender.id === user.id && (
                                                        <span className="ml-2">
                                                            {message.is_read ? '✓✓' : '✓'}
                                                        </span>
                                                    )}
                                                    {/* Значки редактирования и удаления */}
                                                    {canEdit(message) && (
                                                        <PencilSquareIcon
                                                            className="h-4 w-4 ml-2 cursor-pointer text-gray-400 hover:text-blue-500 inline"
                                                            onClick={() => startEdit(message)}
                                                            title="Редактировать"
                                                        />
                                                    )}
                                                    {canDelete(message) && (
                                                        <TrashIcon
                                                            className="h-4 w-4 ml-1 cursor-pointer text-gray-400 hover:text-red-500 inline"
                                                            onClick={() => handleDelete(message.id)}
                                                            title="Удалить"
                                                        />
                                                    )}
                                                    {message.sender.id !== user.id && (
                                                        <ArrowUturnLeftIcon
                                                            className="h-4 w-4 ml-2 cursor-pointer text-gray-400 hover:text-green-500 inline"
                                                            onClick={() => setReplyToMessage(message)}
                                                            title="Ответить"
                                                        />
                                                    )}
                                                </p>
                                            </div>
                                        </div>
                                    </React.Fragment>
                                ))
                            )}
                            {/* Модальное окно для просмотра оригинала */}
                            {modalImage && (
                                <div
                                    className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50"
                                    onClick={() => setModalImage(null)}
                                >
                                    <div className="relative" onClick={e => e.stopPropagation()}>
                                        <button
                                            className="absolute top-2 right-2 text-white bg-black bg-opacity-50 rounded-full p-1 hover:bg-opacity-80"
                                            onClick={() => setModalImage(null)}
                                        >
                                            <XMarkIcon className="h-6 w-6" />
                                        </button>
                                        <img
                                            src={getMediaUrl(modalImage)}
                                            alt="Оригинал"
                                            className="max-w-[90vw] max-h-[80vh] rounded shadow-lg"
                                        />
                                    </div>
                                </div>
                            )}
                            
                            {/* Индикатор печати */}
                            {wsUserTyping && wsUserTyping.userId !== user.id && (
                                <div className="flex justify-start px-4 pb-2">
                                    <div className="flex items-center space-x-2">
                                        <img
                                            src={getCachedUserAvatar(selectedDialog?.participant, 'mini', API_BASE_URL)}
                                            alt={selectedDialog?.participant?.display_name}
                                            className="w-8 h-8 rounded-full border object-cover flex-shrink-0"
                                            onError={e => { e.target.onerror = null; e.target.src = '/lpo/ava.webp'; }}
                                        />
                                        <div className={`px-3 py-2 rounded-lg ${
                                            theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'
                                        }`}>
                                            <div className="flex items-center space-x-1">
                                                <span className="text-sm italic">печатает</span>
                                                <div className="flex space-x-1">
                                                    <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
                                                    <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{animationDelay: '150ms'}}></div>
                                                    <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{animationDelay: '300ms'}}></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}
                            
                            <div ref={messagesEndRef} />
                        </div>

                        {/* Форма отправки сообщения */}
                        {selectedDialog?.participant?.display_name === 'Аккаунт удален' ? (
                            <div className="p-4 border-t border-gray-200 bg-gray-50 dark:bg-gray-800">
                                <div className="text-center text-gray-500 dark:text-gray-400">
                                    <p>Невозможно отправить сообщение.</p>
                                    <p className="text-sm">Пользователь удалил свой аккаунт.</p>
                                </div>
                            </div>
                        ) : (
                            <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-200">
                            <div className="flex space-x-2 items-end">
                                <button
                                    type="button"
                                    onClick={() => fileInputRef.current?.click()}
                                    className={`p-2 rounded transition-colors ${
                                        theme === 'dark'
                                            ? 'text-gray-300 hover:text-white bg-gray-700 hover:bg-gray-600'
                                            : 'text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200'
                                    }`}
                                    title="Прикрепить изображение"
                                >
                                    <PhotoIcon className="h-6 w-6" />
                                </button>
                                <input
                                    type="file"
                                    accept="image/*"
                                    ref={fileInputRef}
                                    onChange={handleFileSelect}
                                    style={{ display: 'none' }}
                                />
                                <div className="flex-1 flex flex-col justify-end">
                                    {replyToMessage && (
                                        <div className="flex items-center bg-gray-100 dark:bg-gray-800 rounded px-2 py-1 mb-2 border-l-4 border-blue-400 relative">
                                            <div className="flex-1">
                                                <div className="text-xs text-gray-500">Ответ на сообщение:</div>
                                                <div className="text-sm truncate">
                                                    {replyToMessage.image_thumbnail_url && (
                                                        <img src={getMediaUrl(replyToMessage.image_thumbnail_url)} alt="" className="inline-block h-6 w-6 rounded mr-2 align-middle" />
                                                    )}
                                                    {replyToMessage.text ? replyToMessage.text.slice(0, 60) : '[Изображение]'}
                                                </div>
                                            </div>
                                            <button
                                                className="ml-2 text-gray-400 hover:text-red-500"
                                                onClick={() => setReplyToMessage(null)}
                                                title="Отменить ответ"
                                            >
                                                <XMarkIcon className="h-4 w-4" />
                                            </button>
                                        </div>
                                    )}
                                    <textarea
                                        ref={textareaRef}
                                        value={newMessage}
                                        onChange={(e) => { 
                                            setNewMessage(e.target.value); 
                                            handleTextareaInput(e);
                                            // Вызываем handleTyping только если есть текст
                                            if (e.target.value.trim()) {
                                                handleTyping();
                                            }
                                        }}
                                        onInput={handleTextareaInput}
                                        placeholder="Введите сообщение..."
                                        maxLength={2000}
                                        rows={1}
                                        style={{ resize: 'none', minHeight: 40, maxHeight: 200 }}
                                        className={`w-full rounded-lg px-4 py-2 transition-all duration-100 ${
                                            theme === 'dark'
                                                ? 'bg-gray-700 text-white placeholder-gray-400'
                                                : 'bg-gray-100 text-gray-900 placeholder-gray-500'
                                        }`}
                                    />
                                    {newMessage.length > 0 && (
                                        <div className="text-xs text-gray-400 text-right mt-1">{newMessage.length} / 2000</div>
                                    )}
                                </div>
                                {/* Кнопка эмодзи */}
                                <button
                                    type="button"
                                    onClick={() => setShowEmojiMartModal(true)}
                                    className={`p-2 rounded transition-colors ${
                                        theme === 'dark'
                                            ? 'text-yellow-300 hover:text-yellow-400 bg-gray-700 hover:bg-gray-600'
                                            : 'text-yellow-500 hover:text-yellow-600 bg-gray-100 hover:bg-gray-200'
                                    }`}
                                    title="Эмодзи"
                                >
                                    <span role="img" aria-label="emoji">😊</span>
                                </button>
                                {/* Кнопка Giphy */}
                                <button
                                    type="button"
                                    onClick={() => setShowGiphyModal(true)}
                                    className={`p-2 rounded transition-colors ${
                                        theme === 'dark'
                                            ? 'text-pink-300 hover:text-pink-400 bg-gray-700 hover:bg-gray-600'
                                            : 'text-pink-500 hover:text-pink-600 bg-gray-100 hover:bg-gray-200'
                                    }`}
                                    title="GIF/Giphy"
                                >
                                    <span role="img" aria-label="gif">🎬</span>
                                </button>
                                <button
                                    type="submit"
                                    disabled={isSending}
                                    className={`px-4 py-2 rounded-lg flex items-center justify-center ${
                                        isSending
                                            ? (theme === 'dark' ? 'bg-blue-400 cursor-not-allowed text-white' : 'bg-blue-300 cursor-not-allowed text-white')
                                            : (theme === 'dark' ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-blue-500 hover:bg-blue-600 text-white')
                                    }`}
                                    title={isSending ? "Отправка..." : "Отправить"}
                                >
                                    <PaperAirplaneIcon className="h-5 w-5" />
                                </button>
                            </div>
                            {thumbPreview && (
                                <div className="mt-2 flex items-center space-x-2">
                                    <img src={thumbPreview} alt="Превью" className="max-h-20 max-w-20 rounded border" />
                                    <button
                                        type="button"
                                        onClick={() => { setSelectedFile(null); setThumbPreview(null); }}
                                        className="text-sm text-red-500 hover:text-red-700"
                                    >
                                        Удалить
                                    </button>
                                </div>
                            )}
                        </form>
                        )}
                    </>
                ) : (
                    <div className={`flex items-center justify-center h-full ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        Выберите диалог для начала общения
                    </div>
                )}
            </div>

            {/* Модальное окно для редактирования */}
            {editingMsg && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className={`${theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} rounded-lg p-6 shadow-lg w-full max-w-md relative`}>
                        <textarea
                            className={`w-full border rounded p-2 mb-4 ${theme === 'dark' ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-800'}`}
                            value={editText}
                            onChange={e => setEditText(e.target.value)}
                            rows={4}
                            maxLength={2000}
                        />
                        {editText.length > 0 && (
                            <div className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} text-right mb-2`}>{editText.length} / 2000</div>
                        )}
                        <div className="flex justify-end gap-2">
                            <button 
                                className={`p-2 rounded ${theme === 'dark' ? 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-red-400' : 'bg-gray-200 text-gray-500 hover:bg-gray-300 hover:text-red-500'}`} 
                                onClick={() => setEditingMsg(null)} 
                                title="Отменить"
                            >❌</button>
                            <button 
                                className={`p-2 text-white rounded ${theme === 'dark' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'}`} 
                                onClick={handleEditSave} 
                                title="Сохранить"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Модальное окно для создания диалога */}
            {showNewDialogModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className={`${theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} rounded-lg p-6 shadow-lg w-full max-w-md relative`}>
                        <h2 className="text-lg font-bold mb-4">Новый диалог</h2>
                        <div className="relative">
                            <input
                                type="text"
                                className={`w-full ${theme === 'dark' ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-800'} border rounded p-2 mb-2`}
                                placeholder="Поиск по логину или имени пользователя"
                                value={searchInputValue}
                                onChange={e => {
                                    const value = e.target.value;
                                    setSearchInputValue(value);
                                    
                                    // Если поле ввода очищено, сбрасываем все состояния
                                    if (!value.trim()) {
                                        setNewDialogError('');
                                        setSearchResults([]);
                                        setHasSearched(false);
                                        setSelectedUser(null);
                                    }
                                    
                                    debouncedSearchUsers(value);
                                }}
                            />
                            {isSearching && (
                                <div className="absolute right-3 top-3">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                                </div>
                            )}
                        </div>
                        {newDialogError && <div className="text-red-500 text-sm mb-2">{newDialogError}</div>}
                        
                        {/* Результаты поиска */}
                        {searchResults.length > 0 && (
                            <div className="mb-4">
                                {searchResults.length > 5 && (
                                    <div className={`text-xs mb-2 text-center ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                                        Найдено {searchResults.length} пользователей. Прокрутите для просмотра всех.
                                    </div>
                                )}
                                <div className="relative">
                                    <div className={`max-h-[320px] overflow-y-auto border rounded custom-scrollbar ${theme === 'dark' ? 'border-gray-700' : 'border-gray-300'}`}>
                                    {searchResults.map(user => (
                                    <div 
                                        key={user.id} 
                                        className={`p-2 flex items-center cursor-pointer ${selectedUser && selectedUser.id === user.id ? (theme === 'dark' ? 'bg-blue-700' : 'bg-blue-100') : ''} hover:${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'} border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-300'} last:border-b-0`}
                                        onClick={() => setSelectedUser(user)}
                                    >
                                        <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                                            <img 
                                                src={getCachedUserAvatar(user, 'mini', API_BASE_URL) || '/lpo/ava.webp'} 
                                                alt={user.username} 
                                                className="w-full h-full object-cover"
                                                onError={e => { e.target.onerror = null; e.target.src = '/lpo/ava.webp'; }}
                                            />
                                        </div>
                                        <div className="flex-1">
                                            <div className="font-medium">{user.display_name || user.username}</div>
                                            {user.display_name && <div className="text-sm text-gray-500">@{user.username}</div>}
                                            {user.hasExistingDialog && (
                                                <div className="text-sm text-blue-500 mt-1">У вас уже есть диалог с этим пользователем</div>
                                            )}
                                        </div>
                                    </div>
                                                                    ))}
                                    </div>
                                    {searchResults.length > 5 && (
                                        <div className={`absolute bottom-0 left-0 right-0 h-4 pointer-events-none bg-gradient-to-t ${theme === 'dark' ? 'from-gray-800' : 'from-white'} to-transparent rounded-b`}></div>
                                    )}
                                </div>
                            </div>
                        )}
                        
                        <div className="flex justify-end gap-2">
                            <button 
                                className={`p-2 rounded ${theme === 'dark' ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`} 
                                onClick={() => {
                                    setShowNewDialogModal(false);
                                    setNewDialogUsername('');
                                    setSearchInputValue('');
                                    setNewDialogError('');
                                    setSearchResults([]);
                                    setSelectedUser(null);
                                    setHasSearched(false);
                                }}
                            >
                                Отмена
                            </button>
                            <button 
                                className={`p-2 text-white ${theme === 'dark' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} rounded`} 
                                onClick={handleCreateDialog}
                                disabled={isSearching || !selectedUser}
                            >
                                {selectedUser && selectedUser.hasExistingDialog ? 'Открыть диалог' : 'Выбрать'}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Модальные окна эмодзи и Giphy */}
            <EmojiMartModal
                open={showEmojiMartModal}
                onClose={() => setShowEmojiMartModal(false)}
                onSelect={emoji => setNewMessage(prev => prev + emoji)}
                theme={theme === 'dark' ? 'dark' : 'light'}
            />
            <GiphyModal
                open={showGiphyModal}
                onClose={() => setShowGiphyModal(false)}
                onSelect={async gif => {
                    if (!selectedDialog) return;
                    try {
                        const csrfToken = getCSRFToken();
                        const response = await csrfFetch(`${API_BASE_URL}/api/dialogs/${selectedDialog.id}/messages/`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': csrfToken
                            },
                            credentials: 'include',
                            body: JSON.stringify({
                                text: '',
                                gif: gif.images.original.url,
                                sender_id: user.id,
                                recipient_id: selectedDialog.participant?.id,
                                dialog: selectedDialog.id
                            })
                        });
                        if (!response.ok) throw new Error('Failed to send GIF');
                        const sentMessage = await response.json();
                        
                        // --- ИСПРАВЛЯЕМ: Не добавляем GIF локально, если WebSocket подключен ---
                        if (!dialogWsConnected) {
                            setMessages(prev => [...prev, sentMessage]);
                        }
                    } catch (err) {
                        setError(err.message);
                    }
                }}
                theme={theme === 'dark' ? 'dark' : 'light'}
            />

            {/* Модальное окно для выбора действия при выходе из чата */}
            {showLeaveDialogModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg w-full max-w-md relative">
                        <h2 className="text-lg font-bold mb-4 text-gray-900 dark:text-gray-100">Покинуть чат</h2>
                        <p className="mb-6 text-gray-700 dark:text-gray-300">Выберите действие:</p>
                        <div className="flex flex-col gap-3">
                            <button
                                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 font-semibold"
                                onClick={() => handleLeaveDialog('all')}
                                disabled={leaveLoading}
                            >
                                Покинуть и удалить историю
                                <div className="text-sm font-normal mt-1 opacity-80">
                                    Диалог будет удален у обоих участников
                                </div>
                            </button>
                            <button
                                className="px-4 py-2 rounded bg-yellow-500 text-white hover:bg-yellow-600 font-semibold"
                                onClick={() => handleLeaveDialog('self')}
                                disabled={leaveLoading}
                            >
                                Покинуть без удаления
                                <div className="text-sm font-normal mt-1 opacity-80">
                                    Диалог останется у собеседника
                                </div>
                            </button>
                            <button
                                className={`px-4 py-2 rounded font-semibold mt-2 ${theme === 'dark' ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-300 text-gray-800 hover:bg-gray-400'}`}
                                onClick={() => setShowLeaveDialogModal(false)}
                                disabled={leaveLoading}
                            >Отмена</button>
                        </div>
                        {leaveError && <div className="text-red-500 text-sm mt-2">{leaveError}</div>}
                    </div>
                </div>
            )}
        </div>
    );
};

export default Messages;