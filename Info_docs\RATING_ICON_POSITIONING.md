# 🎯 Настройка позиции иконок рейтингов

## 📍 Где менять значения для экспериментов

### Файл: `frontend/src/components/ProfileHeader.jsx`

#### 📖 Читательский рейтинг
**Строка ~1218** (ищите комментарий "🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ"):

```jsx
style={{
  filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(0,0,0,0.18))',
  // 🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ: измените значения для смещения иконки
  // offsetX: смещение по горизонтали (+ вправо, - влево)
  // offsetY: смещение по вертикали (+ вниз, - вверх)
  ...getReaderRatingIconOffset(-2, +4), // ← ЗДЕСЬ МЕНЯЙТЕ ПИКСЕЛИ
}}
```

#### ✍️ Авторский рейтинг
**Строка ~1235** (ищите комментарий "🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ"):

```jsx
style={{
  filter: 'drop-shadow(1px 1px 0 #181818) drop-shadow(0 2px 8px rgba(0,0,0,0.18))',
  // 🎯 ОБЛАСТЬ ДЛЯ ЭКСПЕРИМЕНТОВ: измените значения для смещения иконки
  // offsetX: смещение по горизонтали (+ вправо, - влево)
  // offsetY: смещение по вертикали (+ вниз, - вверх)
  ...getAuthorRatingIconOffset(0, -2), // ← ЗДЕСЬ МЕНЯЙТЕ ПИКСЕЛИ
}}
```

## ⚙️ Как настраивать смещение

### Параметры функций смещения:

**Для читательского рейтинга:** `getReaderRatingIconOffset(offsetX, offsetY)`  
**Для авторского рейтинга:** `getAuthorRatingIconOffset(offsetX, offsetY)`

- **offsetX** - смещение по горизонтали:
  - `+` положительные значения = сдвиг вправо
  - `-` отрицательные значения = сдвиг влево
  
- **offsetY** - смещение по вертикали:
  - `+` положительные значения = сдвиг вниз
  - `-` отрицательные значения = сдвиг вверх

## 📋 Примеры настроек

| Код | Результат |
|-----|-----------|
| `getReaderRatingIconOffset(0, 0)` или `getAuthorRatingIconOffset(0, 0)` | Без смещения (по умолчанию) |
| `getReaderRatingIconOffset(2, 0)` или `getAuthorRatingIconOffset(2, 0)` | Сдвиг на 2px вправо |
| `getReaderRatingIconOffset(-3, 0)` или `getAuthorRatingIconOffset(-3, 0)` | Сдвиг на 3px влево |
| `getReaderRatingIconOffset(0, -2)` или `getAuthorRatingIconOffset(0, -2)` | Сдвиг на 2px вверх |
| `getReaderRatingIconOffset(0, 1)` или `getAuthorRatingIconOffset(0, 1)` | Сдвиг на 1px вниз |
| `getReaderRatingIconOffset(1, -1)` или `getAuthorRatingIconOffset(1, -1)` | Сдвиг на 1px вправо и 1px вверх |
| `getReaderRatingIconOffset(-2, 2)` или `getAuthorRatingIconOffset(-2, 2)` | Сдвиг на 2px влево и 2px вниз |

## 🔧 Рекомендуемые значения для тестирования

1. **Точное выравнивание с текстом**: `(0, -1)` или `(0, -2)`
2. **Немного правее**: `(1, -1)` или `(2, -1)`
3. **Немного левее**: `(-1, -1)` или `(-2, -1)`
4. **Поднять выше**: `(0, -3)` или `(0, -4)`
5. **Опустить ниже**: `(0, 1)` или `(0, 2)`

## 💡 Советы по настройке

- Начните с малых значений (±1, ±2 пикселя)
- Протестируйте на разных размерах экрана (мобильные и десктоп)
- Учитывайте, что иконка имеет разные размеры на мобильных и десктопах
- После изменений обновите страницу в браузере для проверки

## 🎨 Текущие настройки

### 📖 Читательский рейтинг
По умолчанию установлено: `getReaderRatingIconOffset(-2, +4)`
- Смещение на 2 пикселя влево
- Смещение на 4 пикселя вниз

### ✍️ Авторский рейтинг
По умолчанию установлено: `getAuthorRatingIconOffset(0, -2)`
- Без горизонтального смещения
- Смещение на 2 пикселя вверх для лучшего выравнивания с текстом 