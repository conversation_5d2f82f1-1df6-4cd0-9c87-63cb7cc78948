import React, { useState, useContext, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import { useQueryClient } from '@tanstack/react-query';
import { getCSRFToken } from '../utils/csrf';

const FeedSettings = () => {
    const [showUnsubscribes, setShowUnsubscribes] = useState(false);
    const [showRemovedFromFriends, setShowRemovedFromFriends] = useState(false);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState(false);
    const navigate = useNavigate();
    const { username } = useParams();
    const { user } = useContext(AuthContext);
    const queryClient = useQueryClient();

    useEffect(() => {
        if (!user || user.username !== username) return;
        setLoading(true);
        axios.get('/api/auth/feed/settings/', { withCredentials: true })
            .then(res => {
                setShowUnsubscribes(!!res.data.show_unsubscribes_in_feed);
                setShowRemovedFromFriends(!!res.data.show_removed_from_friends_in_feed);
                setLoading(false);
            })
            .catch(() => {
                setError('Ошибка загрузки настроек');
                setLoading(false);
            });
    }, [user, username]);

    if (!user || user.username !== username) {
        return (
            <div className="mt-8 max-w-2xl">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                    <div className="flex justify-center items-center h-32">
                        <div className="text-red-500">
                            У вас нет доступа к этой странице
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    const handleSave = async (e) => {
        e.preventDefault();
        setSaving(true);
        setError('');
        setSuccess(false);
        try {
            const csrfToken = getCSRFToken();
            await axios.patch('/api/auth/feed/settings/', {
                show_unsubscribes_in_feed: showUnsubscribes,
                show_removed_from_friends_in_feed: showRemovedFromFriends
            }, {
                withCredentials: true,
                headers: { 'X-CSRFToken': csrfToken }
            });
            setTimeout(() => {
                setSaving(false);
                setSuccess(true);
                queryClient.invalidateQueries(['feed']);
            }, 600);
        } catch {
            setError('Ошибка при сохранении');
            setSaving(false);
        }
    };

    const handleCancel = () => {
        navigate(`/lpu/${username}/feed`);
    };

    if (loading) {
        return (
            <div className="mt-8 max-w-2xl">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                    <div className="flex justify-center items-center h-32">
                        <svg className="animate-spin h-6 w-6 text-blue-500 mr-2" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z" />
                        </svg>
                        <div className="text-gray-500 dark:text-gray-400">Загрузка настроек...</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="mt-8 max-w-2xl">
            <h2 className="text-2xl font-bold mb-8">Настройки уведомлений</h2>
            <form className="space-y-8" onSubmit={handleSave}>
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-start gap-4">
                    {/* Тумблер */}
                    <label className="relative inline-flex items-center cursor-pointer mt-1">
                        <input
                            type="checkbox"
                            checked={showUnsubscribes}
                            onChange={e => setShowUnsubscribes(e.target.checked)}
                            className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 rounded-full peer peer-checked:bg-blue-500 dark:peer-checked:bg-blue-400 transition-colors"></div>
                        <div className="absolute left-1 top-1 bg-white w-4 h-4 rounded-full shadow transition-transform peer-checked:translate-x-5"></div>
                    </label>
                    <div className="flex flex-col">
                        <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Показывать уведомления об отписках</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">(В ленте будут появляться события, когда кто-то отписался от вас. По умолчанию выключено)</span>
                    </div>
                </div>
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-start gap-4">
                    {/* Тумблер */}
                    <label className="relative inline-flex items-center cursor-pointer mt-1">
                        <input
                            type="checkbox"
                            checked={showRemovedFromFriends}
                            onChange={e => setShowRemovedFromFriends(e.target.checked)}
                            className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 rounded-full peer peer-checked:bg-blue-500 dark:peer-checked:bg-blue-400 transition-colors"></div>
                        <div className="absolute left-1 top-1 bg-white w-4 h-4 rounded-full shadow transition-transform peer-checked:translate-x-5"></div>
                    </label>
                    <div className="flex flex-col">
                        <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Показывать уведомления об удалении вас из друзей</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">(В ленте будут появляться события, когда кто-то удалил вас из друзей. По умолчанию выключено)</span>
                    </div>
                </div>
                {error && <div className="text-red-500 text-sm mb-2">{error}</div>}
                <div className="flex gap-4 mt-8">
                    <button
                        type="submit"
                        className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 flex items-center"
                        disabled={saving}
                    >
                        {saving && (
                            <svg className="animate-spin h-5 w-5 mr-2 text-white" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z" />
                            </svg>
                        )}
                        {saving ? 'Сохраняю...' : 'Сохранить'}
                    </button>
                    <button
                        type="button"
                        className="px-6 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600"
                        onClick={handleCancel}
                        disabled={saving}
                    >
                        Отмена
                    </button>
                </div>
            </form>
            {/* Всплывающее уведомление об успехе */}
            {success && (
                <div className="fixed bottom-8 right-8 z-50">
                    <div className="flex items-center bg-green-500 text-white px-6 py-3 rounded shadow-lg animate-fade-in">
                        <svg className="h-6 w-6 mr-2" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                        </svg>
                        Настройки успешно сохранены!
                    </div>
                </div>
            )}
            <style>{`
                @keyframes fade-in {
                    from { opacity: 0; transform: translateY(20px); }
                    to { opacity: 1; transform: translateY(0); }
                }
                .animate-fade-in {
                    animation: fade-in 0.4s ease;
                }
            `}</style>
        </div>
    );
};

export default FeedSettings; 