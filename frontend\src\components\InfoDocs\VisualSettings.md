# Визуальные настройки интерфейса

Документация по настройке визуальных элементов интерфейса LitPortal.

## Счетчик символов в редакторе рассказов

### Описание
Счетчик символов отображается внизу редактора рассказов и показывает текущее количество символов относительно максимального лимита (50,000 символов).

### Расположение кода
**Файл**: `frontend/src/components/BookEditors/StoryEditor.jsx`  
**Строки**: 1706-1720

### Настройка позиции по вертикали

Позиция счетчика настраивается через свойство `top` в стилях компонента:

```javascript
// Ручная настройка позиции счетчика по вертикали
position: 'relative',
top: isFullWidth ? '-7px' : '7px'  // полноэкранный: -7px (вверх), обычный: 7px (вниз)
```

### Параметры настройки

#### Полноэкранный режим (первое значение)
- **Текущее значение**: `-7px` (поднят на 7 пикселей вверх)
- **Отрицательные значения** (`-10px`, `-20px`) - поднимают счетчик вверх
- **Положительные значения** (`10px`, `20px`) - опускают счетчик вниз
- **Ноль** (`0px`) - оставляет на исходной позиции

#### Обычный режим (второе значение)
- **Текущее значение**: `7px` (опущен на 7 пикселей вниз)
- **Отрицательные значения** (`-5px`, `-15px`) - поднимают счетчик вверх
- **Положительные значения** (`5px`, `15px`) - опускают счетчик вниз
- **Ноль** (`0px`) - оставляет на исходной позиции

### Примеры настройки

```javascript
// Поднять в полноэкранном на 15px, в обычном на 10px
top: isFullWidth ? '-15px' : '-10px'

// Опустить в полноэкранном на 5px, в обычном оставить на месте
top: isFullWidth ? '5px' : '0px'

// В обоих режимах поднять на 12px
top: isFullWidth ? '-12px' : '-12px'

// Текущая настройка (оптимальная)
top: isFullWidth ? '-7px' : '7px'
```

### Визуальное поведение

- **Полноэкранный режим**: Счетчик поднят на 7px вверх от стандартной позиции, чтобы не прижиматься к нижней границе экрана
- **Обычный режим**: Счетчик опущен на 7px вниз для лучшего визуального разделения с контентом

### Связанные стили

Дополнительные настройки отступов:
```javascript
marginTop: 12,                           // Отступ сверху
marginBottom: isFullWidth ? 16 : 8,      // Отступ снизу (больше в полноэкранном режиме)
textAlign: 'center',                     // Выравнивание по центру
fontSize: 14,                            // Размер шрифта
fontWeight: 500                          // Толщина шрифта
```

### История изменений

- **v1.0**: Базовая реализация счетчика
- **v1.1**: Добавлена поддержка полноэкранного режима с увеличенными отступами
- **v1.2**: Добавлена возможность точной настройки позиции через `position: relative` и `top`
- **v1.3**: Оптимизированы значения: `-7px` для полноэкранного, `+7px` для обычного режима

### Рекомендации

1. **Тестирование**: После изменения значений проверьте отображение в обоих режимах
2. **Диапазон значений**: Рекомендуется использовать значения от -30px до +30px
3. **Консистентность**: Учитывайте общий дизайн интерфейса при настройке позиции
4. **Адаптивность**: Проверяйте отображение на разных размерах экрана

## Ограничения загрузки DOCX для рассказов

### Описание
При загрузке DOCX файлов в редактор рассказов действуют строгие ограничения для обеспечения качества и производительности.

### Ограничения

#### Размер файла
- **Максимум**: 10 МБ
- **Проверка**: Frontend и Backend
- **Файлы**: `StoryEditor.jsx` (строки 1247-1267), `views.py` (строки 1210-1227)

#### Количество символов
- **Максимум**: 50,000 символов
- **Проверка**: Backend (двойная проверка)
- **Файл**: `views.py` (строки 1235-1250 и 1876-1885)

## Повести (Novella)

### Ограничения DOCX загрузки

#### Размер файла
- **Максимум**: 40 МБ
- **Проверка**: Backend
- **Файл**: `views.py` (строки 1215-1219)

#### Количество символов
- **Максимум на главу**: 50,000 символов
- **Максимум на произведение**: 200,000 символов
- **Проверка**: Backend (двойная проверка)
- **Файл**: `views.py` (строки 1251-1264 и 1900-1909)

### Техническая реализация

#### Ранняя проверка символов
```python
# Быстрый подсчет общего количества символов в документе
total_text_length = 0
for paragraph in doc.paragraphs:
    total_text_length += len(paragraph.text)

if total_text_length > MAX_STORY_LENGTH:
    return Response({'error': 'Документ превышает максимальный размер рассказа'})
```

#### Поздняя проверка символов
```python
# Подсчет после обработки HTML
for chapter_data in chapters_from_docx:
    plain_text = re.sub(r'<[^>]*>', '', chapter_data.get('content', ''))
    total_new_content_length += len(plain_text)
```

#### Проверка для повестей
```python
elif book.type == 'novella':
    MAX_TALE_LENGTH = 200000
    # Быстрый подсчет общего количества символов в документе
    total_text_length = 0
    for paragraph in doc.paragraphs:
        total_text_length += len(paragraph.text)

    if total_text_length > MAX_TALE_LENGTH:
        return Response({'error': 'Документ превышает максимальный размер повести'})
```

### Сообщения об ошибках

#### Превышение лимита символов
```
Превышен максимальный размер рассказа: 50,000 символов.
Загружаемый документ содержит: X символов.
Сократите текст рассказа, либо создайте новую книгу с формой произведения повесть или роман.
```

#### Превышение размера файла
```
Размер файла X МБ превышает максимально допустимый размер 10 МБ для рассказов.
Рекомендуется удалить лишние изображения, сжать изображения или упростить форматирование.
```

#### Превышение лимита символов для повестей
```
Превышен максимальный размер повести: 200,000 символов.
Загружаемый документ содержит: X символов.
Сократите текст повести, либо создайте новую книгу с формой произведения роман.
```

## Валидация DOCX файлов для сборников рассказов

### Ограничения для сборников рассказов
- **Максимум символов на рассказ**: 50,000
- **Максимум символов на произведение**: 2,500,000
- **Максимальный размер файла**: 15MB

### Техническая реализация для сборников рассказов

#### Backend проверка (views.py)
```python
# Проверка лимитов для сборников рассказов
if book.type == 'story_collection':
    MAX_CHAPTER_LENGTH = 50000  # 50k символов на рассказ
    MAX_NOVEL_LENGTH = 2500000  # 2.5M символов на произведение
```

#### Сообщения об ошибках для сборников рассказов
```
Превышен лимит размера рассказа в сборнике: 50,000 символов.
Загружаемый документ содержит рассказ с X символами.
Сократите рассказ или разделите на несколько рассказов меньших размеров.
```

## Валидация DOCX файлов для сборников поэзии

### Ограничения для сборников поэзии
- **Максимум символов на поэтическое произведение**: 50,000
- **Максимум символов на произведение**: 2,500,000
- **Максимальный размер файла**: 15MB

### Техническая реализация для сборников поэзии

#### Backend проверка (views.py)
```python
# Проверка лимитов для сборников поэзии
if book.type == 'poetry_collection':
    MAX_CHAPTER_LENGTH = 50000  # 50k символов на поэтическое произведение
    MAX_NOVEL_LENGTH = 2500000  # 2.5M символов на произведение
```

#### Сообщения об ошибках для сборников поэзии
```
Превышен лимит размера поэтического произведения в сборнике: 50,000 символов.
Загружаемый документ содержит поэтическое произведение с X символами.
Сократите поэтическое произведение или разделите на несколько произведений меньших размеров.
```

### История изменений

- **v1.4**: Добавлена ранняя проверка символов сразу после парсинга документа для предотвращения обработки слишком больших файлов
- **v1.5**: Обновлены сообщения об ошибках для более понятного объяснения пользователям
- **v1.6**: Добавлены отдельные ограничения для повестей: 50,000 символов на главу и 200,000 символов на произведение
- **v1.7**: Добавлены отдельные ограничения для сборников рассказов и поэзии: 50,000 символов на рассказ/поэтическое произведение, 2,500,000 символов на произведение
