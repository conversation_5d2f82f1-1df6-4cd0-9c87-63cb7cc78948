import { useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { csrfFetch } from '../utils/csrf';

export const useLibraryActivity = (bookId) => {
  const { user } = useAuth();

  useEffect(() => {
    if (!user || !bookId) return;

    const updateActivity = async () => {
      try {
        const response = await csrfFetch('/api/library/update-activity/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            book_id: bookId
          })
        });

        // Если активность была обновлена и мы на странице библиотеки, обновляем список
        if (response.ok && window.refreshLibrary) {
          window.refreshLibrary();
        }
      } catch (error) {
        // Тихо игнорируем ошибки - это не критично
        console.debug('Library activity update failed:', error);
      }
    };

    // Обновляем активность при заходе на страницу
    updateActivity();

    // Обновляем активность каждые 5 минут если пользователь активен
    const interval = setInterval(() => {
      updateActivity();
    }, 5 * 60 * 1000); // 5 минут

    return () => clearInterval(interval);
  }, [user, bookId]);
}; 