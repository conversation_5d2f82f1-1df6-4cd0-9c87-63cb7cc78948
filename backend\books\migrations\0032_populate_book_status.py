from django.db import migrations

def populate_book_status(apps, schema_editor):
    Book = apps.get_model('books', 'Book')
    for book in Book.objects.all():
        if not book.is_published:
            book.status = 'draft'
        elif book.is_published and not book.is_finished:
            book.status = 'in_progress'
        else:
            book.status = 'finished'
        book.save(update_fields=['status'])

def reverse_populate_book_status(apps, schema_editor):
    # Обратная операция не требуется, так как поля is_published и is_finished сохраняются
    pass

class Migration(migrations.Migration):

    dependencies = [
        ('books', '0031_add_book_status_fieldpython'),
    ]

    operations = [
        migrations.RunPython(populate_book_status, reverse_populate_book_status),
    ] 