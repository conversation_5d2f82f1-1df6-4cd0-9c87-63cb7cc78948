import React, { useState, useRef } from 'react';
import { PaperAirplaneIcon, PhotoIcon, FaceSmileIcon } from '@heroicons/react/24/outline';
import RussianEmojiPicker from '../RussianEmojiPicker';
import { useSendMessage } from '../../hooks/useSendMessage';
import { useAuth } from '../../context/AuthContext';
import { useMessage } from '../../context/MessageContext';

export const MessageInput = ({ dialogId, recipientId }) => {
    const [message, setMessage] = useState('');
    const [selectedFile, setSelectedFile] = useState(null);
    const [selectedGif, setSelectedGif] = useState(null);
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const fileInputRef = useRef(null);
    const { sendMessage, isLoading } = useSendMessage();
    const { user } = useAuth();
    const { fetchUnreadCount } = useMessage();

    const handleSendMessage = async () => {
        if (!message.trim() && !selectedFile && !selectedGif) return;
        if (isLoading) return; // Предотвращаем повторную отправку, если предыдущая еще не завершена

        const formData = new FormData();
        if (message.trim()) formData.append('text', message.trim());
        if (selectedFile) formData.append('image', selectedFile);
        if (selectedGif) {
            formData.append('gif', selectedGif.url);
            formData.append('gif_id', selectedGif.id);
        }
        formData.append('dialog', dialogId);
        formData.append('recipient', recipientId);

        try {
            await sendMessage(formData);
            setMessage('');
            setSelectedFile(null);
            setSelectedGif(null);
            setShowEmojiPicker(false);
            // Обновляем счетчик непрочитанных сообщений
            await fetchUnreadCount();
        } catch (error) {
            console.error('Error sending message:', error);
        }
    };

    const handleFileSelect = (event) => {
        const file = event.target.files?.[0];
        if (file) {
            setSelectedFile(file);
        }
    };

    const handleEmojiClick = (emojiData) => {
        setMessage(prev => prev + emojiData.emoji);
        setShowEmojiPicker(false);
    };

    return (
        <div className="border-t border-gray-200 p-4">
            <div className="flex items-center space-x-2">
                <button
                    onClick={() => fileInputRef.current?.click()}
                    className="p-2 text-gray-500 hover:text-gray-700"
                >
                    <PhotoIcon className="h-6 w-6" />
                </button>
                <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileSelect}
                    className="hidden"
                    accept="image/*"
                />
                <button
                    onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    className="p-2 text-gray-500 hover:text-gray-700"
                >
                    <FaceSmileIcon className="h-6 w-6" />
                </button>
                <input
                    type="text"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="Введите сообщение..."
                    className="flex-1 rounded-lg border border-gray-300 px-4 py-2 focus:border-blue-500 focus:outline-none"
                />
                <button
                    onClick={handleSendMessage}
                    disabled={isLoading}
                    className={`rounded-full p-2 text-white ${isLoading ? 'bg-blue-300 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600'}`}
                >
                    <PaperAirplaneIcon className="h-6 w-6" />
                </button>
            </div>
            {showEmojiPicker && (
                <div className="absolute bottom-20 left-4">
                    <RussianEmojiPicker
                        onEmojiClick={handleEmojiClick}
                        height={450}
                        width={400}
                    />
                </div>
            )}
            {selectedFile && (
                <div className="mt-2 flex items-center space-x-2">
                    <span className="text-sm text-gray-500">{selectedFile.name}</span>
                    <button
                        onClick={() => setSelectedFile(null)}
                        className="text-sm text-red-500 hover:text-red-700"
                    >
                        Удалить
                    </button>
                </div>
            )}
        </div>
    );
};