# Generated by Django 5.0.2 on 2025-05-15 20:15

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0025_message_reply_to'),
    ]

    operations = [
        migrations.CreateModel(
            name='DialogUserState',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_deleted', models.BooleanField(default=False)),
                ('is_left', models.BooleanField(default=False)),
                ('left_at', models.DateTimeField(blank=True, null=True)),
                ('dialog', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_states', to='users.dialog')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('dialog', 'user')},
            },
        ),
    ]
