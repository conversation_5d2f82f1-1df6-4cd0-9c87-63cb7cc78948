import { useMemo } from 'react';
import { getCachedUserAvatar } from '../utils/avatarCache';

/**
 * Хук для получения аватара пользователя с React мемоизацией
 * @param {Object} user - Объект пользователя
 * @param {string} size - Размер аватара ('mini' или 'full')
 * @param {string} backendUrl - URL бэкенда
 * @param {number} imageVersion - Версия изображения
 * @param {boolean} forceFull - Принудительно использовать полный аватар
 * @returns {string} URL аватара
 */
export const useUserAvatar = (user, size = 'mini', backendUrl = '', imageVersion, forceFull = false) => {
    return useMemo(() => {
        return getCachedUserAvatar(user, size, backendUrl, imageVersion, forceFull);
    }, [
        user?.id,
        user?.username,
        user?.avatar_type,
        user?.avatar_updated_at,
        size,
        backendUrl,
        imageVersion,
        forceFull
    ]);
}; 