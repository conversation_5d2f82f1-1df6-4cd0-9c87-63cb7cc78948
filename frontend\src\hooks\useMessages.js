import { useState, useCallback, useRef } from 'react';
import { getCSRFToken, csrfFetch } from '../utils/csrf';
import { useUserCache } from '../context/UserCacheContext';

export const useMessages = (dialogId) => {
    const [messages, setMessages] = useState([]);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [hasMore, setHasMore] = useState(false);
    const [nextCursor, setNextCursor] = useState(null);
    const [error, setError] = useState(null);
    const [isFirstLoad, setIsFirstLoad] = useState(true);
    
    // Ref для предотвращения дублированных запросов
    const loadingRef = useRef(false);
    
    // Хук для кэширования пользователей
    const { cacheUser } = useUserCache();

    // Загрузка первых сообщений
    const loadMessages = useCallback(async () => {
        if (!dialogId || loadingRef.current) return { isFirstLoad: false };
        
        const wasFirstLoad = isFirstLoad;
        setLoading(true);
        setError(null);
        loadingRef.current = true;
        
        try {
            // При первой загрузке запрашиваем только 10 сообщений, потом по 15
            const pageSize = wasFirstLoad ? 10 : 15;
            const response = await fetch(`/api/dialogs/${dialogId}/messages/?page_size=${pageSize}`, {
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });
            
            if (response.status === 404) {
                setError('Диалог не найден или вы не являетесь его участником');
                return { isFirstLoad: false };
            }
            
            if (!response.ok) {
                throw new Error('Failed to fetch messages');
            }
            
            const data = await response.json();
            
            // Кэшируем данные пользователей из сообщений
            data.results.forEach(message => {
                if (message.sender) {
                    cacheUser(message.sender);
                }
                if (message.recipient) {
                    cacheUser(message.recipient);
                }
            });
            
            setMessages(data.results);
            setHasMore(data.has_more);
            setNextCursor(data.next_cursor);
            setIsFirstLoad(false);
            
            return { 
                isFirstLoad: wasFirstLoad, 
                hasMessages: data.results.length > 0,
                hasMoreMessages: data.has_more 
            };
            
        } catch (err) {
            console.error('Error loading messages:', err);
            setError('Ошибка загрузки сообщений');
            return { isFirstLoad: false };
        } finally {
            setLoading(false);
            loadingRef.current = false;
        }
    }, [dialogId, isFirstLoad, cacheUser]);

    // Загрузка дополнительных сообщений
    const loadMoreMessages = useCallback(async () => {
        if (!dialogId || !hasMore || !nextCursor || loadingRef.current) {
            return false;
        }
        
        setLoadingMore(true);
        loadingRef.current = true;
        
        try {
            const response = await fetch(`/api/dialogs/${dialogId}/messages/?cursor=${nextCursor}&page_size=15`, {
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });
            
            if (!response.ok) {
                throw new Error('Failed to fetch more messages');
            }
            
            const data = await response.json();
            
            // Кэшируем данные пользователей из новых сообщений
            data.results.forEach(message => {
                if (message.sender) {
                    cacheUser(message.sender);
                }
                if (message.recipient) {
                    cacheUser(message.recipient);
                }
            });
            
            // Добавляем новые сообщения в начало массива
            setMessages(prevMessages => [...data.results, ...prevMessages]);
            setHasMore(data.has_more);
            setNextCursor(data.next_cursor);
            
            return true;
            
        } catch (err) {
            console.error('Error loading more messages:', err);
            setError('Ошибка загрузки дополнительных сообщений');
            return false;
        } finally {
            setLoadingMore(false);
            loadingRef.current = false;
        }
    }, [dialogId, hasMore, nextCursor, cacheUser]);

    // Добавление нового сообщения
    const addMessage = useCallback((message) => {
        // Кэшируем данные пользователей из нового сообщения
        if (message.sender) {
            cacheUser(message.sender);
        }
        if (message.recipient) {
            cacheUser(message.recipient);
        }
        
        setMessages(prevMessages => {
            // Проверяем, что сообщение еще не добавлено
            const exists = prevMessages.find(msg => msg.id === message.id);
            if (exists) return prevMessages;
            return [...prevMessages, message];
        });
    }, [cacheUser]);

    // Обновление сообщения
    const updateMessage = useCallback((messageId, updates) => {
        setMessages(prevMessages => 
            prevMessages.map(msg => 
                msg.id === messageId ? { ...msg, ...updates } : msg
            )
        );
    }, []);

    // Обновление статуса прочтения сообщений
    const markMessagesAsRead = useCallback((messageIds, readByUserId, currentUserId) => {
        setMessages(prevMessages => 
            prevMessages.map(msg => {
                // Обновляем только сообщения:
                // 1. Которые есть в списке прочитанных
                // 2. Отправленные текущим пользователем
                // 3. Прочитанные не текущим пользователем
                const shouldUpdate = messageIds.includes(msg.id) && 
                                   msg.sender.id === currentUserId && 
                                   readByUserId !== currentUserId;
                
                if (shouldUpdate) {
                    return { ...msg, is_read: true, read_at: new Date().toISOString() };
                }
                return msg;
            })
        );
    }, []);

    // Сброс состояния
    const resetMessages = useCallback(() => {
        setMessages([]);
        setHasMore(false);
        setNextCursor(null);
        setError(null);
        setIsFirstLoad(true);
        loadingRef.current = false;
    }, []);

    return {
        messages,
        loading,
        loadingMore,
        hasMore,
        error,
        isFirstLoad,
        loadMessages,
        loadMoreMessages,
        addMessage,
        updateMessage,
        markMessagesAsRead,
        resetMessages
    };
}; 