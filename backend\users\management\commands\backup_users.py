"""
Команда для создания резервной копии пользователей.
"""

import json
import os
from datetime import datetime
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.core.serializers import serialize

User = get_user_model()


class Command(BaseCommand):
    help = 'Создает резервную копию пользователей'

    def add_arguments(self, parser):
        parser.add_argument(
            '--output-dir',
            type=str,
            default='backups',
            help='Директория для сохранения резервных копий'
        )

    def handle(self, *args, **options):
        output_dir = options['output_dir']
        
        # Создаем директорию если её нет
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Генерируем имя файла с текущей датой
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'users_backup_{timestamp}.json'
        filepath = os.path.join(output_dir, filename)
        
        try:
            # Получаем всех пользователей
            users = User.objects.all()
            user_count = users.count()
            
            if user_count == 0:
                self.stdout.write(
                    self.style.WARNING('ПРЕДУПРЕЖДЕНИЕ: В системе нет пользователей для резервного копирования!')
                )
                return
            
            # Сериализуем пользователей
            users_data = []
            for user in users:
                user_data = {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'is_active': user.is_active,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser,
                    'date_joined': user.date_joined.isoformat(),
                    'last_login': user.last_login.isoformat() if user.last_login else None,
                    # Добавляем специфичные для проекта поля
                    'display_name': getattr(user, 'display_name', ''),
                    'reader_rating': getattr(user, 'reader_rating', 0),
                    'author_rating': getattr(user, 'author_rating', 0),
                    'is_deleted': getattr(user, 'is_deleted', False),
                }
                users_data.append(user_data)
            
            # Создаем полную структуру резервной копии
            backup_data = {
                'created_at': datetime.now().isoformat(),
                'user_count': user_count,
                'users': users_data
            }
            
            # Сохраняем в файл
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Резервная копия создана успешно!\n'
                    f'Файл: {filepath}\n'
                    f'Пользователей сохранено: {user_count}'
                )
            )
            
            # Также создаем краткий отчет
            report_filename = f'users_report_{timestamp}.txt'
            report_filepath = os.path.join(output_dir, report_filename)
            
            with open(report_filepath, 'w', encoding='utf-8') as f:
                f.write(f"Отчет о резервном копировании пользователей\n")
                f.write(f"Дата создания: {datetime.now()}\n")
                f.write(f"Всего пользователей: {user_count}\n\n")
                
                f.write("Список пользователей:\n")
                for user in users:
                    status = "УДАЛЕН" if getattr(user, 'is_deleted', False) else "АКТИВЕН"
                    f.write(f"- {user.username} ({user.email}) - {status}\n")
            
            self.stdout.write(
                self.style.SUCCESS(f'Отчет сохранен: {report_filepath}')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Ошибка при создании резервной копии: {e}')
            )
