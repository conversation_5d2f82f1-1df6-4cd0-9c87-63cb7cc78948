from django.core.management.base import BaseCommand
from django.utils import timezone
from books.models import BookChapter
from books.tasks import publish_chapter_task
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Принудительная публикация просроченных глав'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Показать что будет сделано без выполнения',
        )
        parser.add_argument(
            '--book-id',
            type=int,
            help='ID конкретной книги для обработки',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        book_id = options['book_id']
        
        self.stdout.write(self.style.SUCCESS('=== Принудительная публикация просроченных глав ==='))
        
        # Находим просроченные главы
        overdue_filter = {
            'scheduled_publish_at__lt': timezone.now(),
            'is_published': False,
            'scheduled_publish_at__isnull': False
        }
        
        if book_id:
            overdue_filter['book_id'] = book_id
            
        overdue_chapters = BookChapter.objects.filter(**overdue_filter).order_by('order')
        
        if not overdue_chapters.exists():
            self.stdout.write(self.style.SUCCESS('✓ Нет просроченных глав'))
            return
            
        self.stdout.write(self.style.WARNING(f'⚠ Найдено просроченных глав: {overdue_chapters.count()}'))
        
        for chapter in overdue_chapters:
            delay = (timezone.now() - chapter.scheduled_publish_at).total_seconds() / 60
            self.stdout.write(
                f'  Глава {chapter.id} (порядок: {chapter.order}): '
                f'просрочена на {delay:.1f} мин - {chapter.book.title}'
            )
            
            if not dry_run:
                try:
                    # ОПТИМИЗИРОВАНО: Используем массовую логику
                    if getattr(chapter, 'publish_as_finished', False):
                        # Если нужно завершить произведение - используем оптимизированную логику
                        book = chapter.book
                        book._skip_rating_update = True
                        book.publish_all_chapters()

                        book.status = 'finished'
                        book.is_published = True
                        book.is_finished = True
                        book.updated_at = timezone.now()
                        book._skip_rating_update = False
                        book.save(update_fields=['status', 'is_published', 'is_finished', 'updated_at'])

                        self.stdout.write(self.style.SUCCESS(f'    ✓ Произведение завершено'))
                    else:
                        # Обычная публикация главы
                        chapter.is_published = True
                        chapter.scheduled_publish_at = None
                        chapter.celery_task_id = None
                        chapter.save()

                        self.stdout.write(self.style.SUCCESS(f'    ✓ Опубликована'))
                    
                    # Отправляем событие в WebSocket
                    from asgiref.sync import async_to_sync
                    from channels.layers import get_channel_layer
                    
                    channel_layer = get_channel_layer()
                    async_to_sync(channel_layer.group_send)(
                        f'book_{chapter.book.id}',
                        {
                            'type': 'chapter_published',
                            'chapter_id': chapter.id,
                            'title': chapter.title,
                        }
                    )
                    
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'    ✗ Ошибка: {e}'))
        
        if dry_run:
            self.stdout.write(self.style.WARNING('\n⚠ Это был dry-run. Для реальной публикации запустите без --dry-run'))
        else:
            self.stdout.write(self.style.SUCCESS(f'\n✓ Обработано глав: {overdue_chapters.count()}'))
        
        self.stdout.write(self.style.SUCCESS('=== Операция завершена ===')) 