# Generated by Django 5.0.2 on 2025-05-16 17:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0027_dialoguserstate_deleted_by_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='dialog',
            name='user_states',
            field=models.ManyToManyField(related_name='dialogs_user_states', through='users.DialogUserState', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='dialog',
            name='last_message',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='last_message_dialog', to='users.message'),
        ),
        migrations.AlterField(
            model_name='dialoguserstate',
            name='dialog',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dialog_user_states', to='users.dialog'),
        ),
    ]
