# Generated by Django 5.0.2 on 2025-06-22 08:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('books', '0024_bookchapter_publish_as_finished'),
        ('users', '0043_ratingcalculationrule_ratingrecalculationtask_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReadingSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('duration_minutes', models.PositiveIntegerField(default=0, help_text='Длительность сессии в минутах')),
                ('is_active', models.BooleanField(default=True)),
                ('auto_added_to_library', models.BooleanField(default=False, help_text='Автоматически добавлена в библиотеку после 5 минут')),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reading_sessions', to='books.book')),
                ('chapter', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='books.bookchapter')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reading_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Сессия чтения',
                'verbose_name_plural': 'Сессии чтения',
                'ordering': ['-started_at'],
                'indexes': [models.Index(fields=['user', 'book', '-started_at'], name='users_readi_user_id_9912fb_idx'), models.Index(fields=['user', 'is_active'], name='users_readi_user_id_f938b2_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserLibrary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('reading', 'Читаю'), ('want_to_read', 'Отложено на потом'), ('read', 'Прочитано')], max_length=20)),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('reading_started_at', models.DateTimeField(blank=True, help_text='Когда начал читать', null=True)),
                ('reading_finished_at', models.DateTimeField(blank=True, help_text='Когда закончил читать', null=True)),
                ('reading_progress', models.DecimalField(decimal_places=2, default=0, help_text='Прогресс чтения в процентах', max_digits=5)),
                ('user_rating', models.PositiveSmallIntegerField(blank=True, help_text='Личная оценка от 1 до 10', null=True)),
                ('private_notes', models.TextField(blank=True, help_text='Личные заметки о книге')),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='in_libraries', to='books.book')),
                ('current_chapter', models.ForeignKey(blank=True, help_text='Текущая глава', null=True, on_delete=django.db.models.deletion.SET_NULL, to='books.bookchapter')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='library', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Книга в библиотеке',
                'verbose_name_plural': 'Книги в библиотеке',
                'ordering': ['-updated_at'],
                'indexes': [models.Index(fields=['user', 'status'], name='users_userl_user_id_f5966b_idx'), models.Index(fields=['user', '-updated_at'], name='users_userl_user_id_027dad_idx'), models.Index(fields=['book', 'status'], name='users_userl_book_id_65cc5c_idx')],
                'unique_together': {('user', 'book')},
            },
        ),
    ]
