# Generated by Django 5.0.2 on 2025-05-11 10:39

import users.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0017_user_show_removed_from_friends_in_feed_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='avatar_preset',
            field=models.CharField(blank=True, default='', help_text='Путь к системному аватару', max_length=255),
        ),
        migrations.AddField(
            model_name='user',
            name='avatar_preset_mini',
            field=models.Char<PERSON>ield(blank=True, default='', help_text='Путь к миниатюре системного аватара', max_length=255),
        ),
        migrations.AddField(
            model_name='user',
            name='avatar_thumbnail',
            field=models.ImageField(blank=True, null=True, upload_to=users.models.user_avatar_thumb_path),
        ),
    ]
