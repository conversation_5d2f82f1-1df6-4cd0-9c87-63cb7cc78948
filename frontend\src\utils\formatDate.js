import moment from 'moment-timezone';

// Ручное сопоставление английских и русских названий месяцев
const MONTH_NAMES = {
  'January': 'января',
  'February': 'февраля', 
  'March': 'марта',
  'April': 'апреля',
  'May': 'мая',
  'June': 'июня',
  'July': 'июля',
  'August': 'августа',
  'September': 'сентября',
  'October': 'октября',
  'November': 'ноября',
  'December': 'декабря'
};

// date — ISO-строка или объект Date
// userTimezone — строка, например 'Europe/Moscow'
// format — строка формата moment, например 'DD.MM.YYYY HH:mm'
export function formatDateWithTimezone(date, userTimezone, format = 'DD.MM.YYYY HH:mm') {
  if (!date) return '';
  
  const momentDate = moment(date).tz(userTimezone || 'Europe/Moscow');
  let formattedDate = momentDate.format(format);
  
  // Заменяем английские названия месяцев на русские
  Object.entries(MONTH_NAMES).forEach(([english, russian]) => {
    formattedDate = formattedDate.replace(english, russian);
  });
  
  return formattedDate;
} 