# Generated by Django 5.0.2 on 2025-06-03 09:19

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0032_alter_user_avatar_alter_user_avatar_thumbnail_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='display_name',
            field=models.CharField(default='Пользователь', help_text='Имя или псевдоним, который будет отображаться на сайте.', max_length=150, verbose_name='display name'),
        ),
        migrations.AlterField(
            model_name='user',
            name='username',
            field=models.CharField(error_messages={'unique': 'Пользователь с таким логином уже существует.'}, help_text='Длина логина не должна превышать 30 символов. Допустимы латинские буквы и цифры.', max_length=30, unique=True, validators=[django.core.validators.RegexValidator(code='invalid_username', message='Логин должен содержать только строчные латинские буквы и цифры.', regex='^[a-z0-9]+$')], verbose_name='username'),
        ),
    ]
