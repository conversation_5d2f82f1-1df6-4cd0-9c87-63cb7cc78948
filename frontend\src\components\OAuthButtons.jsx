import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

const OAuthButtons = () => {
  const [loading, setLoading] = useState({});
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleOAuthLogin = async (provider) => {
    setLoading(prev => ({ ...prev, [provider]: true }));
    
    try {
      if (provider === 'vk') {
        // VK ID использует специальный подход
        handleVKLogin();
      } else {
        // Обычные OAuth провайдеры
        const authUrl = getAuthUrl(provider);
        
        const popup = window.open(
          authUrl,
          `${provider}_auth`,
          'width=600,height=700,scrollbars=yes,resizable=yes'
        );

        const messageListener = (event) => {
          if (event.origin !== window.location.origin) return;
          
          if (event.data.type === 'OAUTH_SUCCESS') {
            popup.close();
            login(event.data.user);
            navigate('/');
            window.removeEventListener('message', messageListener);
          } else if (event.data.type === 'OAUTH_ERROR') {
            popup.close();
            console.error('OAuth error:', event.data.error);
            window.removeEventListener('message', messageListener);
          }
        };

        window.addEventListener('message', messageListener);

        const checkClosed = setInterval(() => {
          if (popup.closed) {
            clearInterval(checkClosed);
            window.removeEventListener('message', messageListener);
            setLoading(prev => ({ ...prev, [provider]: false }));
          }
        }, 1000);
      }

    } catch (error) {
      console.error(`${provider} login error:`, error);
    } finally {
      setLoading(prev => ({ ...prev, [provider]: false }));
    }
  };

  const handleVKLogin = () => {
    const vkClientId = import.meta.env.VITE_VK_CLIENT_ID;
    
    // Используем VK Bridge для авторизации
    const vkAuthUrl = `https://oauth.vk.com/authorize?client_id=${vkClientId}&display=popup&scope=email&response_type=code&v=5.131&redirect_uri=${encodeURIComponent(window.location.origin)}`;
    
    const popup = window.open(
      vkAuthUrl,
      'vk_auth',
      'width=600,height=500,scrollbars=yes,resizable=yes'
    );

    const checkAuth = setInterval(() => {
      try {
        if (popup.closed) {
          clearInterval(checkAuth);
          setLoading(prev => ({ ...prev, vk: false }));
          return;
        }

        // Проверяем URL popup окна на наличие кода авторизации
        const popupUrl = popup.location.href;
        if (popupUrl.includes('code=')) {
          const urlParams = new URLSearchParams(popup.location.search);
          const code = urlParams.get('code');
          
          if (code) {
            popup.close();
            clearInterval(checkAuth);
            
            // Отправляем код на backend
            handleVKCallback(code);
          }
        }
      } catch (e) {
        // Игнорируем CORS ошибки при проверке popup URL
      }
    }, 1000);
  };

  const handleVKCallback = async (code) => {
    try {
      const response = await fetch('/api/users/auth/vk/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ 
          code,
          redirect_uri: window.location.origin
        }),
      });

      if (response.ok) {
        const userData = await response.json();
        login(userData.user);
        navigate('/');
      } else {
        const errorData = await response.json();
        console.error('VK auth error:', errorData);
      }
    } catch (error) {
      console.error('VK callback error:', error);
    }
  };

  const getAuthUrl = (provider) => {
    const baseUrl = window.location.origin;
    const redirectUri = `${baseUrl}/auth/callback/`;
    
    switch (provider) {
      case 'yandex':
        const yandexClientId = import.meta.env.VITE_YANDEX_CLIENT_ID;
        return `https://oauth.yandex.ru/authorize?response_type=code&client_id=${yandexClientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=login:email`;
      
      case 'google':
        const googleClientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
        return `https://accounts.google.com/oauth/authorize?client_id=${googleClientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=openid%20email%20profile&response_type=code`;
      
      default:
        throw new Error('Unknown provider');
    }
  };

  return (
    <div className="space-y-3">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300 dark:border-gray-600" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
            или войти через
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-3">
        {/* VK ID */}
        <button
          onClick={() => handleOAuthLogin('vk')}
          disabled={loading.vk}
          className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading.vk ? (
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-700 dark:text-gray-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none">
              <path d="M15.684 0H8.316C1.592 0 0 1.592 0 8.316v7.368C0 22.408 1.592 24 8.316 24h7.368C22.408 24 24 22.408 24 15.684V8.316C24 1.592 22.408 0 15.684 0z" fill="#4680C2"/>
              <path d="M12.69 13.078c.539 0 .847-.356.847-.983 0-.627-.308-.983-.847-.983-.538 0-.846.356-.846.983 0 .627.308.983.846.983zm-3.461-.983c0-.356.154-.576.384-.576.231 0 .385.22.385.576s-.154.576-.385.576c-.23 0-.384-.22-.384-.576z" fill="#fff"/>
              <path d="M18.927 7.99c-.462-.077-.847-.154-1.539-.154-1.385 0-2.308.693-2.308 1.846 0 .924.385 1.385 1.154 1.693l.692.307c.462.231.692.462.692.77 0 .384-.308.615-.846.615-.539 0-.924-.231-1.154-.615l-.77.692c.384.539 1.077.846 1.924.846 1.462 0 2.385-.693 2.385-1.846 0-.924-.385-1.385-1.154-1.693l-.692-.307c-.462-.231-.692-.462-.692-.77 0-.384.308-.615.846-.615.539 0 .924.231 1.154.615l.77-.692c-.384-.539-1.077-.846-1.924-.846z" fill="#fff"/>
            </svg>
          )}
          VK ID
        </button>

        {/* Yandex ID */}
        <button
          onClick={() => handleOAuthLogin('yandex')}
          disabled={loading.yandex}
          className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading.yandex ? (
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-700 dark:text-gray-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none">
              <rect width="24" height="24" rx="12" fill="#FC3F1D"/>
              <path d="M13.32 17.68V6.32h-1.28c-2.24 0-3.36 1.12-3.36 3.36 0 1.6.64 2.56 1.92 2.88l-2.24 5.12h1.6l2.08-4.8h1.28v4.8h1.28zM12.16 12.16h-1.28c-1.28 0-1.92-.64-1.92-1.92 0-1.28.64-1.92 1.92-1.92h1.28v3.84z" fill="white"/>
            </svg>
          )}
          Yandex ID
        </button>

        {/* Google */}
        <button
          onClick={() => handleOAuthLogin('google')}
          disabled={loading.google}
          className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading.google ? (
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-700 dark:text-gray-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
          )}
          Google
        </button>
      </div>
    </div>
  );
};

export default OAuthButtons; 