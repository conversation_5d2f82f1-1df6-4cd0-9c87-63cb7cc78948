import { useEffect, useRef, useState } from 'react';
import { useAuth } from '../context/AuthContext';

export const useWebSocketNotifications = () => {
    const { isAuthenticated } = useAuth();
    const [notifications, setNotifications] = useState({});
    const [isConnected, setIsConnected] = useState(false);
    const wsRef = useRef(null);

    useEffect(() => {
        if (!isAuthenticated) {
            setNotifications({});
            setIsConnected(false);
            return;
        }

        // Создаем WebSocket соединение
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//localhost:8000/ws/notifications/`;
        
        const ws = new WebSocket(wsUrl);
        wsRef.current = ws;

        ws.onopen = () => {
            setIsConnected(true);
        };

        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                
                if (data.type === 'notifications_update') {
                    // Получаем все уведомления при подключении
                    setNotifications(data.notifications);
                } else if (data.type === 'notification_update') {
                    // Обновляем конкретное уведомление
                    setNotifications(prev => ({
                        ...prev,
                        [data.notification_type]: data.data
                    }));
                }
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        ws.onclose = (event) => {
            console.warn('WebSocket disconnected:', event.code, event.reason);
            setIsConnected(false);
        };

        ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            setIsConnected(false);
        };

        // Очистка при размонтировании
        return () => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        };
    }, [isAuthenticated]);

    const markNotificationRead = (notificationType) => {
        if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
                type: 'mark_read',
                notification_type: notificationType
            }));
        }
    };

    return {
        notifications,
        isConnected,
        markNotificationRead
    };
}; 