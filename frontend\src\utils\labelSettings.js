// Настройки лейблов для обложек книг

export const LABEL_SETTINGS = {
  // Лейбл возрастного ограничения 18+
  ageRating: {
    width: 70,              // Ширина лейбла в пикселях
    height: 70,             // Высота лейбла в пикселях
    offsetX: 25,            // Отступ от правого края
    offsetY: 26,            // Отступ от нижнего края
    position: 'bottom-right', // Позиция на обложке
    description: 'Лейбл 18+ (квадратный, правый нижний угол)'
  },
  
  // Лейбл ненормативной лексики
  profanity: {
    width: 175,             // Ширина лейбла в пикселях
    height: 85,            // Высота лейбла в пикселях
    offsetX: 30,            // Отступ от левого края
    offsetY: 20,            // Отступ от нижнего края
    position: 'bottom-left', // Позиция на обложке
    description: 'Лейбл ненормативной лексики (прямоугольный, левый нижний угол)'
  }
};

// Функция для получения позиции лейбла на canvas
export const calculateLabelPosition = (labelType, canvasWidth, canvasHeight) => {
  const settings = LABEL_SETTINGS[labelType];
  if (!settings) {
    throw new Error(`Unknown label type: ${labelType}`);
  }

  let x, y;

  // Определяем масштаб для миниатюры (210x300 vs 700x1000)
  const isMiniature = canvasWidth === 210 && canvasHeight === 300;
  const scale = isMiniature ? 0.3 : 1; // 210/700 ≈ 0.3

  // Масштабируем размеры лейбла
  const scaledWidth = Math.round(settings.width * scale);
  const scaledHeight = Math.round(settings.height * scale);
  let scaledOffsetX = Math.round(settings.offsetX * scale);
  let scaledOffsetY = Math.round(settings.offsetY * scale);

  // Специальная коррекция для лейбла ненормативной лексики в миниатюре
  if (isMiniature && labelType === 'profanity') {
    scaledOffsetX += 10; // Сдвигаем вправо на 10px
  }

  switch (settings.position) {
    case 'bottom-right':
      x = canvasWidth - scaledWidth - scaledOffsetX;
      y = canvasHeight - scaledHeight - scaledOffsetY;
      break;

    case 'bottom-left':
      x = scaledOffsetX;
      y = canvasHeight - scaledHeight - scaledOffsetY;
      break;

    case 'top-right':
      x = canvasWidth - scaledWidth - scaledOffsetX;
      y = scaledOffsetY;
      break;

    case 'top-left':
      x = scaledOffsetX;
      y = scaledOffsetY;
      break;

    default:
      throw new Error(`Unknown position: ${settings.position}`);
  }

  return {
    x,
    y,
    width: scaledWidth,
    height: scaledHeight
  };
};

// Функция для отрисовки лейбла на canvas
export const drawLabel = (ctx, labelImage, labelType, canvasWidth, canvasHeight) => {
  const position = calculateLabelPosition(labelType, canvasWidth, canvasHeight);
  
  console.log(`Drawing ${labelType} label:`, {
    position: LABEL_SETTINGS[labelType].position,
    x: position.x,
    y: position.y,
    width: position.width,
    height: position.height
  });
  
  ctx.drawImage(labelImage, position.x, position.y, position.width, position.height);
};

// Экспорт для использования в других компонентах
export default LABEL_SETTINGS;
