#!/bin/bash

# Скрипт для запуска Celery worker и beat

echo "Starting Celery services..."

# Проверяем, что Redis запущен
echo "Checking Redis connection..."
python -c "
import redis
try:
    r = redis.Redis(host='localhost', port=6379, db=0)
    r.ping()
    print('✓ Redis is running')
except:
    print('✗ Redis is not running. Please start Redis first.')
    exit(1)
"

if [ $? -ne 0 ]; then
    exit 1
fi

# Запускаем Celery worker в фоне
echo "Starting Celery worker..."
celery -A config worker --loglevel=info --detach --pidfile=celery_worker.pid --logfile=celery_worker.log

# Запускаем Celery beat в фоне
echo "Starting Celery beat..."
celery -A config beat --loglevel=info --detach --pidfile=celery_beat.pid --logfile=celery_beat.log

echo "Celery services started!"
echo "Worker PID file: celery_worker.pid"
echo "Beat PID file: celery_beat.pid"
echo "Worker logs: celery_worker.log"
echo "Beat logs: celery_beat.log"
echo ""
echo "To stop services, run: ./stop_celery.sh"
