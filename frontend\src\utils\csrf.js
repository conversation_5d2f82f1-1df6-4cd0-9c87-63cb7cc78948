export function getCSRFToken() {
  const name = 'csrftoken=';
  const decodedCookie = decodeURIComponent(document.cookie);
  const cookies = decodedCookie.split(';');
  for (let cookie of cookies) {
    cookie = cookie.trim();
    if (cookie.startsWith(name)) {
      return cookie.substring(name.length);
    }
  }
  return '';
}

export async function csrfFetch(url, options = {}) {
  const headers = options.headers ? { ...options.headers } : {};
  headers['X-CSRFToken'] = getCSRFToken();
  return fetch(url, { ...options, headers });
} 