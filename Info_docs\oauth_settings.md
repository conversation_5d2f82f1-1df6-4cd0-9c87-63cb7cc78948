# OAuth Configuration Guide

## Environment Variables

Create a `.env` file in the backend directory with these variables:

```bash
# Database
DB_NAME=your_db_name
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=3306

# OAuth Settings
VK_CLIENT_ID=your_vk_app_id
VK_SECRET_KEY=your_vk_secret_key

YANDEX_CLIENT_ID=your_yandex_client_id
YANDEX_CLIENT_SECRET=your_yandex_client_secret

GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

OK_APP_ID=your_ok_app_id
OK_SECRET_KEY=your_ok_secret_key
OK_PUBLIC_KEY=your_ok_public_key
```

## Service Registration

### 1. VK ID
- Go to: https://dev.vk.com/
- Create new application (choose "Мини-приложение" or "Игра")
- In OAuth settings: Set Redirect URI: `http://localhost:8000/api/auth/vk/callback/`
- Copy App ID and Secret Key

### 2. Yandex ID
- Go to: https://oauth.yandex.com/client/new
- Create new application
- Set Redirect URI: `http://localhost:8000/api/auth/yandex/callback/`
- Copy Client ID and Client Secret

### 3. Google OAuth
- Go to: https://console.cloud.google.com/apis/credentials
- Create OAuth 2.0 Client IDs
- Set Redirect URI: `http://localhost:8000/api/auth/google/callback/`
- Copy Client ID and Client Secret

### 4. Одноклассники (OK)
- Go to: https://apiok.ru/dev/app/create
- Create new application
- Set Redirect URI: `http://localhost:8000/api/auth/ok/callback/`
- Copy App ID, Secret Key, and Public Key

## Usage

### Frontend Integration

```javascript
// VK Login
const vkLogin = () => {
  const clientId = 'your_vk_app_id';
  const redirectUri = 'http://localhost:8000/api/auth/vk/callback/';
  const scope = 'email';
  
  window.location.href = `https://oauth.vk.com/authorize?client_id=${clientId}&display=popup&redirect_uri=${redirectUri}&scope=${scope}&response_type=code&v=5.131`;
};

// Yandex Login
const yandexLogin = () => {
  const clientId = 'your_yandex_client_id';
  const redirectUri = 'http://localhost:8000/api/auth/yandex/callback/';
  
  window.location.href = `https://oauth.yandex.ru/authorize?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}`;
};

// Google Login
const googleLogin = () => {
  const clientId = 'your_google_client_id';
  const redirectUri = 'http://localhost:8000/api/auth/google/callback/';
  const scope = 'profile email';
  
  window.location.href = `https://accounts.google.com/oauth/authorize?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}`;
};

// OK Login
const okLogin = () => {
  const clientId = 'your_ok_app_id';
  const redirectUri = 'http://localhost:8000/api/auth/ok/callback/';
  const scope = 'VALUABLE_ACCESS';
  
  window.location.href = `https://connect.ok.ru/oauth/authorize?client_id=${clientId}&scope=${scope}&response_type=code&redirect_uri=${redirectUri}`;
};
```

### API Endpoints

After OAuth callback, send the authorization code to:
- VK: `POST /api/users/auth/vk/` with `{"code": "authorization_code"}`
- Yandex: `POST /api/users/auth/yandex/` with `{"code": "authorization_code"}`
- Google: `POST /api/users/auth/google/` with `{"code": "authorization_code"}`
- OK: `POST /api/users/auth/ok/` with `{"code": "authorization_code"}`

Response will contain JWT tokens for authentication. 