# Generated by Django 5.0.2 on 2025-06-20 10:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0042_usernotification'),
    ]

    operations = [
        migrations.CreateModel(
            name='RatingCalculationRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating_type', models.CharField(choices=[('reader', 'Читательский рейтинг'), ('author', 'Авторский рейтинг'), ('total', 'Общий рейтинг')], max_length=20)),
                ('metric_name', models.CharField(help_text='Название метрики (поле в UserStats)', max_length=50)),
                ('weight', models.DecimalField(decimal_places=2, default=1.0, help_text='Вес метрики в расчете', max_digits=5)),
                ('max_contribution', models.IntegerField(blank=True, help_text='Максимальный вклад метрики в рейтинг', null=True)),
                ('is_active', models.BooleanField(default=True, help_text='Учитывать ли метрику в расчете')),
                ('description', models.CharField(blank=True, help_text='Описание правила', max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Правило расчета рейтинга',
                'verbose_name_plural': 'Правила расчета рейтингов',
                'indexes': [models.Index(fields=['rating_type', 'is_active'], name='users_ratin_rating__380b5e_idx')],
                'unique_together': {('rating_type', 'metric_name')},
            },
        ),
        migrations.CreateModel(
            name='RatingRecalculationTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_type', models.CharField(choices=[('user_stats', 'Пересчет статистики пользователя'), ('user_rating', 'Пересчет рейтинга пользователя'), ('bulk_recalculation', 'Массовый пересчет'), ('daily_update', 'Ежедневное обновление')], max_length=30)),
                ('status', models.CharField(choices=[('pending', 'Ожидает'), ('processing', 'Выполняется'), ('completed', 'Завершено'), ('failed', 'Ошибка'), ('cancelled', 'Отменено')], default='pending', max_length=20)),
                ('priority', models.IntegerField(default=5, help_text='Приоритет задачи (1-10, 1=высший)')),
                ('parameters', models.JSONField(default=dict, help_text='Дополнительные параметры задачи')),
                ('celery_task_id', models.CharField(blank=True, max_length=100, null=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, help_text='Сообщение об ошибке')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(default='system', max_length=50)),
                ('user', models.ForeignKey(blank=True, help_text='Пользователь для пересчета (если не массовый)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='rating_tasks', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Задача пересчета рейтинга',
                'verbose_name_plural': 'Задачи пересчета рейтингов',
                'ordering': ['priority', 'created_at'],
                'indexes': [models.Index(fields=['status', 'priority'], name='users_ratin_status_14b985_idx'), models.Index(fields=['task_type', 'created_at'], name='users_ratin_task_ty_a28000_idx'), models.Index(fields=['user', 'status'], name='users_ratin_user_id_8d97f2_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserMetricHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('comment_added', 'Добавлен комментарий'), ('comment_removed', 'Удален комментарий'), ('review_added', 'Добавлен отзыв'), ('review_removed', 'Удален отзыв'), ('like_added', 'Поставлен лайк'), ('like_removed', 'Убран лайк'), ('book_published', 'Книга опубликована'), ('book_finished', 'Книга завершена'), ('friend_added', 'Добавлен друг'), ('friend_removed', 'Удален друг'), ('subscriber_added', 'Добавлен подписчик'), ('subscriber_removed', 'Удален подписчик'), ('reading_time', 'Время чтения'), ('manual_adjustment', 'Ручная корректировка'), ('recalculation', 'Пересчет статистики')], max_length=30)),
                ('metric_name', models.CharField(help_text='Название изменившейся метрики', max_length=50)),
                ('old_value', models.BigIntegerField(help_text='Старое значение')),
                ('new_value', models.BigIntegerField(help_text='Новое значение')),
                ('change_delta', models.IntegerField(help_text='Изменение (+/-)')),
                ('related_object_type', models.CharField(blank=True, help_text='Тип связанного объекта', max_length=50)),
                ('related_object_id', models.PositiveIntegerField(blank=True, help_text='ID связанного объекта', null=True)),
                ('reader_rating_before', models.IntegerField(default=0)),
                ('reader_rating_after', models.IntegerField(default=0)),
                ('author_rating_before', models.IntegerField(default=0)),
                ('author_rating_after', models.IntegerField(default=0)),
                ('total_rating_before', models.IntegerField(default=0)),
                ('total_rating_after', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(default='system', help_text='Кто инициировал изменение', max_length=50)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='metric_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'История метрик',
                'verbose_name_plural': 'История метрик',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', '-created_at'], name='users_userm_user_id_a5c085_idx'), models.Index(fields=['action_type', '-created_at'], name='users_userm_action__062c82_idx'), models.Index(fields=['metric_name', '-created_at'], name='users_userm_metric__e350ad_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_rating', models.IntegerField(default=0, help_text='Общий рейтинг пользователя')),
                ('reader_rating', models.IntegerField(default=0, help_text='Читательский рейтинг')),
                ('author_rating', models.IntegerField(default=0, help_text='Авторский рейтинг')),
                ('friends_count', models.IntegerField(default=0, help_text='Количество друзей')),
                ('subscribers_count', models.IntegerField(default=0, help_text='Количество подписчиков')),
                ('subscriptions_count', models.IntegerField(default=0, help_text='Количество подписок')),
                ('books_count', models.IntegerField(default=0, help_text='Количество книг')),
                ('published_books_count', models.IntegerField(default=0, help_text='Количество опубликованных книг')),
                ('finished_books_count', models.IntegerField(default=0, help_text='Количество завершенных книг')),
                ('total_chapters_count', models.IntegerField(default=0, help_text='Общее количество глав')),
                ('total_words_count', models.BigIntegerField(default=0, help_text='Общее количество слов в произведениях')),
                ('books_likes_count', models.IntegerField(default=0, help_text='Общее количество лайков на книгах')),
                ('books_reviews_count', models.IntegerField(default=0, help_text='Общее количество отзывов на книгах')),
                ('books_comments_count', models.IntegerField(default=0, help_text='Общее количество комментариев на книгах')),
                ('comments_left_count', models.IntegerField(default=0, help_text='Количество оставленных комментариев')),
                ('reviews_left_count', models.IntegerField(default=0, help_text='Количество оставленных отзывов')),
                ('likes_left_count', models.IntegerField(default=0, help_text='Количество поставленных лайков')),
                ('reading_time_minutes', models.BigIntegerField(default=0, help_text='Время чтения в минутах')),
                ('books_read_count', models.IntegerField(default=0, help_text='Количество прочитанных книг')),
                ('profile_views_count', models.IntegerField(default=0, help_text='Количество просмотров профиля')),
                ('messages_sent_count', models.IntegerField(default=0, help_text='Количество отправленных сообщений')),
                ('days_active_count', models.IntegerField(default=0, help_text='Количество дней активности')),
                ('registration_bonus_given', models.BooleanField(default=False, help_text='Приветственный бонус выдан')),
                ('daily_comments_count', models.IntegerField(default=0, help_text='Комментарии за сегодня (сбрасывается)')),
                ('last_comment_date', models.DateField(blank=True, help_text='Дата последнего комментария', null=True)),
                ('book_purchases_count', models.IntegerField(default=0, help_text='Покупки книг')),
                ('awards_given_count', models.IntegerField(default=0, help_text='Награды произведениям')),
                ('awards_received_count', models.IntegerField(default=0, help_text='Полученные награды')),
                ('blog_posts_count', models.IntegerField(default=0, help_text='Посты в блоге')),
                ('comment_likes_received', models.IntegerField(default=0, help_text='Лайки полученные на комментарии')),
                ('comment_dislikes_received', models.IntegerField(default=0, help_text='Дизлайки полученные на комментарии')),
                ('last_updated', models.DateTimeField(auto_now=True, help_text='Время последнего обновления статистики')),
                ('recalculation_scheduled', models.BooleanField(default=False, help_text='Запланирован пересчет статистики')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='stats', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Статистика пользователя',
                'verbose_name_plural': 'Статистика пользователей',
                'indexes': [models.Index(fields=['total_rating'], name='users_users_total_r_f694a6_idx'), models.Index(fields=['reader_rating'], name='users_users_reader__8a921e_idx'), models.Index(fields=['author_rating'], name='users_users_author__83ec7b_idx'), models.Index(fields=['last_updated'], name='users_users_last_up_63e67c_idx')],
            },
        ),
    ]
