# Управление паролями пользователей

## Описание

Скрипт `change_user_password.py` позволяет администратору изменять пароли любых пользователей в системе. Это полезно для сброса забытых паролей или административного управления аккаунтами.

## Расположение
```
backend/change_user_password.py
```

## Использование

### Основной синтаксис
```bash
python change_user_password.py <username_or_email> [new_password]
```

### Примеры

**1. С<PERSON><PERSON><PERSON> пароля с указанием нового пароля:**
```bash
python change_user_password.py litportalx 123456
python change_user_password.py <EMAIL> newpassword123
```

**2. Смена пароля с автогенерацией:**
```bash
python change_user_password.py litportalx
# Пароль будет сгенерирован автоматически
```

**3. Просмотр всех пользователей:**
```bash
python change_user_password.py --list
```

## Возможности скрипта

### ✅ Поиск пользователей
- Поиск по **логину** (username)
- Поиск по **email**
- Автоматическое определение типа поиска

### ✅ Генерация паролей
- Автоматическая генерация безопасных паролей
- Длина: 12 символов
- Включает: буквы, цифры, спецсимволы

### ✅ Безопасность
- Подтверждение перед изменением пароля
- Показ информации о найденном пользователе
- Отображение нового пароля для записи

### ✅ Список пользователей
- Просмотр всех пользователей в системе
- Показ ID, логина, email и отображаемого имени

## Пример работы

```bash
$ python change_user_password.py litportalx 123456

🔐 Смена пароля пользователя...
   Поиск: litportalx
   Новый пароль: 123456

👤 Найден пользователь:
   ID: 1
   Логин: litportalx
   Email: <EMAIL>
   Отображаемое имя: ЛитПортал
   Найден по: username

🔑 Установлен указанный пароль: 123456
Продолжить смену пароля? (yes/no): yes

✅ Пароль успешно изменен!
   Пользователь: litportalx
   Email для входа: <EMAIL>
   Новый пароль: 123456

🔓 Пользователь может войти используя:
   Email: <EMAIL>
   Пароль: 123456
```

## Безопасность

⚠️ **Важные моменты:**

1. **Доступ к скрипту**: Должен быть доступен только администраторам
2. **Логирование**: Все действия видны в консоли
3. **Подтверждение**: Скрипт запрашивает подтверждение перед изменением
4. **Новые пароли**: Показываются в консоли - убедитесь что их записали

## Интеграция с системой аутентификации

После смены пароля пользователь может войти в систему используя:
- **Email** (рекомендуется)
- **Новый пароль**

Система поддерживает вход только по email + пароль (логин + пароль отключен).

## Связанные файлы

- `backend/create_admin_user.py` - создание пользователей с запрещенными логинами
- `backend/fix_user_password.py` - старый скрипт (можно удалить)
- `frontend/src/pages/Login.jsx` - форма входа (только email)

## Устранение проблем

**Пользователь не найден:**
- Проверьте правильность написания логина/email
- Используйте `--list` для просмотра всех пользователей

**Ошибка валидации пароля:**
- Пароль должен соответствовать требованиям Django
- Минимальная длина обычно 8 символов

**Ошибка доступа к базе данных:**
- Убедитесь что Django настроен правильно
- Проверьте что база данных доступна 