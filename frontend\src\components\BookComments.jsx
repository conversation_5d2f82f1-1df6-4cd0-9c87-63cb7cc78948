import React, { useState, useEffect, useRef } from 'react';
import { Button, Input, Avatar, message, Spin, Modal, Divider, Dropdown } from 'antd';
import { UserOutlined, SendOutlined, HeartOutlined, MessageOutlined, LikeOutlined, DislikeOutlined, CloseOutlined, EditOutlined, DeleteOutlined, SmileOutlined } from '@ant-design/icons';
import RussianEmojiPicker from './RussianEmojiPicker';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { formatDateWithTimezone } from '../utils/formatDate';
import { useUserSettings } from '../context/UserSettingsContext';
import { csrfFetch } from '../utils/csrf';
import { useTheme } from '../theme/ThemeContext';
import { getCachedUserAvatar } from '../utils/avatarCache';
import './BookComments.css';

const { TextArea } = Input;

// Выносим CommentItem за пределы основного компонента для предотвращения перерендеров
const CommentItem = ({ 
  comment, 
  isReply = false, 
  onReplySubmit, 
  onReplySubmitForm, 
  onLikeClick, 
  replyTo, 
  replyText, 
  setReplyText, 
  replyPrefix, 
  submitting, 
  closeReplyForm, 
  user, 
  theme, 
  timezone, 
  commentLikes, 
  expandedReplies, 
  toggleRepliesVisibility, 
  parentCommentId, 
  editingComment, 
  editText, 
  setEditText, 
  editSubmitting, 
  onEditComment = () => {}, 
  onCancelEdit = () => {}, 
  onSaveEdit = () => {}, 
  onDeleteComment = () => {},
  showReplyEmojiPicker = false,
  onReplyEmojiClick = () => {},
  onToggleReplyEmojiPicker = () => {},
  replyEmojiPickerRef = null,
  isSingleEmoji = () => false,
  showEditEmojiPicker = false,
  onEditEmojiClick = () => {},
  onToggleEditEmojiPicker = () => {},
  editEmojiPickerRef = null,
  revealedDeletedComments = new Set(),
  onRevealDeletedComment = () => {},
  findCommentById = () => null
}) => {
  const isDeletedUser = comment.user.display_name === 'Аккаунт удален';
  const isRevealed = revealedDeletedComments.has(comment.id);

  return (
  <div className={`flex gap-3 ${isReply ? 'ml-12 mt-3' : 'mb-4'}`}>
    <Avatar 
      src={getCachedUserAvatar(comment.user, 'mini') || '/ava_presets/ava_U_s.webp'} 
      icon={<UserOutlined />}
      size={isReply ? 32 : 40}
    />
    <div className="flex-1">
      <div className="flex items-center gap-2 mb-1">
        {comment.user.display_name === 'Аккаунт удален' ? (
          <span className={`font-medium text-sm ${
            theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
          }`}>
            Аккаунт удален
          </span>
        ) : (
          <Link
            to={`/lpu/${comment.user.username}`}
            className={`font-medium text-sm hover:underline ${
              theme === 'dark'
                ? 'text-blue-400 hover:text-blue-300'
                : 'text-blue-600 hover:text-blue-500'
            }`}
          >
            {comment.user.display_name || comment.user.username}
          </Link>
        )}
        <span className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
          {formatDateWithTimezone(comment.created_at, timezone)}
        </span>
        {comment.is_edited && (
          <span className={`text-xs ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>(изменено)</span>
        )}
      </div>
      
      {/* Отображение комментария или формы редактирования */}
      {editingComment === comment.id ? (
        <div className="mb-2 relative">
          {/* Счетчик символов для редактирования - появляется поверх поля ввода */}
          {editText.trim() && (
            <div className={`absolute top-[-20px] right-0 text-xs z-10 px-2 py-1 rounded transition-opacity duration-200 ${
              theme === 'dark' 
                ? 'text-gray-300 bg-gray-700/90' 
                : 'text-gray-600 bg-white/90'
            } backdrop-blur-sm shadow-sm`}>
              {editText.length}/2000
            </div>
          )}
          <div className="relative">
            <TextArea
              value={editText}
              onChange={(e) => setEditText(e.target.value)}
              autoSize={{ minRows: 2, maxRows: 8 }}
              maxLength={2000}
              className="mb-2"
              style={{
                backgroundColor: theme === 'dark' ? '#374151' : '#fff',
                borderColor: theme === 'dark' ? '#4b5563' : '#d9d9d9',
                color: theme === 'dark' ? '#e5e7eb' : '#000',
                resize: 'none',
                paddingRight: '40px' // Место для кнопки эмодзи
              }}
            />
            {/* Кнопка эмодзи для редактирования */}
            <Button
              type="text"
              icon={<SmileOutlined />}
              size="small"
              onClick={onToggleEditEmojiPicker}
              className="absolute right-2 top-1 z-10"
              style={{
                color: theme === 'dark' ? '#9ca3af' : '#6b7280'
              }}
            />
          </div>
          
          {/* Эмодзи пикер для редактирования */}
          {showEditEmojiPicker && (
            <div ref={editEmojiPickerRef} className="absolute bottom-full right-0 z-50 mb-2">
              <RussianEmojiPicker
                onEmojiClick={onEditEmojiClick}
                theme={theme === 'dark' ? 'dark' : 'light'}
                height={450}
                width={400}
              />
            </div>
          )}
          <div className="flex gap-2">
            <Button 
              type="primary"
              size="small"
              loading={editSubmitting}
              onClick={() => onSaveEdit && onSaveEdit(comment.id)}
              style={{
                backgroundColor: theme === 'dark' ? '#1d4ed8' : '#1976d2',
                borderColor: theme === 'dark' ? '#1d4ed8' : '#1976d2'
              }}
            >
              Сохранить
            </Button>
            <Button 
              size="small"
              onClick={() => onCancelEdit && onCancelEdit()}
              style={{
                backgroundColor: theme === 'dark' ? 'transparent' : undefined,
                borderColor: theme === 'dark' ? '#4b5563' : undefined,
                color: theme === 'dark' ? '#e5e7eb' : undefined
              }}
            >
              Отмена
            </Button>
          </div>
        </div>
      ) : (
        <div className={`mb-2 whitespace-pre-wrap ${theme === 'dark' ? 'text-gray-200' : 'text-gray-800'} ${
          !comment.is_deleted && isSingleEmoji(comment.text) ? 'text-4xl leading-normal' : 'text-sm'
        }`}
             id={`comment-${comment.id}`}>
          {(() => {
            // Если пользователь удален и комментарий не раскрыт, показываем спойлер
            if (isDeletedUser && !isRevealed) {
              return (
                <span
                  className={`italic text-sm cursor-pointer ${theme === 'dark' ? 'text-gray-500 hover:text-gray-400' : 'text-gray-400 hover:text-gray-500'}`}
                  onClick={() => onRevealDeletedComment(comment.id)}
                >
                  Сообщение от удаленного пользователя скрыто <span className={`underline ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}>Показать</span>
                </span>
              );
            }

            // Если это ответ на ответ и текст начинается с @Имя,
            // выделяем @Имя как ссылку
            // Обновленное регулярное выражение для поддержки имен с пробелами
            const match = comment.text.match(/^@([^,:\n]+)[,:]?\s?/);
            if (isReply && match) {
              let mention = match[0];
              const rest = comment.text.slice(mention.length);

              // Проверяем, нужно ли заменить упоминание на "Аккаунт удален"
              // Ищем целевой комментарий по reply_to_id
              const targetId = comment.reply_to_id || comment.reply_to;
              if (targetId) {
                const targetComment = findCommentById(targetId);
                if (targetComment && targetComment.user.display_name === 'Аккаунт удален') {
                  // Заменяем упоминание на "Аккаунт удален"
                  const mentionText = match[1]; // Извлекаем имя из упоминания
                  const separator = match[0].slice(mentionText.length + 1); // Извлекаем разделитель (, или :)
                  mention = `@Аккаунт удален${separator}`;
                }
              }

              // Проверяем, является ли остальная часть (без упоминания) одним эмодзи
              const isRestSingleEmoji = isSingleEmoji(rest);

              return <>
                <span
                  className={`reply-mention ${isRestSingleEmoji ? 'text-2xl' : ''}`}
                  onClick={() => {
                    // Используем reply_to_id для точного определения целевого комментария
                    const targetId = comment.reply_to_id || comment.reply_to;
                                                                if (targetId) {
                        const target = document.getElementById(`comment-${targetId}`);

                        if (target) {
                          // Добавляем небольшую вибрацию для обратной связи
                          if (navigator.vibrate) {
                            navigator.vibrate(50);
                          }

                          target.classList.add('highlight-reply');
                          target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'nearest'
                          });
                          setTimeout(() => target.classList.remove('highlight-reply'), 2000);
                        }
                      }
                  }}
                >{mention}</span>
                <span className={isRestSingleEmoji ? 'text-4xl' : ''}>{rest}</span>
              </>;
            }
            return comment.is_deleted ? (
              <span className={`italic ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                {comment.deletion_message}
              </span>
            ) : (
              comment.text
            );
          })()}
        </div>
      )}
      
      <div className={`flex items-center gap-2 text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
        {/* Кнопки лайк/дизлайк - для удаленных комментариев показываем неактивными без количества */}
        {comment.is_deleted ? (
          <div className="flex items-center gap-1">
            <Button 
              type="text" 
              size="small"
              icon={<LikeOutlined />}
              disabled
              className={`flex items-center gap-1 px-2 cursor-not-allowed ${
                theme === 'dark' ? 'text-gray-600' : 'text-gray-400'
              }`}
            />
            
            <Button 
              type="text" 
              size="small"
              icon={<DislikeOutlined />}
              disabled
              className={`px-2 cursor-not-allowed ${
                theme === 'dark' ? 'text-gray-600' : 'text-gray-400'
              }`}
            />
          </div>
        ) : (
          <div className="flex items-center gap-1">
            <Button 
              type="text" 
              size="small"
              icon={<LikeOutlined />}
              onClick={() => onLikeClick(comment.id, true)}
              className={`comment-like-button flex items-center gap-1 px-2 ${
                commentLikes[comment.id]?.userReaction === 'like' ? 'liked' : ''
              }`}
              style={{
                color: commentLikes[comment.id]?.userReaction === 'like'
                  ? (theme === 'dark' ? '#4ade80' : '#16a34a')
                  : (theme === 'dark' ? '#9ca3af' : '#6b7280')
              }}
            >
              {commentLikes[comment.id]?.likesCount || 0}
            </Button>
            
            <Button 
              type="text" 
              size="small"
              icon={<DislikeOutlined />}
              onClick={() => onLikeClick(comment.id, false)}
              className={`comment-dislike-button px-2 ${
                commentLikes[comment.id]?.userReaction === 'dislike' ? 'disliked' : ''
              }`}
              style={{
                color: commentLikes[comment.id]?.userReaction === 'dislike'
                  ? (theme === 'dark' ? '#f87171' : '#dc2626')
                  : (theme === 'dark' ? '#9ca3af' : '#6b7280')
              }}
            />
          </div>
        )}

        {/* Кнопка "Ответить" - скрываем для удаленных комментариев */}
        {!comment.is_deleted && (
          <Button 
            type="text" 
            size="small"
            icon={<MessageOutlined />}
            onClick={() => {
              if (isReply) {
                // Отвечаем на ответ - передаем ID родительского комментария, имя автора и id ответа
                const authorName = comment.user.display_name;
                onReplySubmit(parentCommentId || comment.id, true, authorName, comment.id);
              } else {
                // Отвечаем на основной комментарий
                onReplySubmit(comment.id);
              }
            }}
            className="comment-reply-button"
            style={{
              color: theme === 'dark' ? '#9ca3af' : '#6b7280'
            }}
          >
            Ответить
          </Button>
        )}

        {/* Кнопки редактирования и удаления для владельца комментария */}
        {user && user.id === comment.user.id && !comment.is_deleted && (
          <>
            <Button 
              type="text" 
              size="small"
              icon={<EditOutlined />}
              onClick={() => onEditComment && onEditComment(comment)}
              className="comment-edit-button"
              title="Редактировать"
              style={{
                color: theme === 'dark' ? '#9ca3af' : '#6b7280'
              }}
            />
            <Button 
              type="text" 
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => onDeleteComment && onDeleteComment(comment.id)}
              className="comment-delete-button"
              title="Удалить"
              style={{
                color: theme === 'dark' ? '#9ca3af' : '#6b7280'
              }}
            />
          </>
        )}
      </div>

      {/* Кнопка для показа ответов */}
      {!isReply && comment.replies_count > 0 && (
        <div className="mt-2">
          <Button 
            type="text" 
            size="small"
            onClick={() => toggleRepliesVisibility(comment.id)}
            className={`p-0 h-auto font-medium ${theme === 'dark' ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-500'}`}
          >
            <div className="flex items-center gap-2">
              <span className={`text-sm transition-transform duration-200 ${expandedReplies.has(comment.id) ? 'rotate-90' : ''}`}>
                ▶
              </span>
              <span>
                {comment.replies_count} {comment.replies_count === 1 ? 'ответ' : 
                  comment.replies_count < 5 ? 'ответа' : 'ответов'}
              </span>
            </div>
          </Button>
        </div>
      )}
      
      {/* Ответы - показываются только при разворачивании */}
      {!isReply && expandedReplies.has(comment.id) && comment.replies && comment.replies.length > 0 && (
        <div className="mt-3">
          {[...comment.replies].sort((a, b) => a.id - b.id).map(reply => {
            return (
              <CommentItem
                key={reply.id}
                comment={reply}
                isReply={true}
                onReplySubmit={onReplySubmit}
                onReplySubmitForm={onReplySubmitForm}
                onLikeClick={onLikeClick}
                replyTo={replyTo}
                replyText={replyText}
                setReplyText={setReplyText}
                replyPrefix={replyPrefix}
                submitting={submitting}
                closeReplyForm={closeReplyForm}
                user={user}
                theme={theme}
                timezone={timezone}
                commentLikes={commentLikes}
                expandedReplies={expandedReplies}
                toggleRepliesVisibility={toggleRepliesVisibility}
                parentCommentId={comment.id}
                editingComment={editingComment}
                editText={editText}
                setEditText={setEditText}
                editSubmitting={editSubmitting}
                onEditComment={onEditComment}
                onCancelEdit={onCancelEdit}
                onSaveEdit={onSaveEdit}
                onDeleteComment={onDeleteComment}
                revealedDeletedComments={revealedDeletedComments}
                onRevealDeletedComment={onRevealDeletedComment}
                findCommentById={findCommentById}
              />
            );
          })}
        </div>
      )}
      
      {/* Форма ответа - перемещена после ответов */}
      {replyTo === comment.id && (
        <div className="mt-3 flex gap-2 pt-4 relative comment-reply-form" style={{maxWidth: '95%'}}>
          <Avatar 
            src={getCachedUserAvatar(user, 'mini') || '/ava_presets/ava_U_s.webp'} 
            icon={<UserOutlined />}
            size={32}
          />
          <div className="flex-1">
            <div className="relative">
              <TextArea
                placeholder={replyPrefix ? `${replyPrefix}Написать ответ...` : "Написать ответ..."}
                autoSize={{ minRows: 1, maxRows: 3 }}
                value={replyPrefix + replyText}
                onChange={(e) => {
                  const newValue = e.target.value;
                  if (newValue.startsWith(replyPrefix)) {
                    setReplyText(newValue.substring(replyPrefix.length));
                  } else if (!replyPrefix) {
                    setReplyText(newValue);
                  }
                }}
                className="mb-2"
                maxLength={1000}
                style={{
                  backgroundColor: theme === 'dark' ? '#374151' : '#fff',
                  borderColor: theme === 'dark' ? '#4b5563' : '#d9d9d9',
                  color: theme === 'dark' ? '#e5e7eb' : '#000',
                  resize: 'none',
                  width: '100%',
                  paddingRight: '40px' // Место для кнопки эмодзи
                }}
                onKeyDown={(e) => {
                  if (replyPrefix && e.target.selectionStart < replyPrefix.length) {
                    if (e.key === 'Backspace' || e.key === 'Delete' || e.key === 'ArrowLeft') {
                      e.preventDefault();
                      e.target.setSelectionRange(replyPrefix.length, replyPrefix.length);
                    }
                  }
                }}
                onSelect={(e) => {
                  if (replyPrefix && e.target.selectionStart < replyPrefix.length) {
                    setTimeout(() => {
                      e.target.setSelectionRange(replyPrefix.length, replyPrefix.length);
                    }, 0);
                  }
                }}
              />
              {/* Кнопка эмодзи для ответа */}
              <Button
                type="text"
                icon={<SmileOutlined />}
                size="small"
                onClick={onToggleReplyEmojiPicker}
                className="absolute right-2 top-1 z-10"
                style={{
                  color: theme === 'dark' ? '#9ca3af' : '#6b7280'
                }}
              />
              {/* Кнопка закрытия - привязана к TextArea */}
              <Button
                type="text"
                size="small"
                icon={<CloseOutlined />}
                onClick={closeReplyForm}
                className={`comment-reply-close ${theme === 'dark' ? 'dark' : ''}`}
              />
              
              {/* Эмодзи пикер для ответа */}
              {showReplyEmojiPicker && (
                <div ref={replyEmojiPickerRef} className="absolute bottom-full right-0 z-50 mb-2">
                  <RussianEmojiPicker
                    onEmojiClick={onReplyEmojiClick}
                    theme={theme === 'dark' ? 'dark' : 'light'}
                    height={450}
                    width={400}
                  />
                </div>
              )}
            </div>
            
            {/* Счетчик символов для ответа - теперь под полем справа */}
            {replyText.trim() && (
              <div className={`comment-reply-counter ${theme === 'dark' ? 'dark' : ''}`}>
                {(replyPrefix + replyText).length}/1000
              </div>
            )}
            {/* Кнопки ответа появляются только при наличии текста */}
            {replyText.trim() && (
              <div className="flex gap-2 animate-fade-in">
                <Button 
                  type="primary" 
                  size="small"
                  loading={submitting}
                  onClick={() => onReplySubmitForm(comment.id)}
                  style={{
                    backgroundColor: theme === 'dark' ? '#1d4ed8' : '#1976d2',
                    borderColor: theme === 'dark' ? '#1d4ed8' : '#1976d2'
                  }}
                >
                  Ответить
                </Button>
                <Button 
                  size="small"
                  onClick={closeReplyForm}
                  style={{
                    backgroundColor: theme === 'dark' ? 'transparent' : undefined,
                    borderColor: theme === 'dark' ? '#4b5563' : undefined,
                    color: theme === 'dark' ? '#e5e7eb' : undefined
                  }}
                >
                  Отмена
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  </div>
  );
};

const BookComments = ({ bookId, initialCommentsCount = 0, onCommentCountChange }) => {
  const { user } = useAuth();
  const { timezone } = useUserSettings();
  const { theme } = useTheme();
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [newComment, setNewComment] = useState('');
  const [replyTo, setReplyTo] = useState(null);
  const [replyText, setReplyText] = useState('');
  const [replyPrefix, setReplyPrefix] = useState(''); // Префикс для ответа на ответ
  const [submitting, setSubmitting] = useState(false);
  const [expandedReplies, setExpandedReplies] = useState(new Set());
  const [commentLikes, setCommentLikes] = useState({}); // Состояние лайков для комментариев
  const [revealedDeletedComments, setRevealedDeletedComments] = useState(new Set()); // Состояние для раскрытых комментариев удаленных пользователей
  // Новые состояния для редактирования и удаления
  const [editingComment, setEditingComment] = useState(null);
  const [editText, setEditText] = useState('');
  const [editSubmitting, setEditSubmitting] = useState(false);
  const [currentCommentsCount, setCurrentCommentsCount] = useState(initialCommentsCount);
  const [replyToId, setReplyToId] = useState(null);
  // Состояние для фильтра сортировки комментариев
  const [commentSortFilter, setCommentSortFilter] = useState('newest'); // 'newest', 'popular', 'oldest'
  // Состояния для эмодзи пикеров
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showReplyEmojiPicker, setShowReplyEmojiPicker] = useState(false);
  const [showEditEmojiPicker, setShowEditEmojiPicker] = useState(false);
  // Рефы для эмодзи пикеров
  const emojiPickerRef = useRef(null);
  const replyEmojiPickerRef = useRef(null);
  const editEmojiPickerRef = useRef(null);

  // Функция для раскрытия комментария удаленного пользователя
  const revealDeletedComment = (commentId) => {
    setRevealedDeletedComments(prev => new Set([...prev, commentId]));
  };

  // Функция для поиска комментария по ID
  const findCommentById = (commentId) => {
    for (const comment of comments) {
      if (comment.id === commentId) return comment;
      if (comment.replies) {
        for (const reply of comment.replies) {
          if (reply.id === commentId) return reply;
        }
      }
    }
    return null;
  };

  // Функция для подсчета общего количества комментариев
  const getTotalCommentsCount = () => {
    // Используем текущее значение счетчика (из API с обновлениями)
    return currentCommentsCount;
  };

  useEffect(() => {
    if (bookId) {
      fetchComments();
    }
  }, [bookId]);

  // Обновляем локальный счетчик при изменении переданного значения
  useEffect(() => {
    setCurrentCommentsCount(initialCommentsCount);
  }, [initialCommentsCount]);

  // Обработчик кликов вне эмодзи пикеров
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target)) {
        setShowEmojiPicker(false);
      }
      if (replyEmojiPickerRef.current && !replyEmojiPickerRef.current.contains(event.target)) {
        setShowReplyEmojiPicker(false);
      }
      if (editEmojiPickerRef.current && !editEmojiPickerRef.current.contains(event.target)) {
        setShowEditEmojiPicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);



  const fetchComments = async () => {
    try {
      const response = await fetch(`/api/books/${bookId}/comments/`);
      if (response.ok) {
        const data = await response.json();
        setComments(data);
        
        // Инициализируем состояние лайков для комментариев из данных API
        const likesState = {};
        data.forEach(comment => {
          likesState[comment.id] = {
            likesCount: comment.likes_count || 0,
            userReaction: comment.user_reaction
          };
          // Также инициализируем лайки для ответов
          if (comment.replies) {
            comment.replies.forEach(reply => {
              likesState[reply.id] = {
                likesCount: reply.likes_count || 0,
                userReaction: reply.user_reaction
              };
            });
          }
        });
        setCommentLikes(likesState);
      } else {
        message.error('Ошибка при загрузке комментариев');
      }
    } catch (error) {
      message.error('Ошибка при загрузке комментариев');
    } finally {
      setLoading(false);
    }
  };

  const submitComment = async () => {
    if (!user) {
      message.warning('Для добавления комментария необходимо войти в систему');
      return;
    }

    if (!newComment.trim()) {
      message.warning('Комментарий не может быть пустым');
      return;
    }

    setSubmitting(true);
    try {
      const response = await csrfFetch(`/api/books/${bookId}/comments/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          text: newComment.trim()
        }),
      });

      if (response.ok) {
        const newCommentData = await response.json();
        setComments(prev => [newCommentData, ...prev]);
        setNewComment('');
        message.success('Комментарий добавлен!');
        
        // Инициализируем состояние лайков для нового комментария
        setCommentLikes(prev => ({
          ...prev,
          [newCommentData.id]: {
            likesCount: newCommentData.likes_count || 0,
            userReaction: newCommentData.user_reaction
          }
        }));
        
        // Уведомляем родительский компонент об изменении количества комментариев
        if (onCommentCountChange) {
          const newCount = currentCommentsCount + 1;
          setCurrentCommentsCount(newCount);
          onCommentCountChange(newCount);
        }
      } else {
        throw new Error('Failed to submit comment');
      }
    } catch (error) {
      message.error('Ошибка при добавлении комментария');
    } finally {
      setSubmitting(false);
    }
  };

  const handleReplySubmit = (commentId, isReplyToReply = false, authorName = '', replyId = null) => {
    if (replyTo === commentId) {
      closeReplyForm();
    } else {
      setReplyTo(commentId);
      if (isReplyToReply && authorName && replyId) {
        setReplyPrefix(`@${authorName}, `);
        setReplyToId(replyId);
        setReplyText('');
      } else {
        setReplyPrefix('');
        setReplyToId(null);
        setReplyText('');
      }
    }
  };

  const submitReply = async (parentId) => {
    if (!user) {
      message.warning('Для добавления ответа необходимо войти в систему');
      return;
    }
    const fullReplyText = replyPrefix + replyText.trim();
    if (!fullReplyText.trim() || !replyText.trim()) {
      message.warning('Ответ не может быть пустым');
      return;
    }
    setSubmitting(true);
    try {
      const requestBody = {
        text: fullReplyText,
        parent_id: parentId,
        reply_to: replyToId,
      };
      
      const response = await csrfFetch(`/api/books/${bookId}/comments/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(requestBody),
      });
      if (response.ok) {
        const newReplyData = await response.json();
        setComments(prev => prev.map(comment => {
          if (comment.id === parentId) {
            return {
              ...comment,
              replies: [...comment.replies, newReplyData],
              replies_count: comment.replies_count + 1
            };
          }
          return comment;
        }));
        setCommentLikes(prev => ({
          ...prev,
          [newReplyData.id]: {
            likesCount: newReplyData.likes_count || 0,
            userReaction: newReplyData.user_reaction
          }
        }));
        setReplyTo(null);
        setReplyText('');
        setReplyPrefix('');
        setReplyToId(null);
        message.success('Ответ добавлен!');
        
        // Уведомляем родительский компонент об изменении количества комментариев
        if (onCommentCountChange) {
          const newCount = currentCommentsCount + 1;
          setCurrentCommentsCount(newCount);
          onCommentCountChange(newCount);
        }
      } else {
        throw new Error('Failed to submit reply');
      }
    } catch (error) {
      message.error('Ошибка при добавлении ответа');
    } finally {
      setSubmitting(false);
    }
  };

  const loadMoreReplies = async (commentId) => {
    try {
      const response = await fetch(`/api/books/${bookId}/comments/${commentId}/replies/`);
      if (response.ok) {
        const replies = await response.json();
        setComments(prev => prev.map(comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              replies: replies
            };
          }
          return comment;
        }));
        
        // Инициализируем состояние лайков для загруженных ответов
        const newLikesState = {};
        replies.forEach(reply => {
          newLikesState[reply.id] = {
            likesCount: reply.likes_count || 0,
            userReaction: reply.user_reaction
          };
        });
        setCommentLikes(prev => ({
          ...prev,
          ...newLikesState
        }));
        
        setExpandedReplies(prev => new Set([...prev, commentId]));
      }
    } catch (error) {
      message.error('Ошибка при загрузке ответов');
    }
  };

  const toggleRepliesVisibility = (commentId) => {
    setExpandedReplies(prev => {
      const newSet = new Set(prev);
      if (newSet.has(commentId)) {
        newSet.delete(commentId);
      } else {
        newSet.add(commentId);
        // Если ответы еще не загружены, загружаем их
        const comment = comments.find(c => c.id === commentId);
        if (comment && comment.replies_count > comment.replies.length) {
          loadMoreReplies(commentId);
        }
      }
      return newSet;
    });
  };

  // Функция для закрытия формы ответа
  const closeReplyForm = () => {
    setReplyTo(null);
    setReplyText('');
    setReplyPrefix('');
  };

  // Функция для изменения фильтра сортировки
  const handleCommentSortChange = (sortType) => {
    setCommentSortFilter(sortType);
  };

  // Функция для получения отсортированных комментариев
  const getSortedComments = () => {
    const sortedComments = [...comments];
    
    switch (commentSortFilter) {
      case 'newest':
        // Сначала более новые (по умолчанию, как сейчас)
        return sortedComments.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      
      case 'popular':
        // Сначала популярные (больше лайков и ответов)
        return sortedComments.sort((a, b) => {
          const aPopularity = (a.likes_count || 0) + (a.replies_count || 0);
          const bPopularity = (b.likes_count || 0) + (b.replies_count || 0);
          return bPopularity - aPopularity;
        });
      
      case 'oldest':
        // Сначала более старые
        return sortedComments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
      
      default:
        return sortedComments;
    }
  };

  // Функции для работы с эмодзи
  const handleEmojiClick = (emojiData) => {
    setNewComment(prev => prev + emojiData.emoji);
    setShowEmojiPicker(false);
  };

  const handleReplyEmojiClick = (emojiData) => {
    setReplyText(prev => prev + emojiData.emoji);
    setShowReplyEmojiPicker(false);
  };

  const toggleReplyEmojiPicker = () => {
    setShowReplyEmojiPicker(!showReplyEmojiPicker);
  };

  const handleEditEmojiClick = (emojiData) => {
    setEditText(prev => prev + emojiData.emoji);
    setShowEditEmojiPicker(false);
  };

  const toggleEditEmojiPicker = () => {
    setShowEditEmojiPicker(!showEditEmojiPicker);
  };

  // Функция для проверки, является ли текст одним эмодзи
  const isSingleEmoji = (text) => {
    if (!text) return false;
    
    // Убираем пробелы
    const trimmed = text.trim();
    if (!trimmed) return false;
    
    try {
      // Современный подход с использованием Intl.Segmenter для правильного подсчета графем
      if (typeof Intl !== 'undefined' && Intl.Segmenter) {
        const segmenter = new Intl.Segmenter('en', { granularity: 'grapheme' });
        const segments = Array.from(segmenter.segment(trimmed));
        
        // Проверяем, что это ровно один графемный кластер
        if (segments.length === 1) {
          const segment = segments[0].segment;
          
          // Проверяем, содержит ли графема эмодзи
          const hasEmoji = /\p{Emoji}/u.test(segment);
          
          console.log('isSingleEmoji check (modern):', {
            text: text,
            trimmed: trimmed,
            segments: segments.length,
            segment: segment,
            hasEmoji: hasEmoji
          });
          
          return hasEmoji;
        }
      }
    } catch (e) {
      // Fallback если Intl.Segmenter не поддерживается
    }
    
    // Fallback: расширенное регулярное выражение
    const emojiRegex = /^(?:[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F018}-\u{1F270}]|[\u2764]|[\u2665]|[\u2661]|[\u2763]|[\u{1F90D}]|[\u{1F90E}]|[\u{1F90F}]|[\u{1F970}]|[\u{1F60D}]|[\u{1F929}]|[\u{1F618}])(?:\u{FE0F})?$/u;
    
    const result = emojiRegex.test(trimmed) && trimmed.length >= 1 && trimmed.length <= 7;
    
    // Временная отладка для fallback
    console.log('isSingleEmoji check (fallback):', {
      text: text,
      trimmed: trimmed,
      length: trimmed.length,
      charCodes: [...trimmed].map(c => c.codePointAt(0).toString(16)),
      regexTest: emojiRegex.test(trimmed),
      result: result
    });
    
    return result;
  };

  const handleCommentLike = async (commentId, isLike) => {
    if (!user) {
      message.warning('Для оценки комментария необходимо войти в систему');
      return;
    }

    const reaction = isLike ? 'like' : 'dislike';

    try {
      const response = await csrfFetch(`/api/books/${bookId}/comments/${commentId}/like/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          reaction: reaction
        }),
      });

      if (response.ok) {
        const data = await response.json();
        
        // Обновляем локальное состояние на основе ответа сервера
        setCommentLikes(prev => ({
          ...prev,
          [commentId]: {
            likesCount: data.likes_count,
            userReaction: data.user_reaction
          }
        }));
      } else {
        throw new Error('Failed to update comment reaction');
      }
    } catch (error) {
      message.error('Ошибка при обновлении реакции на комментарий');
    }
  };

  // Функция для начала редактирования комментария
  const handleEditComment = (comment) => {
    setEditingComment(comment.id);
    setEditText(comment.text);
  };

  // Функция для отмены редактирования
  const cancelEdit = () => {
    setEditingComment(null);
    setEditText('');
  };

  // Функция для сохранения отредактированного комментария
  const saveEdit = async (commentId) => {
    if (!editText.trim()) {
      message.warning('Комментарий не может быть пустым');
      return;
    }

    setEditSubmitting(true);
    try {
      const response = await csrfFetch(`/api/books/${bookId}/comments/${commentId}/edit/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          text: editText.trim()
        }),
      });

      if (response.ok) {
        const updatedComment = await response.json();
        
        // Обновляем комментарий в состоянии
        setComments(prev => prev.map(comment => {
          if (comment.id === commentId) {
            return { ...comment, ...updatedComment };
          }
          // Проверяем также ответы
          return {
            ...comment,
            replies: comment.replies.map(reply => 
              reply.id === commentId ? { ...reply, ...updatedComment } : reply
            )
          };
        }));
        
        setEditingComment(null);
        setEditText('');
        message.success('Комментарий обновлен!');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update comment');
      }
    } catch (error) {
      message.error(`Ошибка при обновлении комментария: ${error.message}`);
    } finally {
      setEditSubmitting(false);
    }
  };

  // Функция для удаления комментария
  const handleDeleteComment = (commentId) => {
    Modal.confirm({
      title: 'Удалить комментарий?',
      content: 'Это действие нельзя отменить.',
      okText: 'Удалить',
      okType: 'danger',
      cancelText: 'Отмена',
      onOk: () => deleteComment(commentId),
      className: theme === 'dark' ? 'dark-theme-modal' : '',
      style: theme === 'dark' ? {
        '--ant-color-bg-container': '#374151',
        '--ant-color-text': '#e5e7eb',
        '--ant-color-text-secondary': '#9ca3af',
        '--ant-color-border': '#4b5563',
      } : {},
      rootClassName: theme === 'dark' ? 'dark-modal-root' : '',
    });
  };

  const deleteComment = async (commentId) => {
    try {
      const response = await csrfFetch(`/api/books/${bookId}/comments/${commentId}/delete/`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        const updatedComment = await response.json();
        
        // Определяем, является ли это ответом
        const isReply = comments.some(comment => 
          comment.replies.some(reply => reply.id === commentId)
        );
        
        if (isReply) {
          // Обрабатываем удаление ответа
          setComments(prev => prev.map(comment => {
            if (comment.replies.some(reply => reply.id === commentId)) {
              const updatedReplies = comment.replies.map(reply => {
                if (reply.id === commentId) {
                  // Проверяем, есть ли ответы на этот ответ (через префикс имени)
                  const replyAuthorName = reply.user.display_name;
                  const hasResponses = comment.replies.some(r =>
                    r.id !== commentId &&
                    !r.deleted_by_user &&
                    !r.deleted_by_admin &&
                    r.text.startsWith(`@${replyAuthorName}`)
                  );
                  
                  if (hasResponses) {
                    // Если есть ответы на этот ответ, показываем как удаленный
                    return { ...reply, ...updatedComment };
                  } else {
                    // Если нет ответов, возвращаем null для удаления
                    return null;
                  }
                }
                return reply;
              }).filter(reply => reply !== null); // Удаляем null элементы
              
              return {
                ...comment,
                replies: updatedReplies,
                replies_count: Math.max(0, comment.replies_count - 1)
              };
            }
            return comment;
          }));
        } else {
          // Обрабатываем удаление основного комментария
          setComments(prev => {
            return prev.map(comment => {
              if (comment.id === commentId) {
                // Проверяем, есть ли ответы у этого комментария
                if (comment.replies_count > 0) {
                  // Если есть ответы, показываем как удаленный
                  return { ...comment, ...updatedComment };
                } else {
                  // Если нет ответов, помечаем для удаления
                  return null;
                }
              }
              return comment;
            }).filter(comment => comment !== null); // Удаляем null элементы
          });
        }
        
        message.success('Комментарий удален');
        
        // Уменьшаем счетчик комментариев только если комментарий полностью скрывается
        if (onCommentCountChange) {
          let shouldDecrement = false;
          
          if (isReply) {
            // Для ответа проверяем, был ли он скрыт
            const originalComment = comments.find(c => 
              c.replies.some(r => r.id === commentId)
            );
            if (originalComment) {
              const originalReply = originalComment.replies.find(r => r.id === commentId);
              if (originalReply) {
                const replyAuthorName = originalReply.user.display_name;
                const hasResponses = originalComment.replies.some(r =>
                  r.id !== commentId &&
                  !r.deleted_by_user &&
                  !r.deleted_by_admin &&
                  r.text.startsWith(`@${replyAuthorName}`)
                );
                shouldDecrement = !hasResponses; // Уменьшаем только если нет ответов
              }
            }
          } else {
            // Для основного комментария проверяем, есть ли ответы
            const originalComment = comments.find(c => c.id === commentId);
            shouldDecrement = originalComment && originalComment.replies_count === 0;
          }
          
          if (shouldDecrement) {
            const newCount = currentCommentsCount - 1;
            setCurrentCommentsCount(newCount);
            onCommentCountChange(newCount);
          }
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete comment');
      }
    } catch (error) {
      message.error(`Ошибка при удалении комментария: ${error.message}`);
    }
  };



  if (loading) {
    return (
      <div className="text-center py-8">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <>
      <style>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-4px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
          animation: fadeIn 0.2s ease-out;
        }
      `}</style>
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 mt-6">
        <div className="flex items-center gap-3 mb-4">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Комментарии ({getTotalCommentsCount()})
          </h3>
          {/* Фильтр сортировки - показываем только если есть больше 1 родительского комментария */}
          {comments.length > 1 && (
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'newest',
                    label: 'Сначала более новые',
                    onClick: () => handleCommentSortChange('newest'),
                  },
                  {
                    key: 'popular',
                    label: 'Сначала популярные',
                    onClick: () => handleCommentSortChange('popular'),
                  },
                  {
                    key: 'oldest',
                    label: 'Сначала более старые',
                    onClick: () => handleCommentSortChange('oldest'),
                  },
                ],
                selectedKeys: [commentSortFilter],
              }}
              trigger={['click']}
              placement="bottomLeft"
              overlayClassName={theme === 'dark' ? 'dark-dropdown' : ''}
            >
              <button className={`flex items-center gap-1 px-3 py-2 rounded-md transition-colors border ${
                theme === 'dark' 
                  ? 'text-gray-300 hover:text-white hover:bg-gray-700/50 border-gray-600 bg-gray-800/50' 
                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100/50 border-gray-300 bg-white'
              }`}>
                <svg width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path d="M3 6h18M7 12h10m-7 6h4"/>
                </svg>
                <span className="text-sm font-medium">
                  {commentSortFilter === 'newest' && 'Новые'}
                  {commentSortFilter === 'popular' && 'Популярные'}
                  {commentSortFilter === 'oldest' && 'Старые'}
                </span>
              </button>
            </Dropdown>
          )}
        </div>
        
        {/* Форма добавления комментария */}
        {user ? (
          <div className="flex gap-3 mb-6 pt-4">
            <Avatar 
              src={getCachedUserAvatar(user, 'mini') || '/ava_presets/ava_U_s.webp'} 
              icon={<UserOutlined />}
              size={40}
            />
            <div className="flex-1 relative">
              {/* Счетчик символов - появляется поверх поля ввода */}
              {newComment.trim() && (
                <div className="main-comment-counter">
                  {newComment.length}/2000
                </div>
              )}
              <div className="relative">
                <TextArea
                  placeholder="Написать комментарий..."
                  autoSize={{ minRows: 1, maxRows: 3 }}
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="mb-3"
                  maxLength={2000}
                  style={{
                    backgroundColor: theme === 'dark' ? '#374151' : '#fff',
                    borderColor: theme === 'dark' ? '#4b5563' : '#d9d9d9',
                    color: theme === 'dark' ? '#e5e7eb' : '#000',
                    resize: 'none',
                    paddingRight: '40px' // Место для кнопки эмодзи
                  }}
                />
                {/* Кнопка эмодзи */}
                <Button
                  type="text"
                  icon={<SmileOutlined />}
                  size="small"
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  className="absolute right-2 top-1 z-10"
                  style={{
                    color: theme === 'dark' ? '#9ca3af' : '#6b7280'
                  }}
                />
              </div>
              
              {/* Эмодзи пикер */}
              {showEmojiPicker && (
                <div ref={emojiPickerRef} className="absolute bottom-full right-0 z-50 mb-2">
                  <RussianEmojiPicker
                    onEmojiClick={handleEmojiClick}
                    theme={theme === 'dark' ? 'dark' : 'light'}
                    height={450}
                    width={400}
                  />
                </div>
              )}
              
              {/* Кнопки появляются только при наличии текста */}
              {newComment.trim() && (
                <div className="flex justify-end gap-2 animate-fade-in">
                  <Button 
                    onClick={() => setNewComment('')}
                    size="small"
                    style={{
                      backgroundColor: theme === 'dark' ? 'transparent' : undefined,
                      borderColor: theme === 'dark' ? '#4b5563' : undefined,
                      color: theme === 'dark' ? '#e5e7eb' : undefined
                    }}
                  >
                    Отмена
                  </Button>
                  <Button 
                    type="primary"
                    icon={<SendOutlined />}
                    loading={submitting}
                    onClick={submitComment}
                    size="small"
                    style={{
                      backgroundColor: theme === 'dark' ? '#1d4ed8' : '#1976d2',
                      borderColor: theme === 'dark' ? '#1d4ed8' : '#1976d2'
                    }}
                  >
                    Комментировать
                  </Button>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-4 text-gray-500 dark:text-gray-400 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg mb-6">
            Войдите в систему, чтобы оставить комментарий
          </div>
        )}
        
        {/* Список комментариев */}
        <div className="space-y-4">
          {comments.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              Пока нет комментариев. Будьте первым!
            </div>
          ) : (
            getSortedComments().map((comment, index) => (
              <div key={comment.id}>
                <CommentItem 
                  comment={comment}
                  isReply={false}
                  onReplySubmit={handleReplySubmit}
                  onReplySubmitForm={submitReply}
                  onLikeClick={handleCommentLike}
                  replyTo={replyTo}
                  replyText={replyText}
                  setReplyText={setReplyText}
                  replyPrefix={replyPrefix}
                  submitting={submitting}
                  closeReplyForm={closeReplyForm}
                  user={user}
                  theme={theme}
                  timezone={timezone}
                  commentLikes={commentLikes}
                  expandedReplies={expandedReplies}
                  toggleRepliesVisibility={toggleRepliesVisibility}
                  parentCommentId={comment.id}
                  editingComment={editingComment}
                  editText={editText}
                  setEditText={setEditText}
                  editSubmitting={editSubmitting}
                  onEditComment={handleEditComment}
                  onCancelEdit={cancelEdit}
                  onSaveEdit={saveEdit}
                  onDeleteComment={handleDeleteComment}
                  showReplyEmojiPicker={showReplyEmojiPicker}
                  onReplyEmojiClick={handleReplyEmojiClick}
                  onToggleReplyEmojiPicker={toggleReplyEmojiPicker}
                  replyEmojiPickerRef={replyEmojiPickerRef}
                  isSingleEmoji={isSingleEmoji}
                  showEditEmojiPicker={showEditEmojiPicker}
                  onEditEmojiClick={handleEditEmojiClick}
                  onToggleEditEmojiPicker={toggleEditEmojiPicker}
                  editEmojiPickerRef={editEmojiPickerRef}
                  revealedDeletedComments={revealedDeletedComments}
                  onRevealDeletedComment={revealDeletedComment}
                  findCommentById={findCommentById}
                />
                {index < getSortedComments().length - 1 && (
                  <Divider style={{ 
                    borderColor: theme === 'dark' ? '#4b5563' : '#e5e7eb',
                    margin: '16px 0' 
                  }} />
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </>
  );
};

export default BookComments; 