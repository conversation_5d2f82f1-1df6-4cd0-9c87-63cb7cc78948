import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Typography, Spin, message, Button, Modal, Tooltip, Dropdown } from 'antd';
import { ArrowLeftOutlined, CloseOutlined, EditOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';
import { getBookCoverUrl, getBookCoverMiniUrl } from '../utils/bookCover';
import { useUserSettings } from '../context/UserSettingsContext';
import { formatDateWithTimezone } from '../utils/formatDate';
import { formatPublishedDate, formatUpdatedDate } from '../utils/dateHelpers';
import ProfileSidebar from '../components/ProfileSidebar';
import { useTheme } from '../theme/ThemeContext';
import '../theme/custom-tooltip.css';
import { csrfFetch } from '../utils/csrf';
import { useLibraryActivity } from '../hooks/useLibraryActivity';
import BookComments from '../components/BookComments';

const { Title, Text } = Typography;

const BookView = () => {
  const { id } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [book, setBook] = useState(null);
  const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
  const { timezone } = useUserSettings();
  const { theme } = useTheme();
  const [coverModalOpen, setCoverModalOpen] = useState(false);
  const [ageConfirmVisible, setAgeConfirmVisible] = useState(false);
  const [ageConfirmed, setAgeConfirmed] = useState(() => window.sessionStorage.getItem('adultConfirmed') === 'true');
  const [likingInProgress, setLikingInProgress] = useState(false);
  const [ageRestricted, setAgeRestricted] = useState(false);
  const [libraryStatus, setLibraryStatus] = useState(null); // null, 'reading', 'want_to_read'
  const [libraryLoading, setLibraryLoading] = useState(false);

  // Функция для проверки возраста пользователя
  const checkUserAge = useCallback((birthDate) => {
    if (!birthDate) return true; // Если дата рождения не указана, разрешаем доступ

    const today = new Date();
    const birth = new Date(birthDate);
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    // Проверяем, исполнилось ли уже 18 лет
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return age - 1 >= 18;
    }

    return age >= 18;
  }, []);

  // Функция для проверки доступа к книге 18+
  const checkBookAccess = useCallback((book, user) => {
    if (!book || book.age_rating !== '18+') {
      return true; // Доступ разрешен для всех книг кроме 18+
    }

    if (!user) {
      return false; // Неавторизованные пользователи не могут просматривать 18+ контент
    }

    return checkUserAge(user.birth_date);
  }, [checkUserAge]);
  const [commentsCount, setCommentsCount] = useState(0);

  // Состояния для интерактивной области
  const [activeTab, setActiveTab] = useState('annotation');
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Обработчики для интерактивной области
  const handleExpand = () => {
    setIsExpanded(true);
  };
  
  const handleCollapse = () => {
    setIsExpanded(false);
  };

  // Функция для определения первой доступной вкладки
  const getFirstAvailableTab = () => {
    if (book?.description) return 'annotation';
    if (book?.author_notes) return 'author_notes';
    if (book?.additional_materials) return 'materials';
    if (book?.type !== 'story' && book?.chapters?.length > 0) return 'contents';
    return 'annotation';
  };

  // Обновляем активную вкладку при загрузке книги
  useEffect(() => {
    if (book) {
      setActiveTab(getFirstAvailableTab());
    }
  }, [book]);

  // Обновляем активность в библиотеке при просмотре книги
  useLibraryActivity(id);

  useEffect(() => {
    fetchBook();
    if (user) {
      checkLibraryStatus();
    }
    // eslint-disable-next-line
  }, [id, user]);

  const fetchBook = async () => {
    try {
      const response = await fetch(`/api/books/${id}/`);
      if (!response.ok) {
        if (response.status === 403) {
          message.error('У вас нет доступа к этому произведению');
          navigate('/');
          return;
        }
        throw new Error('Failed to fetch book');
      }
      const data = await response.json();

      // Проверяем доступ к книге 18+
      if (!checkBookAccess(data, user)) {
        setAgeRestricted(true);
        setLoading(false);
        return;
      }

      setBook(data);
      setCommentsCount(data.comments_count || 0);
    } catch (error) {
      message.error('Ошибка при загрузке произведения');
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const checkLibraryStatus = async () => {
    if (!user || !id) return;
    
    try {
      const response = await fetch(`${backendUrl}/api/check-library-status/${id}/`, {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setLibraryStatus(data.in_library ? data.status : null);
      } else if (response.status === 404) {
        // Книга не найдена или не опубликована
        setLibraryStatus(null);
      } else {
        console.error('Error checking library status:', response.status, response.statusText);
        setLibraryStatus(null);
      }
    } catch (error) {
      console.error('Error checking library status:', error);
      setLibraryStatus(null);
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleLikeToggle = async () => {
    if (likingInProgress) return;
    
    if (!user) {
      message.warning('Для оценки произведения необходимо войти в систему');
      return;
    }

    setLikingInProgress(true);
    try {
      const response = await csrfFetch(`/api/books/${book.id}/like/`, {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        
        // Обновляем состояние книги
        setBook(prevBook => ({
          ...prevBook,
          is_liked: data.status === 'liked',
          likes_count: data.status === 'liked' 
            ? (prevBook.likes_count || 0) + 1 
            : Math.max((prevBook.likes_count || 0) - 1, 0)
        }));

        message.success(
          data.status === 'liked' 
            ? 'Лайк поставлен!' 
            : 'Лайк убран!'
        );
      } else {
        throw new Error('Failed to toggle like');
      }
    } catch (error) {
      message.error('Ошибка при оценке произведения');
    } finally {
      setLikingInProgress(false);
    }
  };

  const handleLibraryAction = async (status) => {
    if (!user) {
      message.warning('Для добавления в библиотеку необходимо войти в систему');
      return;
    }

    setLibraryLoading(true);
    try {
      const response = await csrfFetch(`${backendUrl}/api/quick-add-to-library/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          book_id: book.id,
          status: status
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setLibraryStatus(status);
        
        // Обновляем счетчик библиотеки в книге только если книга не была в библиотеке
        if (!libraryStatus) {
          setBook(prevBook => ({
            ...prevBook,
            library_count: (prevBook.library_count || 0) + 1
          }));
        }

        const statusText = status === 'reading' ? 'Читаю' : 'Отложено на потом';
        message.success(`Книга добавлена в "${statusText}"`);
      } else {
        throw new Error('Failed to add to library');
      }
    } catch (error) {
      message.error('Ошибка при добавлении в библиотеку');
    } finally {
      setLibraryLoading(false);
    }
  };

  const handleRemoveFromLibrary = async () => {
    if (!user || !libraryStatus) return;

    setLibraryLoading(true);
    try {
      // Сначала получаем информацию о книге в библиотеке
      const response = await fetch(`${backendUrl}/api/check-library-status/${book.id}/`, {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.in_library && data.library_entry) {
          const libraryId = data.library_entry.id;
          
          const deleteResponse = await csrfFetch(`${backendUrl}/api/library/${libraryId}/`, {
            method: 'DELETE',
            credentials: 'include',
          });

          if (deleteResponse.ok) {
            setLibraryStatus(null);
            
            // Обновляем счетчик библиотеки в книге
            setBook(prevBook => ({
              ...prevBook,
              library_count: Math.max((prevBook.library_count || 0) - 1, 0)
            }));

            message.success('Книга удалена из библиотеки');
          } else {
            throw new Error('Failed to remove from library');
          }
        } else {
          // Книги нет в библиотеке, обновляем состояние
          setLibraryStatus(null);
          message.info('Книга уже не в библиотеке');
        }
      } else if (response.status === 404) {
        // Книги нет в библиотеке - это нормально
        setLibraryStatus(null);
        message.info('Книга не найдена в библиотеке');
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      message.error('Ошибка при удалении из библиотеки');
    } finally {
      setLibraryLoading(false);
    }
  };

  const isOwner = user && book && user.username === book.author.username;

  // Проверка 18+
  let isAdultBlocked = false;
  if (book && book.is_adult && !isOwner) {
    const birthDate = user?.birth_date;
    let age = null;
    if (birthDate) {
      const today = new Date();
      const dob = new Date(birthDate);
      age = today.getFullYear() - dob.getFullYear();
      const m = today.getMonth() - dob.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
        age--;
      }
    }
    if (!birthDate || age < 18) {
      isAdultBlocked = true;
    }
  }

  // Главы: для автора все, для других — только опубликованные
  const chapters = book?.chapters || [];
  const visibleChapters = isOwner
    ? chapters
    : chapters.filter(ch => ch.is_published);

  // Статус книги
  let status = '';
  if (!chapters.some(ch => ch.is_published)) {
    status = 'Черновик (видна только вам)';
  } else if (chapters.some(ch => !ch.is_published)) {
    status = 'Частично опубликовано';
  } else {
    status = 'Опубликовано';
  }

  // Публикация/скрытие главы
  const togglePublishChapter = async (chapterId, publish) => {
    try {
      const res = await csrfFetch(`/api/books/${book.id}/chapters/${chapterId}/`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ is_published: publish })
      });
      if (!res.ok) throw new Error();
      fetchBook();
    } catch {
      message.error('Ошибка при обновлении главы');
    }
  };

  if (loading) {
    return (
      <div className="p-5 max-w-[1200px] mx-auto">
        <div className="text-center py-[50px]">
          <Spin size="large" />
        </div>
      </div>
    );
  }

  if (!book) return null;

  // --- Подсчет знаков и АЛ ---
  function getTextStats(text) {
    if (!text) return { count: 0, formatted: '0', al: '0.00' };
    const plain = text.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ');
    const count = plain.length;
    const formatted = count.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
    const al = (count / 40000).toFixed(2);
    return { count, formatted, al };
  }

  // Подсчитываем общее количество знаков всех опубликованных глав
  const getTotalStats = () => {
    if (book?.type === 'story') {
      // Для рассказа берем первую главу
      const mainChapter = book?.chapters && book.chapters[0];
      return getTextStats(mainChapter?.content || '');
    } else {
      // Для романов и повестей суммируем все опубликованные главы
      const publishedChapters = book?.chapters?.filter(ch => ch.is_published) || [];
      let totalContent = '';
      
      publishedChapters.forEach(chapter => {
        if (chapter.content) {
          totalContent += chapter.content + ' ';
        }
      });
      
      return getTextStats(totalContent);
    }
  };

  const mainChapter = book?.chapters && book.chapters[0];
  const stats = getTotalStats();
  const BOOK_TYPE_LABELS = {
    story: 'Рассказ',
    novella: 'Повесть',
    novel: 'Роман',
    story_collection: 'Сборник рассказов',
    poetry_collection: 'Сборник поэзии',
  };
  // --- Статус ---
  let statusBlock = null;
  if (book.status === 'draft') {
    statusBlock = (
      <div className="text-xs mt-1 flex items-center gap-1" style={{ color: '#f59e42', fontWeight: 600 }}>
        <span><svg width="15" height="15" fill="none" stroke="#f59e42" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg></span>
        Черновик
      </div>
    );
  } else if (book.status === 'in_progress') {
    statusBlock = (
      <div className="text-xs mt-1" style={{ color: '#60A5FA', fontWeight: 600 }}>
        <div className="flex items-center gap-1">
          <EditOutlined style={{ color: '#60A5FA', fontSize: 15, marginRight: 4 }} /> В процессе публикации
        </div>
        {book.updated_at && (
          <div className="text-xs mt-1" style={{ color: theme === 'dark' ? '#9ca3af' : '#6b7280', fontWeight: 400 }}>
            {formatUpdatedDate(book.updated_at, timezone)}
          </div>
        )}
      </div>
    );
  } else if (book.status === 'finished') {
    statusBlock = (
      <div className="text-xs mt-1" style={{ color: '#22c55e', fontWeight: 600 }}>
        <div className="flex items-center gap-1">
          <span style={{ fontSize: 18, marginRight: 4 }}>✔</span>
          Завершено
        </div>
        {book.published_at && (
          <div className="text-xs mt-1" style={{ color: theme === 'dark' ? '#9ca3af' : '#6b7280', fontWeight: 400 }}>
            {formatPublishedDate(book.published_at, timezone)}
          </div>
        )}
      </div>
    );
  }

  if (book && book.status === 'draft') {
    return (
      <div className="p-5 max-w-[1200px] mx-auto">
        <div className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded p-8 text-center text-lg font-semibold">
          Доступ к этой книге запрещён или она ещё не опубликована.
        </div>
      </div>
    );
  }

  // Проверка возрастных ограничений
  if (ageRestricted) {
    return (
      <div className="p-5 max-w-[1200px] mx-auto">
        <div className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 rounded p-8 text-center">
          <div className="text-6xl mb-4">🔞</div>
          <h2 className="text-2xl font-bold mb-4">Возрастное ограничение 18+</h2>
          <p className="text-lg mb-4">
            Данное произведение содержит контент, предназначенный только для совершеннолетних читателей.
          </p>
          {!user ? (
            <div>
              <p className="mb-4">Для просмотра этого контента необходимо войти в систему и указать дату рождения в профиле.</p>
              <Button
                type="primary"
                size="large"
                onClick={() => navigate('/login')}
              >
                Войти в систему
              </Button>
            </div>
          ) : (
            <div>
              <p className="mb-4">
                {user.birth_date
                  ? 'Вам еще не исполнилось 18 лет. Доступ к данному контенту ограничен.'
                  : 'Для просмотра контента 18+ необходимо указать дату рождения в настройках профиля.'
                }
              </p>
              {!user.birth_date && (
                <Button
                  type="primary"
                  size="large"
                  onClick={() => navigate('/profile/settings')}
                >
                  Настройки профиля
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  if (book && book.is_adult) {
    if (user && user.birth_date) {
      const today = new Date();
      const dob = new Date(user.birth_date);
      let age = today.getFullYear() - dob.getFullYear();
      const m = today.getMonth() - dob.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
        age--;
      }
      if (age < 18) {
        return (
          <div className="p-5 max-w-[1200px] mx-auto">
            <div className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded p-8 text-center text-lg font-semibold">
              Контент доступен только пользователям старше 18 лет
            </div>
          </div>
        );
      }
    } else if (!ageConfirmed) {
      return (
        <>
          <Modal
            open={true}
            footer={null}
            closable={false}
            centered
            className={theme === 'dark' ? 'dark-modal' : ''}
            styles={{
              header: { background: theme === 'dark' ? '#23272f' : '#fff', borderBottom: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}` },
              content: { background: theme === 'dark' ? '#23272f' : '#fff' },
              mask: { backdropFilter: 'blur(4px)' },
            }}
          >
            <div className="mb-4">
              <p className={theme === 'dark' ? 'text-white' : 'text-gray-900'} style={{ fontSize: '1.1rem', fontWeight: 600, textAlign: 'center' }}>
                Для просмотра этого контента необходимо подтвердить, что вам исполнилось 18 лет.
              </p>
            </div>
            <div className="flex justify-end space-x-4 mt-4">
              <Button
                type="primary"
                onClick={() => {
                  window.sessionStorage.setItem('adultConfirmed', 'true');
                  setAgeConfirmed(true);
                }}
                className={theme === 'dark' ? 'bg-green-700 hover:bg-green-800 border-none text-white' : 'bg-green-600 hover:bg-green-700 border-none text-white'}
              >
                Да, мне исполнилось 18 лет
              </Button>
              <Button
                danger
                onClick={() => window.location.href = '/'}
                className={theme === 'dark' ? 'bg-red-700 hover:bg-red-800 border-none text-white' : 'bg-red-600 hover:bg-red-700 border-none text-white'}
              >
                Нет, мне меньше 18 лет
              </Button>
            </div>
          </Modal>
          <style>{`
            .dark-modal .ant-modal-content {
              background: #23272f !important;
            }
            .dark-modal .ant-modal-header {
              background: #23272f !important;
              border-bottom: 1px solid #374151 !important;
            }
            .dark-modal .ant-modal-title {
              color: #fff !important;
            }
            .dark-modal .ant-modal-close-x {
              color: #fff !important;
            }
            .dark-dropdown .ant-dropdown-menu {
              background: #374151 !important;
              border: 1px solid #4b5563 !important;
            }
            .dark-dropdown .ant-dropdown-menu-item {
              color: #e5e7eb !important;
            }
            .dark-dropdown .ant-dropdown-menu-item:hover {
              background: #4b5563 !important;
            }
            .dark-dropdown .ant-dropdown-menu-item-disabled {
              color: #9ca3af !important;
            }
            .dark-dropdown .ant-dropdown-menu-item-danger {
              color: #ef4444 !important;
            }
            .dark-dropdown .ant-dropdown-menu-item-danger:hover {
              background: #7f1d1d !important;
            }
            .dark-dropdown .ant-dropdown-menu-item-divider {
              border-top: 1px solid #4b5563 !important;
            }
          `}</style>
        </>
      );
    }
  }

  if (isAdultBlocked) {
    return (
      <div className="p-5 max-w-[1200px] mx-auto">
        <div className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded p-8 text-center text-lg font-semibold">
          Контент доступен только пользователям старше 18 лет
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-[1200px] mx-auto px-4 flex flex-row w-full gap-[15px]">
      <div className="flex-1 w-full pr-[0px]">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <div className="flex flex-col md:flex-row gap-5">
            <div className="w-full max-w-[210px] flex flex-col items-center aspect-[7/10]">
              <div className="relative group cursor-pointer w-full">
                {/* Основная обложка книги */}
                <div className="relative w-full aspect-[7/10] bg-gray-200 dark:bg-gray-700 overflow-hidden shadow-lg transform transition-transform duration-300 group-hover:scale-105" 
                     style={{
                       borderRadius: '0 8px 8px 0'
                     }}
                     onClick={() => setCoverModalOpen(true)}
                >
                  <img
                    src={getBookCoverMiniUrl(book, backendUrl)}
                    alt={book.title}
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Корешок книги - левая полоска */}
                  <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-r from-black/30 via-black/10 to-transparent"></div>
                  
                  {/* Дополнительная тень для глубины */}
                  <div className="absolute left-1 top-0 bottom-0 w-1 bg-gradient-to-r from-black/20 to-transparent"></div>
                  
                  {/* Блик на обложке */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                
                {/* Тень под книгой */}
                <div className="absolute -bottom-1 left-1 right-2 h-1 bg-black/20 rounded-full blur-sm transform transition-transform duration-300 group-hover:scale-110"></div>
              </div>
              
              {/* Иконки статистики под обложкой */}
              <div className="flex items-center justify-center gap-3 text-gray-400 dark:text-gray-500 text-sm mt-3">
                <Tooltip title="Просмотры" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                  <span className="flex items-center gap-1 cursor-default">
                    <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                      <circle cx="12" cy="12" r="3"/>
                    </svg>
                    {book.views_count || 0}
                  </span>
                </Tooltip>
                <Tooltip title="Лайки" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                  <span 
                    className={`flex items-center gap-1 transition-colors duration-200 ${
                      likingInProgress 
                        ? 'cursor-not-allowed opacity-50' 
                        : 'cursor-pointer hover:text-red-500'
                    }`}
                    onClick={handleLikeToggle}
                  >
                    <svg 
                      width="20" 
                      height="20" 
                      fill={book.is_liked ? "#ef4444" : "none"} 
                      stroke={book.is_liked ? "#ef4444" : "currentColor"} 
                      strokeWidth="2" 
                      viewBox="0 0 24 24"
                    >
                      <path d="M4.318 6.318a4.5 4.5 0 0 1 6.364 0L12 7.636l1.318-1.318a4.5 4.5 0 1 1 6.364 6.364L12 21.682l-7.682-7.682a4.5 4.5 0 0 1 0-6.364z"/>
                    </svg>
                    {book.likes_count || 0}
                  </span>
                </Tooltip>
                <Tooltip title="В библиотеках" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                  <Dropdown
                    menu={{
                      items: libraryStatus ? [
                        {
                          key: 'current',
                          label: (
                            <span className="text-green-600 font-medium">
                              {libraryStatus === 'reading' ? '✓ Читаю' : '✓ Отложено на потом'}
                            </span>
                          ),
                          disabled: true,
                        },
                        {
                          key: 'change',
                          label: libraryStatus === 'reading' ? 'Переместить в "Отложено на потом"' : 'Переместить в "Читаю"',
                          onClick: () => handleLibraryAction(libraryStatus === 'reading' ? 'want_to_read' : 'reading'),
                        },
                        {
                          type: 'divider',
                        },
                        {
                          key: 'remove',
                          label: 'Удалить из библиотеки',
                          onClick: handleRemoveFromLibrary,
                          danger: true,
                        },
                      ] : [
                        {
                          key: 'reading',
                          label: 'Добавить на полку "Читаю"',
                          onClick: () => handleLibraryAction('reading'),
                        },
                        {
                          key: 'want_to_read',
                          label: 'Добавить на полку "Отложено на потом"',
                          onClick: () => handleLibraryAction('want_to_read'),
                        },
                      ]
                    }}
                    trigger={['click']}
                    disabled={libraryLoading}
                    overlayClassName={theme === 'dark' ? 'dark-dropdown' : ''}
                  >
                    <span 
                      className={`flex items-center gap-1 transition-colors duration-200 ${
                        libraryLoading
                          ? 'cursor-not-allowed opacity-50'
                          : 'cursor-pointer hover:text-green-500'
                      }`}
                    >
                      <svg 
                        width="20" 
                        height="20" 
                        fill={libraryStatus ? "rgba(34, 197, 94, 0.7)" : "none"} 
                        stroke={libraryStatus ? "#22c55e" : "currentColor"} 
                        strokeWidth="2" 
                        viewBox="0 0 24 24"
                      >
                        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
                      </svg>
                      {book.library_count || 0}
                    </span>
                  </Dropdown>
                </Tooltip>
                <Tooltip title="Комментарии" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                  <span className="flex items-center gap-1 cursor-default">
                    <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                    </svg>
                    {commentsCount}
                  </span>
                </Tooltip>
              </div>
              
              {(book.status === 'in_progress' || book.status === 'finished') && (
                <Button 
                  type="primary" 
                  size="large"
                  className="mt-3 w-full bg-blue-600 hover:bg-blue-700 border-none text-white flex items-center justify-center"
                  onClick={() => navigate(`/book/${book.id}/reader`)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C20.832 18.477 19.247 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                  Читать
                </Button>
              )}
              {coverModalOpen && (
                <div
                  className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70"
                  style={{ backdropFilter: 'blur(2px)' }}
                >
                  <div className="relative max-w-[90vw] max-h-[90vh] flex items-center justify-center">
                    <img
                      src={getBookCoverUrl(book, backendUrl)}
                      alt={book.title}
                      className="rounded-lg shadow-lg max-w-full max-h-[90vh] bg-white dark:bg-gray-900"
                      style={{ objectFit: 'contain' }}
                    />
                    <button
                      className="absolute top-2 right-2 text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg focus:outline-none"
                      onClick={() => setCoverModalOpen(false)}
                      aria-label="Закрыть"
                    >
                      <CloseOutlined style={{ fontSize: 22 }} />
                    </button>
                  </div>
                </div>
              )}
            </div>
            <div className="flex-1 flex flex-col justify-start">
              <div className="text-2xl font-bold mb-1" style={{ color: theme === 'dark' ? '#fff' : '#222' }}>{book.title}</div>
              <div className="text-base mb-1">
                {book && book.author ? (
                  <Link
                    to={`/lpu/${book.author.username}/books`}
                    className={
                      (theme === 'dark'
                        ? 'text-blue-300 hover:text-blue-400'
                        : 'text-blue-700 hover:text-blue-900') +
                      ' font-medium transition-colors duration-150'
                    }
                    style={{ textDecoration: 'underline', textUnderlineOffset: 2 }}
                  >
                    {book.author.display_name}
                  </Link>
                ) : null}
              </div>
              <div className="text-sm flex items-center gap-2 mb-1" style={{ color: theme === 'dark' ? '#a1a1aa' : '#888', fontStyle: 'italic' }}>
                {book && (BOOK_TYPE_LABELS[book.type] || book.type)}
                {mainChapter && stats.count > 0 && (
                  <>
                    <span className="mx-2 text-gray-400">|</span>
                    <span style={{ color: theme === 'dark' ? '#a1a1aa' : '#888', fontStyle: 'italic' }}>{stats.formatted} зн.</span>
                    <Tooltip title="Авторский лист = 40 000 знаков" classNames={{ root: theme === 'dark' ? 'dark-tooltip' : '' }}>
                      <span style={{ color: '#38bdf8', fontStyle: 'italic', marginLeft: 8 }}>АЛ: {stats.al}</span>
                    </Tooltip>
                  </>
                )}
              </div>
              {statusBlock}
              {(book.description || book.author_notes || book.additional_materials || (book.type !== 'story' && book.chapters?.length > 0)) && (
                <div className="mt-6">
                  {/* Вкладки */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {book.description && (
                      <button
                        onClick={() => {
                          setActiveTab('annotation');
                          handleCollapse();
                        }}
                        className={`tab-button px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                          activeTab === 'annotation'
                            ? theme === 'dark'
                              ? 'bg-blue-600 text-white'
                              : 'bg-blue-600 text-white'
                            : theme === 'dark'
                              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        Аннотация
                      </button>
                    )}
                    
                    {book.author_notes && (
                      <button
                        onClick={() => {
                          setActiveTab('author_notes');
                          handleCollapse();
                        }}
                                                 className={`tab-button px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                           activeTab === 'author_notes'
                             ? theme === 'dark'
                               ? 'bg-blue-600 text-white'
                               : 'bg-blue-600 text-white'
                             : theme === 'dark'
                               ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                               : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                         }`}
                      >
                        Прим. автора
                      </button>
                    )}
                    
                    {book.additional_materials && (
                      <button
                        onClick={() => {
                          setActiveTab('materials');
                          handleCollapse();
                        }}
                                                 className={`tab-button px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                           activeTab === 'materials'
                             ? theme === 'dark'
                               ? 'bg-blue-600 text-white'
                               : 'bg-blue-600 text-white'
                             : theme === 'dark'
                               ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                               : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                         }`}
                      >
                        Доп. материалы
                      </button>
                    )}
                    
                    {book.type !== 'story' && book.chapters?.length > 0 && (
                      <button
                        onClick={() => {
                          setActiveTab('contents');
                          handleCollapse();
                        }}
                                                 className={`tab-button px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                           activeTab === 'contents'
                             ? theme === 'dark'
                               ? 'bg-blue-600 text-white'
                               : 'bg-blue-600 text-white'
                             : theme === 'dark'
                               ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                               : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                         }`}
                      >
                        Оглавление
                      </button>
                    )}
                  </div>

                  {/* Контентная область */}
                  <div className="relative">
                    <div 
                      className={`border rounded-lg p-4 transition-all duration-300 custom-scrollbar interactive-content-area ${
                        theme === 'dark' 
                          ? 'bg-gray-800 border-gray-700' 
                          : 'bg-gray-50 border-gray-200'
                      }`}
                      style={{
                        maxHeight: isExpanded ? 'none' : '216px', // ~9 строк при line-height 1.5 и font-size 0.9rem
                        overflow: isExpanded ? 'visible' : 'auto'
                      }}
                    >
                      {(() => {
                      const annotationFontSize = '0.9rem';
                      const annotationLineHeight = 1.5;
                      const textStyle = { 
                        fontSize: annotationFontSize, 
                        lineHeight: annotationLineHeight,
                        color: theme === 'dark' ? '#e5e7eb' : '#374151'
                      };

                      switch (activeTab) {
                        case 'annotation':
                          return book.description ? (
                            <div
                              className="annotation-preview"
                              style={textStyle}
                              dangerouslySetInnerHTML={{ __html: book.description }}
                            />
                          ) : (
                            <div style={textStyle}>Аннотация не указана</div>
                          );

                        case 'author_notes':
                          return book.author_notes ? (
                            <div
                              className="author-notes-preview"
                              style={textStyle}
                              dangerouslySetInnerHTML={{ __html: book.author_notes }}
                            />
                          ) : (
                            <div style={textStyle}>Примечания автора отсутствуют</div>
                          );

                        case 'materials':
                          return book.additional_materials ? (
                            <div className="additional-materials" style={textStyle}>
                              {/* Здесь будет контент дополнительных материалов */}
                              <div>Дополнительные материалы в разработке</div>
                            </div>
                          ) : (
                            <div style={textStyle}>Дополнительные материалы отсутствуют</div>
                          );

                        case 'contents':
                          const visibleChapters = book.chapters?.filter(ch => ch.is_published) || [];
                          return (
                            <div className="contents-list" style={textStyle}>
                              {visibleChapters.length > 0 ? (
                                <div className="space-y-3">
                                  {visibleChapters
                                    .sort((a, b) => (a.order || 0) - (b.order || 0))
                                    .map((chapter, index) => {
                                      // Подсчитываем знаки для главы
                                      const chapterStats = getTextStats(chapter.content || '');
                                      const chapterTitle = chapter.order === 0 
                                        ? 'Предисловие' 
                                        : chapter.title || `Глава ${chapter.order}`;
                                        
                                      return (
                                        <div 
                                          key={chapter.id}
                                          className={`flex justify-between items-start p-3 rounded border ${
                                            theme === 'dark'
                                              ? 'bg-gray-700 border-gray-600'
                                              : 'bg-white border-gray-200'
                                          }`}
                                        >
                                          <div className="flex-1">
                                            <Link
                                              to={`/book/${book.id}/reader#chapter-${chapter.id}`}
                                              className={`font-medium ${
                                                theme === 'dark'
                                                  ? 'text-blue-300 hover:text-blue-400'
                                                  : 'text-blue-600 hover:text-blue-800'
                                              } transition-colors duration-200 hover:underline`}
                                              style={{ textDecoration: 'none' }}
                                            >
                                              {chapterTitle}
                                            </Link>
                                          </div>
                                          <div className="flex items-center gap-3 text-sm opacity-75">
                                            <span>
                                              {chapterStats.formatted} зн.
                                            </span>
                                            {chapter.published_at && (
                                              <span>
                                                {formatDateWithTimezone(chapter.published_at, timezone)}
                                              </span>
                                            )}
                                          </div>
                                        </div>
                                      );
                                    })}
                                </div>
                              ) : (
                                <div>Опубликованные главы отсутствуют</div>
                              )}
                            </div>
                          );

                        default:
                          return <div style={textStyle}>Контент не найден</div>;
                      }
                                            })()}
                      </div>

                      {/* Градиент для обрезки в свернутом состоянии */}
                      {!isExpanded && (
                        <div 
                          className="absolute bottom-0 left-0 right-0 h-8 pointer-events-none rounded-b-lg"
                          style={{
                            background: theme === 'dark' 
                              ? 'linear-gradient(transparent, #1f2937)'
                              : 'linear-gradient(transparent, #f9fafb)'
                          }}
                        />
                      )}

                    </div>

                    {/* Кнопки управления - вне области с градиентом */}
                    {!isExpanded && (
                      <div className="flex justify-end mt-4">
                        <button
                          onClick={handleExpand}
                          className={`px-3 py-1 rounded-md text-sm font-medium transition-all duration-200 flex items-center gap-1 ${
                            theme === 'dark'
                              ? 'bg-blue-600 hover:bg-blue-700 text-white'
                              : 'bg-blue-600 hover:bg-blue-700 text-white'
                          }`}
                        >
                          Показать больше
                          <DownOutlined style={{ fontSize: '10px' }} />
                        </button>
                      </div>
                    )}

                    {/* Кнопка "Свернуть" - справа */}
                    {isExpanded && (
                      <div className="flex justify-end mt-4">
                        <button
                          onClick={handleCollapse}
                          className={`px-3 py-1 rounded-md text-sm font-medium transition-all duration-200 flex items-center gap-1 ${
                            theme === 'dark'
                              ? 'bg-gray-600 hover:bg-gray-700 text-white'
                              : 'bg-gray-300 hover:bg-gray-400 text-gray-700'
                          }`}
                        >
                          Свернуть
                          <UpOutlined style={{ fontSize: '10px' }} />
                        </button>
                      </div>
                    )}
                </div>
              )}
            </div>
          </div>
          <div className="mt-8">
            {/* Список глав временно скрыт по требованию */}
          </div>
          
          {/* Комментарии */}
          <BookComments 
            bookId={book.id} 
            initialCommentsCount={commentsCount}
            onCommentCountChange={setCommentsCount}
          />
        </div>
      </div>
      {isOwner && (
        <aside className="w-[250px] flex-shrink-0 ml-[0px] mt-[0px] px-[0px] hidden min-[1020px]:flex flex-col">
          <ProfileSidebar username={book.author.username} isOwner={isOwner} />
        </aside>
      )}
      
      {/* Стили для темной темы */}
      <style>{`
        .dark-dropdown .ant-dropdown-menu {
          background: #374151 !important;
          border: 1px solid #4b5563 !important;
          border-radius: 6px !important;
        }
        .dark-dropdown .ant-dropdown-menu-item {
          color: #e5e7eb !important;
          padding: 8px 12px !important;
        }
        .dark-dropdown .ant-dropdown-menu-item:hover {
          background: #4b5563 !important;
          color: #f9fafb !important;
        }
        .dark-dropdown .ant-dropdown-menu-item-disabled {
          color: #9ca3af !important;
          background: transparent !important;
        }
        .dark-dropdown .ant-dropdown-menu-item-disabled:hover {
          background: transparent !important;
          color: #9ca3af !important;
        }
        .dark-dropdown .ant-dropdown-menu-item-danger {
          color: #ef4444 !important;
        }
        .dark-dropdown .ant-dropdown-menu-item-danger:hover {
          background: #7f1d1d !important;
          color: #fca5a5 !important;
        }
        .dark-dropdown .ant-dropdown-menu-item-divider {
          border-top: 1px solid #4b5563 !important;
          margin: 4px 0 !important;
        }
        .dark-tooltip .ant-tooltip-inner {
          background: #374151 !important;
          color: #e5e7eb !important;
        }
        .dark-tooltip .ant-tooltip-arrow::before {
          background: #374151 !important;
        }

        /* Стили для интерактивной области */
        .annotation-preview p, .author-notes-preview p {
          margin-bottom: 0.8em;
        }
        .annotation-preview p:last-child, .author-notes-preview p:last-child {
          margin-bottom: 0;
        }
        
        .contents-list .space-y-3 > * + * {
          margin-top: 0.75rem;
        }
        
        /* Стили для ссылок на главы в оглавлении */
        .contents-list a {
          display: inline-block;
          text-decoration: none;
          border-radius: 4px;
          padding: 2px 0;
          position: relative;
        }
        
        .contents-list a:hover {
          text-decoration: underline;
          text-underline-offset: 3px;
        }
        
        /* Анимации для кнопок вкладок */
        .tab-button {
          position: relative;
          overflow: hidden;
        }
        .tab-button::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
          transition: left 0.5s;
        }
        .tab-button:hover::before {
          left: 100%;
        }

        /* Дополнительные стили для интерактивной области */
        .interactive-content-area {
          scrollbar-width: thin;
          scrollbar-color: rgba(156, 163, 175, 0.5) rgba(156, 163, 175, 0.1);
        }
        
        /* Плавная прокрутка */
        .interactive-content-area.custom-scrollbar {
          scroll-behavior: smooth;
        }
        
        /* Тень внутри контейнера при скролле */
        .interactive-content-area::-webkit-scrollbar-corner {
          background: transparent;
        }
      `}</style>
    </div>
  );
};

export default BookView;