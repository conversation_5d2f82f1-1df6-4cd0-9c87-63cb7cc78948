import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { csrfFetch } from '../utils/csrf';

const Library = () => {
  const { username } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [allBooks, setAllBooks] = useState([]); // Все книги
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [stats, setStats] = useState(null);
  
  const isOwner = user?.username === username;
  const pageSize = 25; // 5x5 сетка
  
  // Фильтруем книги на клиенте
  const filteredBooks = allBooks.filter(book => {
    if (statusFilter === 'all') return true;
    return book.status === statusFilter;
  });
  
  // Пагинация отфильтрованных книг
  const totalBooks = filteredBooks.length;
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const books = filteredBooks.slice(startIndex, endIndex);
  
  const statusOptions = [
    { value: 'all', label: 'Все книги', color: 'gray' },
    { value: 'reading', label: 'Читаю', color: 'blue' },
    { value: 'want_to_read', label: 'Отложено на потом', color: 'yellow' },
    { value: 'read', label: 'Прочитано', color: 'green' },
  ];
  
  useEffect(() => {
    if (!isOwner) {
      // Только владелец может просматривать свою библиотеку
      navigate(`/lpu/${username}`);
      return;
    }
    
    fetchLibrary();
    fetchStats();
  }, [username, isOwner]); // Убираем statusFilter и currentPage из зависимостей
  
  const fetchLibrary = async () => {
    try {
      setLoading(true);
      // Загружаем ВСЕ книги без фильтрации
      const params = new URLSearchParams({
        status: 'all',
        page_size: 1000 // Большой лимит чтобы получить все книги
      });
      
      const response = await csrfFetch(`/api/library/?${params}`, {
        credentials: 'include'
      });
      
      if (response.ok) {
        const data = await response.json();
        setAllBooks(data.results || []);
      } else {
        throw new Error('Ошибка загрузки библиотеки');
      }
    } catch (error) {
      console.error('Error fetching library:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const fetchStats = async () => {
    try {
      const response = await csrfFetch('/api/library/stats/', {
        credentials: 'include'
      });
      
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };
  
  const handleStatusChange = (value) => {
    setStatusFilter(value);
    setCurrentPage(1); // Сбрасываем на первую страницу при смене фильтра
  };
  
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };
  
  const getBookCoverUrl = (book) => {
    return book.cover_mini_url || book.cover_temp_url || '/covertemp/covertemp.jpg';
  };

  // Функция для обновления списка при изменении активности книги
  const refreshLibrary = () => {
    fetchLibrary();
  };

  // Делаем функцию доступной через window для использования в других компонентах
  useEffect(() => {
    window.refreshLibrary = refreshLibrary;
    return () => {
      delete window.refreshLibrary;
    };
  }, []);
  
  const BookCard = ({ libraryEntry }) => {
    const book = libraryEntry.book;
    const status = libraryEntry.status;
    const statusOption = statusOptions.find(opt => opt.value === status);
    
    const [modalOpen, setModalOpen] = useState(false);
    
    const handleStatusClick = (e) => {
      e.preventDefault();
      e.stopPropagation();
      setModalOpen(true);
    };
    
    const handleStatusChange = async (newStatus) => {
      try {
        const response = await csrfFetch(`/api/library/${libraryEntry.id}/update_status/`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ status: newStatus })
        });
        
        if (response.ok) {
          const updatedEntry = await response.json();
          
          // Обновляем локальный массив книг
          setAllBooks(prevBooks => 
            prevBooks.map(book => 
              book.id === libraryEntry.id 
                ? { ...book, status: newStatus, updated_at: updatedEntry.updated_at }
                : book
            )
          );
          
          // Обновляем статистику
          fetchStats();
          setModalOpen(false);
        } else {
          console.error('Failed to update status');
        }
      } catch (error) {
        console.error('Error updating status:', error);
      }
    };
    
    const handleRemove = async () => {
      if (!confirm('Удалить книгу из библиотеки?')) return;
      
      try {
        const response = await csrfFetch(`/api/library/${libraryEntry.id}/`, {
          method: 'DELETE',
          credentials: 'include'
        });
        
        if (response.ok) {
          // Удаляем книгу из локального массива
          setAllBooks(prevBooks => 
            prevBooks.filter(book => book.id !== libraryEntry.id)
          );
          
          // Обновляем статистику
          fetchStats();
          setModalOpen(false);
        } else {
          console.error('Failed to remove book');
        }
      } catch (error) {
        console.error('Error removing book:', error);
      }
    };
    
    return (
      <>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          <div 
            className="cursor-pointer relative group aspect-[7/10]"
            onClick={() => navigate(`/book/${book.id}`)}
          >
            {/* Основная обложка книги */}
            <div className="relative h-full bg-gray-200 dark:bg-gray-700 overflow-hidden shadow-lg transform transition-transform duration-300 group-hover:scale-105" 
                 style={{
                   borderRadius: '0 8px 8px 0'
                 }}>
              <img
                alt={book.title}
                src={getBookCoverUrl(book)}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.src = '/covertemp/covertemp.jpg';
                }}
              />
              
              {/* Корешок книги - левая полоска */}
              <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-r from-black/30 via-black/10 to-transparent"></div>
              
              {/* Дополнительная тень для глубины */}
              <div className="absolute left-1 top-0 bottom-0 w-1 bg-gradient-to-r from-black/20 to-transparent"></div>
              
              {/* Блик на обложке */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            
            {/* Тень под книгой */}
            <div className="absolute -bottom-1 left-1 right-2 h-1 bg-black/20 rounded-full blur-sm transform transition-transform duration-300 group-hover:scale-110"></div>
            
            {/* Кнопка статуса на обложке */}
            <button
              onClick={handleStatusClick}
              className="absolute bottom-2 left-1/2 transform -translate-x-1/2 px-2 py-1 rounded text-xs font-medium cursor-pointer transition-all duration-200 backdrop-blur-sm text-white z-10"
              style={{
                backgroundColor: statusOption?.color === 'blue' ? 'rgba(59, 130, 246, 0.1)' :
                                statusOption?.color === 'yellow' ? 'rgba(245, 158, 11, 0.1)' :
                                statusOption?.color === 'green' ? 'rgba(34, 197, 94, 0.1)' :
                                'rgba(107, 114, 128, 0.1)',
                color: statusOption?.color === 'blue' ? '#3b82f6' :
                       statusOption?.color === 'yellow' ? '#f59e0b' :
                       statusOption?.color === 'green' ? '#22c55e' :
                       '#6b7280',
                width: 'auto',
                minWidth: 'fit-content',
                maxWidth: 'calc(100% - 16px)'
              }}
            >
              {statusOption?.label}
            </button>
          </div>
          
          <div className="p-3 text-center">
            <h3 
              className="font-semibold text-sm mb-2 line-clamp-2 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400"
              onClick={() => navigate(`/book/${book.id}`)}
              title={book.title}
            >
              {book.title}
            </h3>
            
            <p 
              className="text-gray-600 dark:text-gray-400 text-xs mb-3 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400"
              onClick={() => navigate(`/lpu/${book.author_username}`)}
            >
              {book.author_display_name}
            </p>
            
            {/* Кнопка ЧИТАТЬ для всех книг */}
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(`/book/${book.id}/reader`);
              }}
              className={`w-full px-3 py-2 text-white text-sm font-medium rounded transition-colors duration-200 ${
                status === 'reading' 
                  ? 'bg-blue-500 hover:bg-blue-600' 
                  : 'bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700'
              }`}
            >
              ЧИТАТЬ
            </button>
          </div>
        </div>
        
        {/* Модальное окно для управления статусом */}
        {modalOpen && (
          <div 
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
            onClick={() => setModalOpen(false)}
          >
            <div 
              className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm w-full mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
                Управление книгой
              </h3>
              
              <div className="mb-4">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Текущий статус:
                </p>
                <span className={`px-3 py-1 rounded text-sm bg-${statusOption?.color}-100 text-${statusOption?.color}-800 dark:bg-${statusOption?.color}-900 dark:text-${statusOption?.color}-200`}>
                  {statusOption?.label}
                </span>
              </div>
              
              <div className="space-y-2 mb-6">
                {statusOptions.slice(1).map(option => (
                  <button
                    key={option.value}
                    onClick={() => handleStatusChange(option.value)}
                    disabled={option.value === status}
                    className={`w-full text-left px-3 py-2 rounded text-sm transition-colors duration-200 ${
                      option.value === status
                        ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                        : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-white cursor-pointer border border-gray-200 dark:border-gray-600'
                    }`}
                  >
                    {option.value === status ? `✓ ${option.label}` : `Переместить в "${option.label}"`}
                  </button>
                ))}
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={handleRemove}
                  className="flex-1 px-4 py-2 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded text-sm hover:bg-red-200 dark:hover:bg-red-800 transition-colors duration-200"
                >
                  Удалить из библиотеки
                </button>
                <button
                  onClick={() => setModalOpen(false)}
                  className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                >
                  Отмена
                </button>
              </div>
            </div>
          </div>
        )}
      </>
    );
  };
  
  if (loading) {
    return (
      <div className="p-6">
        <h2 className="text-2xl font-bold mb-6">📚 Моя библиотека</h2>
        <div className="text-center py-8">Загрузка...</div>
      </div>
    );
  }
  
  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-4">📚 Моя библиотека</h2>
        
        {/* Кликабельные метки для фильтрации */}
        {stats && (
          <div className="flex gap-4 mb-4 text-sm flex-wrap">
            <button
              onClick={() => handleStatusChange('all')}
              className={`px-3 py-1 rounded transition-colors duration-200 ${
                statusFilter === 'all'
                  ? 'bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              Все книги: {stats.total_books}
            </button>
            <button
              onClick={() => handleStatusChange('reading')}
              className={`px-3 py-1 rounded transition-colors duration-200 ${
                statusFilter === 'reading'
                  ? 'bg-blue-200 dark:bg-blue-800 text-blue-900 dark:text-blue-100'
                  : 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800'
              }`}
            >
              Читаю: {stats.reading_books}
            </button>
            <button
              onClick={() => handleStatusChange('want_to_read')}
              className={`px-3 py-1 rounded transition-colors duration-200 ${
                statusFilter === 'want_to_read'
                  ? 'bg-yellow-200 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-100'
                  : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800'
              }`}
            >
              Отложено на потом: {stats.want_to_read_books}
            </button>
            <button
              onClick={() => handleStatusChange('read')}
              className={`px-3 py-1 rounded transition-colors duration-200 ${
                statusFilter === 'read'
                  ? 'bg-green-200 dark:bg-green-800 text-green-900 dark:text-green-100'
                  : 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800'
              }`}
            >
              Прочитано: {stats.read_books}
            </button>
          </div>
        )}
      </div>
      
      {/* Убираем старое выпадающее меню и заменяем только на информацию о количестве */}
      <div className="mb-6 flex justify-end items-center">
        {totalBooks > 0 && (
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Показано {Math.min((currentPage - 1) * pageSize + 1, totalBooks)}–
            {Math.min(currentPage * pageSize, totalBooks)} из {totalBooks}
          </span>
        )}
      </div>
      
      {books.length > 0 ? (
        <>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-6">
            {books.map(libraryEntry => (
              <BookCard key={libraryEntry.id} libraryEntry={libraryEntry} />
            ))}
          </div>
          
          {totalBooks > pageSize && (
            <div className="text-center">
              <div className="inline-flex gap-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border rounded disabled:opacity-50"
                >
                  ← Назад
                </button>
                <span className="px-3 py-1">
                  {currentPage} из {Math.ceil(totalBooks / pageSize)}
                </span>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= Math.ceil(totalBooks / pageSize)}
                  className="px-3 py-1 border rounded disabled:opacity-50"
                >
                  Вперед →
                </button>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-500 dark:text-gray-400 mb-4">
            {statusFilter === 'all' 
              ? "В библиотеке пока нет книг"
              : `Нет книг в статусе "${statusOptions.find(opt => opt.value === statusFilter)?.label}"`
            }
          </div>
          {statusFilter === 'all' && (
            <button 
              onClick={() => navigate('/books')}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Найти книги
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default Library; 