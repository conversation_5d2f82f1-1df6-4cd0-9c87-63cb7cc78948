from django.core.management.base import BaseCommand
from books.tasks import cleanup_abandoned_books, cleanup_book_covers


class Command(BaseCommand):
    help = 'Cleanup book covers manually'

    def add_arguments(self, parser):
        parser.add_argument(
            '--book-id',
            type=int,
            help='Cleanup covers for specific book ID',
        )
        parser.add_argument(
            '--abandoned',
            action='store_true',
            help='Cleanup abandoned books (older than 2 days in creating status)',
        )
        parser.add_argument(
            '--sync',
            action='store_true',
            help='Run synchronously (for testing)',
        )

    def handle(self, *args, **options):
        if options['book_id']:
            self.stdout.write(f"Starting cleanup for book {options['book_id']}...")
            if options['sync']:
                # Выполняем задачу синхронно для тестирования
                result = cleanup_book_covers(options['book_id'])
                self.stdout.write(
                    self.style.SUCCESS(f"Cleanup completed for book {options['book_id']}")
                )
            else:
                # Выполняем задачу асинхронно
                cleanup_book_covers.delay(options['book_id'])
                self.stdout.write(
                    self.style.SUCCESS(f"Cleanup task scheduled for book {options['book_id']}")
                )
        elif options['abandoned']:
            self.stdout.write("Starting cleanup of abandoned books...")
            if options['sync']:
                # Выполняем задачу синхронно для тестирования
                result = cleanup_abandoned_books()
                self.stdout.write(
                    self.style.SUCCESS("Abandoned books cleanup completed")
                )
            else:
                # Выполняем задачу асинхронно
                cleanup_abandoned_books.delay()
                self.stdout.write(
                    self.style.SUCCESS("Abandoned books cleanup task scheduled")
                )
        else:
            self.stdout.write(
                self.style.ERROR("Please specify --book-id <ID> or --abandoned")
            )
