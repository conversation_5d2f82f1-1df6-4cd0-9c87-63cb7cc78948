from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action, api_view, parser_classes, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404, render
from users.models import User
from .models import Book, BookChapter, Like, Review, Comment, CommentLike, Genre, Hashtag, move_book_to_category, BookChapterImage
from .serializers import (
    BookSerializer, BookCreateSerializer, BookListSerializer,
    BookChapterSerializer, BookChapterCreateSerializer,
    ReviewSerializer, CommentSerializer, GenreSerializer, HashtagSerializer,
    BookChapterUpdateSerializer
)
from django.db.models import Q, Exists, OuterRef, Count, Case, When, IntegerField, BooleanField, Value
from django.db import models
from rest_framework.parsers import MultiPartParser, FormParser
from django.conf import settings
from PIL import Image as PilImage
import os
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from users.storage_backends import PrivateMediaStorage
import io
import uuid
import boto3
from bs4 import BeautifulSoup
from docx import Document
from django.utils.html import escape
from users.models import get_group_folder, random_prefix
import re
from django.db.models import Max
from rest_framework.pagination import PageNumberPagination
from .models import BookChapterImage
import logging
import random
import string
from celery.result import AsyncResult

# Создаем логгер
logger = logging.getLogger(__name__)

# Кастомный класс пагинации для глав, который показывает все главы
class ChapterPagination(PageNumberPagination):
    page_size = 10000  # Достаточно большое число, чтобы показать все главы
    page_size_query_param = 'page_size'
    max_page_size = 10000

def home(request):
    return render(request, 'books/home.html')

@api_view(['GET'])
def book_list(request):
    books = Book.objects.select_related('author').prefetch_related('genres', 'hashtags').annotate(
        views_count=Count('viewed_by', distinct=True),
        likes_count=Count('likes', distinct=True),
        library_count=Count('in_libraries', distinct=True),
        reviews_count=Count('reviews', distinct=True),
        comments_count=Count('comments', filter=Q(comments__deleted_by_user=False) & Q(comments__deleted_by_admin=False), distinct=True),
        chapters_count=Count('chapters', distinct=True),
        published_chapters_count=Count('chapters', filter=Q(chapters__is_published=True), distinct=True),
        is_liked=Exists(
            Like.objects.filter(
                book=OuterRef('pk'),
                user=request.user.id if request.user.is_authenticated else None
            )
        ) if request.user.is_authenticated else Value(False, output_field=BooleanField())
    ).filter(status__in=['in_progress', 'finished'])
    
    serializer = BookListSerializer(books, many=True, context={'request': request})
    return Response(serializer.data)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticatedOrReadOnly])
def user_books(request, username):
    try:
        user = User.objects.get(username=username)
        if request.user.is_authenticated and request.user == user:
            books = Book.objects.filter(author=user)
        else:
            # Фильтруем только опубликованные книги (статус in_progress или finished)
            books = Book.objects.filter(author=user, status__in=['in_progress', 'finished'])
        finished = books.filter(status='finished').order_by('position_finished')
        in_progress = books.filter(status='in_progress').order_by('position_in_progress')
        drafts = books.filter(status='draft').order_by('position_draft')
        all_books = list(finished) + list(in_progress) + list(drafts)
        serializer = BookSerializer(all_books, many=True, context={'request': request, 'short_chapters': True})
        return Response({
            'count': len(all_books),
            'next': None,
            'previous': None,
            'results': serializer.data
        })
    except User.DoesNotExist:
        return Response(
            {'error': 'Пользователь не найден'},
            status=status.HTTP_404_NOT_FOUND
        )

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticatedOrReadOnly])
def user_books_with_user_data_simple(request, username):
    """
    Упрощенная версия объединенного API endpoint для тестирования
    """
    try:
        user = User.objects.select_related().get(username=username)
        
        # Сначала попробуем простой запрос без сложных аннотаций
        base_queryset = Book.objects.filter(author=user).select_related('author')
        
        # Фильтруем видимые книги
        if request.user.is_authenticated and request.user == user:
            visible_books = base_queryset
        else:
            visible_books = base_queryset.filter(status__in=['in_progress', 'finished'])
        
        # Получаем книги и добавляем аннотации постепенно
        try:
            # Добавляем аннотации по одной для диагностики
            books_with_basic_counts = visible_books.annotate(
                chapters_count=Count('chapters', distinct=True),
                published_chapters_count=Count('chapters', filter=Q(chapters__is_published=True), distinct=True)
            )
            
            # Добавляем новые счетчики с правильным library_count
            books_final = books_with_basic_counts.annotate(
                views_count=Count('viewed_by', distinct=True),
                likes_count=Count('likes', distinct=True),
                reviews_count=Count('reviews', distinct=True),
                comments_count=Count('comments', filter=Q(comments__deleted_by_user=False) & Q(comments__deleted_by_admin=False), distinct=True),
                library_count=Count('in_libraries', distinct=True)  # Восстанавливаем правильный подсчет
            )
            
            # Добавляем is_liked
            if request.user.is_authenticated:
                books_final = books_final.annotate(
                    is_liked=Exists(
                        Like.objects.filter(
                            book=OuterRef('pk'),
                            user=request.user.id
                        )
                    )
                )
            else:
                books_final = books_final.annotate(
                    is_liked=Value(False, output_field=BooleanField())
                )
            
            # Получаем список книг
            all_books = list(books_final.order_by('created_at'))
            
        except Exception as e:
            print(f"Annotation error: {e}")
            # Fallback к простому запросу
            all_books = list(visible_books.order_by('created_at'))
            # Добавляем поля вручную
            for book in all_books:
                book.chapters_count = book.chapters.count()
                book.published_chapters_count = book.chapters.filter(is_published=True).count()
                book.views_count = book.viewed_by.count()
                book.likes_count = book.likes.count()
                book.library_count = book.in_libraries.count()
                book.reviews_count = book.reviews.count()
                book.comments_count = book.comments.filter(deleted_by_user=False, deleted_by_admin=False).count()
                book.is_liked = False
                if request.user.is_authenticated:
                    book.is_liked = book.likes.filter(user=request.user).exists()
        
        # Получаем порядок блоков пользователя
        user_order = user.books_blocks_order if hasattr(user, 'books_blocks_order') and user.books_blocks_order else ['in_progress', 'finished']
        
        # Используем оптимизированный BookListSerializer с новыми полями
        books_serializer = BookListSerializer(all_books, many=True, context={'request': request})
        
        # Формируем ответ
        return Response({
            'user': {
                'id': user.id,
                'username': user.username,
                'display_name': user.display_name,
                'avatar': user.avatar.url if user.avatar else None,
                'books_blocks_order': user_order,
                'author_rating': getattr(user, 'author_rating', 0),
                'reader_rating': getattr(user, 'reader_rating', 0),
                'birth_date': user.birth_date,
                'gender': user.gender,
                'motto': user.motto,
                'hide_email': user.hide_email,
                'timezone': user.timezone or 'UTC',
                'created_at': user.date_joined,
            },
            'books': {
                'count': len(books_serializer.data),
                'results': books_serializer.data
            },
            'books_blocks_order': user_order
        })
        
    except User.DoesNotExist:
        return Response(
            {'error': 'Пользователь не найден'},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        print(f"General error in user_books_with_user_data_simple: {e}")
        import traceback
        traceback.print_exc()
        return Response(
            {'error': f'Внутренняя ошибка сервера: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
def genre_list(request):
    genres = Genre.objects.all()
    serializer = GenreSerializer(genres, many=True)
    return Response(serializer.data)

@api_view(['GET'])
def hashtag_autocomplete(request):
    q = request.GET.get('q', '').strip().lower()
    if q:
        # Сначала ищем по началу слова
        hashtags_startswith = Hashtag.objects.filter(name__istartswith=q)[:10]
        
        # Если запрос длиннее 3 символов, добавляем поиск по вхождению в середине
        if len(q) >= 4:
            hashtags_contains = Hashtag.objects.filter(
                name__icontains=q
            ).exclude(
                name__istartswith=q
            )[:10]
            
            # Объединяем результаты: сначала начинающиеся с запроса, потом содержащие
            hashtag_ids = list(hashtags_startswith.values_list('id', flat=True)) + \
                         list(hashtags_contains.values_list('id', flat=True))
            hashtags = Hashtag.objects.filter(id__in=hashtag_ids[:20])
        else:
            hashtags = hashtags_startswith
    else:
        hashtags = Hashtag.objects.all()[:20]
    return Response(HashtagSerializer(hashtags, many=True).data)

class BookViewSet(viewsets.ModelViewSet):
    queryset = Book.objects.all()
    serializer_class = BookSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        queryset = Book.objects.all()
        if not self.request.user.is_authenticated:
            # Фильтруем только опубликованные книги (статус in_progress или finished)
            return queryset.filter(status__in=['in_progress', 'finished'])
        return queryset.filter(Q(status__in=['in_progress', 'finished']) | Q(author=self.request.user))

    def get_serializer_class(self):
        if self.action == 'create':
            return BookCreateSerializer
        return BookSerializer

    def perform_create(self, serializer):
        serializer.save()

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.status == 'draft' and instance.author != request.user:
            return Response(
                {'error': 'У вас нет прав для просмотра этого произведения'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Отслеживаем просмотр книги авторизованными пользователями
        if request.user.is_authenticated:
            instance.viewed_by.add(request.user)
        
        # Добавляем аннотации для статистики
        book_with_stats = Book.objects.select_related('author').prefetch_related('genres', 'hashtags').annotate(
            views_count=Count('viewed_by', distinct=True),
            likes_count=Count('likes', distinct=True),
            library_count=Count('in_libraries', distinct=True),
            reviews_count=Count('reviews', distinct=True),
            comments_count=Count('comments', filter=Q(comments__deleted_by_user=False) & Q(comments__deleted_by_admin=False), distinct=True),
            chapters_count=Count('chapters', distinct=True),
            published_chapters_count=Count('chapters', filter=Q(chapters__is_published=True), distinct=True),
            is_liked=Exists(
                Like.objects.filter(
                    book=OuterRef('pk'),
                    user=request.user.id if request.user.is_authenticated else None
                )
            ) if request.user.is_authenticated else Value(False, output_field=BooleanField())
        ).get(pk=instance.pk)
            
        serializer = self.get_serializer(book_with_stats)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.author != request.user:
            return Response(
                {'error': 'Только автор может редактировать это произведение'},
                status=status.HTTP_403_FORBIDDEN
            )
        # Синхронизация статусов при обновлении
        data = request.data
        status_value = data.get('status')
        
        if status_value:
            old_status = instance.status
            logger.info(f"ОТЛАДКА update: Изменение статуса книги {instance.id} с {old_status} на {status_value}")
            
            # Обработка изменения статуса
            if status_value == 'finished':
                # Устанавливаем флаг, чтобы избежать двойного обновления рейтинга
                instance._skip_rating_update = True
                instance.publish_all_chapters()
                # Сохраняем старый статус для рейтинга
                old_status = instance.status
                instance.status = 'finished'
                instance.is_published = True
                instance.is_finished = True
                # Сбрасываем флаг для обработки рейтинга при сохранении
                instance._skip_rating_update = False
                instance.save()
                logger.info(f"ОТЛАДКА update: Книга {instance.id} переведена в статус finished")
            elif status_value == 'in_progress':
                # Сохраняем старый статус для рейтинга
                old_status = instance.status
                instance.status = 'in_progress'
                instance.is_published = True
                instance.is_finished = False
                instance.save()
                logger.info(f"ОТЛАДКА update: Книга {instance.id} переведена в статус in_progress")
            elif status_value == 'draft':
                # Устанавливаем флаг, чтобы избежать двойного обновления рейтинга
                instance._skip_rating_update = True
                instance.unpublish_all_chapters()
                # Сохраняем старый статус для рейтинга
                old_status = instance.status
                instance.status = 'draft'
                instance.is_published = False
                instance.is_finished = False
                # Сбрасываем флаг для обработки рейтинга при сохранении
                instance._skip_rating_update = False
                instance.save()
                logger.info(f"ОТЛАДКА update: Книга {instance.id} переведена в статус draft")
        else:
            # Для обратной совместимости обрабатываем is_published и is_finished
            is_published = data.get('is_published')
            is_finished = data.get('is_finished')
            if is_finished is not None and str(is_finished).lower() == 'true':
                # Устанавливаем флаг, чтобы избежать двойного обновления рейтинга
                instance._skip_rating_update = True
                instance.publish_all_chapters()
                instance.is_published = True
                instance.is_finished = True
                instance.status = 'finished'
                # Сбрасываем флаг для обработки рейтинга при сохранении
                instance._skip_rating_update = False
                instance.save()
                logger.info(f"ОТЛАДКА update: Книга {instance.id} переведена в статус finished через is_finished")
            elif is_published is not None and str(is_published).lower() == 'false':
                # Устанавливаем флаг, чтобы избежать двойного обновления рейтинга
                instance._skip_rating_update = True
                instance.unpublish_all_chapters()
                instance.is_published = False
                instance.is_finished = False
                instance.status = 'draft'
                # Сбрасываем флаг для обработки рейтинга при сохранении
                instance._skip_rating_update = False
                instance.save()
                logger.info(f"ОТЛАДКА update: Книга {instance.id} переведена в статус draft через is_published")
        
        response = super().update(request, *args, **kwargs)
        instance.sync_status()
        # --- ВАЖНО: обновляем позицию при смене категории ---
        category = get_book_category(instance)
        move_book_to_category(instance, instance.author, category)
        return response

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.author != request.user:
            return Response(
                {'error': 'Только автор может редактировать это произведение'},
                status=status.HTTP_403_FORBIDDEN
            )
        # Синхронизация статусов при частичном обновлении
        data = request.data
        
        # Приоритет отдаем полю status, если оно есть
        status_value = data.get('status')
        if status_value:
            old_status = instance.status
            logger.info(f"ОТЛАДКА partial_update: Изменение статуса книги {instance.id} с {old_status} на {status_value}")
            
            # Обработка изменения статуса
            if status_value == 'finished':
                # Устанавливаем флаг, чтобы избежать двойного обновления рейтинга
                instance._skip_rating_update = True
                instance.publish_all_chapters()
                # Сохраняем старый статус для рейтинга
                old_status = instance.status
                instance.status = 'finished'
                instance.is_published = True
                instance.is_finished = True
                # Сбрасываем флаг для обработки рейтинга при сохранении
                instance._skip_rating_update = False
                instance.save()
                logger.info(f"ОТЛАДКА partial_update: Книга {instance.id} переведена в статус finished")
            elif status_value == 'in_progress':
                # Сохраняем старый статус для рейтинга
                old_status = instance.status
                instance.status = 'in_progress'
                instance.is_published = True
                instance.is_finished = False
                instance.save()
                logger.info(f"ОТЛАДКА partial_update: Книга {instance.id} переведена в статус in_progress")
            elif status_value == 'draft':
                # Устанавливаем флаг, чтобы избежать двойного обновления рейтинга
                instance._skip_rating_update = True
                instance.unpublish_all_chapters()
                # Сохраняем старый статус для рейтинга
                old_status = instance.status
                instance.status = 'draft'
                instance.is_published = False
                instance.is_finished = False
                # Сбрасываем флаг для обработки рейтинга при сохранении
                instance._skip_rating_update = False
                instance.save()
                logger.info(f"ОТЛАДКА partial_update: Книга {instance.id} переведена в статус draft")
        else:
            # Для обратной совместимости обрабатываем is_published и is_finished
            is_published = data.get('is_published')
            is_finished = data.get('is_finished')
            if is_finished is not None and str(is_finished).lower() == 'true':
                # Устанавливаем флаг, чтобы избежать двойного обновления рейтинга
                instance._skip_rating_update = True
                instance.publish_all_chapters()
                instance.is_published = True
                instance.is_finished = True
                instance.status = 'finished'
                # Сбрасываем флаг для обработки рейтинга при сохранении
                instance._skip_rating_update = False
                instance.save()
                logger.info(f"ОТЛАДКА partial_update: Книга {instance.id} переведена в статус finished через is_finished")
            elif is_published is not None and str(is_published).lower() == 'false':
                # Устанавливаем флаг, чтобы избежать двойного обновления рейтинга
                instance._skip_rating_update = True
                instance.unpublish_all_chapters()
                instance.is_published = False
                instance.is_finished = False
                instance.status = 'draft'
                # Сбрасываем флаг для обработки рейтинга при сохранении
                instance._skip_rating_update = False
                instance.save()
                logger.info(f"ОТЛАДКА partial_update: Книга {instance.id} переведена в статус draft через is_published")
        
        response = super().partial_update(request, *args, **kwargs)
        instance.sync_status()
        # --- ВАЖНО: обновляем позицию при смене категории ---
        category = get_book_category(instance)
        move_book_to_category(instance, instance.author, category)
        return response

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.author != request.user:
            return Response(
                {'error': 'Только автор может удалить это произведение'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        # Удаляем все изображения книги и папку книги перед удалением
        try:
            delete_book_images_folder(instance)
            logger.info(f"Удалены изображения книги {instance.id}")
        except Exception as e:
            logger.error(f"Ошибка при удалении изображений книги {instance.id}: {e}")
            
        # Обработка рейтинга при удалении книги в зависимости от статуса
        author = instance.author
        book_status = instance.status
        book_type = instance.type or 'story'
        
        # Импортируем здесь, чтобы избежать циклических импортов
        from users.rating_service import RatingService
        
        logger.info(f"ОТЛАДКА destroy: Удаление книги {instance.id} со статусом {book_status}")
        
        # Для рассказов
        if book_type == 'story' or book_type == 'story_collection':
            if book_status == 'in_progress':
                # Снимаем 10 очков за удаление рассказа в процессе публикации
                points = -10
                action_type = "story_delete_in_progress"
                logger.info(f"ОТЛАДКА destroy: Снимаем {-points} очков за удаление рассказа в процессе публикации")
            elif book_status == 'finished':
                # Снимаем 20 очков за удаление завершенного рассказа
                points = -20
                action_type = "story_delete_finished"
                logger.info(f"ОТЛАДКА destroy: Снимаем {-points} очков за удаление завершенного рассказа")
            else:
                # Для черновиков рейтинг не меняется
                points = 0
                action_type = None
                logger.info(f"ОТЛАДКА destroy: Рейтинг не меняется при удалении черновика рассказа")
        # Для романов/повестей
        elif book_type == 'novel' or book_type == 'novella':
            if book_status == 'in_progress':
                # Снимаем 20 очков за удаление романа/повести в процессе публикации
                points = -20
                action_type = "novel_delete_in_progress"
                logger.info(f"ОТЛАДКА destroy: Снимаем {-points} очков за удаление романа/повести в процессе публикации")
            elif book_status == 'finished':
                # Снимаем 50 очков за удаление завершенного романа/повести
                points = -50
                action_type = "novel_delete_finished"
                logger.info(f"ОТЛАДКА destroy: Снимаем {-points} очков за удаление завершенного романа/повести")
            else:
                # Для черновиков рейтинг не меняется
                points = 0
                action_type = None
                logger.info(f"ОТЛАДКА destroy: Рейтинг не меняется при удалении черновика романа/повести")
        else:
            # Для неизвестных типов используем логику рассказов
            if book_status == 'in_progress':
                points = -10
                action_type = "story_delete_in_progress"
                logger.info(f"ОТЛАДКА destroy: Снимаем {-points} очков за удаление произведения в процессе публикации")
            elif book_status == 'finished':
                points = -20
                action_type = "story_delete_finished"
                logger.info(f"ОТЛАДКА destroy: Снимаем {-points} очков за удаление завершенного произведения")
            else:
                points = 0
                action_type = None
                logger.info(f"ОТЛАДКА destroy: Рейтинг не меняется при удалении черновика произведения")
        
        # Обновляем рейтинг, если нужно
        if points != 0 and action_type:
            RatingService.update_metric(
                user=author,
                action_type=action_type,
                metric_name='author_rating',
                change_delta=points,
                related_object=None,  # Объект будет удален, поэтому не привязываем
                created_by='system'
            )
            logger.info(f"Начислено {points} баллов автору {author.username} за удаление книги {instance.id} со статусом {book_status}")
        
        return super().destroy(request, *args, **kwargs)

    @action(detail=True, methods=['post'], url_path='publish_all_chapters', permission_classes=[IsAuthenticated])
    def publish_all_chapters_action(self, request, pk=None):
        """
        Быстрая публикация всех неопубликованных глав книги.
        Альтернатива множественным PATCH запросам для каждой главы.
        """
        book = self.get_object()
        if book.author != request.user:
            return Response({'error': 'Только автор может публиковать главы'}, status=status.HTTP_403_FORBIDDEN)

        # Подсчитываем количество неопубликованных глав до операции
        unpublished_count = book.chapters.filter(is_published=False).count()

        if unpublished_count == 0:
            return Response({
                'message': 'Все главы уже опубликованы',
                'published_count': 0,
                'total_chapters': book.chapters.count()
            })

        # Публикуем все главы одним запросом
        import time
        start_time = time.time()
        book.publish_all_chapters()
        end_time = time.time()

        return Response({
            'message': f'Успешно опубликовано {unpublished_count} глав',
            'published_count': unpublished_count,
            'total_chapters': book.chapters.count(),
            'execution_time': f'{end_time - start_time:.2f} сек',
            'book_status': book.status
        })

    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        book = self.get_object()
        like, created = Like.objects.get_or_create(book=book, user=request.user)
        if not created:
            like.delete()
            return Response({'status': 'unliked'})
        return Response({'status': 'liked'})

    @action(detail=True, methods=['get', 'post'])
    def reviews(self, request, pk=None):
        book = self.get_object()
        if request.method == 'POST':
            serializer = ReviewSerializer(
                data=request.data,
                context={'request': request, 'book_id': book.id}
            )
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        reviews = book.reviews.all()
        serializer = ReviewSerializer(reviews, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get', 'post'])
    def comments(self, request, pk=None):
        book = self.get_object()
        if request.method == 'POST':
            serializer = CommentSerializer(
                data=request.data,
                context={'request': request, 'book_id': book.id}
            )
            if serializer.is_valid():
                comment = serializer.save()
                # Возвращаем полный сериализованный объект
                response_serializer = CommentSerializer(comment, context={'request': request, 'book_id': book.id})
                return Response(response_serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        # Получаем все основные комментарии (без родителя)
        all_comments = book.comments.filter(parent=None).prefetch_related('replies__user', 'user').order_by('-created_at')
        
        # Фильтруем: исключаем удаленные комментарии без ответов
        filtered_comments = []
        for comment in all_comments:
            if comment.is_deleted:
                # Если удаленный комментарий имеет ответы, показываем его
                if comment.get_replies_count() > 0:
                    filtered_comments.append(comment)
                # Иначе скрываем полностью
            else:
                # Неудаленные комментарии показываем всегда
                filtered_comments.append(comment)
        
        serializer = CommentSerializer(filtered_comments, many=True, context={'request': request, 'book_id': book.id})
        return Response(serializer.data)

    @action(detail=True, methods=['get'], url_path='comments/(?P<comment_id>[^/.]+)/replies')
    def comment_replies(self, request, pk=None, comment_id=None):
        """Получить все ответы на конкретный комментарий"""
        book = self.get_object()
        try:
            parent_comment = Comment.objects.get(id=comment_id, book=book, parent=None)
        except Comment.DoesNotExist:
            return Response({'error': 'Comment not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Применяем ту же логику фильтрации, что и в get_replies сериализатора
        all_replies = parent_comment.replies.all().select_related('user').order_by('created_at')
        
        filtered_replies = []
        for reply in all_replies:
            if reply.is_deleted:
                # Для удаленного ответа проверяем, есть ли ответы с его именем в префиксе
                reply_author_name = reply.user.display_name or reply.user.username
                has_responses = parent_comment.replies.filter(
                    deleted_by_user=False, 
                    deleted_by_admin=False,
                    text__startswith=f"{reply_author_name}  "  # Двойной пробел как в коде
                ).exists()
                
                if has_responses:
                    filtered_replies.append(reply)
                # Иначе скрываем удаленный ответ без откликов
            else:
                # Неудаленные ответы показываем всегда
                filtered_replies.append(reply)
        
        serializer = CommentSerializer(filtered_replies, many=True, context={'request': request, 'book_id': book.id})
        return Response(serializer.data)

    @action(detail=True, methods=['post'], url_path='comments/(?P<comment_id>[^/.]+)/like')
    def comment_like(self, request, pk=None, comment_id=None):
        """Поставить лайк/дизлайк комментарию"""
        if not request.user.is_authenticated:
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)
        
        book = self.get_object()
        try:
            comment = Comment.objects.get(id=comment_id, book=book)
        except Comment.DoesNotExist:
            return Response({'error': 'Comment not found'}, status=status.HTTP_404_NOT_FOUND)
        
        reaction = request.data.get('reaction')  # 'like' или 'dislike'
        if reaction not in ['like', 'dislike']:
            return Response({'error': 'Invalid reaction'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Импортируем асинхронные задачи для обработки изменений
        from users.tasks import process_like_removed_async, process_like_added_async

        # Получаем существующую реакцию пользователя
        try:
            existing_like = CommentLike.objects.get(comment=comment, user=request.user)
            old_reaction = existing_like.reaction

            if old_reaction == reaction:
                # Пользователь нажал ту же кнопку - удаляем реакцию
                # Снятие реакции обрабатывается сигналом post_delete
                existing_like.delete()
                user_reaction = None
            else:
                # Пользователь изменил реакцию - делаем два асинхронных действия:
                # 1. Убираем старую реакцию асинхронно
                process_like_removed_async.delay(
                    user_id=existing_like.user.id,
                    comment_id=existing_like.comment.id,
                    reaction=old_reaction,
                    content_type='comment'
                )

                # 2. Обновляем реакцию БЕЗ сигналов
                from django.db import transaction
                with transaction.atomic():
                    # Отключаем сигналы для этого обновления
                    CommentLike.objects.filter(pk=existing_like.pk).update(reaction=reaction)
                    # Обновляем объект в памяти
                    existing_like.reaction = reaction

                # 3. Обрабатываем новую реакцию асинхронно
                process_like_added_async.delay(existing_like.id, 'comment')
                user_reaction = reaction

        except CommentLike.DoesNotExist:
            # Создаем новую реакцию
            comment_like = CommentLike.objects.create(
                comment=comment,
                user=request.user,
                reaction=reaction
            )
            # Новая реакция обрабатывается автоматически сигналом post_save
            user_reaction = reaction
        
        # Возвращаем обновленную статистику
        likes_count = comment.likes.filter(reaction='like').count()
        
        return Response({
            'likes_count': likes_count,
            'user_reaction': user_reaction
        })

    @action(detail=True, methods=['patch'], url_path='comments/(?P<comment_id>[^/.]+)/edit')
    def edit_comment(self, request, pk=None, comment_id=None):
        """Редактировать комментарий"""
        if not request.user.is_authenticated:
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)
        
        book = self.get_object()
        try:
            comment = Comment.objects.get(id=comment_id, book=book)
        except Comment.DoesNotExist:
            return Response({'error': 'Comment not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Проверяем права доступа
        if comment.user != request.user:
            return Response({'error': 'You can only edit your own comments'}, status=status.HTTP_403_FORBIDDEN)
        
        if comment.is_deleted:
            return Response({'error': 'Cannot edit deleted comment'}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = CommentSerializer(
            comment,
            data=request.data,
            partial=True,
            context={'request': request, 'book_id': book.id}
        )
        
        if serializer.is_valid():
            updated_comment = serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['delete'], url_path='comments/(?P<comment_id>[^/.]+)/delete')
    def delete_comment(self, request, pk=None, comment_id=None):
        """Мягкое удаление комментария"""
        if not request.user.is_authenticated:
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)
        
        book = self.get_object()
        try:
            comment = Comment.objects.get(id=comment_id, book=book)
        except Comment.DoesNotExist:
            return Response({'error': 'Comment not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Проверяем права доступа
        if comment.user != request.user and not request.user.is_staff:
            return Response({'error': 'You can only delete your own comments'}, status=status.HTTP_403_FORBIDDEN)
        
        if comment.is_deleted:
            return Response({'error': 'Comment already deleted'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Мягкое удаление
        by_admin = request.user.is_staff and comment.user != request.user
        comment.soft_delete(by_admin=by_admin)
        
        # Возвращаем обновленные данные комментария
        serializer = CommentSerializer(comment, context={'request': request, 'book_id': book.id})
        return Response(serializer.data)

    @action(detail=True, methods=['post'], url_path='chapters/upload_image', parser_classes=[MultiPartParser, FormParser])
    def upload_image(self, request, pk=None):
        book = self.get_object()
        if book.author != request.user:
            return Response({'error': 'Только автор может загружать изображения'}, status=status.HTTP_403_FORBIDDEN)
        file = request.FILES.get('image')
        if not file:
            return Response({'error': 'Нет файла изображения'}, status=status.HTTP_400_BAD_REQUEST)
            
        # Проверяем размер файла (максимум 10MB)
        max_file_size = 10 * 1024 * 1024  # 10MB в байтах
        if file.size > max_file_size:
            return Response({'error': 'Размер файла превышает 10MB'}, status=status.HTTP_400_BAD_REQUEST)
            
        # Проверяем формат файла (поддерживаемые растровые форматы)
        allowed_formats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/bmp', 'image/tiff', 'image/gif']
        if file.content_type.lower() not in allowed_formats:
            return Response({'error': f'Неподдерживаемый формат файла. Поддерживаются: JPEG, PNG, WebP, BMP, TIFF, GIF'}, status=status.HTTP_400_BAD_REQUEST)
            
        chapter_id = request.data.get('chapter_id')
        position = int(request.data.get('position', 0))
        align = request.data.get('align', 'center')
        width = request.data.get('width')
        height = request.data.get('height')
        
        # Логируем для диагностики
        logger.info(f"Загрузка изображения для книги {book.id}, chapter_id: {chapter_id}, тип: {file.content_type}")
        
        # --- Генерируем путь (теперь с расширением .webp) ---
        group_folder = get_group_folder(book.id)
        rand = random_prefix()
        # Добавляем ID главы в путь после chapics/
        if chapter_id:
            s3_path = f"book_pics/{group_folder}/{book.id}/chapics/{chapter_id}/{book.id}_{rand}.webp"
        else:
            # Если нет ID главы, используем старый формат для совместимости
            s3_path = f"book_pics/{group_folder}/{book.id}/chapics/{book.id}_{rand}.webp"
            
        # --- Обработка изображения ---
        try:
            img = PilImage.open(file)
            
            # Сохраняем прозрачность для изображений с альфа-каналом
            if img.mode in ('RGBA', 'LA'):
                # Конвертируем в RGBA для сохранения прозрачности
                img = img.convert('RGBA')
            elif img.mode == 'P':
                # Проверяем, есть ли прозрачность в палитре
                if 'transparency' in img.info:
                    img = img.convert('RGBA')
                else:
                    img = img.convert('RGB')
            else:
                img = img.convert('RGB')
            
            # Сжимаем до максимальной ширины 1200px (если изображение больше)
            max_width = 1200
            w, h = img.size
            if w > max_width:
                new_h = int(h * max_width / w)
                img = img.resize((max_width, new_h), PilImage.LANCZOS)
            
            # Сохраняем в формате WebP с качеством 85% (с поддержкой прозрачности)
            buf = io.BytesIO()
            save_kwargs = {'format': 'WebP', 'quality': 85, 'method': 6}
            if img.mode == 'RGBA':
                save_kwargs['lossless'] = False  # Позволяем WebP сохранять прозрачность
            img.save(buf, **save_kwargs)
            buf.seek(0)
            
        except Exception as e:
            return Response({'error': f'Ошибка обработки изображения: {e}'}, status=status.HTTP_400_BAD_REQUEST)
            
        # --- Сохраняем в S3 ---
        storage = PrivateMediaStorage()
        storage.save(s3_path, ContentFile(buf.getvalue()))
        
        # --- Создаём запись BookChapterImage ---
        chapter = None
        if chapter_id:
            try:
                chapter = BookChapter.objects.get(id=chapter_id, book=book)
                logger.info(f"Найдена глава {chapter_id} для книги {book.id}")
            except BookChapter.DoesNotExist:
                logger.warning(f"Глава {chapter_id} не найдена для книги {book.id}")
                chapter = None
        

        
        image_obj = BookChapterImage.objects.create(
            book=book,
            chapter=chapter,
            path=s3_path,
            position=position,
            align=align,
            width=width,
            height=height
        )
        

        
        # --- Генерируем временную ссылку ---
        s3 = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            endpoint_url=settings.AWS_S3_ENDPOINT_URL,
            region_name=settings.AWS_S3_REGION_NAME,
        )
        bucket = settings.AWS_STORAGE_BUCKET_NAME
        url = s3.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket, 'Key': f"{storage.location}/{s3_path}"},
            ExpiresIn=3600
        )
        return Response({'url': url, 'id': image_obj.id, 'path': s3_path})

    @action(detail=False, methods=['patch'], url_path='reorder', url_name='reorder', permission_classes=[IsAuthenticated])
    def reorder(self, request, *args, **kwargs):
        user = request.user
        username = kwargs.get('username') or request.data.get('username')
        if not username or username != user.username:
            return Response({'detail': 'Недостаточно прав'}, status=status.HTTP_403_FORBIDDEN)
        category = request.data.get('category')
        order = request.data.get('order')
        if category not in ['finished', 'in_progress', 'drafts'] or not isinstance(order, list):
            return Response({'detail': 'Некорректные данные'}, status=status.HTTP_400_BAD_REQUEST)
        field_map = {
            'finished': 'position_finished',
            'in_progress': 'position_in_progress',
            'drafts': 'position_draft',
        }
        field = field_map[category]
        # ОПТИМИЗИРОВАНО: Массовое обновление порядка книг
        import time
        start_time = time.time()

        books = list(Book.objects.filter(author=user, id__in=order))
        books_dict = {book.id: book for book in books}

        books_to_update = []
        for idx, book_id in enumerate(order):
            if book_id in books_dict:
                book = books_dict[book_id]
                setattr(book, field, idx + 1)
                books_to_update.append(book)

        # Обновляем все книги одним запросом
        if books_to_update:
            from django.db import transaction
            with transaction.atomic():
                Book.objects.bulk_update(books_to_update, [field], batch_size=100)

            operation_time = time.time() - start_time
            logger.info(f"Массово обновлен порядок {len(books_to_update)} книг в категории {category} за {operation_time:.2f} сек")
        return Response({'detail': 'Порядок обновлён'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def abandon(self, request, pk=None):
        book = self.get_object()
        if book.creation_status == 'creating' and book.author == request.user:
            book.abandon_at = timezone.now()
            book.save(update_fields=['abandon_at'])
            return Response({'ok': True})
        return Response({'error': 'Not allowed'}, status=status.HTTP_403_FORBIDDEN)

    @action(detail=True, methods=['delete'], url_path='chapters/delete_image')
    def delete_image(self, request, pk=None):
        book = self.get_object()
        image_id = request.data.get('image_id')
        if not image_id:
            return Response({'error': 'Не передан image_id'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            image = BookChapterImage.objects.get(id=image_id, book=book)
        except BookChapterImage.DoesNotExist:
            return Response({'error': 'Изображение не найдено'}, status=status.HTTP_404_NOT_FOUND)
        if book.author != request.user:
            return Response({'error': 'Нет прав на удаление'}, status=status.HTTP_403_FORBIDDEN)
        # Удаляем файл из S3
        storage = PrivateMediaStorage()
        if storage.exists(image.path):
            storage.delete(image.path)
        image.delete()
        return Response({'ok': True})

    @action(detail=True, methods=['patch'], url_path='chapters/update_image')
    def update_image(self, request, pk=None):
        book = self.get_object()
        image_id = request.data.get('image_id')
        if not image_id:
            return Response({'error': 'Не передан image_id'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            image = BookChapterImage.objects.get(id=image_id, book=book)
        except BookChapterImage.DoesNotExist:
            return Response({'error': 'Изображение не найдено'}, status=status.HTTP_404_NOT_FOUND)
        if book.author != request.user:
            return Response({'error': 'Нет прав на изменение'}, status=status.HTTP_403_FORBIDDEN)
        # Обновляем параметры
        for field in ['position', 'align', 'width', 'height']:
            if field in request.data:
                setattr(image, field, request.data[field])
        image.save()
        return Response({
            'id': image.id,
            'position': image.position,
            'align': image.align,
            'width': image.width,
            'height': image.height,
        })

    @action(detail=True, methods=['get'], url_path='cover_task_status')
    def cover_task_status(self, request, pk=None):
        """
        Проверить статус задачи сохранения обложки
        """
        book = self.get_object()
        if book.author != request.user:
            return Response({'error': 'Только автор может проверять статус'}, status=status.HTTP_403_FORBIDDEN)

        task_id = request.query_params.get('task_id')
        logger.info(f"Checking cover task status for task_id: {task_id}")

        if not task_id:
            return Response({'error': 'Не передан task_id'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            result = AsyncResult(task_id)
            logger.info(f"Task {task_id} state: {result.state}, info: {result.info}")

            if result.state == 'PENDING':
                response = {
                    'state': result.state,
                    'current': 0,
                    'total': 100,
                    'status': 'Задача в очереди...'
                }
            elif result.state == 'PROGRESS':
                response = {
                    'state': result.state,
                    'current': result.info.get('current', 0),
                    'total': result.info.get('total', 100),
                    'status': result.info.get('status', '')
                }
            elif result.state == 'SUCCESS':
                response = {
                    'state': result.state,
                    'current': 100,
                    'total': 100,
                    'status': 'Завершено',
                    'result': result.info
                }
            else:  # FAILURE
                response = {
                    'state': result.state,
                    'current': 0,
                    'total': 100,
                    'status': f'Ошибка: {str(result.info)}'
                }

            logger.info(f"Returning response: {response}")
            return Response(response)

        except Exception as e:
            return Response({
                'state': 'FAILURE',
                'current': 0,
                'total': 100,
                'status': f'Ошибка при проверке статуса: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], url_path='get_image_links', permission_classes=[IsAuthenticated])
    def get_image_links(self, request):
        """
        Получить presigned URL для списка путей к изображениям (только для авторов книги).
        """
        paths = request.data.get('paths', [])
        result = {}
        user = request.user
        # Можно добавить более сложную проверку доступа при необходимости
        s3 = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            endpoint_url=settings.AWS_S3_ENDPOINT_URL,
            region_name=settings.AWS_S3_REGION_NAME,
        )
        bucket = settings.AWS_STORAGE_BUCKET_NAME
        prefix = settings.AWS_S3_PRIVATE_MEDIA_PREFIX
        for path in paths:
            # Убираем префикс, если есть
            clean_path = path
            if clean_path.startswith('/media/private/'):
                clean_path = clean_path[len('/media/private/'):]
            elif clean_path.startswith('/media/'):
                clean_path = clean_path[len('/media/'):]
            else:
                clean_path = clean_path.lstrip('/')
            try:
                img = BookChapterImage.objects.select_related('book').get(path=clean_path)
                if img.book.author != user:
                    continue  # не автор — не выдаём ссылку
            except BookChapterImage.DoesNotExist:
                continue  # нет такого файла
            url = s3.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket, 'Key': f"{prefix}/{clean_path}"},
                ExpiresIn=3600
            )
            result[path] = url
        return Response(result)

    @action(detail=True, methods=['post'], url_path='get_reader_image_links', permission_classes=[permissions.IsAuthenticatedOrReadOnly])
    def get_reader_image_links(self, request, pk=None):
        """
        Получить presigned URL для списка путей к изображениям для читалки (доступно всем, у кого есть доступ к главе).
        Формат путей и структура ответа — как в редакторе.
        """
        paths = request.data.get('paths', [])
        result = {}
        
        # Логируем для диагностики
        logger.info(f"get_reader_image_links: запрос на {len(paths)} изображений для книги {pk}")
        
        s3 = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            endpoint_url=settings.AWS_S3_ENDPOINT_URL,
            region_name=settings.AWS_S3_REGION_NAME,
        )
        bucket = settings.AWS_STORAGE_BUCKET_NAME
        prefix = settings.AWS_S3_PRIVATE_MEDIA_PREFIX

        # Проверка доступа к книге
        book = self.get_object()
        is_author = request.user.is_authenticated and book.author == request.user
        
        # Если книга не опубликована и пользователь не автор - отказываем в доступе
        if not book.is_published and not is_author:
            logger.warning(f"get_reader_image_links: отказано в доступе к книге {pk}, is_published={book.is_published}, is_author={is_author}")
            return Response(result)

        for path in paths:
            # Убираем префикс, если есть
            clean_path = path
            if clean_path.startswith('/media/private/'):
                clean_path = clean_path[len('/media/private/'):]
            elif clean_path.startswith('/media/'):
                clean_path = clean_path[len('/media/'):]
            else:
                clean_path = clean_path.lstrip('/')
                
            logger.debug(f"get_reader_image_links: обработка пути {path} -> {clean_path}")
                
            try:
                img = BookChapterImage.objects.select_related('book', 'chapter').get(path=clean_path)
                # Проверка: изображение принадлежит этой книге
                if img.book_id != book.id:
                    logger.warning(f"get_reader_image_links: изображение принадлежит другой книге: {img.book_id} != {book.id}")
                    continue
                # Проверка доступа к главе: автор видит все изображения, остальные только из опубликованных глав
                if img.chapter and not img.chapter.is_published and not is_author:
                    logger.warning(f"get_reader_image_links: глава не опубликована: chapter_id={img.chapter.id}, is_published={img.chapter.is_published}")
                    continue
            except BookChapterImage.DoesNotExist:
                # Логируем отсутствие изображения для диагностики
                logger.warning(f"get_reader_image_links: не найдено изображение с путем: {clean_path}")
                continue
                
            url = s3.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket, 'Key': f"{prefix}/{clean_path}"},
                ExpiresIn=3600
            )
            result[path] = url
            logger.debug(f"get_reader_image_links: сгенерирована ссылка для {path}")
            
        logger.info(f"get_reader_image_links: возвращено {len(result)} ссылок из {len(paths)} запрошенных")
        return Response(result)

@api_view(['POST'])
@parser_classes([MultiPartParser])
def upload_docx(request, book_id):
    """
    Загрузка docx-файла с изображениями для книги. Возвращает html для вставки в главу с сохранением форматирования.
    Обрабатывает обновление существующих неопубликованных глав, создание новых и формирование отчета.
    """
    import time
    import logging

    logger = logging.getLogger(__name__)
    start_time = time.time()
    logger.info(f"Начало загрузки DOCX для книги {book_id}")

    user = request.user
    book = get_object_or_404(Book, id=book_id)
    if book.author != user:
        return Response({'error': 'Только автор может загружать файл'}, status=403)
    file = request.FILES.get('file')
    if not file:
        return Response({'error': 'Файл не передан'}, status=400)

    # Проверяем размер файла в зависимости от типа книги
    if book.type == 'story':
        # Для рассказов - максимум 10MB
        max_file_size = 10 * 1024 * 1024  # 10MB в байтах
        max_size_text = '10 МБ'
        recommendations = 'Рекомендуется удалить лишние изображения, сжать изображения или упростить форматирование.'
    else:
        # Для романов и повестей - максимум 40MB
        max_file_size = 40 * 1024 * 1024  # 40MB в байтах
        max_size_text = '40 МБ'
        recommendations = 'Рекомендуется разделить документ на части или сжать изображения.'

    if file.size > max_file_size:
        file_size_mb = file.size / (1024 * 1024)
        book_type_text = 'рассказов' if book.type == 'story' else 'произведений'
        return Response({
            'error': f'Размер файла {file_size_mb:.1f} МБ превышает максимально допустимый размер {max_size_text} для {book_type_text}. '
                    f'{recommendations}'
        }, status=400)

    try:
        parse_start = time.time()
        doc = Document(file)
        logger.info(f"Парсинг DOCX файла занял {time.time() - parse_start:.2f} сек")
    except Exception as e:
        return Response({'error': f'Ошибка чтения docx: {e}'}, status=400)

    # Ранняя проверка размера - подсчитываем символы сразу после парсинга
    if book.type == 'story':
        MAX_STORY_LENGTH = 50000
        # Быстрый подсчет общего количества символов в документе
        total_text_length = 0
        for paragraph in doc.paragraphs:
            total_text_length += len(paragraph.text)

        if total_text_length > MAX_STORY_LENGTH:
            logger.warning(f"DOCX файл превышает лимит рассказа при ранней проверке: {total_text_length} > {MAX_STORY_LENGTH}")
            return Response({
                'error': f'Превышен максимальный размер рассказа: {MAX_STORY_LENGTH:,} символов. '
                        f'Загружаемый документ содержит: {total_text_length:,} символов.\n'
                        f'Сократите текст рассказа, либо создайте новую книгу с формой произведения повесть или роман.'
            }, status=400)
    elif book.type == 'novella':
        MAX_TALE_LENGTH = 200000
        # Быстрый подсчет общего количества символов в документе
        total_text_length = 0
        for paragraph in doc.paragraphs:
            total_text_length += len(paragraph.text)

        if total_text_length > MAX_TALE_LENGTH:
            logger.warning(f"DOCX файл превышает лимит повести при ранней проверке: {total_text_length} > {MAX_TALE_LENGTH}")
            return Response({
                'error': f'Превышен максимальный размер повести: {MAX_TALE_LENGTH:,} символов. '
                        f'Загружаемый документ содержит: {total_text_length:,} символов.\n'
                        f'Сократите текст повести, либо создайте новую книгу с формой произведения роман.'
            }, status=400)

    # --- Вспомогательные функции для форматирования текста (определены один раз) ---

    # Карта RGB значений к цветам панели (вынесена наружу для оптимизации)
    RGB_TO_COLOR_MAP = {
        # Красные оттенки
        'ffcccc': '#ffcccc',  # Светло-красный
        'ff0000': '#ff0000',  # Красный
        'cc0000': '#cc0000',  # Темно-красный
        '800000': '#800000',  # Бордовый

        # Оранжевые и желтые оттенки
        'ffcc99': '#ffcc99',  # Светло-оранжевый
        'ff9900': '#ff9900',  # Оранжевый
        'ffffcc': '#ffffcc',  # Светло-желтый
        'ffff00': '#ffff00',  # Желтый
        'cccc00': '#cccc00',  # Темно-желтый

        # Зеленые оттенки
        'ccffcc': '#ccffcc',  # Светло-зеленый
        '00cc00': '#00cc00',  # Зеленый
        '008000': '#008000',  # Темно-зеленый
        '99ff99': '#99ff99',  # Салатовый
        '808000': '#808000',  # Оливковый

        # Синие и фиолетовые оттенки
        'ccffff': '#ccffff',  # Светло-голубой
        '00ffff': '#00ffff',  # Голубой
        '0000ff': '#0000ff',  # Синий
        '000080': '#000080',  # Темно-синий
        '800080': '#800080',  # Фиолетовый

        # Серые оттенки, черный и белый
        'e6e6e6': '#e6e6e6',  # Светло-серый
        '808080': '#808080',  # Серый
        '404040': '#404040',  # Темно-серый
        '000000': '#000000',  # Черный
        'ffffff': '#ffffff',  # Белый

        # Дополнительные цвета из логов
        'ee0000': '#ff0000',  # Красный (из логов)
        'e97132': '#ff9900',  # Оранжевый (из логов)
        '196b24': '#008000',  # Темно-зеленый (из логов)
        '4ea72e': '#00cc00',  # Зеленый (из логов)
        '00b0f0': '#00ffff',  # Голубой (из логов)
        '4c94d8': '#0000ff',  # Синий (из логов)
    }

    def map_rgb_to_panel_color(rgb_str):
        """Сопоставляет RGB значения с цветами из панели редактора"""
        # Убираем префикс # если есть
        clean_rgb = rgb_str.replace('#', '').lower()
        return RGB_TO_COLOR_MAP.get(clean_rgb, f'#{clean_rgb}')

    # Предкомпилированные регулярные выражения для оптимизации
    RGB_PATTERN = re.compile(r'RGB\((\d+),\s*(\d+),\s*(\d+)\)')
    SHADING_PATTERN = re.compile(r'w:fill="([^"]+)"')
    HIGHLIGHT_PATTERN = re.compile(r'w:highlight[^>]*w:val="([^"]+)"')
    SHD_FILL_PATTERN = re.compile(r'w:shd[^>]*w:fill="([^"]+)"')

    # Быстрая функция экранирования HTML (оптимизированная)
    def fast_html_escape(text):
        """Быстрое экранирование HTML символов"""
        if not text:
            return text
        # Используем str.replace вместо html.escape для скорости
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))



    # Карта цветов маркеров Word (вынесена наружу для оптимизации)
    HIGHLIGHT_COLOR_MAP = {
        # Стандартные цвета Word
        'yellow': '#FFFF00',
        'lightYellow': '#FFFF00',
        'green': '#00FF00',
        'cyan': '#00FFFF',
        'magenta': '#FF00FF',
        'blue': '#0000FF',
        'red': '#FF0000',
        'darkBlue': '#000080',
        'darkCyan': '#008080',
        'darkGreen': '#008000',
        'darkMagenta': '#800080',
        'darkRed': '#800000',
        'darkYellow': '#808000',
        'darkGray': '#808080',
        'lightGray': '#C0C0C0',
        'black': '#000000',
        'white': '#FFFFFF',
        'none': None,

        # Дополнительные цвета из панели (для совместимости)
        'lightRed': '#ffcccc',
        'darkRed': '#cc0000',
        'burgundy': '#800000',
        'lightOrange': '#ffcc99',
        'orange': '#ff9900',
        'lightYellow': '#ffffcc',
        'darkYellow': '#cccc00',
        'lightGreen': '#ccffcc',
        'darkGreen': '#008000',
        'lime': '#99ff99',
        'olive': '#808000',
        'lightCyan': '#ccffff',
        'darkBlue': '#000080',
        'purple': '#800080',
        'lightGray': '#e6e6e6',
        'gray': '#808080',
        'darkGray': '#404040'
    }

    def run_to_html(run):
        # Быстрая проверка - если текст пустой, сразу возвращаем пустую строку
        if not run.text:
            return ''

        text = fast_html_escape(run.text)
        if not text:
            return ''

        # Быстрая проверка - если нет форматирования, возвращаем просто текст
        has_formatting = (run.bold or run.italic or run.underline or
                         (run.font.color and run.font.color.rgb) or
                         (hasattr(run.font, 'highlight_color') and run.font.highlight_color))

        # Проверяем XML только если есть потенциальное форматирование
        has_xml_formatting = False
        if hasattr(run, 'element') and run.element is not None:
            xml = run.element.xml
            # Быстрая проверка наличия форматирования в XML
            has_xml_formatting = ('w:fill=' in xml or 'w:highlight' in xml or 'w:shd' in xml)

        # Если нет никакого форматирования, возвращаем просто текст
        if not has_formatting and not has_xml_formatting:
            return text

        # Есть форматирование - обрабатываем полностью
        tags = []
        if run.bold:
            tags.append('b')
        if run.italic:
            tags.append('i')
        if run.underline:
            tags.append('u')
        style = ''
        color = None
        background_color = None

        # Обработка цвета текста
        if run.font.color is not None:
            if run.font.color.rgb is not None:
                # Конвертируем RGB в hex формат
                rgb_str = str(run.font.color.rgb)

                # Проверяем разные форматы RGB
                # Формат 1: "RGB(255, 0, 0)"
                rgb_match = RGB_PATTERN.search(rgb_str)
                if rgb_match:
                    r, g, b = map(int, rgb_match.groups())
                    hex_color = f'{r:02x}{g:02x}{b:02x}'
                    color = map_rgb_to_panel_color(hex_color)
                else:
                    # Формат 2: "EE0000" (прямой hex без префикса)
                    hex_match = re.search(r'^([0-9A-Fa-f]{6})$', rgb_str)
                    if hex_match:
                        color = map_rgb_to_panel_color(rgb_str)
    
                    else:
                        # Формат 3: "255, 0, 0" (только числа)
                        numbers_match = re.search(r'^(\d+),\s*(\d+),\s*(\d+)$', rgb_str)
                        if numbers_match:
                            r, g, b = map(int, numbers_match.groups())
                            hex_color = f'{r:02x}{g:02x}{b:02x}'
                            color = map_rgb_to_panel_color(hex_color)
    
                        else:
                            color = map_rgb_to_panel_color(rgb_str)
        
            elif hasattr(run.font.color, 'theme_color') and run.font.color.theme_color is not None:
                from docx.enum.dml import MSO_THEME_COLOR
                theme_map = {
                    MSO_THEME_COLOR.ACCENT_1: '#4472C4', MSO_THEME_COLOR.ACCENT_2: '#ED7D31',
                    MSO_THEME_COLOR.ACCENT_3: '#A5A5A5', MSO_THEME_COLOR.ACCENT_4: '#FFC000',
                    MSO_THEME_COLOR.ACCENT_5: '#5B9BD5', MSO_THEME_COLOR.ACCENT_6: '#70AD47',
                    MSO_THEME_COLOR.HYPERLINK: '#0563C1', MSO_THEME_COLOR.FOLLOWED_HYPERLINK: '#954F72',
                    MSO_THEME_COLOR.BACKGROUND_1: '#FFFFFF', MSO_THEME_COLOR.TEXT_1: '#000000',
                    MSO_THEME_COLOR.BACKGROUND_2: '#EEECE1', MSO_THEME_COLOR.TEXT_2: '#1F497D',
                }
                color = theme_map.get(run.font.color.theme_color)
            elif hasattr(run.font.color, 'type') and run.font.color.type is not None:
                if str(run.font.color.type) == 'SCHEME':
                    color = '#000000'
        if color:
            style += f'color:{color};'
            
        # Обработка маркеров (фона текста)
        if hasattr(run, 'font') and hasattr(run.font, 'highlight_color') and run.font.highlight_color is not None:
            highlight_color = HIGHLIGHT_COLOR_MAP.get(str(run.font.highlight_color), None)
            if highlight_color:
                background_color = highlight_color
        
                
        # Обработка заливки (shading) - альтернативный способ маркеров
        if has_xml_formatting and hasattr(run, 'element') and run.element is not None:
            xml = run.element.xml

            # Оптимизированный поиск всех атрибутов за один проход
            if not background_color:  # Только если еще не установлен
                # Ищем заливку в XML (приоритет 1)
                shading_match = SHADING_PATTERN.search(xml)
                if shading_match:
                    fill_color = shading_match.group(1)
                    if fill_color != 'auto' and fill_color != 'FFFFFF':
                        background_color = map_rgb_to_panel_color(fill_color)

                # Ищем highlight (приоритет 2)
                elif 'w:highlight' in xml:
                    highlight_match = HIGHLIGHT_PATTERN.search(xml)
                    if highlight_match:
                        highlight_val = highlight_match.group(1)
                        highlight_color = HIGHLIGHT_COLOR_MAP.get(highlight_val, None)
                        if highlight_color:
                            background_color = highlight_color

                # Ищем shading (приоритет 3)
                elif 'w:shd' in xml:
                    shd_fill_match = SHD_FILL_PATTERN.search(xml)
                    if shd_fill_match:
                        shd_fill = shd_fill_match.group(1)
                        if shd_fill != 'auto' and shd_fill != 'FFFFFF':
                            background_color = map_rgb_to_panel_color(shd_fill)
                
            # Ищем любые атрибуты, связанные с цветом фона
            if 'fill=' in xml:
                pass
            if 'background' in xml:
                pass
            if 'bgcolor' in xml:
                pass
                    
        if background_color:
            style += f'background-color:{background_color};'
            
        if run.font.size:
            style += f'font-size:{run.font.size.pt}pt;'
        # --- Отключаем обработку изображений ---
        # Если run содержит изображение, игнорируем его
        if hasattr(run, 'element') and run.element is not None:
            xml = run.element.xml
            if '<w:drawing' in xml or '<w:pict' in xml or '<pic:pic' in xml or '<a:blip' in xml:
                return ''
        # --- конец блока отключения изображений ---

        if style:
            text = f'<span style="{style}">{text}</span>'
        for tag in reversed(tags):
            text = f'<{tag}>{text}</{tag}>'
        return text

    def para_alignment_style(para):
        if para.alignment:
            align_map = {0: 'left', 1: 'center', 2: 'right', 3: 'justify'}
            align = align_map.get(para.alignment, 'left')
            return f'text-align:{align};'
        return ''

    def para_to_html(para):
        # Быстрая проверка - если параграф пустой, возвращаем пустой <p>
        if not para.runs:
            style = para_alignment_style(para)
            return f'<p style="{style}">&nbsp;</p>' if style else '<p>&nbsp;</p>'

        # Оптимизированная обработка runs
        runs_html_parts = []
        for run in para.runs:
            run_html = run_to_html(run)
            if run_html:  # Добавляем только непустые runs
                runs_html_parts.append(run_html)

        runs_html = ''.join(runs_html_parts)
        style = para_alignment_style(para)
        return f'<p style="{style}">{runs_html or "&nbsp;"}</p>' if style else f'<p>{runs_html or "&nbsp;"}</p>'

    auto_chapter_split = request.data.get('auto_chapter_split', 'true').lower() == 'true'
    is_novel_or_novella = book.type in ['novel', 'novella']
    
    

    if is_novel_or_novella:
        # --- Вспомогательные функции для определения типа главы (специфичные для романа/повести) ---
        def is_special_chapter(text):
            special_chapters = ['вступление', 'пролог', 'предисловие', 'вступление.', 'пролог.', 'предисловие.']
            text_lower = text.lower().strip()
            return any(text_lower == chapter for chapter in special_chapters)

        def is_final_chapter(text):
            # Расширим список возможных вариантов эпилога и послесловия
            text_lower = text.lower().strip()
            
            # Базовые варианты
            final_chapters = ['эпилог', 'послесловие', 'эпилог.', 'послесловие.']
            
            # Варианты с дополнительными символами
            if any(keyword in text_lower for keyword in ['эпилог', 'послесловие']):
                return True
                
            # Точное соответствие
            return any(text_lower == chapter for chapter in final_chapters)

        def is_chapter_title(text):
            # Для сборников рассказов ищем "Рассказ X"
            if book.type == 'story_collection':
                patterns = [
                    r'^рассказ\s+\d+(\s*[:.]?.*)?', # Matches "Рассказ 1", "Рассказ 1.", "Рассказ 1: Название"
                    r'^\d+\.\s*.*', # Matches "1.", "1. Название"
                ]
            else:
                # Для остальных типов книг используем стандартную логику
                patterns = [
                    r'^глава\s+\d+(\s*[:.]?.*)?', # Matches "Глава 1", "Глава 1.", "Глава 1: Название"
                    r'^часть\s+\d+(\s*[:.]?.*)?', # Matches "Часть 1", "Часть 1.", "Часть 1: Название"
                    r'^книга\s+\d+(\s*[:.]?.*)?', # Matches "Книга 1", "Книга 1.", "Книга 1: Название"
                    r'^\d+\.\s*.*', # Matches "1.", "1. Название"
                ]
            return any(re.match(pattern, text.lower()) for pattern in patterns)

        def get_chapter_number(text):
            # Для сборников рассказов ищем "Рассказ X"
            if book.type == 'story_collection':
                match = re.search(r'(?:рассказ)\s+(\d+)', text, re.IGNORECASE)
                if match:
                    return int(match.group(1))
            else:
                # Для остальных типов книг используем стандартную логику
                match = re.search(r'(?:глава|часть|книга)\s+(\d+)', text, re.IGNORECASE)
                if match:
                    return int(match.group(1))

            # Общая логика для числовых паттернов
            match = re.search(r'^(\d+)\.', text)
            if match:
                return int(match.group(1))
            return None

        def get_chapter_title_only(text):
            # Extracts only the descriptive part of the title after a numbered chapter/part/book identifier
            if book.type == 'story_collection':
                # Для сборников рассказов ищем "Рассказ X" и убираем этот префикс
                match_story = re.search(r'(?:рассказ)\s+\d+\s*[:.]?\s*(.*)$', text, re.IGNORECASE)
                if match_story:
                    title_part = match_story.group(1).strip()
                    cleaned_title = re.sub(r'^[.:/\s-]*', '', title_part).strip()
                    return cleaned_title if cleaned_title else ''
            else:
                # Для остальных типов книг используем стандартную логику
                match_chapter = re.search(r'(?:глава|часть|книга)\s+\d+\s*[:.]?\s*(.*)$', text, re.IGNORECASE)
                if match_chapter:
                    title_part = match_chapter.group(1).strip()
                    cleaned_title = re.sub(r'^[.:/\s-]*', '', title_part).strip()
                    return cleaned_title if cleaned_title else ''

            # For patterns like "1. Title"
            match_numbered = re.search(r'^\d+\.\s*(.*)$', text)
            if match_numbered:
                title_part = match_numbered.group(1).strip()
                return title_part if title_part else ''

            return '' # Return empty string if no specific title part is found

        # --- Обработка документа ---
        processing_start = time.time()
        logger.info("Начало обработки содержимого DOCX")

        # КРИТИЧЕСКАЯ ОПТИМИЗАЦИЯ: Кэшируем все параграфы один раз
        cache_start = time.time()
        cached_paragraphs = list(doc.paragraphs)  # Загружаем все параграфы в память
        cache_time = time.time() - cache_start
        logger.info(f"Кэширование {len(cached_paragraphs)} параграфов заняло {cache_time:.2f} сек")

        total_paragraphs_processed = 0
        chapters_from_docx = []
        current_chapter_data = None
        leading_text_buffer = [] # Buffer for text before the first recognized chapter
        trailing_text_buffer = [] # Buffer for text after the last recognized chapter
        found_any_chapter = False # Flag to track if we found any chapter

        max_existing_order = BookChapter.objects.filter(book=book).aggregate(Max('order'))['order__max'] or 0
        next_chapter_order = max_existing_order + 1

        if not auto_chapter_split:
            # Если автоматическое разделение отключено, весь текст в одну главу
            content = []
            for para in doc.paragraphs:
                content.append(para_to_html(para))

            if content:
                max_order = BookChapter.objects.filter(book=book).aggregate(Max('order'))['order__max'] or 0
                next_order = max_order + 1
                chapters_from_docx.append({
                    'title': f"Глава {next_order}",
                    'content': ''.join(content),
                    'order': next_order
                })
        else:
            # Оптимизированный подход для больших документов
            # Вместо хранения всех параграфов в памяти, сначала находим только индексы заголовков глав
            chapter_indices = []
            chapter_types = []  # Тип главы: 0 - предисловие, 1 - обычная глава, 2 - эпилог
            chapter_texts = []  # Тексты заголовков для определения названий глав
            
            # Первый проход - находим только заголовки глав
            for i, para in enumerate(cached_paragraphs):
                text = para.text.strip()
                if not text:  # Игнорируем пустые строки
                    continue
                    
                is_special = is_special_chapter(text)
                is_final = text.lower() in ['эпилог', 'послесловие', 'эпилог.', 'послесловие.']
                is_title = is_chapter_title(text)
                
                if is_special or is_final or is_title:
                    chapter_indices.append(i)
                    chapter_texts.append(text)
                    if is_special:
                        chapter_types.append(0)  # Предисловие
                    elif is_final:
                        chapter_types.append(2)  # Эпилог
                    else:
                        chapter_types.append(1)  # Обычная глава
            
            # Если нет заголовков глав, создаем одну главу из всего текста
            if not chapter_indices:
                single_chapter_start = time.time()
                content = []
                para_count = 0
                for para in cached_paragraphs:
                    text = para.text.strip()
                    if text:
                        content.append(para_to_html(para))
                        para_count += 1

                single_chapter_time = time.time() - single_chapter_start
                if book.type == 'story_collection':
                    logger.info(f"Один рассказ обработан за {single_chapter_time:.2f} сек ({para_count} параграфов)")
                else:
                    logger.info(f"Одна глава обработана за {single_chapter_time:.2f} сек ({para_count} параграфов)")
                
                max_order = BookChapter.objects.filter(book=book).aggregate(Max('order'))['order__max'] or 0
                next_order = max_order + 1
                if book.type == 'story_collection':
                    default_title = f"Рассказ {next_order}"
                else:
                    default_title = f"Глава {next_order}"

                chapters_from_docx.append({
                    'title': default_title,
                    'content': ''.join(content),
                    'order': next_order,
                    'epilogue_type': None
                })
            else:
                # ИЗМЕНЕНО: Текст до первой главы теперь игнорируется, если нет явных вступительных глав
                # Это соответствует логике: если автор не указал пролог/предисловие/вступление явно,
                # то текст до первой главы не включается в книгу

                # Обрабатываем каждую главу
                chapters_loop_start = time.time()
                for i, idx in enumerate(chapter_indices):
                    chapter_type = chapter_types[i]
                    text = chapter_texts[i]
                    
                    # Определяем порядковый номер и название главы
                    if chapter_type == 0:  # Предисловие
                        order = 0
                        title = text.capitalize()
                        epilogue_type = None
                    elif chapter_type == 2:  # Эпилог или Послесловие
                        # Временно присваиваем любой положительный номер, позже переопределим
                        order = 999999  # Временный большой номер
                        epilogue_type = 'epilogue'
                        if text.lower().strip() in ['эпилог', 'эпилог.']:
                            title = 'Эпилог'
                        else:
                            title = 'Послесловие'
                            epilogue_type = 'afterword'
                    else:  # Обычная глава/рассказ
                        epilogue_type = None
                        order = get_chapter_number(text)
                        if order is None:
                            order = next_chapter_order
                            next_chapter_order += 1
                        title_only = get_chapter_title_only(text)

                        # Для сборников рассказов используем только название без префикса
                        if book.type == 'story_collection':
                            if title_only:
                                title = title_only
                            else:
                                title = f"Рассказ {order}"
                        else:
                            # Для остальных типов книг используем стандартную логику
                            if title_only:
                                title = f"Глава {order}: {title_only}"
                            else:
                                title = f"Глава {order}"
                    
                    # Определяем содержимое главы (собираем его на лету, не храня весь документ в памяти)
                    content_start = idx + 1
                    content_end = chapter_indices[i + 1] if i + 1 < len(chapter_indices) else len(cached_paragraphs)
                    content = []

                    # Логируем обработку каждой главы для диагностики
                    chapter_start_time = time.time()
                    para_count = 0

                    for j in range(content_start, content_end):
                        paragraph = cached_paragraphs[j]
                        para_text = paragraph.text.strip()
                        if para_text:
                            content.append(para_to_html(paragraph))
                            para_count += 1
                            total_paragraphs_processed += 1

                    chapter_time = time.time() - chapter_start_time
                    if chapter_time > 1.0:  # Логируем только медленные главы
                        logger.info(f"Глава '{title}' обработана за {chapter_time:.2f} сек ({para_count} параграфов)")
                    
                    # Создаем главу
                    chapters_from_docx.append({
                        'title': title,
                        'content': ''.join(content),
                        'order': order,
                        'epilogue_type': epilogue_type
                    })

                chapters_loop_time = time.time() - chapters_loop_start
                logger.info(f"Цикл обработки {len(chapter_indices)} глав занял {chapters_loop_time:.2f} сек")
                
            # Теперь у нас есть все главы, включая предисловие и эпилог
                
                # Этот код больше не используется, так как мы полностью изменили логику обработки DOCX файла
                # Этот код больше не используется, так как мы полностью изменили логику обработки DOCX файла

        # Добавим дополнительную проверку для поиска эпилога в конце документа
        if auto_chapter_split and chapters_from_docx:
            # Проверяем, есть ли уже эпилог или послесловие
            has_epilogue = any(ch.get('epilogue_type') is not None for ch in chapters_from_docx)
            
            # Если эпилога нет, проверяем последние параграфы документа
            if not has_epilogue:
                # Проверим последние 20 параграфов (или меньше, если документ короче)
                last_paragraphs = cached_paragraphs[-min(20, len(cached_paragraphs)):]
                epilogue_start_idx = None
                
                # Ищем заголовок эпилога
                for i, para in enumerate(last_paragraphs):
                    text = para.text.strip().lower()
                    if text in ['эпилог', 'эпилог.', 'послесловие', 'послесловие.'] or 'эпилог' in text or 'послесловие' in text:
                        epilogue_start_idx = i
                        break
                
                if epilogue_start_idx is not None:
                    # Нашли эпилог в конце документа
                    epilogue_para = last_paragraphs[epilogue_start_idx]
                    epilogue_text = epilogue_para.text.strip().lower()
                    epilogue_title = 'Эпилог' if 'эпилог' in epilogue_text else 'Послесловие'
                    epilogue_content = []
                    
                    # Собираем содержимое эпилога
                    for j in range(len(cached_paragraphs) - len(last_paragraphs) + epilogue_start_idx + 1, len(cached_paragraphs)):
                        para_text = cached_paragraphs[j].text.strip()
                        if para_text:
                            epilogue_content.append(para_to_html(cached_paragraphs[j]))
                    
                    # Добавляем эпилог как отдельную главу
                    if epilogue_content:
                        chapters_from_docx.append({
                            'title': epilogue_title,
                            'content': ''.join(epilogue_content),
                            'order': 999999,  # Временный большой номер
                            'epilogue_type': 'epilogue' if epilogue_title == 'Эпилог' else 'afterword'
                        })
                

        if not chapters_from_docx and any(p.text.strip() for p in cached_paragraphs):
            max_order = BookChapter.objects.filter(book=book).aggregate(Max('order'))['order__max'] or 0
            next_order = max_order + 1
            if book.type == 'story_collection':
                default_title = file.name.replace('.docx', '') or f"Рассказ {next_order}"
            else:
                default_title = file.name.replace('.docx', '') or f"Глава {next_order}"

            chapters_from_docx.append({
                'title': default_title,
                'content': ''.join(para_to_html(p) for p in cached_paragraphs if p.text.strip()),
                'order': next_order,
                'epilogue_type': None
            })
        elif not chapters_from_docx and not leading_text_buffer:
            return Response({'error': 'Документ пуст'}, status=400)

        # --- Проверка структуры глав ---
        intro_chapters = [c for c in chapters_from_docx if c['order'] == 0]
        regular_chapters = [c for c in chapters_from_docx if c['order'] > 0 and c.get('epilogue_type') is None]
        epilogue_chapters = [c for c in chapters_from_docx if c.get('epilogue_type') is not None]
        
        errors = []
        # Проверка, что спецглавы только в начале
        if intro_chapters and regular_chapters:
            first_non_intro_idx = min(i for i, c in enumerate(chapters_from_docx)
                                    if c['order'] > 0 and c.get('epilogue_type') is None)
            if any(i > first_non_intro_idx for i, c in enumerate(chapters_from_docx) if c['order'] == 0):
                errors.append("Вступительные главы (Пролог, Вступление, Предисловие) должны идти только в начале.")
                
        # Проверка на дубли среди обычных глав
        if regular_chapters:
            from collections import Counter
            orders = [c['order'] for c in regular_chapters]
            order_counts = Counter(orders)
            duplicates = [order for order, count in order_counts.items() if count > 1]
            if duplicates:
                errors.append(f"Дублируются номера глав: {', '.join(str(d) for d in duplicates)}")
                
        if errors:
            return Response({'error': "Ошибка структуры глав:\n" + "\n".join(errors)}, status=400)
            
        # Определяем порядковые номера для эпилогов и послесловий
        # Они должны идти после всех обычных глав
        if regular_chapters:
            max_regular_order = max(c['order'] for c in regular_chapters)
        else:
            max_regular_order = 0
            
        # Присваиваем эпилогам и послесловиям порядковые номера после обычных глав
        # Сначала послесловия, потом эпилоги (если есть оба)
        epilogue_order = max_regular_order + 1
        
        # Сортируем: сначала послесловия, потом эпилоги
        sorted_epilogues = sorted(epilogue_chapters, key=lambda x: x['epilogue_type'], reverse=True)
        
        for chapter in sorted_epilogues:
            chapter['order'] = epilogue_order
            epilogue_order += 1
            # Удаляем временное поле, оно нам больше не нужно
            del chapter['epilogue_type']
            
        # Удаляем временное поле из всех остальных глав
        for chapter in intro_chapters + regular_chapters:
            if 'epilogue_type' in chapter:
                del chapter['epilogue_type']
            
        # Собрать итоговый список: вступительные, обычные главы, эпилоги и послесловия
        chapters_from_docx = intro_chapters + sorted(regular_chapters, key=lambda x: x['order']) + sorted_epilogues

        processing_time = time.time() - processing_start
        logger.info(f"Обработка содержимого DOCX завершена за {processing_time:.2f} сек, найдено {len(chapters_from_docx)} глав, обработано {total_paragraphs_processed} параграфов")

        # --- Проверка ограничений перед сохранением ---
        MAX_NOVEL_LENGTH = 2500000  # 2.5M символов на произведение

        # Подсчитываем общий объем загружаемого контента
        total_new_content_length = 0
        for chapter_data in chapters_from_docx:
            # Убираем HTML теги для точного подсчета символов
            plain_text = re.sub(r'<[^>]*>', '', chapter_data.get('content', ''))
            total_new_content_length += len(plain_text)

        # Проверяем, не превышает ли общий объем лимит произведения
        if book.type == 'story':
            # Для рассказов - лимит 50,000 символов
            MAX_STORY_LENGTH = 50000
            if total_new_content_length > MAX_STORY_LENGTH:
                logger.warning(f"DOCX файл превышает лимит рассказа: {total_new_content_length} > {MAX_STORY_LENGTH}")
                return Response({
                    'error': f'Превышен максимальный размер рассказа: {MAX_STORY_LENGTH:,} символов. '
                            f'Загружаемый документ содержит: {total_new_content_length:,} символов.\n'
                            f'Сократите текст рассказа, либо создайте новую книгу с формой произведения повесть или роман.'
                }, status=400)
        elif book.type == 'novella':
            # Для повестей - лимит 200,000 символов
            MAX_TALE_LENGTH = 200000
            if total_new_content_length > MAX_TALE_LENGTH:
                logger.warning(f"DOCX файл превышает лимит повести: {total_new_content_length} > {MAX_TALE_LENGTH}")
                return Response({
                    'error': f'Превышен максимальный размер повести: {MAX_TALE_LENGTH:,} символов. '
                            f'Загружаемый документ содержит: {total_new_content_length:,} символов.\n'
                            f'Сократите текст повести, либо создайте новую книгу с формой произведения роман.'
                }, status=400)
        else:
            # Для романов и сборников - лимит 2.5M символов
            if total_new_content_length > MAX_NOVEL_LENGTH:
                logger.warning(f"DOCX файл превышает лимит произведения: {total_new_content_length} > {MAX_NOVEL_LENGTH}")
                return Response({
                    'error': f'Документ превышает максимальный размер произведения в {MAX_NOVEL_LENGTH:,} символов. '
                            f'Текущий размер: {total_new_content_length:,} символов. '
                            f'Рекомендуется разделить документ на части.'
                }, status=400)

        # Логируем успешную проверку ограничений
        if book.type == 'story':
            logger.info(f"Проверка ограничений пройдена для рассказа: {total_new_content_length} символов из {50000}")
        elif book.type == 'novella':
            logger.info(f"Проверка ограничений пройдена для повести: {total_new_content_length} символов из {200000}")
        else:
            logger.info(f"Проверка ограничений пройдена: {total_new_content_length} символов из {MAX_NOVEL_LENGTH}")

        # Проверяем отдельные главы на превышение лимита (для предупреждений)
        if book.type == 'novella':
            MAX_CHAPTER_LENGTH = 50000  # 50k символов на главу для повестей
        elif book.type in ['story_collection', 'poetry_collection']:
            MAX_CHAPTER_LENGTH = 50000  # 50k символов на рассказ/поэтическое произведение в сборниках
        else:
            MAX_CHAPTER_LENGTH = 100000  # 100k символов на главу для романов
        over_limit_chapters = []

        for chapter_data in chapters_from_docx:
            plain_text = re.sub(r'<[^>]*>', '', chapter_data.get('content', ''))
            if len(plain_text) > MAX_CHAPTER_LENGTH:
                over_limit_chapters.append({
                    'title': chapter_data.get('title', 'Без названия'),
                    'length': len(plain_text),
                    'order': chapter_data.get('order', 0)
                })

        if over_limit_chapters:
            logger.warning(f"Найдено {len(over_limit_chapters)} глав, превышающих лимит главы")
            for ch in over_limit_chapters:
                logger.warning(f"  - {ch['title']}: {ch['length']} символов")

        # --- Сохраняем/обновляем главы в базе данных и формируем отчет ---
        db_start = time.time()
        logger.info("Начало работы с базой данных")

        existing_chapters_map = {ch.order: ch for ch in BookChapter.objects.filter(book=book)}
        processed_existing_chapter_orders = set()
        messages = []
        created_chapters_data = []
        
        # Сначала добавляем все существующие главы в created_chapters_data
        # Это нужно, чтобы на фронтенде отображались все главы, а не только новые/обновленные
        for order, chapter_obj in existing_chapters_map.items():
            created_chapters_data.append({
                'id': chapter_obj.id,
                'title': chapter_obj.title,
                'order': chapter_obj.order,
                'content': chapter_obj.content,
                'is_published': chapter_obj.is_published
            })
            
        # Собираем номера глав из нового документа, чтобы не удалять их
        docx_chapter_orders = {chapter['order'] for chapter in chapters_from_docx}
        
        # Удаляем неопубликованные главы, которых нет в новом документе, только если включено авторазбиение
        deleted_count = 0 # Initialize outside the conditional block
        deleted_chapters_ids = []  # Сохраняем ID удаленных глав
        chapters_to_delete = []  # Собираем главы для пакетного удаления

        if auto_chapter_split:
            for order, chapter_obj in existing_chapters_map.items():
                # Не удаляем главы, которые есть в новом документе
                if order not in docx_chapter_orders and not chapter_obj.is_published:
                    deleted_chapters_ids.append(chapter_obj.id)  # Сохраняем ID перед удалением
                    chapters_to_delete.append(chapter_obj)
                    if book.type == 'story_collection':
                        messages.append(f"Неопубликованный рассказ {order} \"{chapter_obj.title}\" удален (отсутствует в документе).")
                    else:
                        messages.append(f"Неопубликованная глава {order} \"{chapter_obj.title}\" удалена (отсутствует в документе).")
                    deleted_count += 1 # Increment here

            # Пакетное удаление глав (быстрее чем по одной)
            if chapters_to_delete:
                # Собираем изображения для пакетного удаления
                all_images = []
                for chapter in chapters_to_delete:
                    chapter_images = BookChapterImage.objects.filter(chapter=chapter)
                    all_images.extend(chapter_images)

                # Удаляем изображения пакетно
                if all_images:
                    try:
                        delete_images_batch(all_images)
                    except Exception as e:
                        logger.error(f"Ошибка при пакетном удалении изображений при загрузке DOCX: {e}")

                # Удаляем главы пакетно
                chapter_ids = [ch.id for ch in chapters_to_delete]
                BookChapter.objects.filter(id__in=chapter_ids).delete()

        # Удаляем из created_chapters_data те главы, которые были удалены
        created_chapters_data = [ch for ch in created_chapters_data if ch['id'] not in deleted_chapters_ids]

        for docx_chapter_data in chapters_from_docx:
            docx_order = docx_chapter_data['order']
            docx_title = docx_chapter_data['title']
            docx_content = docx_chapter_data['content']

            # Если авторазбиение выключено, всегда создаем новую главу с новым порядковым номером
            if not auto_chapter_split:
                chapter_obj = BookChapter.objects.create(
                    book=book,
                    title=docx_title,
                    content=docx_content,
                    order=docx_order,
                    is_published=False
                )
                if book.type == 'story_collection':
                    messages.append(f"Рассказ {docx_order} \"{docx_title}\" успешно создан.")
                else:
                    messages.append(f"Глава {docx_order} \"{docx_title}\" успешно создана.")
                created_chapters_data.append({
                    'id': chapter_obj.id,
                    'title': chapter_obj.title,
                    'order': chapter_obj.order,
                    'content': chapter_obj.content,
                    'is_published': chapter_obj.is_published
                })
                continue

            # Далее логика для авторазбиения
            existing_chapter = existing_chapters_map.get(docx_order)

            if existing_chapter:
                if existing_chapter.is_published:
                    if book.type == 'story_collection':
                        messages.append(f"Рассказ {docx_order} в статусе \"опубликовано\" не изменен.")
                    else:
                        messages.append(f"Глава {docx_order} в статусе \"опубликовано\" не изменена.")
                    processed_existing_chapter_orders.add(docx_order)
                    # Не добавляем повторно в created_chapters_data, так как уже добавили выше
                else:
                    if existing_chapter.title != docx_title or existing_chapter.content != docx_content:
                        # Обновляем существующую запись в created_chapters_data
                        for i, ch_data in enumerate(created_chapters_data):
                            if ch_data['id'] == existing_chapter.id:
                                existing_chapter.title = docx_title
                                existing_chapter.content = docx_content
                                # Пропускаем синхронизацию статуса при обновлении главы
                                existing_chapter._skip_sync_status = True
                                existing_chapter.save()
                                created_chapters_data[i] = {
                                    'id': existing_chapter.id,
                                    'title': existing_chapter.title,
                                    'order': existing_chapter.order,
                                    'content': existing_chapter.content,
                                    'is_published': existing_chapter.is_published
                                }
                                if book.type == 'story_collection':
                                    messages.append(f"Рассказ {docx_order} успешно обновлен.")
                                else:
                                    messages.append(f"Глава {docx_order} успешно обновлена.")
                                break
                    else:
                        if book.type == 'story_collection':
                            messages.append(f"Рассказ {docx_order} не требовал обновления.")
                        else:
                            messages.append(f"Глава {docx_order} не требовала обновления.")
                    processed_existing_chapter_orders.add(docx_order)
            else:
                chapter_obj = BookChapter.objects.create(
                    book=book,
                    title=docx_title,
                    content=docx_content,
                    order=docx_order,
                    is_published=False
                )
                if book.type == 'story_collection':
                    messages.append(f"Рассказ {docx_order} \"{docx_title}\" успешно создан.")
                else:
                    messages.append(f"Глава {docx_order} \"{docx_title}\" успешно создана.")
                created_chapters_data.append({
                    'id': chapter_obj.id,
                    'title': chapter_obj.title,
                    'order': chapter_obj.order,
                    'content': chapter_obj.content,
                    'is_published': chapter_obj.is_published
                })

        summary_message = "Результаты загрузки DOCX:\n" + "\n".join(messages)

        logger.info(f"Работа с базой данных завершена за {time.time() - db_start:.2f} сек")

        # Синхронизируем статус книги один раз в конце
        sync_start = time.time()
        book.sync_status()
        logger.info(f"Синхронизация статуса книги заняла {time.time() - sync_start:.2f} сек")

        total_time = time.time() - start_time
        logger.info(f"Загрузка DOCX завершена за {total_time:.2f} сек")

        return Response({'chapters': created_chapters_data, 'message': summary_message})

    else:
        # Для остальных типов книг (рассказы) создаем главу с HTML
        html = ''
        # --- Отключаем обработку изображений для всех типов книг ---
        # image_map = {} 
        # from docx.oxml.ns import qn
        # from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

        # rels = doc.part.rels
        # for rel in rels.values():
        #     if rel.reltype == 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/image':
        #         img_blob = rel.target_part.blob
        #         group_folder = get_group_folder(book.id)
        #         rand = random_prefix()
        #         s3_path = f"book_pics/{group_folder}/{book.id}/chapics/{book.id}_{rand}.webp"
        #         try:
        #             img = PilImage.open(io.BytesIO(img_blob))
                    
        #             # Обрабатываем изображения с прозрачностью
        #             if img.mode in ('RGBA', 'LA', 'P'):
        #                 # Создаем белый фон для изображений с прозрачностью
        #                 background = PilImage.new('RGB', img.size, (255, 255, 255))
        #                 if img.mode == 'P':
        #                     img = img.convert('RGBA')
        #                 background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
        #                 img = background
        #             else:
        #                 img = img.convert('RGB')
                    
        #             # Сжимаем до максимальной ширины 1200px (если изображение больше)
        #             max_width = 1200
        #             w, h = img.size
        #             if w > max_width:
        #                 new_h = int(h * max_width / w)
        #                 img = img.resize((max_width, new_h), PilImage.LANCZOS)
                    
        #             # Сохраняем в формате WebP с качеством 85%
        #             buf = io.BytesIO()
        #             img.save(buf, format='WebP', quality=85, method=6)
        #             buf.seek(0)
        #         except Exception as e:
        #             continue
        #         storage = PrivateMediaStorage()
        #         storage.save(s3_path, ContentFile(buf.getvalue()))
        #         image_obj = BookChapterImage.objects.create(
        #             book=book, chapter=None, path=s3_path, position=0, align='center', width=None, height=None
        #         )
        #         s3 = boto3.client(
        #             's3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        #             aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        #             endpoint_url=settings.AWS_S3_ENDPOINT_URL, region_name=settings.AWS_S3_REGION_NAME,
        #         )
        #         bucket = settings.AWS_STORAGE_BUCKET_NAME
        #         url = s3.generate_presigned_url(
        #             'get_object', Params={'Bucket': bucket, 'Key': f"{storage.location}/{s3_path}"}, ExpiresIn=3600
        #         )
        #         image_map[rel.target_ref] = url

        for para in doc.paragraphs:
            html += para_to_html(para)

        # Для рассказов всегда возвращаем HTML для вставки в существующую главу
        # Фронтенд сам создаст главу, если её нет

        return Response({'html': html})

class BookChapterViewSet(viewsets.ModelViewSet):
    serializer_class = BookChapterSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    pagination_class = ChapterPagination # Используем кастомную пагинацию

    def get_queryset(self):
        book_id = self.kwargs.get('book_pk')
        if book_id:
            queryset = BookChapter.objects.filter(book_id=book_id)

            # Автоматически очищаем устаревшие задачи планирования для автора
            if self.request.user.is_authenticated and \
               Book.objects.filter(id=book_id, author=self.request.user).exists():
                # Очищаем устаревшие задачи только для глав этой книги
                from django.utils import timezone
                expired_chapters = queryset.filter(
                    scheduled_publish_at__lt=timezone.now(),
                    scheduled_publish_at__isnull=False
                )
                for chapter in expired_chapters:
                    chapter.clean_expired_schedule()

            # Добавляем фильтрацию по is_published
            # Для глав оставляем поле is_published, так как у них только два статуса
            if not self.request.user.is_authenticated or \
               (self.request.user.is_authenticated and \
                not Book.objects.filter(id=book_id, author=self.request.user).exists()):
                queryset = queryset.filter(is_published=True)
            return queryset
        return BookChapter.objects.all()

    def get_serializer_class(self):
        if self.action in ['update', 'partial_update']:
            return BookChapterUpdateSerializer
        return BookChapterSerializer

    def perform_create(self, serializer):
        book = get_object_or_404(Book, pk=self.kwargs.get('book_pk'))
        if book.author != self.request.user:
            raise permissions.PermissionDenied(
                'Only the author can add chapters'
            )
        
        # Логируем данные перед сохранением

        if 'content' in serializer.validated_data:
            content = serializer.validated_data['content']
    
        
        chapter = serializer.save(book=book)

        return chapter

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.book.author != request.user:
            return Response({'error': 'Только автор может редактировать главу'}, status=status.HTTP_403_FORBIDDEN)
        response = super().update(request, *args, **kwargs)
        instance.refresh_from_db()
        self.cleanup_unused_images(instance)
        return response

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.book.author != request.user:
            return Response({'error': 'Только автор может редактировать главу'}, status=status.HTTP_403_FORBIDDEN)
        response = super().partial_update(request, *args, **kwargs)
        instance.refresh_from_db()

        self.cleanup_unused_images(instance)
        return response

    def cleanup_unused_images(self, chapter):
        # Получаем все относительные пути к картинкам из текста главы
        soup = BeautifulSoup(chapter.content or '', 'html.parser')
        used_paths = set()

        
        for img in soup.find_all('img'):
            src = img.get('src')
            if src:

                # Убираем префиксы, если есть
                original_src = src
                
                # Обрабатываем различные форматы путей
                if src.startswith('/media/private/'):
                    src = src[len('/media/private/'):]
                elif src.startswith('/media/'):
                    src = src[len('/media/'):]
                elif src.startswith('http'):
                    # Это presigned URL, извлекаем путь из URL
                    # Пример: https://s3.amazonaws.com/bucket/media/private/book_pics/...
                    try:
                        from urllib.parse import urlparse
                        parsed = urlparse(src)
                        path = parsed.path
                        # Убираем префиксы из пути
                        if path.startswith('/media/private/'):
                            path = path[len('/media/private/'):]
                        elif path.startswith('/media/'):
                            path = path[len('/media/'):]
                        src = path.lstrip('/')
                    except Exception as e:
                        logger.error(f"Cleanup: error parsing URL {src}: {e}")
                        continue
                else:
                    src = src.lstrip('/')
                
                used_paths.add(src)

        

        
        # Получаем все картинки, связанные с этой главой
        images = BookChapterImage.objects.filter(chapter=chapter)

        
        storage = PrivateMediaStorage()
        for image in images:

            if image.path not in used_paths:
                # Проверяем, не было ли изображение загружено совсем недавно (защита от удаления только что загруженных)
                from django.utils import timezone
                from datetime import timedelta
                recent_threshold = timezone.now() - timedelta(minutes=5)
                
                if image.inserted_at and image.inserted_at > recent_threshold:
    
                    continue
                

                if storage.exists(image.path):
                    storage.delete(image.path)

                image.delete()
            else:
                pass
          
          # Дополнительно: ищем картинки без привязки к главе, но используемые в тексте этой главы
        # и привязываем их к этой главе (для изображений из docx)
        for used_path in used_paths:
            # Ищем изображения без привязки к главе, которые используются в тексте
            orphaned_images = BookChapterImage.objects.filter(
                book=chapter.book,
                chapter=None,
                path=used_path
            )
            for orphaned_image in orphaned_images:
                # Привязываем к текущей главе
                orphaned_image.chapter = chapter
                orphaned_image.save(update_fields=['chapter'])
                
                # Если это старый формат пути (без ID главы), перемещаем файл в новую структуру
                if '/chapics/' in orphaned_image.path and f'/chapics/{chapter.id}/' not in orphaned_image.path:
                    old_path = orphaned_image.path
                    # Создаем новый путь с ID главы
                    path_parts = old_path.split('/chapics/')
                    if len(path_parts) == 2:
                        new_path = f"{path_parts[0]}/chapics/{chapter.id}/{path_parts[1]}"
                        
                        # Проверяем, существует ли старый файл
                        if storage.exists(old_path):
                            try:
                                # Копируем файл в новое место
                                old_file = storage.open(old_path, 'rb')
                                file_content = old_file.read()
                                old_file.close()
                                
                                storage.save(new_path, ContentFile(file_content))
                                
                                # Удаляем старый файл только после успешного создания нового
                                storage.delete(old_path)
                                
                                # Обновляем путь в базе данных
                                orphaned_image.path = new_path
                                orphaned_image.save(update_fields=['path'])
                                
                            except Exception as e:
                                # Если что-то пошло не так, оставляем старый путь
                                pass

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.book.author != request.user:
            return Response({'error': 'Только автор может удалять главу'}, status=status.HTTP_403_FORBIDDEN)

        # Удаляем изображения главы более эффективно
        try:
            chapter_images = BookChapterImage.objects.filter(chapter=instance)
            if chapter_images.exists():
                delete_images_batch(list(chapter_images))
        except Exception as e:
            logger.error(f"Ошибка при удалении изображений главы {instance.id}: {e}")

        return super().destroy(request, *args, **kwargs)

    @action(detail=False, methods=['patch'], url_path='reorder', permission_classes=[IsAuthenticated])
    def reorder(self, request, book_pk=None):
        """
        Массовое обновление порядка глав. Ожидает: [{id: 1, order: 1}, ...]
        """
        book = get_object_or_404(Book, pk=book_pk)
        if book.author != request.user:
            return Response({'error': 'Нет прав'}, status=status.HTTP_403_FORBIDDEN)
        chapters_data = request.data
        if not isinstance(chapters_data, list):
            return Response({'error': 'Некорректные данные'}, status=status.HTTP_400_BAD_REQUEST)
        # ОПТИМИЗИРОВАНО: Массовое обновление порядка глав
        import time
        start_time = time.time()

        # Собираем ID глав и их новые порядки
        chapter_updates = {}
        for ch in chapters_data:
            chapter_id = ch.get('id')
            order = ch.get('order')
            if chapter_id and order is not None:
                chapter_updates[chapter_id] = order

        if not chapter_updates:
            return Response({'updated': 0}, status=status.HTTP_200_OK)

        # Получаем главы для обновления
        chapters_to_update = list(BookChapter.objects.filter(
            id__in=chapter_updates.keys(),
            book=book
        ))

        # Обновляем порядок в памяти
        for chapter in chapters_to_update:
            if chapter.id in chapter_updates:
                chapter.order = chapter_updates[chapter.id]

        # Обновляем все главы одним запросом
        from django.db import transaction
        with transaction.atomic():
            BookChapter.objects.bulk_update(
                chapters_to_update,
                ['order'],
                batch_size=100
            )

        updated = len(chapters_to_update)
        operation_time = time.time() - start_time
        logger.info(f"Массово обновлен порядок {updated} глав книги {book.id} за {operation_time:.2f} сек")
        return Response({'updated': updated}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['delete'], url_path='batch_delete', permission_classes=[IsAuthenticated])
    def batch_delete(self, request, book_pk=None):
        """
        Массовое удаление глав. Ожидает: {'chapter_ids': [1, 2, 3, ...]}
        """
        import time
        start_time = time.time()
        logger.info(f"Начало пакетного удаления глав для книги {book_pk}")

        book = get_object_or_404(Book, pk=book_pk)
        if book.author != request.user:
            return Response({'error': 'Нет прав'}, status=status.HTTP_403_FORBIDDEN)
        
        chapter_ids = request.data.get('chapter_ids', [])
        if not isinstance(chapter_ids, list) or not chapter_ids:
            return Response({'error': 'Не переданы ID глав для удаления'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Получаем главы для удаления
        chapters_to_delete = BookChapter.objects.filter(
            id__in=chapter_ids,
            book=book
        )
        
        if not chapters_to_delete.exists():
            return Response({'error': 'Главы не найдены'}, status=status.HTTP_404_NOT_FOUND)
        
        deleted_count = 0
        book_to_sync = None
        
        # Собираем все изображения для пакетного удаления
        all_images_to_delete = []
        chapter_ids_to_delete = []

        for chapter in chapters_to_delete:
            # Сохраняем ссылку на книгу для синхронизации статуса
            if book_to_sync is None:
                book_to_sync = chapter.book

            # Собираем изображения для пакетного удаления
            chapter_images = BookChapterImage.objects.filter(chapter=chapter)
            all_images_to_delete.extend(chapter_images)
            chapter_ids_to_delete.append(chapter.id)

            # Удаляем главу, но пропускаем синхронизацию статуса
            chapter._skip_sync_status = True
            chapter.delete()
            deleted_count += 1

        # Пакетное удаление изображений (быстрее чем по одной главе)
        if all_images_to_delete:
            try:
                delete_images_batch(all_images_to_delete)
            except Exception as e:
                logger.error(f"Ошибка при пакетном удалении изображений: {e}")
        
        # Синхронизируем статус книги один раз после всех удалений
        if book_to_sync:
            sync_start = time.time()
            book_to_sync.sync_status()
            logger.info(f"Синхронизация статуса книги заняла {time.time() - sync_start:.2f} сек")

        total_time = time.time() - start_time
        logger.info(f"Пакетное удаление {deleted_count} глав завершено за {total_time:.2f} сек")

        return Response({'deleted_count': deleted_count}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['patch'], url_path='batch_publish', permission_classes=[IsAuthenticated])
    def batch_publish(self, request, book_pk=None):
        """
        Массовая публикация/снятие с публикации глав. 
        Ожидает: {'chapter_ids': [1, 2, 3, ...], 'is_published': true/false, 'publish_as_finished': true/false}
        """
        book = get_object_or_404(Book, pk=book_pk)
        if book.author != request.user:
            return Response({'error': 'Нет прав'}, status=status.HTTP_403_FORBIDDEN)
        
        chapter_ids = request.data.get('chapter_ids', [])
        is_published = request.data.get('is_published', True)
        publish_as_finished = request.data.get('publish_as_finished', False)
        
        if not isinstance(chapter_ids, list) or not chapter_ids:
            return Response({'error': 'Не переданы ID глав'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Получаем главы для обновления
        chapters_to_update = BookChapter.objects.filter(
            id__in=chapter_ids,
            book=book
        )
        
        if not chapters_to_update.exists():
            return Response({'error': 'Главы не найдены'}, status=status.HTTP_404_NOT_FOUND)
        
        # КРИТИЧЕСКАЯ ОПТИМИЗАЦИЯ: Массовое обновление одним SQL-запросом
        import time
        start_time = time.time()
        from django.utils import timezone
        from django.db import transaction

        now = timezone.now()

        # Обновляем все главы одним SQL-запросом без циклов
        with transaction.atomic():
            update_fields = {
                'is_published': is_published,
                'scheduled_publish_at': None,
                'celery_task_id': None
            }

            # Устанавливаем published_at только для публикуемых глав, у которых его еще нет
            if is_published:
                # Сначала обновляем published_at для глав, у которых его нет
                chapters_to_update.filter(published_at__isnull=True).update(published_at=now)

            # Затем обновляем остальные поля
            updated_count = chapters_to_update.update(**update_fields)

        operation_time = time.time() - start_time
        action = "опубликовано" if is_published else "снято с публикации"
        logger.info(f"МАССОВО {action} {updated_count} глав книги {book.id} за {operation_time:.2f} сек одним SQL-запросом")
        
        # Обновляем статус книги
        if is_published:
            if publish_as_finished:
                # Принудительно завершаем произведение
                book.status = 'finished'
                book.is_finished = True
                book.save(update_fields=['status', 'is_finished'])
                logger.info(f"Произведение {book.id} принудительно завершено через batch_publish")
            else:
                # Синхронизируем статус книги, но НЕ завершаем принудительно
                old_status = book.status
                book.sync_status()

                # Если sync_status установил статус "finished", но пользователь выбрал "В процессе"
                if book.status == 'finished' and not publish_as_finished:
                    book.status = 'in_progress'
                    book.is_finished = False
                    book.is_published = True  # Обязательно устанавливаем is_published для обратной совместимости
                    book.save(update_fields=['status', 'is_finished', 'is_published'])
                    logger.info(f"Произведение {book.id} переведено в 'in_progress' по выбору пользователя (было бы 'finished')")
        else:
            # Синхронизируем статус книги один раз в конце
            book.sync_status()

        # Отправляем WebSocket события для обновления статуса в реальном времени
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync

            channel_layer = get_channel_layer()

            # Отправляем события для каждой главы с флагом массовой операции
            for chapter in chapters_to_update:
                event_type = 'chapter_published' if is_published else 'chapter_unpublished'
                async_to_sync(channel_layer.group_send)(
                    f'book_{book.id}',
                    {
                        'type': event_type,
                        'chapter_id': chapter.id,
                        'title': chapter.title,
                        'book_is_finished': book.is_finished,
                        'book_status': book.status,
                        'is_batch_operation': True,  # Флаг массовой операции
                    }
                )

            # Отправляем событие массового обновления
            event_type = 'chapters_batch_published' if is_published else 'chapters_batch_unpublished'
            chapter_data = []
            for chapter in chapters_to_update:
                chapter_data.append({
                    'id': chapter.id,
                    'title': chapter.title,
                    'order': chapter.order
                })

            async_to_sync(channel_layer.group_send)(
                f'book_{book.id}',
                {
                    'type': event_type,
                    'chapters': chapter_data,
                    'count': len(chapter_data),
                    'book_is_finished': book.is_finished,
                    'book_status': book.status,
                    'was_batch_operation': True
                }
            )

            logger.info(f"WebSocket события отправлены для массового {'публикации' if is_published else 'снятия с публикации'} {updated_count} глав книги {book.id}")

        except Exception as e:
            logger.error(f"Ошибка отправки WebSocket событий: {e}")

        return Response({
            'updated_count': updated_count,
            'book_status': {
                'status': book.status,
                'is_published': book.is_published,  # Для обратной совместимости
                'is_finished': book.is_finished     # Для обратной совместимости
            }
        }, status=status.HTTP_200_OK)

    @action(detail=False, methods=['patch'], url_path='batch_schedule', permission_classes=[IsAuthenticated])
    def batch_schedule(self, request, book_pk=None):
        """
        Массовое планирование публикации глав.
        Ожидает: {'chapter_ids': [1, 2, 3, ...], 'scheduled_publish_at': '2024-01-01T12:00:00Z', 'publish_as_finished': true/false}
        """
        book = get_object_or_404(Book, pk=book_pk)
        if book.author != request.user:
            return Response({'error': 'Нет прав'}, status=status.HTTP_403_FORBIDDEN)
        
        chapter_ids = request.data.get('chapter_ids', [])
        scheduled_publish_at = request.data.get('scheduled_publish_at')
        publish_as_finished = request.data.get('publish_as_finished', False)
        
        if not isinstance(chapter_ids, list) or not chapter_ids:
            return Response({'error': 'Не переданы ID глав'}, status=status.HTTP_400_BAD_REQUEST)
        
        if not scheduled_publish_at:
            return Response({'error': 'Не указано время публикации'}, status=status.HTTP_400_BAD_REQUEST)

        # Парсим время публикации
        try:
            from django.utils.dateparse import parse_datetime
            import logging
            logger = logging.getLogger(__name__)

            logger.info(f"batch_schedule: получено время {scheduled_publish_at} (тип: {type(scheduled_publish_at)})")

            if isinstance(scheduled_publish_at, str):
                scheduled_publish_at = parse_datetime(scheduled_publish_at)
            if not scheduled_publish_at:
                return Response({'error': 'Неверный формат времени публикации'}, status=status.HTTP_400_BAD_REQUEST)

            logger.info(f"batch_schedule: распарсено время {scheduled_publish_at}")
        except Exception as e:
            logger.error(f"batch_schedule: ошибка парсинга времени: {e}")
            return Response({'error': f'Ошибка парсинга времени: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Получаем главы для планирования
        chapters_to_schedule = BookChapter.objects.filter(
            id__in=chapter_ids,
            book=book,
            is_published=False  # Планировать можно только неопубликованные главы
        )

        if not chapters_to_schedule.exists():
            return Response({'error': 'Неопубликованные главы не найдены'}, status=status.HTTP_404_NOT_FOUND)

        # Если планируем с флагом "завершить произведение", применяем умную логику планирования
        chapters_to_reschedule_ids = list(chapter_ids)  # Изначально только выбранные пользователем

        logger.info(f"batch_schedule: publish_as_finished = {publish_as_finished}")
        logger.info(f"batch_schedule: выбрано пользователем {len(chapter_ids)} глав: {chapter_ids}")
        logger.info(f"batch_schedule: финальное время: {scheduled_publish_at}")

        # Логируем только общую статистику
        all_chapters = BookChapter.objects.filter(book=book, is_published=False)
        scheduled_count = all_chapters.filter(scheduled_publish_at__isnull=False).count()
        logger.info(f"batch_schedule: всего неопубликованных глав: {all_chapters.count()}, из них запланировано: {scheduled_count}")

        if publish_as_finished:
            # ОПТИМИЗИРОВАНО: Один запрос для получения всех неопубликованных глав с их планированием
            from django.db.models import Q

            all_unpublished_chapters = BookChapter.objects.filter(
                book=book,
                is_published=False
            ).values('id', 'scheduled_publish_at')

            # Разделяем главы по категориям в Python (быстрее чем отдельные запросы)
            chapters_without_schedule_ids = []
            chapters_scheduled_later_ids = []
            chapters_scheduled_earlier_ids = []

            for chapter in all_unpublished_chapters:
                if chapter['scheduled_publish_at'] is None:
                    chapters_without_schedule_ids.append(chapter['id'])
                elif chapter['scheduled_publish_at'] > scheduled_publish_at:
                    chapters_scheduled_later_ids.append(chapter['id'])
                elif chapter['scheduled_publish_at'] < scheduled_publish_at:
                    chapters_scheduled_earlier_ids.append(chapter['id'])

            logger.info(f"batch_schedule: найдено {len(chapters_without_schedule_ids)} глав без планирования")
            logger.info(f"batch_schedule: найдено {len(chapters_scheduled_later_ids)} глав с планированием позже {scheduled_publish_at}")
            logger.info(f"batch_schedule: найдено {len(chapters_scheduled_earlier_ids)} глав с планированием раньше {scheduled_publish_at}")

            # Создаем множества для быстрого поиска
            without_schedule_ids = set(chapters_without_schedule_ids)
            later_ids = set(chapters_scheduled_later_ids)
            earlier_ids = set(chapters_scheduled_earlier_ids)

            # ОПТИМИЗИРОВАНО: Анализируем выбранные пользователем главы
            selected_chapters_with_early_schedule = [ch_id for ch_id in chapter_ids if ch_id in earlier_ids]
            selected_chapters_with_late_schedule = [ch_id for ch_id in chapter_ids if ch_id in later_ids]
            selected_chapters_without_schedule = [ch_id for ch_id in chapter_ids if ch_id in without_schedule_ids]

            logger.info(f"batch_schedule: из выбранных пользователем - {len(selected_chapters_with_early_schedule)} с ранним планированием, {len(selected_chapters_with_late_schedule)} с поздним планированием, {len(selected_chapters_without_schedule)} без планирования")

            # ОПТИМИЗИРОВАНО: Добавляем к перепланированию только те, что НЕ выбраны пользователем
            chapters_without_schedule_to_add = [ch_id for ch_id in without_schedule_ids if ch_id not in chapter_ids]
            chapters_scheduled_later_to_add = [ch_id for ch_id in later_ids if ch_id not in chapter_ids]
            chapters_scheduled_earlier_not_selected = [ch_id for ch_id in earlier_ids if ch_id not in chapter_ids]

            if chapters_without_schedule_to_add:
                chapters_to_reschedule_ids.extend(chapters_without_schedule_to_add)
                logger.info(f"batch_schedule: добавлено {len(chapters_without_schedule_to_add)} глав без планирования (не выбранных пользователем)")

            if chapters_scheduled_later_to_add:
                chapters_to_reschedule_ids.extend(chapters_scheduled_later_to_add)
                logger.info(f"batch_schedule: перенесено {len(chapters_scheduled_later_to_add)} глав с более поздним планированием (не выбранных пользователем)")

            # Логируем главы с ранним планированием (их НЕ трогаем, если не выбраны пользователем)
            if chapters_scheduled_earlier_not_selected:
                logger.info(f"batch_schedule: НЕ трогаем {len(chapters_scheduled_earlier_not_selected)} глав с ранним планированием (не выбранных пользователем)")

            # ВАЖНО: Если пользователь выбрал главы с ранним планированием, предупреждаем
            if selected_chapters_with_early_schedule:
                logger.warning(f"batch_schedule: ВНИМАНИЕ! Пользователь принудительно перепланировал {len(selected_chapters_with_early_schedule)} глав с РАННИМ планированием")

            logger.info(f"batch_schedule: итого для перепланирования: {len(chapters_to_reschedule_ids)} глав")

        # Получаем главы для планирования (только те, что нужно перепланировать)
        chapters_to_schedule = BookChapter.objects.filter(
            id__in=chapters_to_reschedule_ids,
            book=book,
            is_published=False
        )
        
        # ОПТИМИЗИРОВАНО: Массовое планирование глав
        import time
        start_time = time.time()

        # ОПТИМИЗИРОВАНО: Создаем одну задачу для всех глав на одно время
        try:
            from books.tasks import publish_chapters_batch_task
            from celery.result import AsyncResult
            chapters_list = list(chapters_to_schedule)

            # ОПТИМИЗИРОВАНО: Отмена задач в фоновом режиме (не блокируем пользователя)
            unique_task_ids = set()
            for chapter in chapters_list:
                if chapter.celery_task_id:
                    unique_task_ids.add(chapter.celery_task_id)

            # Отменяем задачи асинхронно в отдельном потоке
            if unique_task_ids:
                import threading
                def revoke_tasks_async():
                    for task_id in unique_task_ids:
                        try:
                            AsyncResult(task_id).revoke(terminate=True)
                        except Exception:
                            pass

                # Запускаем отмену в фоновом потоке
                revoke_thread = threading.Thread(target=revoke_tasks_async, daemon=True)
                revoke_thread.start()
                logger.info(f"batch_schedule: запущена асинхронная отмена {len(unique_task_ids)} задач")

            # Преобразуем время в UTC для Celery
            import pytz
            if scheduled_publish_at.tzinfo is None:
                # Если нет timezone info, считаем что время в настройках проекта
                tz = pytz.timezone(settings.TIME_ZONE)
                scheduled_publish_at = tz.localize(scheduled_publish_at)
            eta_utc = scheduled_publish_at.astimezone(pytz.utc)

            # Создаем задачу ТОЛЬКО для перепланируемых глав
            logger.info(f"batch_schedule: создаем задачу для {len(chapters_to_reschedule_ids)} глав на время {eta_utc}")
            task = publish_chapters_batch_task.apply_async(
                args=[book.id, chapters_to_reschedule_ids, publish_as_finished],
                eta=eta_utc
            )
            logger.info(f"batch_schedule: создана задача {task.id}")

            # Обновляем только перепланируемые главы
            from django.db import transaction
            with transaction.atomic():
                updated_count = chapters_to_schedule.update(
                    scheduled_publish_at=scheduled_publish_at,
                    celery_task_id=task.id,
                    publish_as_finished=publish_as_finished
                )
                logger.info(f"batch_schedule: перепланировано {updated_count} глав на время {scheduled_publish_at}")

            # НЕ трогаем главы с ранним планированием - у них остаются свои задачи

            scheduled_count = len(chapters_to_reschedule_ids)
            operation_time = time.time() - start_time

            if publish_as_finished:
                logger.info(f"МАССОВО запланировано {scheduled_count} глав книги {book.id} ОДНОЙ задачей с ЗАВЕРШЕНИЕМ произведения за {operation_time:.2f} сек")
            else:
                logger.info(f"МАССОВО запланировано {scheduled_count} глав книги {book.id} ОДНОЙ задачей за {operation_time:.2f} сек")

        except ImportError:
            logger.error("batch_schedule: ImportError - планирование публикации недоступно")
            return Response({'error': 'Планирование публикации недоступно'}, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception as e:
            logger.error(f"batch_schedule: неожиданная ошибка: {e}")
            return Response({'error': f'Ошибка планирования: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'scheduled_count': scheduled_count,
            'scheduled_at': scheduled_publish_at
        }, status=status.HTTP_200_OK)

    @action(detail=False, methods=['patch'], url_path='batch_cancel_schedule', permission_classes=[IsAuthenticated])
    def batch_cancel_schedule(self, request, book_pk=None):
        """
        Массовая отмена планирования публикации глав.
        Ожидает: {'chapter_ids': [1, 2, 3, ...]}
        """
        book = get_object_or_404(Book, pk=book_pk)
        if book.author != request.user:
            return Response({'error': 'Нет прав'}, status=status.HTTP_403_FORBIDDEN)

        chapter_ids = request.data.get('chapter_ids', [])
        if not chapter_ids:
            return Response({'error': 'Не переданы ID глав'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            import logging
            logger = logging.getLogger(__name__)

            # Получаем главы для отмены планирования
            chapters_to_cancel = BookChapter.objects.filter(
                id__in=chapter_ids,
                book=book,
                scheduled_publish_at__isnull=False  # Только запланированные главы
            )

            if not chapters_to_cancel.exists():
                return Response({'error': 'Запланированные главы не найдены'}, status=status.HTTP_404_NOT_FOUND)

            # Группируем главы по celery_task_id для умной отмены
            from collections import defaultdict
            task_groups = defaultdict(list)

            for chapter in chapters_to_cancel:
                if chapter.celery_task_id:
                    task_groups[chapter.celery_task_id].append(chapter)

            cancelled_count = 0

            # Обрабатываем каждую группу задач
            for task_id, chapters_in_task in task_groups.items():
                # Находим все главы с этой же задачей (не только выбранные для отмены)
                all_chapters_in_task = BookChapter.objects.filter(
                    book=book,
                    celery_task_id=task_id,
                    is_published=False
                )

                # Главы, которые остаются запланированными
                remaining_chapters = all_chapters_in_task.exclude(
                    id__in=[ch.id for ch in chapters_in_task]
                )

                # Отменяем старую задачу
                try:
                    from celery.result import AsyncResult
                    AsyncResult(task_id).revoke(terminate=True)
                    logger.info(f"batch_cancel_schedule: отменена задача {task_id}")
                except Exception as e:
                    logger.warning(f"batch_cancel_schedule: не удалось отменить задачу {task_id}: {e}")

                # Если есть оставшиеся главы - создаем новую задачу
                if remaining_chapters.exists():
                    # Берем время публикации от первой оставшейся главы
                    first_remaining = remaining_chapters.first()
                    scheduled_time = first_remaining.scheduled_publish_at
                    publish_as_finished = first_remaining.publish_as_finished

                    try:
                        from books.tasks import publish_chapters_batch_task
                        import pytz

                        if scheduled_time.tzinfo is None:
                            tz = pytz.timezone(settings.TIME_ZONE)
                            scheduled_time = tz.localize(scheduled_time)
                        eta_utc = scheduled_time.astimezone(pytz.utc)

                        # Создаем новую задачу для оставшихся глав
                        remaining_ids = list(remaining_chapters.values_list('id', flat=True))
                        new_task = publish_chapters_batch_task.apply_async(
                            args=[book.id, remaining_ids, publish_as_finished],
                            eta=eta_utc
                        )

                        # Обновляем task_id у оставшихся глав
                        remaining_chapters.update(celery_task_id=new_task.id)
                        logger.info(f"batch_cancel_schedule: создана новая задача {new_task.id} для {remaining_chapters.count()} оставшихся глав")

                    except Exception as e:
                        logger.error(f"batch_cancel_schedule: ошибка при создании новой задачи: {e}")

            # Отменяем планирование для выбранных глав одним запросом
            from django.db import transaction
            with transaction.atomic():
                updated_count = chapters_to_cancel.update(
                    scheduled_publish_at=None,
                    celery_task_id=None,
                    publish_as_finished=None
                )
                cancelled_count = updated_count

            logger.info(f"batch_cancel_schedule: отменено планирование для {cancelled_count} глав книги {book.id}")

            return Response({
                'cancelled_count': cancelled_count
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"batch_cancel_schedule: неожиданная ошибка: {e}")
            return Response({'error': f'Ошибка отмены планирования: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

def get_book_category(instance):
    # Используем новое поле status вместо is_published и is_finished
    status_to_category = {
        'draft': 'drafts',
        'in_progress': 'in_progress',
        'finished': 'finished'
    }
    return status_to_category.get(instance.status, 'drafts')

STATE_BEFORE_FIRST_CHAPTER = 0
STATE_IN_CHAPTER_PROCESSING = 1

def process_docx_content(content, existing_chapters=None):
    """
    Process DOCX content to extract chapters and preserve formatting
    
    Args:
        content: The DOCX content to process (list of python-docx paragraph objects)
        existing_chapters: List of existing chapters with their numbers and publication status
    """
    chapters = []
    current_chapter_title = None
    current_chapter_content = []
    chapter_number = 0
    parsing_state = STATE_BEFORE_FIRST_CHAPTER

    # Define special chapter markers for beginning
    beginning_chapters = {
        'вступление': 'introduction',
        'пролог': 'prologue',
        'предисловие': 'preface'
    }
    
    # Define special chapter markers for end
    end_chapters = {
        'эпилог': 'epilogue',
        'послесловие': 'afterword'
    }
    
    # Define chapter number words
    chapter_number_words = {
        'первая': 1, 'вторая': 2, 'третья': 3, 'четвертая': 4, 'пятая': 5,
        'шестая': 6, 'седьмая': 7, 'восьмая': 8, 'девятая': 9, 'десятая': 10,
        'first': 1, 'second': 2, 'third': 3, 'fourth': 4, 'fifth': 5,
        'sixth': 6, 'seventh': 7, 'eighth': 8, 'ninth': 9, 'tenth': 10
    }
    
    # If we have existing chapters, initialize chapter_number based on published chapters
    if existing_chapters:
        max_published_num = 0
        has_published_beginning_chapter = False
        for chapter in existing_chapters:
            if chapter.get('is_published'):
                title = chapter.get('title', '').lower()
                # Check for numbered chapters
                match = re.search(r'глава\s*(\d+)', title)
                if match:
                    try:
                        num = int(match.group(1))
                        max_published_num = max(max_published_num, num)
                    except ValueError:
                        pass
                # Check for published beginning special chapters
                if title in beginning_chapters:
                    has_published_beginning_chapter = True
        
        chapter_number = max_published_num
        # If a beginning chapter was published but no numbered chapters, the next numbered chapter starts at 1
        if max_published_num == 0 and has_published_beginning_chapter:
            # chapter_number remains 0, will be incremented to 1 for the first regular chapter.
            # parsing_state should be in_chapter_processing
            parsing_state = STATE_IN_CHAPTER_PROCESSING
        elif max_published_num > 0 or has_published_beginning_chapter:
            parsing_state = STATE_IN_CHAPTER_PROCESSING
    
    for paragraph in content:
        text = paragraph.text.strip()
        lower_text = text.lower()

        if not text:
            continue

        # Critical: Always explicitly skip unwanted book/part titles, regardless of state
        if lower_text.startswith('книга') or lower_text.startswith('часть'):
            continue 

        is_current_paragraph_a_chapter_title = False
        current_chapter_type_category = None # 'beginning', 'regular', 'end'

        # 1. Check for Beginning Special Chapters (exact match, case-insensitive)
        if lower_text in beginning_chapters:
            current_chapter_type_category = 'beginning'
            is_current_paragraph_a_chapter_title = True
        # 2. Check for End Special Chapters (exact match, case-insensitive) - these increment like regular chapters
        elif lower_text in end_chapters:
            current_chapter_type_category = 'end'
            is_current_paragraph_a_chapter_title = True
        # 3. Check for Regular Chapters (must start with "Глава" or "Chapter" followed by number/word)
        # Using regex for robust matching at the start of the string.
        else:
            # Regex for "Глава N" or "Chapter N"
            match_num = re.match(r'^(глава|chapter)\s*(\d+)', lower_text)
            # Regex for "Глава первая" etc.
            match_word = re.match(r'^(глава)\s*(' + '|'.join(chapter_number_words.keys()) + r')', lower_text)

            if match_num or match_word:
                current_chapter_type_category = 'regular'
                is_current_paragraph_a_chapter_title = True
        
        # State management for skipping initial text
        if parsing_state == STATE_BEFORE_FIRST_CHAPTER:
            if not is_current_paragraph_a_chapter_title:
                continue # Skip current paragraph, still looking for first chapter
            else:
                # First chapter title found, transition to processing state
                parsing_state = STATE_IN_CHAPTER_PROCESSING
                # If the first chapter found is a beginning special chapter in a new book, it's Chapter 0
                if current_chapter_type_category == 'beginning' and not existing_chapters:
                    chapter_number = 0 # Chapter 0 for "Вступление" etc.
                # If the first chapter found is a regular chapter (and not in an existing book where Chapter 0 might exist)
                # then chapter_number should be 0 to become 1 when incremented below.
                elif current_chapter_type_category == 'regular' and not existing_chapters:
                    chapter_number = 0 # Will be incremented to 1
                # For end chapters appearing as first (unlikely but robust) or for existing books, chapter_number will be incremented.
                
        # Process the chapter content if a chapter title was just identified
        if is_current_paragraph_a_chapter_title:
            # If we have a current chapter, save it before starting a new one
            if current_chapter_title:
                chapters.append({
                    'title': current_chapter_title,
                    'content': '\n'.join(current_chapter_content),
                    'type': 'chapter'
                })
            
            # Determine title and number for the new chapter
            if current_chapter_type_category == 'beginning':
                current_chapter_title = text # Original title, no prefix (e.g., "Вступление")
                # chapter_number is already handled by state transition for new books
            elif current_chapter_type_category == 'regular':
                chapter_number += 1
                current_chapter_title = f"Глава {chapter_number}: {text}"
            elif current_chapter_type_category == 'end':
                chapter_number += 1 # Epilogue/Afterword are regular numbered chapters, just different naming
                current_chapter_title = text # Original title, no prefix (e.g., "Эпилог")
            
            current_chapter_content = [] # Reset content for new chapter
            continue # Do not add the title paragraph to its own content
            
        # Add paragraph to current chapter content only if we are in processing state and a chapter has been started
        if parsing_state == STATE_IN_CHAPTER_PROCESSING and current_chapter_title:
            formatted_text = text
            # Preserve formatting from runs
            # This assumes 'paragraph' objects have a 'runs' attribute (from python-docx)
            for run in paragraph.runs:
                if run.bold:
                    formatted_text = f'**{formatted_text}**'
                if run.italic:
                    formatted_text = f'*{formatted_text}*'
                if run.underline:
                    formatted_text = f'__{formatted_text}__'
            current_chapter_content.append(formatted_text)
    
    # Add the last chapter if it exists and was properly being processed
    if current_chapter_title and current_chapter_content and parsing_state == STATE_IN_CHAPTER_PROCESSING:
        chapters.append({
            'title': current_chapter_title,
            'content': '\n'.join(current_chapter_content),
            'type': 'chapter'
        })
    
    return chapters

def delete_chapter_images_folder(chapter):
    """
    Удаляет все изображения главы и папку главы на S3
    """
    storage = PrivateMediaStorage()
    
    # Удаляем все записи изображений из базы данных для этой главы
    images = BookChapterImage.objects.filter(chapter=chapter)
    for image in images:
        if storage.exists(image.path):
            try:
                storage.delete(image.path)
                logger.info(f"Удален файл изображения: {image.path}")
            except Exception as e:
                logger.error(f"Ошибка при удалении изображения {image.path}: {e}")
        image.delete()
    
    # Если у нас есть ID главы в пути, пытаемся удалить папку главы
    if chapter.id:
        group_folder = get_group_folder(chapter.book.id)
        chapter_folder_path = f"book_pics/{group_folder}/{chapter.book.id}/chapics/{chapter.id}/"
        
        try:
            # Получаем S3 клиент
            s3 = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                endpoint_url=settings.AWS_S3_ENDPOINT_URL,
                region_name=settings.AWS_S3_REGION_NAME,
            )
            bucket = settings.AWS_STORAGE_BUCKET_NAME
            full_prefix = f"{storage.location}/{chapter_folder_path}"
            
            # Список всех объектов в папке главы
            response = s3.list_objects_v2(Bucket=bucket, Prefix=full_prefix)
            
            if 'Contents' in response:
                # Удаляем все файлы в папке
                objects_to_delete = [{'Key': obj['Key']} for obj in response['Contents']]
                if objects_to_delete:
                    s3.delete_objects(
                        Bucket=bucket,
                        Delete={'Objects': objects_to_delete}
                    )

            
        except Exception as e:
            logger.error(f"Ошибка при удалении папки главы {chapter_folder_path}: {e}")


def delete_book_images_folder(book):
    """
    Удаляет все изображения книги и папку книги на S3
    """
    storage = PrivateMediaStorage()
    
    # Удаляем все записи изображений из базы данных для этой книги
    images = BookChapterImage.objects.filter(book=book)
    for image in images:
        if storage.exists(image.path):
            try:
                storage.delete(image.path)
                logger.info(f"Удален файл изображения: {image.path}")
            except Exception as e:
                logger.error(f"Ошибка при удалении изображения {image.path}: {e}")
        image.delete()
    
    # Удаляем папку книги
    group_folder = get_group_folder(book.id)
    book_folder_path = f"book_pics/{group_folder}/{book.id}/"
    
    try:
        # Получаем S3 клиент
        s3 = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            endpoint_url=settings.AWS_S3_ENDPOINT_URL,
            region_name=settings.AWS_S3_REGION_NAME,
        )
        bucket = settings.AWS_STORAGE_BUCKET_NAME
        full_prefix = f"{storage.location}/{book_folder_path}"
        
        # Список всех объектов в папке книги
        response = s3.list_objects_v2(Bucket=bucket, Prefix=full_prefix)
        
        if 'Contents' in response:
            # Удаляем все файлы в папке
            objects_to_delete = [{'Key': obj['Key']} for obj in response['Contents']]
            if objects_to_delete:
                s3.delete_objects(
                    Bucket=bucket,
                    Delete={'Objects': objects_to_delete}
                )
                

    except Exception as e:
        logger.error(f"Ошибка при удалении папки книги {book_folder_path}: {e}")


def delete_images_batch(images_list):
    """
    Пакетное удаление изображений из S3 и базы данных.
    Более эффективно чем удаление по одному изображению.
    """
    if not images_list:
        return

    import time
    start_time = time.time()
    logger.info(f"Начало пакетного удаления {len(images_list)} изображений")

    storage = PrivateMediaStorage()

    # Собираем все пути файлов для удаления
    files_to_delete = []
    for image in images_list:
        if image.path:
            files_to_delete.append(image.path)

    # Пакетное удаление файлов из S3
    if files_to_delete:
        try:
            s3 = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                endpoint_url=settings.AWS_S3_ENDPOINT_URL,
                region_name=settings.AWS_S3_REGION_NAME,
            )
            bucket = settings.AWS_STORAGE_BUCKET_NAME

            # Формируем список объектов для удаления
            objects_to_delete = []
            for file_path in files_to_delete:
                full_path = f"{storage.location}/{file_path}"
                objects_to_delete.append({'Key': full_path})

            # Удаляем файлы пакетно (до 1000 за раз)
            batch_size = 1000
            for i in range(0, len(objects_to_delete), batch_size):
                batch = objects_to_delete[i:i + batch_size]
                if batch:
                    s3.delete_objects(
                        Bucket=bucket,
                        Delete={'Objects': batch}
                    )
                    logger.info(f"Удалено {len(batch)} файлов из S3 (пакет {i//batch_size + 1})")

        except Exception as e:
            logger.error(f"Ошибка при пакетном удалении файлов из S3: {e}")

    # Пакетное удаление записей из базы данных
    try:
        image_ids = [img.id for img in images_list]
        deleted_count = BookChapterImage.objects.filter(id__in=image_ids).delete()[0]
        logger.info(f"Удалено {deleted_count} записей изображений из базы данных")
    except Exception as e:
        logger.error(f"Ошибка при пакетном удалении записей изображений: {e}")

    total_time = time.time() - start_time
    logger.info(f"Пакетное удаление {len(images_list)} изображений завершено за {total_time:.2f} сек")


def get_group_folder(book_id):
    group = book_id // 500
    letter_index = group // 1000
    letter = chr(ord('a') + letter_index)
    number = f"{group % 1000:03d}"
    return f"{letter}{number}"


def random_prefix():
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
