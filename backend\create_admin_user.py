#!/usr/bin/env python3
"""
Скрипт для создания пользователей с запрещенными логинами.
Может использоваться только администратором в обход стандартных проверок.

Использование:
python create_admin_user.py <username> <email> <display_name> [password]

Если пароль не указан, будет сгенерирован автоматически.
"""

import os
import sys
import django
from pathlib import Path

# Добавляем путь к проекту Django
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# Настраиваем Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
import secrets
import string

User = get_user_model()

def generate_password(length=12):
    """Генерирует случайный пароль"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for i in range(length))
    return password

def create_admin_user(username, email, display_name, password=None):
    """
    Создает пользователя с запрещенным логином в обход стандартных проверок
    """
    # Запрещенные слова (для информации)
    forbidden_words = [
        'admin', 'administrator', 'moderator', 'login', 'boss', 'director', 'litportal'
    ]
    
    # Проверяем содержит ли логин запрещенные слова
    contains_forbidden = any(word in username.lower() for word in forbidden_words)
    
    if contains_forbidden:
        print(f"⚠️  ВНИМАНИЕ: Логин '{username}' содержит запрещенные слова!")
        print("Этот логин будет создан в обход стандартных проверок.")
        confirm = input("Продолжить? (yes/no): ").lower().strip()
        if confirm not in ['yes', 'y', 'да', 'д']:
            print("Отменено.")
            return False
    
    # Проверяем существует ли пользователь
    if User.objects.filter(username=username).exists():
        print(f"❌ Ошибка: Пользователь с логином '{username}' уже существует!")
        return False
    
    if User.objects.filter(email=email).exists():
        print(f"❌ Ошибка: Пользователь с email '{email}' уже существует!")
        return False
    
    # Генерируем пароль если не указан
    if not password:
        password = generate_password()
        print(f"🔑 Сгенерирован пароль: {password}")
    
    try:
        # Создаем пользователя
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            display_name=display_name
        )
        
        # Устанавливаем системный аватар
        user.update_system_avatar()
        user.save()
        user.update_avatar_urls()
        
        print(f"✅ Пользователь успешно создан!")
        print(f"   Логин: {username}")
        print(f"   Email: {email}")
        print(f"   Отображаемое имя: {display_name}")
        print(f"   ID: {user.id}")
        
        if contains_forbidden:
            print(f"   🛡️  Создан с запрещенным логином в обход проверок")
        
        return True
        
    except ValidationError as e:
        print(f"❌ Ошибка валидации: {e}")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False

def main():
    if len(sys.argv) < 4:
        print("Использование: python create_admin_user.py <username> <email> <display_name> [password]")
        print("Пример: python create_admin_user.py admin_user <EMAIL> 'Администратор' mypassword123")
        return
    
    username = sys.argv[1]
    email = sys.argv[2]
    display_name = sys.argv[3]
    password = sys.argv[4] if len(sys.argv) > 4 else None
    
    print("🚀 Создание пользователя с админскими правами...")
    print(f"   Логин: {username}")
    print(f"   Email: {email}")
    print(f"   Отображаемое имя: {display_name}")
    print()
    
    success = create_admin_user(username, email, display_name, password)
    
    if success:
        print()
        print("✨ Готово! Пользователь может войти используя:")
        print(f"   Email: {email}")
        print(f"   Пароль: {password if password else 'сгенерированный выше'}")
    else:
        print("💥 Не удалось создать пользователя.")

if __name__ == "__main__":
    main() 